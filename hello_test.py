#!/usr/bin/env python3
import datetime
import random

def hello():
    """Simple hello function for testing"""
    print("Hello, World!")
    print("This is a test program.")
    return "Hello test completed!"

def personalized_hello(name="User"):
    """Personalized greeting function"""
    greetings = ["Hello", "Hi", "Hey", "Greetings", "Welcome"]
    greeting = random.choice(greetings)
    return f"{greeting}, {name}!"

def time_based_hello():
    """Time-aware greeting function"""
    current_hour = datetime.datetime.now().hour
    
    if 5 <= current_hour < 12:
        return "Good morning! ☀️"
    elif 12 <= current_hour < 17:
        return "Good afternoon! 🌤️"
    elif 17 <= current_hour < 22:
        return "Good evening! 🌅"
    else:
        return "Good night! 🌙"

def multilingual_hello():
    """Hello in different languages"""
    hellos = {
        "English": "Hello",
        "Spanish": "Hola",
        "French": "Bonjour",
        "German": "Hallo",
        "Italian": "Ciao",
        "Japanese": "こんにちは",
        "Chinese": "你好",
        "Arabic": "مرحبا"
    }
    
    for lang, greeting in hellos.items():
        print(f"{lang}: {greeting}")

if __name__ == "__main__":
    print("=== Basic Hello ===")
    result = hello()
    print(f"Result: {result}\n")
    
    print("=== Personalized Hello ===")
    print(personalized_hello("Alice"))
    print(personalized_hello("Bob"))
    print(personalized_hello())
    
    print("\n=== Time-based Hello ===")
    print(time_based_hello())
    
    print("\n=== Multilingual Hello ===")
    multilingual_hello()