import json
import os
from termcolor import colored
from openai import OpenAI
from typing import List, Dict, Any
try:
    from exa_py import Exa
except ImportError:
    print(colored("Please install exa-py package", "red"))
    exit(1)

# Constants
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
EXA_API_KEY = os.getenv("EXA_API_KEY")
MAX_SOURCES = 3

# Initialize clients
try:
    client = OpenAI(api_key=OPENAI_API_KEY)
    exa_client = Exa(api_key=EXA_API_KEY)
    print(colored("✓ API clients initialized successfully", "green"))
except Exception as e:
    print(colored(f"Error initializing API clients: {str(e)}", "red"))
    exit(1)

def extract_claims(text: str) -> List[str]:
    """Extract factual claims from text using OpenAI."""
    try:
        print(colored("\nExtracting claims...", "cyan"))
        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "system", "content": "Extract factual claims from the text. Return as a JSON object with a 'claims' key containing an array of strings."},
                {"role": "user", "content": f"Extract claims from: {text}"}
            ],
            response_format={"type": "json_object"}
        )
        result = json.loads(response.choices[0].message.content)
        claims = result.get('claims', [])
        print(colored(f"Found {len(claims)} claims:", "green"))
        for i, claim in enumerate(claims, 1):
            print(colored(f"{i}. {claim}", "yellow"))
        return claims
    except Exception as e:
        print(colored(f"Error extracting claims: {str(e)}", "red"))
        return []

def search_evidence(claim: str) -> List[str]:
    """Search for evidence using Exa."""
    try:
        print(colored(f"Searching evidence for: {claim}", "cyan"))
        results = exa_client.search_and_contents(
            claim, 
            num_results=MAX_SOURCES,
            text=True,
            highlights=True
        )
        
        sources = []
        for result in results.results:
            content = result.text or ""
            highlights = "\nHighlights: " + "\n".join(result.highlights) if hasattr(result, 'highlights') and result.highlights else ""
            source = f"URL: {result.url}\nContent: {content}{highlights}"
            sources.append(source)
            
        print(colored(f"Found {len(sources)} sources", "green"))
        return sources
    except Exception as e:
        print(colored(f"Error searching evidence: {str(e)}", "red"))
        return []

def verify_claim(claim: str, sources: List[str]) -> Dict[str, Any]:
    """Verify claim against sources."""
    try:
        print(colored("Verifying claim...", "cyan"))
        if not sources:
            return {
                "claim": claim,
                "assessment": "Insufficient information",
                "confidence": 0.0
            }
        
        sources_text = "\n\n".join(sources)
        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "system", "content": "You are a fact checker. Assess if the claim is supported by the sources. Return a JSON with keys: assessment (SUPPORTED/REFUTED/UNCERTAIN) and confidence (0-1)."},
                {"role": "user", "content": f"Claim: {claim}\nSources: {sources_text}"}
            ],
            response_format={"type": "json_object"}
        )
        result = json.loads(response.choices[0].message.content)
        return {**result, "claim": claim}
    except Exception as e:
        print(colored(f"Error verifying claim: {str(e)}", "red"))
        return {"claim": claim, "assessment": "Error", "confidence": 0.0}

def check_text(text: str) -> List[Dict[str, Any]]:
    """Main function to check text for hallucinations."""
    try:
        claims = extract_claims(text)
        results = []
        
        for i, claim in enumerate(claims, 1):
            print(colored(f"\n[Claim {i}/{len(claims)}]", "blue", attrs=['bold']))
            print(colored(f"Checking: {claim}", "cyan"))
            
            sources = search_evidence(claim)
            verification = verify_claim(claim, sources)
            
            # Print immediate result for this claim
            color = "green" if verification["assessment"] == "SUPPORTED" else "red" if verification["assessment"] == "REFUTED" else "yellow"
            print(colored(f"Result: {verification['assessment']} (Confidence: {verification['confidence']:.2f})", color))
            
            results.append(verification)
            
        return results
    except Exception as e:
        print(colored(f"Error checking text: {str(e)}", "red"))
        return []

if __name__ == "__main__":
    # Example usage
    print(colored("\n=== Hallucination Detector ===", "cyan", attrs=['bold']))
    text = input(colored("\nEnter text to check for hallucinations: ", "yellow"))
    results = check_text(text)
    
    print(colored("\n=== Final Results ===", "green", attrs=['bold']))
    for i, result in enumerate(results, 1):
        color = "green" if result["assessment"] == "SUPPORTED" else "red" if result["assessment"] == "REFUTED" else "yellow"
        print(colored(f"\nClaim {i}:", "blue", attrs=['bold']))
        print(colored(f"Statement: {result['claim']}", "white"))
        print(colored(f"Assessment: {result['assessment']}", color))
        print(colored(f"Confidence: {result['confidence']:.2f}", color)) 