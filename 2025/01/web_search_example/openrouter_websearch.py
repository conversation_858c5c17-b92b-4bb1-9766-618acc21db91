import os
import requests
from termcolor import colored

# CONSTANTS
OPENROUTER_API_KEY = os.getenv("OPENROUTER_API_KEY", "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")
OPENROUTER_URL = "https://openrouter.ai/api/v1/chat/completions"

try:
    print(colored("🔍 Making API request with web search enabled...", "cyan"))
    
    headers = {
        "Authorization": f"Bearer {OPENROUTER_API_KEY}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "model": "openai/gpt-4o:online",
        "messages": [
            {
                "role": "user",
                "content": "what other movies are like 'io'. list all links you have considered at the end"
            }
        ],
        "plugins": [{
            "id": "web",
            "max_results": 5,
        }]
    }
    
    response = requests.post(
        OPENROUTER_URL,
        headers=headers,
        json=payload
    )
    
    response.raise_for_status()
    response_json = response.json()
    
    print(colored("✅ Response received successfully!", "green"))
    
    # Extract content and format output
    content = response_json['choices'][0]['message']['content']
    
    # Split content and citations
    main_text = content.split('[')[0].strip()
    citations = [f"[{citation}" for citation in content.split('[')[1:] if ']' in citation]
    
    print("\n" + colored("Response:", "yellow"))
    print(main_text)
    
    if citations:
        print("\n" + colored("Sources:", "cyan"))
        for citation in citations:
            print(citation)

except requests.exceptions.RequestException as e:
    print(colored(f"❌ Network or API Error: {str(e)}", "red"))
    if hasattr(e.response, 'json'):
        print(colored("API Error Details:", "red"))
        print(e.response.json())
except Exception as e:
    print(colored(f"❌ Error occurred: {str(e)}", "red"))
    print(colored("Detailed error information:", "red"))
    print(colored(f"Type: {type(e).__name__}", "red"))
    print(colored(f"Details: {str(e)}", "red"))