# Web Search and Hallucination Detection Tools

This repository contains a collection of Python tools for web search integration and hallucination detection using various AI models and search APIs.

## Tools Overview

### 1. Hallucination Detector (`hallucination_detector.py`)
A tool that analyzes text for potential hallucinations by:
- Extracting factual claims from input text using GPT-4
- Searching for supporting evidence using Exa search
- Verifying claims against found sources
- Providing confidence scores and assessments

### 2. OpenAI Web Search Tutorial (`openai_websearch_tutorial.py`)
A RAG (Retrieval-Augmented Generation) implementation that:
- Combines OpenAI's GPT models with Exa search
- Supports customizable date ranges for search results
- Demonstrates different search configurations and use cases
- Includes example queries for various topics

### 3. OpenRouter Web Search (`openrouter_websearch.py`)
A simple implementation of web-enabled GPT-4 using OpenRouter that:
- Makes API requests with web search capability
- Formats responses with citations
- Handles errors gracefully

## Prerequisites

You'll need the following API keys:
- `OPENAI_API_KEY` - OpenAI API key
- `EXA_API_KEY` - Exa API key
- `OPENROUTER_API_KEY` - OpenRouter API key

## Installation

1. Install required packages:
```bash
pip install openai exa-py termcolor requests
```

2. Set up environment variables:
```bash
export OPENAI_API_KEY="your-openai-key"
export EXA_API_KEY="your-exa-key"
export OPENROUTER_API_KEY="your-openrouter-key"
```

## Usage

### Hallucination Detector
```python
python hallucination_detector.py
# Follow the prompts to input text for hallucination checking
```

### OpenAI Web Search Tutorial
```python
python openai_websearch_tutorial.py
# Runs example queries with different date ranges and configurations
```

### OpenRouter Web Search
```python
python openrouter_websearch.py
# Demonstrates web-enabled search with citation tracking
```

## Features

- Real-time web search integration
- Fact-checking and hallucination detection
- Customizable search parameters
- Source citation and tracking
- Error handling and colored output
- Support for multiple search APIs

## Notes

- The tools use colored terminal output for better readability
- All API keys should be kept secure and not committed to version control
- Default search configurations can be modified in the respective files 