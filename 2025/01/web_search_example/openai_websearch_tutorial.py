from termcolor import colored
from openai import OpenAI
from exa_py import Exa
import os
from datetime import datetime, timedelta

# Configuration
MODEL_NAME = "gpt-4o"
DEFAULT_NUM_RESULTS = 3
EXA_API_KEY = os.getenv("EXA_API_KEY")
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
# Get date 6 months ago for default published date
DAYS_AGO = 7
DEFAULT_START_DATE = (datetime.now() - timedelta(days=DAYS_AGO)).strftime("%Y-%m-%d")

def print_step(message, color="cyan"):
    print(colored(f"\n→ {message}", color))

def rag_query(
    question: str,
    model: str = MODEL_NAME,
    use_exa: str = "required", # "auto", "required", "none"
    num_results: int = DEFAULT_NUM_RESULTS,
    start_published_date: str = DEFAULT_START_DATE,
    **exa_params
):
    """
    Perform RAG-enhanced query using Exa and OpenAI
    """
    try:
        # Initialize clients
        print_step("Initializing clients...")
        openai_client = OpenAI(api_key=OPENAI_API_KEY)
        exa = Exa(EXA_API_KEY)
        exa_openai = exa.wrap(openai_client)

        # Perform RAG query
        print_step(f"Querying {model} with RAG enhancement...", "yellow")
        print_step(f"Searching for content published since {start_published_date}", "yellow")
        
        response = exa_openai.chat.completions.create(
            model=model,
            messages=[{"role": "user", "content": question}],
            use_exa=use_exa,
            num_results=num_results,
            start_published_date=start_published_date,
            **exa_params
        )

        return response.choices[0].message.content

    except Exception as e:
        print(colored(f"\n⚠️ Error during RAG query: {str(e)}", "red"))
        raise

def main():
    try:
        # Example queries with different date ranges
        questions = [
            ("What's the latest breakthrough in fusion energy?", {
                "num_results": 2
                # Using default start date (7 days ago)
            }),
            ("How are LLMs being used in healthcare diagnostics?", {
                "category": "research",
                "start_published_date": (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d")  # Last month
            }),
            ("Explain quantum computing breakthroughs in 2024", {
                "use_exa": "required",
                "start_published_date": "2024-01-01"  # Since start of 2024
            })
        ]

        for idx, (question, params) in enumerate(questions):
            print(colored(f"\nQuestion {idx+1}: {question}", "magenta"))
            print(colored(f"Using date range: {params.get('start_published_date', DEFAULT_START_DATE)}", "cyan"))
            answer = rag_query(question, **params)
            print(colored("\nAnswer:", "green"))
            print(colored(answer, "white"))

    except Exception as e:
        print(colored(f"\n🚨 Critical error in main execution: {str(e)}", "red"))

if __name__ == "__main__":
    # Verify environment variables
    if not EXA_API_KEY or not OPENAI_API_KEY:
        print(colored("🚫 Error: EXA_API_KEY and OPENAI_API_KEY must be set in environment variables", "red"))
        exit(1)

    print(colored("\n🌟 Starting RAG Tutorial Demo", "blue", attrs=["bold"]))
    main()
    print(colored("\n✅ Demo completed successfully!", "green", attrs=["bold"])) 