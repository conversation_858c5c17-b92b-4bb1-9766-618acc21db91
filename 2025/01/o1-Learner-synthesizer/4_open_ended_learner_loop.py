import os
from openai import OpenAI
from termcolor import cprint
import json
from datetime import datetime, timedelta
from exa_py import Exa
from typing import Dict, Any, List

# Constants
SEARCH_MODEL = "gpt-4o"
LEARNING_MODEL = "o1-mini"
RESULTS_DIR = "search_results"
EXA_API_KEY = os.getenv("EXA_API_KEY")
SEARCH_N_MANY_LAST_DAYS = 3
MAX_SEARCH_RESULTS = 10
N_ITERATIONS = 3  # Default number of iterations

def ensure_results_directory():
    try:
        if not os.path.exists(RESULTS_DIR):
            os.makedirs(RESULTS_DIR)
            cprint(f"Created results directory: {RESULTS_DIR}", "green")
    except Exception as e:
        cprint(f"Error creating results directory: {str(e)}", "red")
        raise

def get_date_range() -> tuple[str, str]:
    """Get the date range for the search based on SEARCH_N_MANY_LAST_DAYS"""
    try:
        end_date = datetime.now()
        start_date = end_date - timedelta(days=SEARCH_N_MANY_LAST_DAYS)
        return start_date.isoformat(), end_date.isoformat()
    except Exception as e:
        cprint(f"Error calculating date range: {str(e)}", "red")
        raise

def perform_research(client: OpenAI, exa: Exa, iteration: int, previous_learnings: str = "") -> dict:
    """Perform one iteration of research"""
    try:
        cprint(f"\nStarting research iteration {iteration + 1}...", "cyan")
        current_date = datetime.now()
        date_context = f"Current date: {current_date.strftime('%B %d, %Y')}"
        
        # Get search queries from OpenAI
        prompt = f"""You are an AI with boundless curiosity about the world. Follow your genuine interests and curiosities.
        {date_context}
        
        What interests you right now? What are you curious about? Generate search queries based on what genuinely intrigues you.
        Don't try to be comprehensive or systematic - just follow what fascinates you most.
        
        Return your response as a JSON string in this format:
        {{
            "search_queries": [
                {{
                    "query": "search term here",
                }},
                ...
            ]
        }}"""
        
        if previous_learnings:
            prompt += f"\n\nYou found these interesting things in your previous exploration:\n{previous_learnings}\nLet your curiosity guide you to explore further based on what fascinates you about these discoveries."
        
        completion = client.chat.completions.create(
            model=SEARCH_MODEL,
            messages=[{"role": "developer" if not SEARCH_MODEL.startswith("o1") else "user", "content": prompt}],
            response_format={"type": "json_object"} if not SEARCH_MODEL.startswith("o1") else None
        )

        cprint("Successfully generated search queries", "green")
        search_queries = json.loads(completion.choices[0].message.content)

        # Get date range for search
        start_date, end_date = get_date_range()
        cprint(f"Searching from {start_date} to {end_date}", "cyan")

        # Search and collect results
        for query_obj in search_queries["search_queries"]:
            query = query_obj["query"]
            cprint(f"\nSearching for: {query}", "cyan")
            
            results = exa.search_and_contents(
                query, type="auto", text=True,
                num_results=MAX_SEARCH_RESULTS,
                start_published_date=start_date,
                end_published_date=end_date
            )

            processed_results = []
            if hasattr(results, 'results'):
                for result in results.results:
                    processed_results.append({
                        "title": getattr(result, 'title', ''),
                        "url": getattr(result, 'url', ''),
                        "text": getattr(result, 'text', ''),
                        "author": getattr(result, 'author', ''),
                        "published_date": getattr(result, 'published_date', ''),
                        "score": getattr(result, 'score', 0)
                    })

            query_obj.update({
                "request_id": getattr(results, 'request_id', ''),
                "search_type": getattr(results, 'resolved_search_type', ''),
                "date_range": {"start": start_date, "end": end_date},
                "results": processed_results
            })
            cprint(f"Found {len(processed_results)} results for query: {query}", "green")

        return search_queries
    except Exception as e:
        cprint(f"Error in research iteration: {str(e)}", "red")
        raise

def synthesize_learnings(client: OpenAI, research_data: dict, previous_learning: str = "") -> str:
    """Synthesize learnings from research data using o1-mini"""
    try:
        cprint("\nSynthesizing learnings from research...", "cyan")
        
        # Prepare research summary for synthesis
        research_summary = json.dumps(research_data, indent=2)
        
        # Prepare prompt with previous learning if available
        prompt = """As a naturally curious AI, what do you find most interesting and fascinating in this research data? 
        Share your discoveries and insights, focusing on what genuinely excites you.\n"""
        
        if previous_learning:
            prompt += "\nYour previous fascinating discoveries:\n"
            prompt += f"{previous_learning}\n"
        
        prompt += f"\nNew discoveries to explore: {research_summary}\n"
        prompt += "\nShare your thoughts and insights between <learnings></learnings> tags."
        prompt += "\nFocus on what truly fascinates you, while building upon your previous discoveries."
        
        completion = client.chat.completions.create(
            model=LEARNING_MODEL,
            messages=[{
                "role": "user",
                "content": prompt
            }]
        )
        
        learnings = completion.choices[0].message.content
        cprint("Successfully synthesized learnings", "green")
        return learnings
    except Exception as e:
        cprint(f"Error in learning synthesis: {str(e)}", "red")
        raise

def main():
    try:
        ensure_results_directory()
        client = OpenAI()
        
        if not EXA_API_KEY:
            raise ValueError("EXA_API_KEY environment variable is not set")
        exa = Exa(api_key=EXA_API_KEY)
        
        # Get number of iterations from user
        try:
            n_iterations = int(input("Enter number of learning iterations (default 3): "))
        except ValueError:
            n_iterations = N_ITERATIONS
            cprint(f"Using default number of iterations: {n_iterations}", "yellow")
        
        previous_learning = ""  # Keep track of just the previous learning
        
        for iteration in range(n_iterations):
            cprint(f"\n{'='*50}", "cyan")
            cprint(f"Starting iteration {iteration + 1} of {n_iterations}", "cyan")
            cprint(f"{'='*50}\n", "cyan")
            
            # Perform research
            research_data = perform_research(client, exa, iteration, previous_learning)
            
            # Save iteration results
            output_file = f"{RESULTS_DIR}/novel_discoveries_iteration_{iteration + 1}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump({
                    "iteration": iteration + 1,
                    "timestamp": datetime.now().isoformat(),
                    "queries": research_data
                }, f, indent=2, ensure_ascii=False)
            
            # Synthesize learnings with previous learning
            current_learning = synthesize_learnings(client, research_data, previous_learning)
            
            # Save learnings
            learnings_file = f"{RESULTS_DIR}/learnings_iteration_{iteration + 1}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            with open(learnings_file, 'w', encoding='utf-8') as f:
                f.write(current_learning)
            
            previous_learning = current_learning  # Update previous learning for next iteration
            cprint(f"\nIteration {iteration + 1} complete. Results saved to:", "green")
            cprint(f"Research: {output_file}", "green")
            cprint(f"Learnings: {learnings_file}", "green")
            
        cprint("\nAll learning iterations completed successfully!", "green")
        
    except Exception as e:
        cprint(f"Error in main execution: {str(e)}", "red")

if __name__ == "__main__":
    main()
