# Interestingness Finder

A powerful tool for discovering and analyzing the latest information from the web using OpenAI and Exa Search APIs. This project consists of two main scripts, with the advanced knowledge identification system being the primary feature.

## Features

- Intelligent search term generation using OpenAI's GPT models
- Web content search across multiple domains using Exa Search API
- Automated knowledge extraction and analysis
- Configurable search parameters (time range, domains, etc.)
- Detailed JSON output with search results and analysis
- Colorful terminal feedback for better user experience

## Scripts Overview

### 1. Basic Search (`1_basic_search.py`)

A foundational script that:
- Generates intelligent search terms from user queries using OpenAI
- Performs web searches using Exa Search API
- Saves raw search results in JSON format
- Includes configurable search parameters

### 2. Knowledge Identifier (`2_identify_new_knowledge.py`) ⭐

An advanced version that builds upon the basic search with additional features:
- Performs all basic search functionality
- Uses OpenAI's o1-mini model to analyze search results
- Identifies new and noteworthy information from the results
- Provides AI-generated insights about the findings
- Includes temporal context for better relevance assessment

## Requirements

```
openai
exa-py
termcolor
```

## Environment Variables

The following environment variables are required:
- `EXA_API_KEY`: Your Exa Search API key
- `OPENAI_API_KEY`: Your OpenAI API key

## Configuration

Both scripts share common configuration parameters:
- `USE_LAST_N_DAYS`: Number of days to look back (default: 3)
- `MAX_RESULTS`: Maximum number of results per search (default: 25)
- `DOMAINS`: Target domains for search (default: ["x.com", "reddit.com"])

## Usage

1. Set up environment variables:
```bash
export EXA_API_KEY="your_exa_api_key"
export OPENAI_API_KEY="your_openai_api_key"
```

2. Run the basic search:
```bash
python 1_basic_search.py
```

3. Run the advanced knowledge identifier:
```bash
python 2_identify_new_knowledge.py
```

4. Enter your search query when prompted

## Output

Both scripts create JSON files in the `search_results` directory with timestamps:
- Basic search: Raw search results and metadata
- Knowledge identifier: Includes additional AI analysis of the findings

### Output Structure (Knowledge Identifier)

```json
{
    "timestamp": "ISO-8601 timestamp",
    "original_query": "user's search query",
    "search_configuration": {
        "days_searched": 3,
        "domains": ["x.com", "reddit.com"]
    },
    "search_terms": {
        "q1": "primary search term",
        "q2": "secondary aspect search term"
    },
    "search_results": {
        // Detailed search results per term
    },
    "ai_analysis": "AI-generated insights between <learned> tags"
}
```

## Error Handling

Both scripts include comprehensive error handling with:
- Descriptive error messages
- Color-coded terminal output
- Graceful failure handling
- Detailed error logging

## Best Practices

1. Use specific queries for better results
2. Adjust `USE_LAST_N_DAYS` based on your needs
3. Monitor the AI analysis output for relevance
4. Review raw search results when needed
5. Keep API keys secure and never commit them

## Notes

- The knowledge identifier script provides more insightful results but may take longer to process
- Search domains can be modified by updating the `DOMAINS` constant
- All file operations use UTF-8 encoding for universal compatibility 