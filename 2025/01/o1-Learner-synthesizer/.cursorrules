from openai import OpenAI
client = OpenAI()

completion = client.chat.completions.create(
    model="gpt-4o",
    messages=[
        {"role": "developer", "content": "You are a helpful assistant which returns search terms to find the latest about the user's query. return queries to find out the latest about the user's query as a JSON object"},
        {
            "role": "user",
            "content": f"user query: {user_query}"
        }
    ],
    response_format={ "type": "json_object" }
)

print(completion.choices[0].message)

search api:
from exa_py import Exa
import os

exa = Exa(api_key=os.getenv("EXA_API_KEY"))

result = exa.search_and_contents(
  "blog post about Rust",
  type="auto",
  text=True,
  start_published_date="2025-01-01T08:00:02.000Z",
  end_published_date="2025-01-05T08:00:01.000Z",
  include_domains=["x.com", "reddit.com"]

sample output:
{
  "requestId": "c3f759e79b83fdba6cc12e69404adb6e",
  "autopromptString": "Here is a cool blog post about Rust:",
  "resolvedSearchType": "neural",
  "results": [
    {
      "score": 0.1862698495388031,
      "title": "What is Rust and why is it so popular?",
      "id": "https://stackoverflow.blog/2020/01/20/what-is-rust-and-why-is-it-so-popular/",
      "url": "https://stackoverflow.blog/2020/01/20/what-is-rust-and-why-is-it-so-popular",
      "publishedDate": "2020-01-20T00:00:00.000Z",
      "author": "Jake Goulding",
      "text": "Rust has been Stack Overflow's most loved language for four years in a row...,
      "image": "https://cdn.stackoverflow.co/images/jo7n4k8s/production/bda51d5fadd42f26418f6819a6da41985ec838fd-2560x1707.jpg?w=1200&fm=png&auto=format",
      "favicon": "https://stackoverflow.blog/favicon.ico"
    },
    {
      "score": 0.18211449682712555,
      "title": "A little bit Rusty",
step 1:
lets build this simple system
we want to search for all search terms and save the q and As as a json file. we want to save the urls and the text within the json
Ajust so we can use n many last days of search results

step 2:
from openai import OpenAI
client = OpenAI()

prompt = """
Write a bash script that takes a matrix represented as a string with 
format '[1,2],[3,4],[5,6]' and prints the transpose in the same format.
"""

response = client.chat.completions.create(
    model="o1-mini",
    messages=[
        {
            "role": "user", # only use user roel with o1 models
            "content": prompt
        }
    ]
)

print(response.choices[0].message.content)
lets make another call to o1-mini model with all the saerch result texts and allow it to return what it has learned as new from the search results in between <learned></learned> tags
make sure to instruct the model so we can parse perfectly
