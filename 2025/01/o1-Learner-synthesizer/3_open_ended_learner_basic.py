import os
from openai import OpenAI
from termcolor import cprint
import json
from datetime import datetime, timedelta
from exa_py import Exa
from typing import Dict, Any

# Constants
MODEL = "gpt-4o"
RESULTS_DIR = "search_results"
OUTPUT_FILE = f"{RESULTS_DIR}/novel_discoveries_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
EXA_API_KEY = os.getenv("EXA_API_KEY")
SEARCH_N_MANY_LAST_DAYS = 3
MAX_SEARCH_RESULTS = 10

def ensure_results_directory():
    try:
        if not os.path.exists(RESULTS_DIR):
            os.makedirs(RESULTS_DIR)
            cprint(f"Created results directory: {RESULTS_DIR}", "green")
    except Exception as e:
        cprint(f"Error creating results directory: {str(e)}", "red")
        raise

def get_date_range() -> tuple[str, str]:
    """Get the date range for the search based on SEARCH_N_MANY_LAST_DAYS"""
    try:
        end_date = datetime.now()
        start_date = end_date - timedelta(days=SEARCH_N_MANY_LAST_DAYS)
        
        # Format dates in ISO format
        end_date_str = end_date.isoformat()
        start_date_str = start_date.isoformat()
        
        return start_date_str, end_date_str
    except Exception as e:
        cprint(f"Error calculating date range: {str(e)}", "red")
        raise

try:
    ensure_results_directory()
    client = OpenAI()
    cprint("Initializing novel information discovery...", "cyan")

    current_date = datetime.now()
    date_context = f"Current date: {current_date.strftime('%B %d, %Y')}"

    # Get search queries from OpenAI
    completion = client.chat.completions.create(
        model=MODEL,
        messages=[
            {
                "role": "developer" if not MODEL.startswith("o1") else "user",
                "content": f"""You are an AI focused on discovering novel and interesting developments.
                {date_context}
                Please note that your knowledge cutoff date may mean you're not aware of recent developments. 
                This makes it especially important to formulate search queries that will help uncover truly new information.
                
                Your task is to generate search queries that will uncover interesting and new things. Focus on things that are interesting to you
                
                Return your response as a JSON string in this format:
                {{
                    "search_queries": [
                        {{
                            "query": "search term here",
                        }},
                        ...
                    ]
                }}
                Generate as many search queries as you think necessary to thoroughly explore the topic.
                """
            }
        ],
        response_format={"type": "json_object"} if not MODEL.startswith("o1") else None
    )

    cprint("Successfully generated search queries", "green")
    search_queries = json.loads(completion.choices[0].message.content)

    # Initialize Exa client
    if not EXA_API_KEY:
        raise ValueError("EXA_API_KEY environment variable is not set")
    exa = Exa(api_key=EXA_API_KEY)

    # Get date range for search
    start_date, end_date = get_date_range()
    cprint(f"Searching from {start_date} to {end_date}", "cyan")

    # Search and collect results
    for query_obj in search_queries["search_queries"]:
        query = query_obj["query"]
        cprint(f"\nSearching for: {query}", "cyan")
        
        results = exa.search_and_contents(
            query,
            type="auto",
            text=True,
            num_results=MAX_SEARCH_RESULTS,
            start_published_date=start_date,
            end_published_date=end_date
        )

        processed_results = []
        if hasattr(results, 'results'):
            for result in results.results:
                processed_results.append({
                    "title": getattr(result, 'title', ''),
                    "url": getattr(result, 'url', ''),
                    "text": getattr(result, 'text', ''),
                    "author": getattr(result, 'author', ''),
                    "published_date": getattr(result, 'published_date', ''),
                    "score": getattr(result, 'score', 0)
                })

        query_obj.update({
            "request_id": getattr(results, 'request_id', ''),
            "search_type": getattr(results, 'resolved_search_type', ''),
            "date_range": {
                "start": start_date,
                "end": end_date
            },
            "results": processed_results
        })
        cprint(f"Found {len(processed_results)} results for query: {query}", "green")

    # Save results to JSON file
    output_data = {
        "timestamp": datetime.now().isoformat(),
        "queries": search_queries
    }

    with open(OUTPUT_FILE, 'w', encoding='utf-8') as f:
        json.dump(output_data, f, indent=2, ensure_ascii=False)

    cprint(f"\nAll results saved to: {OUTPUT_FILE}", "green")

except Exception as e:
    cprint(f"Error in novel information discovery: {str(e)}", "red")
