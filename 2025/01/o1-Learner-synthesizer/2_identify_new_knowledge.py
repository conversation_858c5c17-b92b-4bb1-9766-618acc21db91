import os
from termcolor import cprint
import json
from openai import OpenAI
from typing import Dict, Any
from datetime import datetime, timedelta
from exa_py import Exa

# Constants
OPENAI_MODEL = "gpt-4o"
RESULTS_DIR = "search_results"
OUTPUT_FILE = f"{RESULTS_DIR}/search_qa_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
EXA_API_KEY = os.getenv("EXA_API_KEY")
USE_LAST_N_DAYS = 3  # Number of days to search back
MAX_RESULTS = 25  # Maximum number of results to return
DOMAINS = ["x.com", "reddit.com"]  # Domains to search in # BY DEFAULT COMMENTED OUT IN EXA SEARCH FUNCTION!!!
O1_MODEL = "o1-mini"

def ensure_results_directory():
    """Ensure the results directory exists"""
    try:
        if not os.path.exists(RESULTS_DIR):
            os.makedirs(RESULTS_DIR)
            cprint(f"Created results directory: {RESULTS_DIR}", "green")
    except Exception as e:
        cprint(f"Error creating results directory: {str(e)}", "red")
        raise

def get_search_terms(query: str) -> Dict[str, Any]:
    """Get search terms from OpenAI API"""
    try:
        cprint("Getting search terms from OpenAI...", "cyan")
        client = OpenAI()
        
        current_date = datetime.now()
        date_context = f"Current date: {current_date.strftime('%B %d, %Y')}"
        
        completion = client.chat.completions.create(
            model=OPENAI_MODEL,
            messages=[
                {
                    "role": "developer", 
                    "content": f"""You are a helpful assistant which returns search terms to find the latest about the user's query. 
                    current date: 
                    {date_context}
                    Return 2 search terms as a JSON object that will help find comprehensive information about the topic.
                    Format:
                    {{
                        "q1": "primary search term",
                        "q2": "secondary aspect search term"
                    }}"""
                },
                {
                    "role": "user",
                    "content": f"user query: {query}"
                }
            ],
            response_format={"type": "json_object"}
        )
        
        cprint("Successfully got search terms!", "green")
        return json.loads(completion.choices[0].message.content)
    
    except Exception as e:
        cprint(f"Error getting search terms: {str(e)}", "red")
        raise

def get_date_range() -> tuple[str, str]:
    """Get the date range for the search based on USE_LAST_N_DAYS"""
    try:
        end_date = datetime.now()
        start_date = end_date - timedelta(days=USE_LAST_N_DAYS)
        
        # Format dates in ISO format
        end_date_str = end_date.isoformat()
        start_date_str = start_date.isoformat()
        
        return start_date_str, end_date_str
    except Exception as e:
        cprint(f"Error calculating date range: {str(e)}", "red")
        raise

def search_exa(query: str) -> Dict[str, Any]:
    """Search using Exa API"""
    try:
        cprint(f"Searching with Exa API for: {query}", "cyan")
        
        if not EXA_API_KEY:
            raise ValueError("EXA_API_KEY environment variable is not set")
        
        exa = Exa(api_key=EXA_API_KEY)
        start_date, end_date = get_date_range()
        
        cprint(f"Searching from {start_date} to {end_date}", "cyan")
        results = exa.search_and_contents(
            query,
            type="auto",
            text=True,
            num_results=MAX_RESULTS,
            start_published_date=start_date,
            end_published_date=end_date,
            # include_domains=DOMAINS
        )
        
        # Extract relevant information
        processed_results = []
        if hasattr(results, 'results'):
            for result in results.results:
                processed_results.append({
                    "title": getattr(result, 'title', ''),
                    "url": getattr(result, 'url', ''),
                    "text": getattr(result, 'text', ''),
                    "author": getattr(result, 'author', ''),
                    "published_date": getattr(result, 'published_date', ''),
                    "score": getattr(result, 'score', 0)
                })
        
        cprint(f"Found {len(processed_results)} results!", "green")
        return {
            "request_id": getattr(results, 'request_id', ''),
            "search_type": getattr(results, 'resolved_search_type', ''),
            "date_range": {
                "start": start_date,
                "end": end_date
            },
            "results": processed_results
        }
    
    except Exception as e:
        cprint(f"Error searching with Exa API: {str(e)}", "red")
        raise

def analyze_search_results(results: Dict[str, Any]) -> str:
    """Analyze search results using o1-mini model to identify new knowledge"""
    try:
        cprint("Analyzing search results with o1-mini...", "cyan")
        client = OpenAI()

        # Prepare the content from search results
        content = []
        for term_results in results.values():
            for result in term_results['results']:
                content.append(f"Title: {result['title']}\nContent: {result['text']}\n")
        
        combined_content = "\n".join(content)

        current_date = datetime.now()
        date_context = f"Current date: {current_date.strftime('%B %d, %Y')}"

        prompt = f"""
        Current date: 
        {date_context}

        Analyze the following search results and identify what new or noteworthy information you can learn from them.
        Return your findings between <learned> and </learned> tags.
        Focus on recent developments, trends, and significant insights.
        Consider what information is most relevant as of the current date.
        
        Search Results:
        {combined_content}"""

        response = client.chat.completions.create(
            model=O1_MODEL,
            messages=[
                {
                    "role": "user",
                    "content": prompt
                }
            ]
        )

        analysis = response.choices[0].message.content
        cprint("Successfully analyzed search results!", "green")
        return analysis

    except Exception as e:
        cprint(f"Error analyzing search results: {str(e)}", "red")
        raise

def save_results(original_query: str, search_terms: Dict[str, Any], results: Dict[str, Any], analysis: str):
    """Save search results and analysis to JSON file"""
    try:
        data = {
            "timestamp": datetime.now().isoformat(),
            "original_query": original_query,
            "search_configuration": {
                "days_searched": USE_LAST_N_DAYS,
                "domains": DOMAINS
            },
            "search_terms": search_terms,
            "search_results": results,
            "ai_analysis": analysis
        }
        
        with open(OUTPUT_FILE, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
            
        cprint(f"Results saved to: {OUTPUT_FILE}", "green")
    
    except Exception as e:
        cprint(f"Error saving results: {str(e)}", "red")
        raise

def main():
    try:
        cprint("Starting search process...", "yellow")
        ensure_results_directory()
        
        # Get user query
        query = input("Enter your search query: ")
        
        # Get search terms from OpenAI
        search_terms = get_search_terms(query)
        cprint(f"Generated search terms: {json.dumps(search_terms, indent=2)}", "green")
        
        # Search for each term and collect results
        all_results = {}
        for key, term in search_terms.items():
            cprint(f"\nProcessing search term: {key}", "yellow")
            results = search_exa(term)
            all_results[key] = results
        
        # Analyze the search results
        cprint("\nAnalyzing search results...", "yellow")
        analysis = analyze_search_results(all_results)
        cprint("\nAI Analysis:", "cyan")
        cprint(analysis, "white")
        
        # Save all results including analysis
        save_results(query, search_terms, all_results, analysis)
        
        cprint("\nAll searches completed successfully!", "green")
        cprint(f"Results saved to: {OUTPUT_FILE}", "green")
        cprint(f"Searched last {USE_LAST_N_DAYS} days on domains: {', '.join(DOMAINS)}", "cyan")
        
    except Exception as e:
        cprint(f"An error occurred: {str(e)}", "red")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main()) 