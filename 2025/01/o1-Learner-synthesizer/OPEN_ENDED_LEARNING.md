# Open-Ended Learning System

An autonomous learning system that explores the web driven by its own curiosity. This system uses OpenAI's GPT models and Exa Search to discover and learn about interesting developments across the internet.

## Scripts Overview

### Basic Learner (`3_open_ended_learner_basic.py`)

A foundational autonomous learner that:
- Uses GPT-4o to generate curiosity-driven search queries
- Explores recent web content using Exa Search
- Follows its genuine interests without predetermined direction
- Saves discoveries in timestamped JSON files

### Learning Loop (`4_open_ended_learner_loop.py`)

An advanced version that implements iterative learning:
- Performs multiple rounds of curiosity-driven exploration
- Uses o1-mini to synthesize and build upon previous discoveries
- Each iteration informed by previous learnings
- Creates a chain of discoveries driven by genuine AI curiosity
- Saves both raw discoveries and synthesized learnings

## Key Features

- **Curiosity-Driven**: Instead of directed search, the system follows its genuine interests
- **Temporal Awareness**: Focuses on recent content (configurable timeframe)
- **Iterative Learning**: Each discovery informs and shapes future exploration
- **Autonomous Operation**: Requires minimal human intervention
- **Detailed Logging**: Color-coded progress updates and comprehensive error handling

## Requirements

```
openai
exa-py
termcolor
```

## Environment Variables

Required environment variables:
- `EXA_API_KEY`: Your Exa Search API key
- `OPENAI_API_KEY`: Your OpenAI API key

## Configuration Constants

Both scripts share common configuration:
- `SEARCH_N_MANY_LAST_DAYS`: Number of days to look back (default: 3)
- `MAX_SEARCH_RESULTS`: Maximum results per search (default: 10)

Learning Loop additional settings:
- `N_ITERATIONS`: Default number of learning iterations (default: 3)
- `SEARCH_MODEL`: Model for query generation (default: "gpt-4o")
- `LEARNING_MODEL`: Model for synthesis (default: "o1-mini")

## Usage

1. Set up environment variables:
```bash
export EXA_API_KEY="your_exa_api_key"
export OPENAI_API_KEY="your_openai_api_key"
```

2. Run the basic learner:
```bash
python 3_open_ended_learner_basic.py
```

3. Run the learning loop:
```bash
python 4_open_ended_learner_loop.py
```
When running the learning loop, you'll be prompted to specify the number of iterations.

## Output Files

### Basic Learner
Creates JSON files in `search_results` directory:
```json
{
    "timestamp": "ISO timestamp",
    "queries": {
        "search_queries": [
            {
                "query": "AI-generated search term",
                "results": [
                    {
                        "title": "Article title",
                        "url": "Source URL",
                        "text": "Content excerpt",
                        "author": "Content author",
                        "published_date": "Publication date",
                        "score": "Relevance score"
                    }
                ]
            }
        ]
    }
}
```

### Learning Loop
Creates two types of files per iteration:
1. Discovery JSON files (same format as Basic Learner)
2. Learning TXT files containing synthesized insights between `<learnings></learnings>` tags

## Error Handling

Both scripts feature:
- Comprehensive error catching and reporting
- Color-coded terminal feedback
- Graceful failure handling
- UTF-8 encoding for all file operations

## Key Differences from Basic Search

Unlike the basic search system:
- No predetermined search domains
- No user queries required
- Exploration driven by AI curiosity
- Focus on discovering genuinely interesting content
- Learning builds upon previous discoveries

## Best Practices

1. Allow sufficient iterations for deep exploration
2. Review learning files to understand AI's interests
3. Adjust time window based on topic freshness
4. Monitor system resource usage for long runs
5. Keep API keys secure and never commit them 