# Progressive Research Agent

A sophisticated research agent that leverages Perplexity AI and O1-mini to conduct comprehensive, iterative research on any given topic. The agent progressively builds knowledge through parallel searches and generates detailed reports with citations.

## Features

* 🔄  **Progressive Research** : Iteratively expands the research scope based on findings.
* ⚡  **Parallel Processing** : Conducts multiple searches simultaneously for faster results.
* 📊  **Real-time Progress Tracking** : Saves all research progress dynamically in JSON format.
* 📋  **Comprehensive Reports** : Generates well-structured markdown reports with citations.
* 🤖  **Smart Completion Detection** : Automatically determines when sufficient information is gathered.
* 🛡️  **Error Handling** : Features robust error handling with informative, color-coded output.

## Requirements

```
requests
openai
aiohttp
asyncio
termcolor
pydantic
```

## Setup

1. Clone the repository
2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```
3. Set up environment variables:
   ```bash
   set PERPLEXITY_API_KEY=your_perplexity_api_key
   set OPENAI_API_KEY=your_openai_api_key
   ```

## Usage

Run the research agent:

```bash
python research_agent.py
```

The agent will:

1. Prompt for a research question
2. Conduct initial research using Perplexity AI
3. Generate follow-up questions using O1-mini
4. Perform parallel searches for each follow-up question
5. Assess information completeness
6. Continue iterations until sufficient information is gathered
7. Generate a comprehensive final report

## Output Structure

The agent creates a timestamped directory for each research session:

```
research_output/
└── YYYYMMDD_HHMMSS/
    ├── progress_0.json     # Initial search
    ├── progress_1.json     # First iteration
    ├── progress_2.json     # Second iteration
    └── report_question.md  # Final report
```

### Progress JSON Format

Each progress file contains:

```json
{
  "question": "original question",
  "iteration": number,
  "timestamp": "ISO timestamp",
  "collected_information": [
    {
      "question": "specific question",
      "content": "answer content",
      "citations": ["url1", "url2", ...]
    }
  ],
  "completeness": number,
  "follow_up_questions": ["question1", "question2", "question3"]
}
```

### Final Report Format

The markdown report includes:

- Executive Summary
- Key Findings
- Detailed Analysis
- Conclusions
- Sources with Citations

## Configuration

Key parameters can be adjusted in `research_agent.py`:

- `MAX_ITERATIONS`: Maximum number of research iterations (default: 5)
- `MIN_CONFIDENCE_THRESHOLD`: Threshold for research completeness (default: 0.8)
- Model parameters for both Perplexity AI and O1-mini

## Error Handling

The agent includes comprehensive error handling:

- Saves error states in progress files
- Provides colored console output for errors
- Maintains partial progress in case of failures

## Models Used

- **Perplexity AI**: Used for web-based research (llama-3.1-sonar-small-128k-online)
- **O1-mini**: Used for:
  - Generating follow-up questions
  - Assessing research completeness
  - Creating final reports

## Contributing

Feel free to submit issues and enhancement requests!

## License

This project is open source and available under the MIT License.

## Acknowledgments

- Perplexity AI for their research API
- O1 for their language model
- All cited sources in research reports
