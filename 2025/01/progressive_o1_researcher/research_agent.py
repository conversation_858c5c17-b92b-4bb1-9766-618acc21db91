import os
import json
import asyncio
import aiohttp
from termcolor import colored
from typing import List, Dict, Tuple, Optional
from openai import Async<PERSON>penA<PERSON>
from datetime import datetime
from pydantic import BaseModel

# Configuration Constants
PERPLEXITY_API_KEY = os.getenv("PERPLEXITY_API_KEY", "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")
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
MAX_ITERATIONS = 5
MIN_CONFIDENCE_THRESHOLD = 0.8
PERPLEXITY_URL = "https://api.perplexity.ai/chat/completions"
MODEL_NAME = "o1-mini"

# Output Directory
OUTPUT_DIR = "research_output"

# Instruction Prompts
RESEARCH_INSTRUCTION = """You are a thorough research assistant. Your responses should be:
1. Analytical and fact-based
2. Well-structured with clear sections
3. Focused on the most relevant information
4. Include citations where possible

Research Question: """

QUESTION_GENERATION_INSTRUCTION = """You are a research question generator.

Based on the current research information, generate exactly 3 follow-up questions.
Requirements for the questions:
1. Each question must explore a new aspect not covered in the current information
2. Questions must be specific and focused
3. Questions should build upon existing findings
4. Format your response exactly as follows:
   1. [First question]
   2. [Second question]
   3. [Third question]

Current information:
"""

COMPLETENESS_ASSESSMENT_INSTRUCTION = """You are a research completeness assessor.

Evaluate the completeness of this research on a scale from 0 to 1.
Requirements for evaluation:
1. Consider depth of coverage
2. Consider breadth of topics
3. Consider reliability of sources
4. Format your response as a single number between 0 and 1
Example response: 0.75

Research information:
"""

FINAL_REPORT_INSTRUCTION = """You are a research report writer.

Create a comprehensive research report based on the provided information.
Requirements for the report:
1. Structure:
   - Executive Summary
   - Key Findings
   - Detailed Analysis
   - Conclusions
2. Format:
   - Use markdown headings (##)
   - Use bullet points for key points
   - Include citations where available
3. Content:
   - Synthesize all collected information
   - Highlight contradictions or gaps
   - Provide balanced viewpoints

Research information:
"""

# API Configuration
PERPLEXITY_HEADERS = {
    "Authorization": f"Bearer {PERPLEXITY_API_KEY}",
    "Content-Type": "application/json"
}

PERPLEXITY_DEFAULT_PARAMS = {
    "model": "llama-3.1-sonar-small-128k-online",
    "temperature": 0.2,
    "top_p": 0.9,
    "search_domain_filter": ["perplexity.ai"],
    "return_images": False,
    "return_related_questions": False,
    "search_recency_filter": "month",
    "top_k": 0,
    "stream": False,
    "presence_penalty": 0,
    "frequency_penalty": 1
}

class PerplexityMessage(BaseModel):
    role: str
    content: str

class PerplexityChoice(BaseModel):
    index: int
    finish_reason: str
    message: PerplexityMessage
    delta: Optional[Dict] = None

class PerplexityResponse(BaseModel):
    id: str
    model: str
    object: str
    created: int
    citations: Optional[List[str]] = []
    choices: List[PerplexityChoice]
    usage: Dict

class ResearchAgent:
    def __init__(self):
        try:
            self.openai_client = AsyncOpenAI(api_key=OPENAI_API_KEY)
            self.session_id = datetime.now().strftime("%Y%m%d_%H%M%S")
            self._ensure_output_directory()
            print(colored("✓ Research Agent initialized successfully", "green"))
        except Exception as e:
            print(colored(f"✗ Error initializing Research Agent: {str(e)}", "red"))
            raise

    def _ensure_output_directory(self):
        """Ensure output directories exist."""
        try:
            os.makedirs(OUTPUT_DIR, exist_ok=True)
            self.session_dir = os.path.join(OUTPUT_DIR, self.session_id)
            os.makedirs(self.session_dir, exist_ok=True)
            print(colored(f"✓ Output directory created: {self.session_dir}", "green"))
        except Exception as e:
            print(colored(f"✗ Error creating output directory: {str(e)}", "red"))
            raise

    def _save_progress(self, data: Dict, iteration: int):
        """Save research progress to JSON file."""
        try:
            progress_file = os.path.join(self.session_dir, f"progress_{iteration}.json")
            with open(progress_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            print(colored(f"✓ Progress saved: {progress_file}", "green"))
        except Exception as e:
            print(colored(f"✗ Error saving progress: {str(e)}", "red"))

    def _save_final_report(self, report: str, question: str, collected_info: List[Dict]):
        """Save final report as markdown file."""
        try:
            # Create report filename from question
            safe_question = "".join(x for x in question[:30] if x.isalnum() or x.isspace()).strip()
            report_file = os.path.join(self.session_dir, f"report_{safe_question}.md")
            
            # Collect all unique citations
            all_citations = set()
            for info in collected_info:
                if "citations" in info:
                    all_citations.update(info["citations"])
            
            # Add metadata and citations to report
            metadata = f"""# Research Report
> **Question**: {question}
> **Date**: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
> **Session ID**: {self.session_id}

---

{report}

## Sources
"""
            if all_citations:
                metadata += "\nCitations:\n"
                for citation in sorted(all_citations):
                    metadata += f"- {citation}\n"
            
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(metadata)
            print(colored(f"✓ Final report saved: {report_file}", "green"))
        except Exception as e:
            print(colored(f"✗ Error saving final report: {str(e)}", "red"))

    def _extract_perplexity_content(self, response: PerplexityResponse) -> Dict:
        """Extract content and citations from Perplexity API response."""
        try:
            content = None
            citations = []
            
            if response.choices:
                content = response.choices[0].message.content
            
            if response.citations:
                citations = response.citations
                
            return {
                "content": content,
                "citations": citations
            }
        except Exception as e:
            print(colored(f"✗ Error extracting Perplexity content: {str(e)}", "red"))
            return {"content": None, "citations": []}

    def _parse_questions(self, content: str) -> List[str]:
        """Parse numbered questions from content."""
        try:
            questions = []
            for line in content.split('\n'):
                line = line.strip()
                if line.startswith(('1.', '2.', '3.')):
                    question = line[2:].strip('[] ')
                    if question:
                        questions.append(question)
            return questions
        except Exception as e:
            print(colored(f"✗ Error parsing questions: {str(e)}", "red"))
            return []

    def _parse_completeness_score(self, content: str) -> float:
        """Parse completeness score from content."""
        try:
            # Extract first number found in the string
            import re
            numbers = re.findall(r"0?\.[0-9]+|[01]", content)
            if numbers:
                score = float(numbers[0])
                return min(max(score, 0.0), 1.0)  # Clamp between 0 and 1
            return 0.0
        except Exception as e:
            print(colored(f"✗ Error parsing completeness score: {str(e)}", "red"))
            return 0.0

    async def perform_perplexity_search(self, question: str) -> Dict:
        """Perform a single Perplexity API search."""
        try:
            print(colored(f"Searching: {question}", "cyan"))
            
            payload = {
                **PERPLEXITY_DEFAULT_PARAMS,
                "messages": [
                    {"role": "user", "content": RESEARCH_INSTRUCTION + question}
                ]
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(PERPLEXITY_URL, json=payload, headers=PERPLEXITY_HEADERS) as response:
                    result = await response.json()
                    perplexity_response = PerplexityResponse(**result)
                    extracted = self._extract_perplexity_content(perplexity_response)
                    return {
                        "question": question,
                        "content": extracted["content"],
                        "citations": extracted["citations"]
                    } if extracted["content"] else {"error": "No content found"}
        except Exception as e:
            print(colored(f"✗ Error in Perplexity search: {str(e)}", "red"))
            return {"error": str(e)}

    async def generate_follow_up_questions(self, current_info: str) -> List[str]:
        """Generate follow-up questions based on current information."""
        try:
            response = await self.openai_client.chat.completions.create(
                model=MODEL_NAME,
                messages=[
                    {"role": "user", "content": QUESTION_GENERATION_INSTRUCTION + current_info}
                ]
            )
            
            questions = self._parse_questions(response.choices[0].message.content)
            if not questions or len(questions) != 3:
                print(colored("⚠ Warning: Failed to generate exactly 3 questions, using default format", "yellow"))
                raw_questions = response.choices[0].message.content.split('\n')
                questions = [q.strip() for q in raw_questions if q.strip()][:3]
            return questions
        except Exception as e:
            print(colored(f"✗ Error generating follow-up questions: {str(e)}", "red"))
            return []

    async def assess_information_completeness(self, collected_info: str) -> float:
        """Assess if enough information has been collected."""
        try:
            response = await self.openai_client.chat.completions.create(
                model=MODEL_NAME,
                messages=[
                    {"role": "user", "content": COMPLETENESS_ASSESSMENT_INSTRUCTION + collected_info}
                ]
            )
            
            return self._parse_completeness_score(response.choices[0].message.content)
        except Exception as e:
            print(colored(f"✗ Error assessing information completeness: {str(e)}", "red"))
            return 0.0

    async def generate_final_report(self, all_information: str) -> str:
        """Generate a comprehensive final report."""
        try:
            response = await self.openai_client.chat.completions.create(
                model=MODEL_NAME,
                messages=[
                    {"role": "user", "content": FINAL_REPORT_INSTRUCTION + all_information}
                ]
            )
            
            return response.choices[0].message.content
        except Exception as e:
            print(colored(f"✗ Error generating final report: {str(e)}", "red"))
            return f"Error generating report: {str(e)}"

    def _format_collected_info(self, collected_info: List[Dict]) -> str:
        """Format collected information for better prompt context."""
        try:
            formatted_info = []
            for i, info in enumerate(collected_info, 1):
                if "error" in info:
                    continue
                
                # Format the main content
                formatted_info.append(f"Source {i}:\nQuestion: {info.get('question', 'N/A')}\nFindings: {info.get('content', 'N/A')}")
                
                # Add citations if available
                citations = info.get('citations', [])
                if citations:
                    formatted_info.append("Citations:")
                    for citation in citations:
                        formatted_info.append(f"- {citation}")
                
                formatted_info.append("")  # Add blank line between sources
                
            return "\n".join(formatted_info)
        except Exception as e:
            print(colored(f"✗ Error formatting collected info: {str(e)}", "red"))
            return str(collected_info)

    async def research(self, initial_question: str) -> str:
        """Main research function that coordinates the entire process."""
        print(colored(f"Starting research on: {initial_question}", "green"))
        
        collected_information = []
        iteration = 0
        
        try:
            # Initial search
            initial_result = await self.perform_perplexity_search(initial_question)
            collected_information.append(initial_result)
            
            # Save initial progress
            self._save_progress({
                "question": initial_question,
                "iteration": iteration,
                "timestamp": datetime.now().isoformat(),
                "collected_information": collected_information,
                "completeness": 0.0
            }, iteration)
            
            while iteration < MAX_ITERATIONS:
                print(colored(f"\nIteration {iteration + 1}/{MAX_ITERATIONS}", "yellow"))
                
                # Format collected information for better context
                formatted_info = self._format_collected_info(collected_information)
                
                # Check if we have enough information
                completeness = await self.assess_information_completeness(formatted_info)
                print(colored(f"Research completeness: {completeness:.2f}", "cyan"))
                
                if completeness >= MIN_CONFIDENCE_THRESHOLD:
                    print(colored("Sufficient information collected!", "green"))
                    break
                
                # Generate and perform parallel follow-up searches
                follow_up_questions = await self.generate_follow_up_questions(formatted_info)
                print(colored("Generated follow-up questions:", "magenta"))
                for q in follow_up_questions:
                    print(colored(f"- {q}", "magenta"))
                
                # Parallel searches
                tasks = [self.perform_perplexity_search(q) for q in follow_up_questions]
                results = await asyncio.gather(*tasks)
                collected_information.extend(results)
                
                # Save progress after each iteration
                iteration += 1
                self._save_progress({
                    "question": initial_question,
                    "iteration": iteration,
                    "timestamp": datetime.now().isoformat(),
                    "collected_information": collected_information,
                    "completeness": completeness,
                    "follow_up_questions": follow_up_questions
                }, iteration)
            
            # Generate final report
            print(colored("\nGenerating final report...", "yellow"))
            final_report = await self.generate_final_report(self._format_collected_info(collected_information))
            
            # Save final report with citations
            self._save_final_report(final_report, initial_question, collected_information)
            print(colored("Research complete!", "green"))
            
            return final_report
            
        except Exception as e:
            error_msg = f"Error in research process: {str(e)}"
            print(colored(error_msg, "red"))
            # Save error state
            self._save_progress({
                "question": initial_question,
                "iteration": iteration,
                "timestamp": datetime.now().isoformat(),
                "error": str(e),
                "collected_information": collected_information
            }, "error")
            return error_msg

async def main():
    """Main function to run the research agent."""
    try:
        # Example usage
        question = input(colored("Enter your research question: ", "cyan"))
        agent = ResearchAgent()
        report = await agent.research(question)
        print(colored("\nFinal Report:", "green"))
        print(report)
    except Exception as e:
        print(colored(f"Fatal error: {str(e)}", "red"))

if __name__ == "__main__":
    asyncio.run(main()) 