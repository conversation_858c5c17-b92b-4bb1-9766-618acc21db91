# DeepSeek-R1 Accuracy Testing on GPQA Diamond Dataset

This repository contains scripts for testing the accuracy of various models on the GPQA Diamond dataset. It includes scripts to test models with single responses, dual verification, and triple verification, using different APIs and models like DeepSeek-R1 (via Groq) and Gemini Flash (via OpenRouter and direct Google API).

## Model Information

- **DeepSeek-R1 70B Llama Distilled**
    - **Provider**: Groq API
    - **Description**: A distilled version of the DeepSeek-R1 70B model, optimized for efficient inference while maintaining strong performance on academic and reasoning tasks
    - **Access**: Via Groq's API service
- **Gemini 2.0 Flash Thinking Exp**
    - **Provider**: Google (via OpenRouter or direct Google Generative AI API)
    - **Description**: A fast and efficient model from the Gemini family, suitable for quick reasoning and response generation.
    - **Access**: Via OpenRouter API or Google Generative AI API

## Dataset Information

- **Name**: GPQA Diamond Dataset
- **Description**: A collection of 198 graduate-level multiple-choice questions designed to test advanced understanding across scientific domains
- **Domains**: Physics, Chemistry, Biology, and other scientific fields
- **Difficulty**: Graduate to post-graduate level questions
- **Format**: Multiple choice (A, B, C, D options)

## ❤️ Join my AI community & Get 400+ AI Projects

This is one of 400+ fascinating projects in my collection! [Support me on Patreon](https://www.patreon.com/c/echohive42/membership) to get:

- 🎯 Access to 400+ AI projects (and growing daily!)
  - Including advanced projects like [2 Agent Real-time voice template with turn taking](https://www.patreon.com/posts/2-agent-real-you-118330397)
- 📥 Full source code & detailed explanations
- 📚 1000x Cursor Course
- 🎓 Live coding sessions & AMAs
- 💬 1-on-1 consultations (higher tiers)

## Prerequisites

For Groq API scripts:
```bash
pip install groq termcolor
```
Set your Groq API key as an environment variable:
```bash
export GROQ_API_KEY='your-api-key'
```

For OpenRouter API scripts:
```bash
pip install openai termcolor
```
Set your OpenRouter API key as an environment variable:
```bash
export OPENROUTER_API_KEY='your-openrouter-api-key'
```

For Google Gemini API scripts:
```bash
pip install google-generativeai termcolor
```
Set your Google Gemini API key as an environment variable:
```bash
export GEMINI_API_KEY='your-gemini-api-key'
```


## Scripts Overview

### 1. test_model_accuracy.py

Basic script that tests model accuracy on multiple-choice questions using **DeepSeek-R1 via Groq API**.

#### Features:

- Processes questions from `gpqa_questions.json`
- Saves results to `model_accuracy_results.json`
- Implements retry logic for API errors
- Real-time progress tracking
- Resume capability for interrupted runs

#### Usage:

```bash
python test_model_accuracy.py
```

#### Output Format:

```json
{
  "metadata": {
    "model": "deepseek-r1-distill-llama-70b",
    "start_time": "...",
    "last_updated": "...",
    "total_questions": 198,
    "questions_processed": 50,
    "correct_answers": 35,
    "accuracy": 70.0
  },
  "processed_questions": [
    {
      "id": 1,
      "model_answer": "A",
      "correct_answer": "A",
      "is_correct": true,
      "timestamp": "..."
    }
  ]
}
```

### 2. test_model_accuracy_with_verification.py

Enhanced version that includes a verification step for each answer, using **DeepSeek-R1 via Groq API**.

#### Additional Features:

- Two-step process: initial answer + verification
- Tracks both original and verified answers
- Counts answers changed by verifier
- More detailed results tracking
- Same retry and resume capabilities as basic version

#### Usage:

```bash
python test_model_accuracy_with_verification.py
```

#### Output Format:

```json
{
  "metadata": {
    "model": "deepseek-r1-distill-llama-70b",
    "start_time": "...",
    "last_updated": "...",
    "total_questions": 198,
    "questions_processed": 50,
    "correct_answers": 35,
    "accuracy": 70.0,
    "answers_changed_by_verifier": 5
  },
  "processed_questions": [
    {
      "id": 1,
      "original_answer": "A",
      "verified_answer": "B",
      "was_changed": true,
      "correct_answer": "B",
      "is_correct": true,
      "timestamp": "..."
    }
  ]
}
```

### 3. test_model_accuracy_with_dual_verification.py

Script that performs accuracy testing with dual verification using **DeepSeek-R1 via Groq API**. It gets two different perspectives on each question and then verifies the answers.

#### Features:
- Dual perspective analysis for each question
- Final verification step to determine the best answer from two perspectives
- Tracks changes made by the verifier and differences between perspectives
- Saves detailed responses for analysis

#### Usage:
```bash
python test_model_accuracy_with_dual_verification.py
```

#### Output Files:
- `model_accuracy_results_dual_verified.json`: Summary of accuracy results with dual verification metrics.
- `model_detailed_responses_dual_verified.json`: Detailed responses from both perspectives and the verifier.

### 4. test_model_accuracy_with_triple_verification.py

Script that extends the dual verification to triple verification, using **DeepSeek-R1 via Groq API**. It gets three different perspectives and then verifies the answers.

#### Features:
- Triple perspective analysis for each question
- Final verification step to determine the best answer from three perspectives
- Tracks changes, and differences between all three perspectives
- Saves even more detailed responses

#### Usage:
```bash
python test_model_accuracy_with_triple_verification.py
```

#### Output Files:
- `model_accuracy_results_triple_verified.json`: Accuracy results with triple verification metrics.
- `model_detailed_responses_triple.json`: Detailed responses from all three perspectives and the verifier.

### 5. test_model_accuracy_gemini_flash.py

Basic accuracy test script using **Gemini Flash via OpenRouter API**. This is a simplified single-model test similar to `test_model_accuracy.py` but uses Gemini Flash.

#### Features:
- Tests accuracy using Gemini Flash model
- Saves results and detailed responses
- Includes retry logic and progress tracking

#### Usage:
```bash
python test_model_accuracy_gemini_flash.py
```

#### Output Files:
- `model_accuracy_results_gemini_flash.json`: Accuracy results for Gemini Flash.
- `model_detailed_responses_gemini_flash.json`: Detailed responses from Gemini Flash.

### 6. test_model_accuracy_with_dual_verification_gemini.py

Dual verification script using **Gemini Flash via direct Google Gemini API**. This script is similar to `test_model_accuracy_with_dual_verification.py` but uses Gemini Flash and the direct Google API.

#### Features:
- Implements dual verification with Gemini Flash
- Uses direct Google Gemini API (google-generativeai library)
- Saves detailed results and responses

#### Usage:
```bash
python test_model_accuracy_with_dual_verification_gemini.py
```

#### Output Files:
- `model_accuracy_results_dual_verified_gemini.json`: Accuracy results for Gemini Flash with dual verification.
- `model_detailed_responses_dual_verified_gemini.json`: Detailed responses from Gemini Flash in dual verification setup.

### 7. test_model_accuracy_with_triple_verification_openrouter.py

Triple verification script using **Gemini Flash via OpenRouter API**. This script is the triple verification counterpart to `test_model_accuracy_with_triple_verification.py` but uses Gemini Flash and OpenRouter.

#### Features:
- Triple verification process with Gemini Flash
- Utilizes OpenRouter API to access Gemini Flash
- Comprehensive result and response saving

#### Usage:
```bash
python test_model_accuracy_with_triple_verification_openrouter.py
```

#### Output Files:
- `model_accuracy_results_triple_verified_gemini.json`: Accuracy results for Gemini Flash with triple verification.
- `model_detailed_responses_triple_gemini.json`: Detailed responses from Gemini Flash in triple verification setup.


### 8. test_model_answers.py

Script to test a **specific question** from the dataset using **DeepSeek-R1 via Groq API**. This is useful for debugging or examining model responses to individual questions.

#### Features:
- Allows testing of a single question specified by `QUESTION_NUMBER` constant in the script.
- Prints the model's answer, the correct answer, and whether the model was correct.
- Can print the explanation of the correct answer if available in the dataset.

#### Usage:
1.  Modify the `QUESTION_NUMBER` variable in the script to the ID of the question you want to test.
2.  Run the script:
    ```bash
    python test_model_answers.py
    ```

#### No Output Files:
- This script prints the output to the console and does not save results to files.

## Configuration

All scripts use constants defined at the beginning of the file for configuration, including:

```python
RESULTS_FILE = "model_accuracy_results.json"
DETAILED_RESPONSES_FILE = "model_detailed_responses.json" # if applicable
MODEL_NAME = "deepseek-r1-distill-llama-70b" # or "google/gemini-2.0-flash-thinking-exp:free"
MAX_RETRIES = 3 # or 6 or 100 or 1000
RETRY_DELAY = 2  # or 15 seconds
```
Modify these constants directly in each script to adjust settings like output file names, model name, retry behavior, and delay.

## Error Handling

- Retries API calls up to a configurable number of times (`MAX_RETRIES`)
- Handles invalid answer formats by retrying
- Graceful shutdown with Ctrl+C, preserving progress
- Detailed error messages with color coding using `termcolor`

## Progress Tracking

Scripts provide real-time progress updates in the console, including:

- Questions processed
- Correct answers count
- Current accuracy percentage
- (For verification scripts) Answers changed by verifier, different perspective answer counts, etc.

## Files

- `gpqa_questions.json`: Input questions dataset in JSON format.
- `model_accuracy_results*.json`: Result files (filenames vary based on the script, see script descriptions).
- `model_detailed_responses*.json`: Detailed response files (filenames vary based on the script, see script descriptions).
- `incorrect_answers_20250126_180547.json`: Used by `test_model_answers.py` to load specific incorrect answers for testing.

## Notes

- Verification scripts (`*_with_verification*`, `*_with_dual_verification*`, `*_with_triple_verification*`) take longer to run due to multiple API calls per question but may provide higher accuracy.
- Scripts can be safely interrupted and resumed; progress is saved after each question.
- Use the appropriate API key environment variable based on the script and API being used (Groq API, OpenRouter API, or Google Gemini API).
- Adjust `MAX_RETRIES` and `RETRY_DELAY` constants based on API reliability and rate limits.
- For quick, basic accuracy testing, use `test_model_accuracy.py` or `test_model_accuracy_gemini_flash.py`.
- For more in-depth analysis and potentially higher accuracy, use the verification scripts.

## Error Messages

Common error messages and their meanings:

- "Invalid answer format": Model response does not contain the expected answer format (e.g., "ANSWER: X").
- "API error": Indicates an issue with the API call, such as connection problems, rate limits, or server errors. Check error details in the console for more information.
- "Maximum retry attempts reached": The script failed to get a valid response or answer after the maximum number of retry attempts (`MAX_RETRIES`). Consider increasing `MAX_RETRIES` or checking API status.
- "Error initializing [Client Name] client": Problem initializing the API client, likely due to missing or incorrect API key environment variable. Ensure the correct API key is set as an environment variable (e.g., `GROQ_API_KEY`, `OPENROUTER_API_KEY`, `GEMINI_API_KEY`).
- "Error loading [file name] file": The script could not load a required JSON file (e.g., `gpqa_questions.json`, `incorrect_answers_*.json`). Verify that the file exists in the correct directory and is not corrupted.
- "Question with ID [question_id] not found": In `test_model_answers.py`, the specified `QUESTION_NUMBER` does not match any question ID in the loaded dataset. Double-check the question ID.
- "Could not find answer in correct format": The model's response was received but did not contain the answer in the expected "ANSWER: X" format. This might indicate an issue with the model's response formatting or the answer extraction logic.
- "Invalid [perspective/verification] format": In verification scripts, the model's response for the second perspective or verification step did not follow the expected format. This could be due to model behavior or prompt issues.
- "Maximum [verification attempts/retry attempts] reached": The script exceeded the maximum number of retries for a specific step (e.g., verification, getting an answer). This might indicate persistent issues with the API or model for a particular question.
