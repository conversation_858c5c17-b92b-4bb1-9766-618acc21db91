from groq import Groq
import os
import json
from termcolor import colored
import time
from datetime import datetime
import signal
import sys

# Constants
RESULTS_FILE = "model_accuracy_results_dual_verified.json"
MODEL_NAME = "deepseek-r1-distill-llama-70b"
MAX_RETRIES = 3
RETRY_DELAY = 2
DETAILED_RESPONSES_FILE = "model_detailed_responses.json"

def init_groq_client():
    try:
        return Groq(api_key=os.getenv('GROQ_API_KEY'))
    except Exception as e:
        print(colored(f"Error initializing Groq client: {e}", "red"))
        return None

def load_questions():
    try:
        with open("gpqa_questions.json", "r", encoding="utf-8") as f:
            return json.load(f)
    except Exception as e:
        print(colored(f"Error loading questions file: {e}", "red"))
        return None

def load_or_create_results():
    try:
        if os.path.exists(RESULTS_FILE):
            with open(RESULTS_FILE, "r", encoding="utf-8") as f:
                return json.load(f)
        return {
            "metadata": {
                "model": MODEL_NAME,
                "start_time": datetime.now().isoformat(),
                "last_updated": datetime.now().isoformat(),
                "total_questions": 0,
                "questions_processed": 0,
                "correct_answers": 0,
                "accuracy": 0.0,
                "answers_changed_by_verifier": 0,
                "different_perspective_changes": 0
            },
            "processed_questions": []
        }
    except Exception as e:
        print(colored(f"Error loading/creating results file: {e}", "red"))
        return None

def save_results(results):
    try:
        results["metadata"]["last_updated"] = datetime.now().isoformat()
        with open(RESULTS_FILE, "w", encoding="utf-8") as f:
            json.dump(results, f, indent=2)
    except Exception as e:
        print(colored(f"Error saving results: {e}", "red"))

def save_detailed_responses(question_id, first_response, second_response, verification_response):
    try:
        # Load existing responses or create new structure
        if os.path.exists(DETAILED_RESPONSES_FILE):
            with open(DETAILED_RESPONSES_FILE, "r", encoding="utf-8") as f:
                responses = json.load(f)
        else:
            responses = {
                "metadata": {
                    "model": MODEL_NAME,
                    "created_at": datetime.now().isoformat(),
                    "last_updated": datetime.now().isoformat()
                },
                "responses": {}
            }
        
        # Add new responses
        responses["responses"][str(question_id)] = {
            "timestamp": datetime.now().isoformat(),
            "first_analysis": first_response,
            "second_analysis": second_response,
            "verification": verification_response
        }
        
        responses["metadata"]["last_updated"] = datetime.now().isoformat()
        
        # Save to file
        with open(DETAILED_RESPONSES_FILE, "w", encoding="utf-8") as f:
            json.dump(responses, f, indent=2)
            
        print(colored(f"Saved detailed responses for question {question_id}", "green"))
    except Exception as e:
        print(colored(f"Error saving detailed responses: {e}", "red"))

def format_question(question_data):
    try:
        prompt = f"""Please solve this multiple choice question. Provide your complete reasoning and then state your final answer.

Question: {question_data['question']}

Options:
A: {question_data['options']['A']}
B: {question_data['options']['B']}
C: {question_data['options']['C']}
D: {question_data['options']['D']}

Please provide your answer in the format 'ANSWER: X' where X is A, B, C, or D."""
        return prompt
    except Exception as e:
        print(colored(f"Error formatting question: {e}", "red"))
        return None

def format_second_perspective_prompt(question_data, first_response, first_answer):
    try:
        prompt = f"""Please solve this multiple choice question using a COMPLETELY DIFFERENT METHOD than the previous response. Your approach should be fundamentally different from the first analysis.

Question: {question_data['question']}

Options:
A: {question_data['options']['A']}
B: {question_data['options']['B']}
C: {question_data['options']['C']}
D: {question_data['options']['D']}

Previous analysis:
{first_response}

Previous answer: {first_answer}

Please provide a fresh analysis using a completely different method. For example:
1. If the first analysis used mathematical reasoning, try logical deduction
2. If the first used theoretical approach, try practical examples
3. If the first used forward reasoning, try backward reasoning
4. If the first used direct calculation, try estimation or approximation
5. If the first used textbook method, try real-world application

Make sure your method is fundamentally different from the previous approach.
Explain why your method provides a valuable alternative perspective.

End your response with 'ANSWER: X' where X is A, B, C, or D based on your alternative analysis."""
        return prompt
    except Exception as e:
        print(colored(f"Error formatting second perspective prompt: {e}", "red"))
        return None

def format_enhanced_verification_prompt(question_data, first_response, second_response, first_answer, second_answer):
    try:
        prompt = f"""Please carefully verify these two different analyses of the same multiple choice question. You are the final verifier. You should also consider alternative perspectives and approaches if you think they are relevant.

Question: {question_data['question']}

Options:
A: {question_data['options']['A']}
B: {question_data['options']['B']}
C: {question_data['options']['C']}
D: {question_data['options']['D']}

First analysis:
{first_response}
First answer: {first_answer}

Second perspective analysis:
{second_response}
Second answer: {second_answer}

As the final verifier, please:
1. Review both analyses critically while considering alternative perspectives and approaches.
2. Consider the strengths and weaknesses of each perspective and also other alternative perspectives and approaches.
3. Determine which reasoning is more sound, or if they complement each other or if they are both flawed.
4. Make a final determination

If you agree with either answer, start with "VERIFIED: " followed by the answer.
If you have a different conclusion, start with "CHANGED: " followed by your answer.

End your response with either:
VERIFIED: X
or
CHANGED: X
where X is A, B, C, or D."""
        return prompt
    except Exception as e:
        print(colored(f"Error formatting enhanced verification prompt: {e}", "red"))
        return None

def extract_answer(response):
    try:
        import re
        match = re.search(r'ANSWER:\s*([A-Da-d])', response, re.IGNORECASE | re.MULTILINE)
        if match:
            return match.group(1).upper()
        return None
    except Exception as e:
        print(colored(f"Error extracting answer: {e}", "red"))
        return None

def extract_verified_answer(response):
    try:
        import re
        verified_match = re.search(r'VERIFIED:\s*([A-Da-d])', response, re.IGNORECASE | re.MULTILINE)
        changed_match = re.search(r'CHANGED:\s*([A-Da-d])', response, re.IGNORECASE | re.MULTILINE)
        
        if verified_match:
            return verified_match.group(1).upper(), False
        elif changed_match:
            return changed_match.group(1).upper(), True
        return None, None
    except Exception as e:
        print(colored(f"Error extracting verified answer: {e}", "red"))
        return None, None

def get_model_answer(client, prompt, retry_count=0):
    if retry_count >= MAX_RETRIES:
        print(colored(f"Maximum retry attempts ({MAX_RETRIES}) reached. Skipping question.", "red"))
        return None, None
        
    try:
        print(colored(f"Attempt {retry_count + 1}/{MAX_RETRIES + 1}...", "cyan"))
        completion = client.chat.completions.create(
            model=MODEL_NAME,
            messages=[{"role": "user", "content": prompt}],
            stream=True
        )
        
        answer = ""
        for chunk in completion:
            if chunk.choices[0].delta.content:
                answer += chunk.choices[0].delta.content
        
        extracted_answer = extract_answer(answer)
        if extracted_answer:
            return extracted_answer, answer
        else:
            print(colored("Invalid answer format, retrying...", "yellow"))
            time.sleep(RETRY_DELAY)
            return get_model_answer(client, prompt, retry_count + 1)
            
    except Exception as e:
        print(colored(f"API error: {e}", "red"))
        print(colored("Retrying...", "yellow"))
        time.sleep(RETRY_DELAY)
        return get_model_answer(client, prompt, retry_count + 1)

def get_second_perspective(client, question_data, first_response, first_answer, retry_count=0):
    if retry_count >= MAX_RETRIES:
        print(colored(f"Maximum retry attempts ({MAX_RETRIES}) reached for second perspective.", "red"))
        return None, None
        
    try:
        print(colored("Getting second perspective...", "cyan"))
        prompt = format_second_perspective_prompt(question_data, first_response, first_answer)
        
        completion = client.chat.completions.create(
            model=MODEL_NAME,
            messages=[{"role": "user", "content": prompt}],
            stream=True
        )
        
        response = ""
        for chunk in completion:
            if chunk.choices[0].delta.content:
                response += chunk.choices[0].delta.content
        
        extracted_answer = extract_answer(response)
        if extracted_answer:
            return extracted_answer, response
        else:
            print(colored("Invalid second perspective format, retrying...", "yellow"))
            time.sleep(RETRY_DELAY)
            return get_second_perspective(client, question_data, first_response, first_answer, retry_count + 1)
            
    except Exception as e:
        print(colored(f"Second perspective API error: {e}", "red"))
        time.sleep(RETRY_DELAY)
        return get_second_perspective(client, question_data, first_response, first_answer, retry_count + 1)

def verify_dual_answers(client, question_data, first_response, second_response, first_answer, second_answer, retry_count=0):
    if retry_count >= MAX_RETRIES:
        print(colored(f"Maximum verification attempts ({MAX_RETRIES}) reached.", "red"))
        return None, None, None
        
    try:
        print(colored("Performing final verification...", "cyan"))
        verification_prompt = format_enhanced_verification_prompt(
            question_data, first_response, second_response, first_answer, second_answer
        )
        
        completion = client.chat.completions.create(
            model=MODEL_NAME,
            messages=[{"role": "user", "content": verification_prompt}],
            stream=True
        )
        
        verification_response = ""
        for chunk in completion:
            if chunk.choices[0].delta.content:
                verification_response += chunk.choices[0].delta.content
        
        verified_answer, was_changed = extract_verified_answer(verification_response)
        if verified_answer:
            return verified_answer, was_changed, verification_response
        else:
            print(colored("Invalid verification format, retrying...", "yellow"))
            time.sleep(RETRY_DELAY)
            return verify_dual_answers(client, question_data, first_response, second_response, 
                                    first_answer, second_answer, retry_count + 1)
            
    except Exception as e:
        print(colored(f"Verification API error: {e}", "red"))
        time.sleep(RETRY_DELAY)
        return verify_dual_answers(client, question_data, first_response, second_response, 
                                 first_answer, second_answer, retry_count + 1)

def print_progress(results):
    processed = results["metadata"]["questions_processed"]
    total = results["metadata"]["total_questions"]
    correct = results["metadata"]["correct_answers"]
    accuracy = results["metadata"]["accuracy"]
    changed = results["metadata"]["answers_changed_by_verifier"]
    different_perspective = results["metadata"]["different_perspective_changes"]
    
    print("\nProgress Update:")
    print(colored(f"Processed: {processed}/{total} questions", "cyan"))
    print(colored(f"Correct: {correct}", "green"))
    print(colored(f"Accuracy: {accuracy:.2f}%", "yellow"))
    print(colored(f"Answers changed by verifier: {changed}", "magenta"))
    print(colored(f"Different perspective answers: {different_perspective}", "blue"))

def signal_handler(sig, frame):
    print(colored("\nGracefully shutting down...", "yellow"))
    sys.exit(0)

def main():
    signal.signal(signal.SIGINT, signal_handler)
    
    print(colored("Initializing test run with dual verification...", "green"))
    
    client = init_groq_client()
    if not client:
        return
    
    questions_data = load_questions()
    if not questions_data:
        return
    
    results = load_or_create_results()
    if not results:
        return
    
    if results["metadata"]["total_questions"] == 0:
        results["metadata"]["total_questions"] = len(questions_data["questions"])
        save_results(results)
    
    processed_ids = {q["id"] for q in results["processed_questions"]}
    
    for question in questions_data["questions"]:
        if question["id"] in processed_ids:
            continue
            
        print(colored(f"\nProcessing question {question['id']}...", "cyan"))
        
        # Get first answer
        prompt = format_question(question)
        if not prompt:
            continue
        
        first_answer, first_response = get_model_answer(client, prompt)
        if not first_answer or not first_response:
            continue
            
        print(colored(f"First answer: {first_answer}", "cyan"))
        
        # Get second perspective
        second_answer, second_response = get_second_perspective(
            client, question, first_response, first_answer
        )
        if not second_answer or not second_response:
            continue
            
        print(colored(f"Second perspective answer: {second_answer}", "cyan"))
        
        # Final verification
        verified_answer, was_changed, verification_response = verify_dual_answers(
            client, question, first_response, second_response, first_answer, second_answer
        )
        if not verified_answer:
            continue
            
        # Save detailed responses
        save_detailed_responses(
            question["id"],
            first_response,
            second_response,
            verification_response
        )
            
        if was_changed:
            print(colored(f"Verifier changed answer to: {verified_answer}", "yellow"))
            results["metadata"]["answers_changed_by_verifier"] += 1
        else:
            print(colored(f"Verifier confirmed answer: {verified_answer}", "green"))
        
        if second_answer != first_answer:
            results["metadata"]["different_perspective_changes"] += 1
        
        # Update results
        correct = verified_answer == question["correct_answer"]
        results["processed_questions"].append({
            "id": question["id"],
            "first_answer": first_answer,
            "second_answer": second_answer,
            "verified_answer": verified_answer,
            "was_changed": was_changed,
            "correct_answer": question["correct_answer"],
            "is_correct": correct,
            "timestamp": datetime.now().isoformat()
        })
        
        results["metadata"]["questions_processed"] += 1
        if correct:
            results["metadata"]["correct_answers"] += 1
        
        results["metadata"]["accuracy"] = (results["metadata"]["correct_answers"] / 
                                         results["metadata"]["questions_processed"]) * 100
        
        save_results(results)
        print_progress(results)
        time.sleep(1)
    
    print(colored("\nDual verification test run completed!", "green"))
    print_progress(results)

if __name__ == "__main__":
    main() 