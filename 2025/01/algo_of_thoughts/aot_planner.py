from openai import OpenAI
import os
from termcolor import colored
import json
import time

# Configuration
MODEL_NAME = "gpt-4o"
API_KEY = os.getenv("OPENAI_API_KEY")
PROBLEM_TYPE = "Blocksworld"
INITIAL_STATE = "A on B on C on Table, D on E on Table"
GOAL_STATE = "E on B, A on C on D"
VALID_ACTIONS = ["unstack", "stack", "pick-up", "put-down"]
MAX_DEPTH = 5

client = OpenAI(api_key=API_KEY)

def aot_plus_prompt():
    return f"""You are an autonomous planning system. Follow these rules:
1. Generate multiple possible actions at each step
2. Track state changes using hierarchical identifiers (e.g., 1.2.3)
3. Every 3 steps, regenerate and restate the full current state
4. If progress stalls, backtrack to previous viable state
5. Validate each action against current state before proceeding

Example of valid process:
1. [Action] Unstack A from B
   [State 1.1] B on C, A on Table
2. [Action] Stack A on D
   [State 1.2] A on D, B on C
3. [Backtrack] State 1.1
4. [Action] Move B to E
   [State 2.1] B on E, A on Table

Current Problem:
- Type: {PROBLEM_TYPE}
- Initial: {INITIAL_STATE}
- Goal: {GOAL_STATE}
- Valid Actions: {', '.join(VALID_ACTIONS)}

Output JSON format:
{{
  "plan": [
    {{
      "id": "1.1",
      "action": "unstack A from B",
      "state": "B on C, A on Table",
      "valid": true,
      "reasoning": "Clearing block B for future moves"
    }},
    {{
      "id": "1.2",
      "action": "stack A on D",
      "state": "A on D, B on C",
      "valid": false,
      "reasoning": "Invalid position for final goal"
    }},
    {{
      "id": "2.1",
      "action": "move B to E",
      "state": "B on E, A on Table",
      "valid": true,
      "reasoning": "Preparing correct foundation"
    }}
  ],
  "backtracks": ["1.2"],
  "success": false
}}"""

def execute_plan():
    print(colored("\nAoT+ Planning System Initializing...", "cyan", attrs=["bold"]))
    print(colored(f"Problem Type: {PROBLEM_TYPE}", "yellow"))
    print(colored(f"Initial State: {INITIAL_STATE}", "yellow"))
    print(colored(f"Target Configuration: {GOAL_STATE}\n", "yellow"))
    
    try:
        start_time = time.time()
        stream = client.chat.completions.create(
            model=MODEL_NAME,
            messages=[
                {"role": "system", "content": aot_plus_prompt()},
                {"role": "user", "content": "Generate complete plan with AoT+ autonomous exploration"}
            ],
            temperature=0.5,
            stream=True,
            response_format={"type": "json_object"}
        )
        
        full_response = ""
        print(colored("Initiating State Space Exploration:", "green"))
        for chunk in stream:
            if chunk.choices[0].delta.content:
                delta = chunk.choices[0].delta.content
                full_response += delta
                print(colored(delta, "white"), end="", flush=True)
                time.sleep(0.02)
                
        print("\n\n" + colored("Finalizing Plan Verification...", "cyan"))
        plan = json.loads(full_response)
        
        # Print formatted results
        print(colored("\nPLAN EXECUTION SUMMARY", "cyan", attrs=["bold"]))
        for step in plan["plan"]:
            status_color = "green" if step["valid"] else "red"
            print(colored(f"\nStep {step['id']}:", "yellow"))
            print(colored(f"Action: {step['action']}", "white"))
            print(colored(f"State: {step['state']}", "white"))
            print(colored(f"Valid: ", "white") + colored(step["valid"], status_color))
            print(colored(f"Reasoning: {step['reasoning']}", "white"))
        
        print(colored("\nBacktrack Points:", "yellow"))
        print(colored(", ".join(plan["backtracks"]) if plan["backtracks"] else "None", "white"))
        
        print(colored("\nFinal Success Status:", "yellow"))
        print(colored(str(plan["success"]), "green" if plan["success"] else "red"))
        
        print(colored(f"\nTotal Planning Time: {time.time()-start_time:.2f}s", "cyan"))

    except Exception as e:
        print(colored(f"\nPlanning Error: {str(e)}", "red"))

if __name__ == "__main__":
    execute_plan() 