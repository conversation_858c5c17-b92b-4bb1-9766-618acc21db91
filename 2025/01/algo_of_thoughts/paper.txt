ABSTRACT
Large language models (LLMs) have demonstrated significant capabilities in natural language processing and reasoning, yet their effectiveness in autonomous
planning has been under debate. While existing studies have utilized LLMs with
external feedback mechanisms or in controlled environments for planning, these
approaches often involve substantial computational and development resources
due to the requirement for careful design and iterative backprompting. Moreover,
even the most advanced LLMs like GPT-4 struggle to match human performance
on standard planning benchmarks, such as the Blocksworld, without additional
support. This paper investigates whether LLMs can independently generate longhorizon plans that rival human baselines. Our novel enhancements to Algorithmof-Thoughts (AoT), which we dub AoT+, help achieve state-of-the-art results in
planning benchmarks out-competing prior methods and human baselines all autonomously.
1 INTRODUCTION
Large language models (LLMs) based on the transformer architecture (<PERSON><PERSON><PERSON><PERSON>, 2017) have emerged
as a transformative force in artificial intelligence, revolutionizing natural language processing and
demonstrating remarkable capabilities across diverse domains. These models, trained on vast corpora of text data, have shown prowess not only in language-related tasks but also in problem-solving
(<PERSON> <PERSON>, 2022), reasoning (<PERSON>, 2020; <PERSON><PERSON><PERSON> et al., 2022), and even coding (<PERSON>
et al., 2021; <PERSON><PERSON><PERSON><PERSON> et al., 2022). The rapid advancements in AI technology have sparked intense interest in exploring their potential for more complex cognitive tasks, reinforcement learning
(<PERSON><PERSON><PERSON> & Jin, 2023; <PERSON><PERSON><PERSON> et al., 2024; <PERSON><PERSON> et al., 2024; 2025; <PERSON><PERSON> et al., 2023), control
(<PERSON>l et al., 2021a; Gunes et al., 2023; Cosk<PERSON> et al., 2022; Sel et al., 2021b; ul <PERSON>een et al., 2024;
2022), optimization (Jin et al., 2023; 2024; Al-Tawaha et al., 2023; Khattar & Jin, 2024; Al-Tawaha
& Jin, 2024; Yang et al., 2023), federated-learning (Khan et al., 2023), cyber-security (Manzoor
et al., 2024; Roy et al., 2024a; Cody et al., 2022; Huang et al., 2022; Wang et al., 2024), including
sequential decision-making and planning. These efforts have yielded promising results, showcasing
the models’ ability to generate solutions for a wide array of challenges (Huang & Chang, 2022;
Suzgun et al., 2022). However, as the complexity of tasks increases, particularly in domains requiring long-horizon planning and precise execution, the limitations of current LLM-based approaches
become apparent (Yao et al., 2022; Long, 2023; Valmeekam et al., 2023; Sel et al., 2024a).
One of the primary challenges in utilizing LLMs for planning tasks is their inherent difficulty in selfverifying outputs (Stechly et al., 2024; Liu et al., 2024b; Roy et al., 2024b). This limitation manifests
in various ways, from suggesting potentially illegal actions to failing to recognize whether a goal
∗Corresponding author
1
arXiv:2501.13545v1 [cs.CL] 23 Jan 2025
Published as a conference paper at ICLR 2025
Process
Problem Instance
Problem
Output
Problem
Output
Problem
Plan
Plan
Verifier
Candidate
Plan
Plan
Valid
Not Valid
Problem Plan
Problem Plan
Problem Plan
Problem Instance
LLM-Modulo
AoT+
Figure 1: Illustration outlining differences between AoT+ and LLM-Modulo-like frameworks.
has been achieved in planning problems. Moreover, LLMs often struggle with inductive reasoning,
finding it challenging to verify whether their hypotheses hold true for given cases – a crucial step
in generalizing from simple observations to broader phenomena (Qiu et al., 2023). To address these
shortcomings, researchers have increasingly turned to hybrid approaches that combine LLMs with
external verification tools (Zhou et al., 2022; Drozdov et al., 2022; Yao et al., 2024, inter alia).
These methods typically use LLMs as idea generators or heuristics, with external systems providing
feedback to guide the models towards more accurate and feasible solutions. While this approach
has shown promise, it introduces significant complexity and potential failure modes, often requiring
substantial development time and resources to implement and maintain (Chu et al., 2023).
An alternative strand of research has focused on improving LLMs’ planning capabilities through
advanced prompting techniques. Mirroring biological cognitive abilities to activate analytical System
2 instead of immediate System 1 (Kahneman, 2011), methods such as Chain-of-Thought (CoT)
aimed at teaching LLMs step-by-step reasoning, have demonstrated some success in enhancing
LLMs’ reasoning abilities (Wei et al., 2022; Kojima et al., 2022). However, recent studies have
highlighted the limitations of these approaches, particularly in more challenging planning domains.
For instance, in benchmarks like Blocksworld, where human performance reaches approximately
78% accuracy, even state-of-the-art models like GPT-4 achieve only around 30% accuracy, with
CoT prompting offering little to no improvement. The situation is even more concerning in complex
planning domains such as logistics, where GPT-4’s success rate for generating valid plans drops to
a mere 14% (Valmeekam et al., 2023). Notably, the performance of open-source LLMs in these
planning tasks is even more limited, further underscoring the significant gap between current LLM
capabilities and human-level planning proficiency across various domains.
These sobering results have led some researchers to conclude that current LLMs are fundamentally ill-suited for autonomous planning tasks, particularly those requiring long-horizon reasoning
(Valmeekam et al., 2023; Stechly et al., 2024; Kambhampati et al., 2024). However, our in-depth
investigations suggest that this pessimism may be premature. We posit that with simple in-context
examples showing the search process that also acknowledges the limitations of LLMs, it is pos2
Published as a conference paper at ICLR 2025
sible to dramatically improve their autonomous planning capabilities without resorting to external
verification tools which significantly increases development and computational costs.
In this paper, we introduce AoT+, an enhanced prompting technique that builds upon the foundation of the Algorithm of Thoughts (AoT) approach (Sel et al., 2024b) to activate System 3 thinking,
a more deliberate decision-making process one uses when facing doubt, dilemma, or disruption
(Webb, 2021). Our method not only challenges the perceived limitations of LLMs in planning
tasks but also demonstrates the potential to surpass previous state-of-the-art results, including those
achieved using external verifiers. Through extensive experimentation and analysis, we seek to answer two critical questions:
1. Can LLMs generate long-horizon plans that rival human performance without external
tools?
2. If so, what key factors differentiate our prompting technique from other step-by-step methods like Chain-of-Thought?
To address these questions, we first examine the limitations of existing approaches. We argue that
while CoT prompting has shown success in many reasoning tasks, it falls short in non-ergodic planning problems where a single misstep can lead to an unrecoverable state. Methods like Tree of
Thoughts (ToT) (Yao et al., 2024) attempt to mitigate this issue by using external systems to track
the search process and guide exploration. However, the computational cost of these approaches renders them impractical for problems with non-trivial depth and breadth. AoT framework (Sel et al.,
2024b), which demonstrated that incorporating “human intuitions” in the search process, along with
self-verification and backtracking mechanisms, could lead to significant improvements over CoT
while remaining computationally efficient. However, our preliminary tests with vanilla AoT revealed a tendency for state hallucinations, which we hypothesize is caused by excessive “cognitive
load” – a phenomenon where the model struggles to manage all the relevant information, leading to
errors in decision-making.
Motivated by recent findings on LLMs’ attention mechanisms, which show a tendency to focus on
the beginning and end of their context (Liu et al., 2024a), we introduced several key innovations in
AoT+:
1. Periodic Structured State Generation: We implement a mechanism for periodically regenerating and restating the current problem state during the search process. This helps alleviate
the cognitive load on the model by reducing the need to attend to the entire context history.
2. Random Trajectory Augmentation: To further simplify the prompting process and improve
generalizability, we introduced random search trajectories augmented with correct steps
leading to goal states. This approach allows for more efficient example generation without
requiring human-authored thought processes.
Figure 1 illustrates the key components and workflow of our AoT+ method compared to traditional
approaches. These innovations have led to remarkable improvements in performance across multiple
challenging planning domains. In the Blocksworld benchmark, AoT+ not only surpasses previous
LLM-based methods but also exceeds human performance benchmarks. Similarly, in the logistics
domain, our method achieves state-of-the-art results, dramatically improving upon the performance
of vanilla GPT-4. The success of AoT+ raises intriguing questions about the nature of reasoning
in LLMs and the potential for unlocking more sophisticated cognitive abilities through better understanding. Our work suggests that LLMs may possess latent planning capabilities that can be
activated through the right combination of context, structure, and guidance.
2 RELATED WORK
Sequential Decision-Making with LLMs. Having been trained on a large corpus of world-wide
text, LLMs excel at understanding a wide range of topics that helps them coming up with possible
continuations. The earliest works have observed improvements over standard prompting (Brown,
2020) for general problem solving, where we directly expect the model to generate the steps one after
the other, by step-by-step reasoning by transforming the original problem to a sequential decisionmaking one, e.g., CoT (Nye et al., 2021; Wei et al., 2022; Kojima et al., 2022; Zhang et al., 2022).
3
Published as a conference paper at ICLR 2025
However, ToT showed the underwhelming performance in problems that are inherently sequential,
such as planning problems (Long, 2023; Yao et al., 2024). These works and their follow-ups, Lei
et al. (2023); Besta et al. (2024); Chen et al. (2024), have relied on using LLMs as mere heuristics
with an external mechanism to keep track of search traces to further boost LLMs’ capabilities. However, due to significantly increased API requests for individual search nodes, they are notoriously
expensive and slow. Sel et al. (2024b) proposed the use of pure LLM framework that requires only
a single query to match or even surpass methods like ToT, using carefully curated examples woven
with human-intuitions in their search trajectories directly in-context, that leads to drastic reductions
in compute and API costs.
Self-Verification. It is an intuitive and natural direction to try to utilize LLMs to evaluate the
reasonability and the correctness of their decisions. There is large literature showing that selfverification can help detect mistakes in the token generation of LLMs to correct them to improve
performance on domains such as instruction-tuning (Bai et al., 2022), coding (Zelikman et al., 2023;
Kim et al., 2024), ethical decision-making (Ma et al., 2023; Sel et al., 2024a), question-answering
(Madaan et al., 2024; Shinn et al., 2024; Paul et al., 2024; Xie et al., 2024; He et al., 2024). However,
there are also cases where LLMs perform poorly at correcting themselves, especially in symbolic
tasks (Valmeekam et al., 2023; Kamoi et al., 2024; Kambhampati et al., 2024).
Classical Search Algorithms. The field of classical search algorithms has a rich history in AI
and planning. Dynamic Programming (Bellman, 1966), laid the groundwork for solving complex
problems by breaking them down into simpler subproblems. The A* algorithm (Hart et al., 1968),
revolutionized pathfinding and graph traversal by combining the benefits of breadth-first search and
best-first search. More recently, Monte Carlo Tree Search (MCTS) methods (Kocsis & Szepesvári,
2006; Coulom, 2006; Gelly & Silver, 2007; Ramadan et al., 2023), exemplified by AlphaGo (Silver
et al., 2017) and AlphaZero (Schrittwieser et al., 2020), have shown remarkable success in gameplaying domains, demonstrating the power of combining search with learned policies. The concept
of heuristics, central to many of these algorithms, guides the search process towards promising
solutions, a principle we leverage in our use of LLMs as heuristic generators.
3 PROMPTING METHODOLOGIES FOR PLANNING PROBLEMS
3.1 PLANNING VS. MYOPIC PROBLEMS
To understand the challenges faced by Large Language Models (LLMs) in planning tasks, it is
crucial to distinguish between myopic and planning problems (Keeney, 1993; Bertsekas, 1995):
Myopic Problems. A myopic problem is a task that can be solved through simple reasoning and
memorization, typically requiring a straightforward, step-by-step approach without the need for
long-term strategy or consideration of future consequences.
Planning Problems. A planning problem is a task that requires the ability to formulate a sequence
of actions to achieve a specific goal, often involving multiple steps, consideration of future states,
and the ability to backtrack or revise the plan based on intermediate outcomes.
The key distinction lies in the cognitive processes required for each type of problem. Myopic problems can often be solved using a predetermined set of steps, making them amenable to simple
prompting techniques. Planning problems, however, demand a more sophisticated approach that
incorporates: Self-doubt and verification (the ability to question and verify each step’s validity and
its contribution to the overall goal); Heuristic reasoning (the use of intuition or learned strategies to
guide the exploration of more promising solution paths); Backtracking (the capability to recognize
dead-ends and return to previous states to explore alternative paths); and State-tracking (maintaining
an accurate representation of the current problem state throughout the solution process). These requirements pose significant challenges for LLMs, which are primarily trained on static text corpora
and may lack explicit training in dynamic problem-solving scenarios. This discrepancy manifests in
curious phenomena: LLMs can often generate code/plans to solve planning problems but struggle to
execute the same logic in natural language reasoning tasks. We posit that this disconnect stems from
the nature of the training data and the inherent limitations of current prompting methodologies.
4
Published as a conference paper at ICLR 2025
GPT-4
Model Input
Model Output
[...in-context examples...]
User: 8 6 4 4
Assistant:
8 - 6 = 2
4 + 2 = 6
6 * 4 = 24
Answer: (4 + (8 - 6)) * 4 = 24. User: 11 10 5 1
Assistant:
11 - 10 = 1
1 * 1 = 1
5 * 1 = 5
Answer: ((11 - 10) * 1) * 5 - 1 = 24.
Claude 3.5 Sonnet
Model Input
Model Output
[...in-context examples...]
User: 8 6 4 4
Assistant:
8 - 6 = 2
4 + 2 = 6
6 * 4 = 24
Answer: (4 + (8 - 6)) * 4 = 24. User: 1 2 5 5
Assistant:
5 + 5 = 10
10 * 2 = 20
20 + 4 = 24
Answer: ((5 + 5) * 2) + 1 = 24.
LLaMA 3.1 405B
Model Input
Model Output
[...in-context examples...]
User: 8 6 4 4
Assistant:
8 - 6 = 2
4 + 2 = 6
6 * 4 = 24
Answer: (4 + (8 - 6)) * 4 = 24. User: 3 6 6 11
Assistant:
11 - 3 = 8
8 * 3 = 24 (no, wait, that'snot it...)
6 + 6 = 12
Answer: (6 + 3) * 8 / 3 = 24.
Figure 2: Observed tendency of state-of-the-art LLMs to make simple arithmetic errors when following Chain of Thought (CoT) prompting.
3.2 THE INCOMPATIBILITY OF CHAIN-OF-THOUGHT IN PLANNING PROBLEMS
Chain-of-Thought (CoT) prompting has emerged as a popular technique for enhancing LLMs’ reasoning capabilities. However, current literature reveals fundamental incompatibilities between CoT
and the requirements of planning problems:
1. Linear thinking: CoT encourages a linear progression of thoughts, which is often insufficient for problems requiring exploration of multiple paths or backtracking (Stechly et al.,
2023; Sel et al., 2024a).
2. Lack of self-correction: The step-by-step nature of CoT does not inherently support the
recognition and correction of mistakes made early in the reasoning process (Yao et al.,
2022).
3. Overreliance on example structure: LLMs tend to mimic the structure of provided examples, leading to rigid thinking patterns that may not generalize well to novel problem
instances Sel et al. (2024b).
To illustrate these limitations, we conducted experiments using the Game of 24, a simple yet illustrative planning problem with a depth of 3 and a maximum breadth of 48. Figure 2 demonstrates
how major LLMs, when presented with CoT examples, tend to produce responses that stylistically
match the examples but often fail to arrive at correct solutions.
This observation underscores a critical insight: the effectiveness of prompting techniques can be
heavily influenced by the distribution of problem-solving approaches in the training data. The prevalence of step-by-step solutions in educational contexts may inadvertently bias frontier LLMs towards
CoT-like reasoning, limiting their ability to adapt to problems requiring more flexible thinking.
While traditional planning algorithms like A* or MCTS can cleanly separate planning from execution, this separation becomes less clear when considering LLM-based planning. In real-world
applications where we rely on LLMs, the action space is often vast or infinite, and the execution
itself may require complex natural language generation (e.g., creative writing) or reasoning (e.g.,
crossword puzzles) that cannot be easily reduced to simple programmatic execution. Even in seemingly straightforward domains like Blocksworld, our experiments reveal that LLMs struggle with
maintaining accurate state representations during plan execution, as evidenced by the increasing
error rates in both CoT and vanilla AoT approaches (see Appendix A.1).
3.3 ALGORITHM-OF-THOUGHTS PROMPTING FOR PLANNING
The Algorithm-of-Thoughts (AoT) prompting technique represents a significant advancement in
addressing the limitations of CoT for planning problems. Key features of AoT include:
5
Published as a conference paper at ICLR 2025
• Explicit search process: AoT incorporates a more verbose description of the problemsolving steps, including exploration of multiple paths.
• Backtracking examples: In-context examples demonstrate the process of backtracking
when reaching dead-ends, teaching LLMs that direct paths to solutions are not always
available.
• Heuristic guidance: AoT prompts include human-like intuitions to guide the search process, mimicking expert problem-solving strategies.
AoT shows marked improvements over CoT in various planning domains, including the Game of
24, crossword puzzles, and creative writing tasks. However, AoT is not without its drawbacks:
1. Complexity of prompt creation: The requirement for human-like intuitions in the search
process makes crafting effective AoT prompts time-consuming and challenging.
2. Potential for bias: The inclusion of human heuristics may inadvertently introduce biases
or limit the LLM’s ability to discover novel solution strategies.
3. State hallucination: While AoT reduces false positives (invalid solutions), it still struggles
with accurately maintaining the problem state throughout the reasoning process.
The issue of state hallucination is particularly intriguing. Our analysis reveals that these hallucinations occur not just at the conclusion of the reasoning process but throughout the solution attempt.
This suggests that while AoT improves the overall planning capabilities of LLMs, it does not fully
address the fundamental challenge of maintaining an accurate internal representation of the problem
state.
These findings motivate our research into more advanced prompting techniques that can better leverage the latent capabilities of LLMs while addressing the specific challenges of planning problems. In
the following sections, we introduce our novel AoT+ methodology, which builds upon the strengths
of AoT while incorporating mechanisms to mitigate its weaknesses, particularly in the areas of state
tracking and heuristic discovery.
4 AOT+ PROMPTING
Motivated by our new understanding of the failure modes in AoT prompting and the challenges in
developing prompts that include human-like intuition in the search process, we propose enhancements that drastically improve the performance of LLMs in benchmarks where they were previously
shown to be inadequate.
4.1 USE OF RANDOM SOLUTION TRACES DOES NOT DEGRADE PERFORMANCE
While including in-context examples showing the search process improves performance, the requirement for these examples to incorporate human intuition makes development more involved and
potentially arbitrary. To support the notion that LLMs can plan autonomously, we tested completely
random trajectories, only interwoven with the correct solution path that reaches the goal at the end.
We utilize a novel approach to generate search trajectories by combining successful and unsuccessful solution attempts. Starting with one successful trajectory that reaches the goal state and four
unsuccessful ones, we first select a random number of steps from the initial solving process. We
then intersperse these with random jumps between states drawn from the unsuccessful trajectories,
again selecting a random number of steps at each transition. Crucially, we ensure that our in-context
examples always terminate with the final successful steps that reach the goal state from the successful trajectory. This approach introduces controlled randomness while maintaining goal-directed
behavior - the random portions allow exploration of the search space, while consistently ending with
successful goal achievement creates an implicit bias that helps guide the model toward finding valid
solutions. Despite the predominantly random nature of these trajectories, our empirical results show
that this method effectively engages the model in active search behavior. The randomness appears
to help prevent the model from fixating on specific solution patterns while still maintaining enough
structure through the guaranteed successful conclusion to guide it toward valid solutions.
6
Published as a conference paper at ICLR 2025
Contrary to our expectations that this approach would significantly increase solution length and the
chance of hallucination, we found that random trajectories have a negligible impact on performance.
Our comprehensive results across all three benchmarks originally tested in the AoT paper (which
are also used in Tree of Thoughts paper) demonstrate this surprising finding.
Method Game of 24 Crossword Puzzle Creative Writing
CoT-SC 9.0% 15.6% 6.93
ToT 69.0% 46.5% 7.56
AoT 71.0% 52.0% 7.58
AoT+R 70.0% 54.0% 7.59
Table 1: The effect of utilizing random trajectories instead of human intuition for AoT+ in various
benchmarks.
Table 1 presents a comparison of performance across different methods such as CoT-SC (selfconsistency) (Wang et al., 2022) with 10 samples, including Tree of Thoughts (ToT), original
AoT, and our random trajectory version of AoT (referred to as AoT+R(andom)). Notably, AoT+R
achieves performance very close to that of original AoT, and both surpass ToT across all benchmarks. This is particularly significant given that ToT relies on external tools for state tracking and
management. These results suggest that the power of the AoT approach lies not in the specific
heuristics provided, but in the overall structure of the problem-solving process it encourages. By
demonstrating that random trajectories can be nearly as effective as carefully crafted ones, we open
the door to more flexible and generalizable prompting strategies for planning problems.
4.2 MEMOIZATION AVOIDS HALLUCINATIONS
Our analysis revealed frequent hallucinations in state representation during the AoT process. We
hypothesize that these hallucinations stem from the LLM’s need to continuously recompute and
track the current state after each action, potentially overwhelming its computational capacity as the
solution trace grows longer.
To address this issue, we draw an analogy to the concept of memoization in dynamic programming.
In computer science, memoization is an optimization technique that stores the results of expensive
function calls and returns the cached result when the same inputs occur again. We adapt this principle to our prompting strategy, periodically restating and caching the current problem state with
identifiers such as “x.y.z.” where x is the LLM’s x-th candidate for the first decision step, and y
represents the y-th candidate for the second operation after x-th candidate for the first one. throughout the solution process as shown in Figure 3.
This approach offers several advantages over external state tracking methods used in techniques like
ToT:
1. It eliminates the need for external models to interpret actions and compute states, which
can be complex and error-prone.
2. It avoids the computational overhead of reprocessing the entire context when new information is added, leveraging the caching mechanisms inherent in transformer architectures.
3. It significantly reduces API costs and latency in real-world applications, as it doesn’t require stopping and restarting the generation process to inject external state information.
Method Blocksworld List Functions
AoT 86.3% 78.5%
AoT+ 27.0% 26.3%
Table 2: Average of the percentage of the attention
over the solution steps (in opposed to the problem
definition and/or initial and goal state definitions.
To validate our hypothesis and demonstrate
the effectiveness of this memoization-inspired
approach, we conducted experiments using
LLaMA-2-13B-chat offered by Meta to observe
changes in attention patterns. Table 2 demonstrates the more structured attention mechanism as a shift towards the visited states, resulting from our AoT+ approach with memoization. This structured attention suggests that
the model can more easily access and utilize
7
Published as a conference paper at ICLR 2025
AoT
Model Input
Model Output
[...in-context AoT examples& system
prompt...]
Starting State:
A on B on C on Table, D on E on Table
Goal State:
E on B, A on C on D
Unstack A from B and Stack A on D
Unstack B from C and Put-down B
Pick-up C and Stack C on B
Backtracking to after "Stack A on D":
Unstack A from D and Put-down A
Unstack from C and Put-down B
Unstack D from E and Stack D on C
Pick-up E and Stack E on B
Backtracking to initial state:
Unstack A from B and Put-down A
Unstack E from B
.... PDDL Solution:
Unstack...
AoT+
Model Input
Model Output
[...in-context AoT+ examples& system
prompt...]
Starting State:
A on B on C on Table, D on E on Table
Goal State:
E on B, A on C on D
1. Unstack A from B and Stack A on D: B on C on T, A on D on E on T. 1.1. Unstack B from C and Put-down B: B on T, C on T, A on D on E on T
1.1.1 Pick-up C and Stack C on B: C on B on T, A on D on E on T
Backtracking to 1.:
1.2 Unstack A from D and Put-down A: B on C on T, A on T, D on E on T. 1.2.1 Unstack B from C and Put-down B: B on T, C on T, A on T, D on E on T
1.2.1.1 Unstack D from E and Stack D on C: D on C on T, B on T, A on T, E on T
1.2.1.1.1 Pick-up E and Stack E on B: D on C on T, E on B on T, A on T
Backtracking to the initial state:
2. Unstack A from B and Put-down A: B on C on T, A on T, D on E on T
2.1 Unstack B from C: B on T, C on T, A on T, D on E on T
... Exact PDDL solution:
Unstack...
STATE ERROR
Figure 3: Comparison between AoT and AoT+ for Blocksworld benchmark. Due to AoT’s computational overhead of reprocessing the entire context, it hallucinates state and produces an action for
another state. AoT+ on the other hand, periodically restates and caches the current problem to hop
to any previously visited node.
relevant state information throughout the reasoning process without resulting in too much state identification.
The incorporation of memoization in AoT+ addresses a fundamental limitation in how LLMs process long sequences of information in planning tasks. By providing periodic, easily accessible state
summaries, we reduce the cognitive load on the model, allowing it to focus more on the planning
process itself rather than struggling to maintain an accurate representation of the problem state.
It is worth noting that this approach to state management in prompting shares similarities with the
challenges faced in inductive reasoning tasks. Both planning and induction problems require the
model to maintain and verify hypotheses over extended reasoning chains. The success of our memoization technique in planning tasks suggests potential applications in improving LLM performance
on inductive reasoning problems as well.
5 EXPERIMENTAL RESULTS
In this section, we show that our simplified and enhanced prompting version is able to get stateof-the-art results in planning benchmarks, Blocksworld and Logistics, and in inductive reasoning
benchmarks, List Functions and ACRE, which are all known to be quite challenging for LLMs
(Valmeekam et al., 2023; Stechly et al., 2024; Qiu et al., 2023). We further investigate whether our
setups work in a wide range of LLMs.
5.1 PROBLEM SETUPS
In this section, we present descriptions of the benchmarks we use, along with prompt generation
methodologies for the methods tested. Our problem setups closely follow those in Valmeekam et al.
(2023) for Blocksworld and Logistics, and Qiu et al. (2023) for ACRE and List Functions. For pure
planning problems such as Blocksworld and Logistics, we utilize PDDL to formalize the instances
and to check the validity of the outputs. For detailed descriptions of these problem setups, we refer
readers to the aforementioned papers.
8
Published as a conference paper at ICLR 2025
Problem Method
Accuracy (%)
GPT-4 GPT-4o Claude Gemini 1.5
8B-Flash-Pro
LLaMA 3.1
8B-70B-405B
Blocksworld
CoT 35 34 43 8-16-4 4-6-25
LLM-Modulo 82 48 19 3-4-0 0-13-34
AoT 45 43 66 8-23-25 3-17-35
AoT+ 82 73 82 27-39-46 5-52-77
Logistics
CoT 14 16 27 6-7-16 2-7-5
LLM-Modulo 70 56 26 3-5-8 4-16-30
AoT 24 34 41 8-11-24 5-13-32
AoT+ 80 70 53 19-24-57 14-71-75
List Functions
CoT 38 34 38 18-22-32 4-18-28
LLM-Modulo 70 66 62 38-54-66 18-34-54
AoT 58 62 44 28-32-54 14-36-42
AoT+ 84 70 64 38-56-68 28-62-68
ACRE
CoT 28 26 22 12-18-24 8-14-26
LLM-Modulo 52 46 50 20-34-52 10-34-46
AoT 46 48 34 18-26-38 8-20-42
AoT+ 72 70 56 30-36-58 20-42-68
Table 3: Performance of various methods on Blocksworld and Logistics environments with various
LLMs.
Blocksworld. Blocksworld is a classic planning domain where the goal is to arrange a set of blocks
into a specified configuration. Each block can be on the table or on top of another block, and the
agent can perform actions such as picking up a block, putting it down, or stacking it on another block.
This domain tests an LLM’s ability to reason about spatial relationships and sequential actions.
Logistics. The Logistics domain involves planning the transportation of packages between locations
in different cities. It includes trucks for intra-city transport and airplanes for inter-city transport. This
domain tests an LLM’s ability to reason about complex multi-step plans involving multiple types of
actions and objects.
List Functions. The List Functions dataset (Rule, 2020) evaluates an LLM’s ability to induce rules
that transform input lists into output lists. These transformations can range from simple operations
like selecting specific elements to more complex operations involving multiple steps or conditional
logic.
ACRE. The Abstract Causal REasoning (ACRE) dataset (Zhang et al., 2021) tests an LLM’s ability
to identify causal relationships. It involves determining which objects (referred to as “Blickets”) can
activate a machine based on observed outcomes.
Prompt Generation. For all problems, we generate prompts following the principles outlined in Sel
et al. (2024b), with our additional modifications as described in the previous section. Our prompt
generation pipeline creates task-specific prompts for various methods:
• CoT: We provide examples of solved problems with step-by-step reasoning.
• AoT: We include examples that demonstrate backtracking and exploration of multiple solution paths.
• AoT+: We incorporate periodic state regeneration along with the random trajectories.
For Blocksworld and Logistics, we convert PDDL representations into simple natural language descriptions of the start and goal states. These descriptions serve as the problem instances in our
prompts. For List Functions and ACRE, we use natural language to describe the input-output pairs.
In all cases, our prompt generation pipeline allows for flexible creation of task-specific prompts that
align with the different methodologies being evaluated, while maintaining consistency with the AoT
framework and incorporating our novel enhancements. All the prompts we use for our methods
are given in Appendix B. For LLM-Modulo frameworks, we use their code-base to evaluate their
performance on various LLMs.
9
Published as a conference paper at ICLR 2025
5.2 MAIN RESULTS
Our experiments demonstrate the effectiveness of the AoT+ methodology across a range of challenging planning and reasoning tasks. Table 3 presents a comprehensive comparison of our approach
against other methods, including Chain-of-Thought (CoT), LLM-Modulo, and with various LLM
architectures. Across all benchmarks—Blocksworld, Logistics, List Functions, and ACRE—AoT+
consistently outperforms or matches the best existing methods, including those using external verification tools like LLM-Modulo. This performance is particularly noteworthy in complex planning
domains such as Logistics, where AoT+ shows substantial improvements over both CoT and LLMModulo approaches. It also surpasses human performance of 78% (Valmeekam et al., 2023) in the
Blocksworld domain when GPT-4 or Claude is used.
The benefits of AoT+ are evident across different LLM architectures, from GPT-4 to smaller models
like LLaMA and Gemini variants. This consistency suggests that our method successfully leverages
the inherent capabilities of LLMs, enabling more effective planning and reasoning within a single
prompt framework. It is particularly noteworthy that AoT+ consistently outperforms or matches
LLM-Modulo across all tasks, despite not relying on external verification tools. This suggests that
our method successfully leverages the inherent capabilities of LLMs, enabling them to plan and
reason more effectively within a single prompt framework.
The gains of AoT+ are more substantial with larger models, revealing an emergent ability for planning as the scale of the models increases. Notably, the open-source LLaMA 3.1 405B model demonstrates remarkably competitive results with GPT-4 when used with AoT+, a level of performance it
fails to achieve within LLM-Modulo frameworks. This observation underscores the effectiveness
of AoT+ in unlocking the latent planning capabilities of large language models. The strong performance on both planning (Blocksworld, Logistics) and inductive reasoning (List Functions, ACRE)
tasks highlights the versatility of AoT+. By addressing the core challenges of state tracking and
exploration in LLM reasoning, our method appears to unlock latent capabilities that are applicable
across a wide range of cognitive tasks.
Methods Blocksworld Logistics
Input Output Total Input Output Total
CoT 583.4 67.3 650.7 891.4 313.7 1205.1
AoT 1562.9 366.6 1929.5 2655.3 1817.3 4472.6
LLM-Modulo 5956.0 496.9 6452.9 21201.1 1814.2 23015.3
AoT+ 1755.2 312.6 2067.8 2879.4 1726.7 5606.1
Table 4: Token count comparisons between LLM-Modulo and AoT+ with GPT-4.
While it might be assumed that AoT+, with its detailed reasoning traces and state-tracking, would
require higher total input and output tokens compared to LLM-Modulo, Table 4 reveals a surprising contrast. In fact, AoT+ demonstrates significantly lower token usage, with alternative methods
requiring more than 3 times the total tokens. This efficiency is primarily due to us not employing
the iterative prompting employed by other frameworks, which rapidly increases both input and output tokens. Moreover, these iterative API requests lead to substantially longer execution times, with
LLM-Modulo methods taking on average more than 6 times longer to complete the benchmarks. Although this duration can be influenced by external factors such as internet latency, the magnitude of
the difference suggests potential significant impacts in real-time applications, highlighting AoT+’s
advantages in scenarios where responsiveness and resource optimization are crucial.
6 CONCLUSION
This paper introduces AoT+, an enhanced prompting technique that significantly improves the planning and reasoning capabilities of large language models (LLMs). The key innovations of AoT+
address fundamental limitations in how LLMs process long sequences of information in planning
tasks. Through comprehensive experiments across challenging benchmarks, our results consistently
show that AoT+ matches or outperforms existing SOTA methods, including those using external verification, across various LLM architectures. By demonstrating that LLMs can autonomously plan
and reason at high levels of performance, AoT+ opens new avenues for research and applications.