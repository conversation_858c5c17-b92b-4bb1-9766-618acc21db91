import os
import json
import requests
from termcolor import cprint
import asyncio
from typing import List, Dict
from datetime import datetime

# Constants
MODEL_NAME = "deepseek-r1:7b"
OLLAMA_URL = "http://192.168.0.18:8234"
MAX_ITERATIONS = 5  # Default maximum number of thinking iterations
CHAT_HISTORY_DIR = "chat_history"

# Create chat history directory if it doesn't exist
os.makedirs(CHAT_HISTORY_DIR, exist_ok=True)

def save_chat_history(history_id: str, messages: List[Dict]):
    """Save chat history to a JSON file"""
    try:
        history_file = os.path.join(CHAT_HISTORY_DIR, f"{history_id}.json")
        with open(history_file, "w", encoding="utf-8") as f:
            json.dump({"messages": messages}, f, indent=2, ensure_ascii=False)
    except Exception as e:
        cprint(f"\n⚠️ Error saving chat history: {str(e)}", "red")

def load_chat_history(history_id: str) -> List[Dict]:
    """Load chat history from a JSON file"""
    try:
        history_file = os.path.join(CHAT_HISTORY_DIR, f"{history_id}.json")
        if os.path.exists(history_file):
            with open(history_file, "r", encoding="utf-8") as f:
                return json.load(f).get("messages", [])
    except Exception as e:
        cprint(f"\n⚠️ Error loading chat history: {str(e)}", "red")
    return []

async def call_ollama_with_iteration_awareness(
    initial_prompt: str,
    history_id: str,
    iterations: int = MAX_ITERATIONS,
    continuation_prompt: str = "Continue your thinking from where you left off."
) -> Dict[str, any]:
    """
    Calls the Ollama API iteratively, with awareness of which iteration it's on.
    On the final iteration, explicitly tells the model to conclude its thinking and provide a final answer.
    """
    try:
        thinking_history = []
        current_prompt = initial_prompt
        final_answer = ""
        
        # Load existing chat history
        messages = load_chat_history(history_id)
        
        for iteration in range(iterations):
            is_final_iteration = iteration == iterations - 1
            cprint(f"\n[Iteration {iteration + 1}/{iterations}] 🔄 Calling Ollama API...", "cyan")
            
            # Print iteration header
            print("\n" + "-"*50)
            cprint(f"🤔 Reasoning Process - Iteration {iteration + 1}:", "yellow", attrs=['bold'])
            print("-"*50 + "\n")
            
            # Initialize response streams
            reasoning_stream = ""
            in_thinking_block = False
            
            # Construct messages array with history and iteration awareness
            current_messages = messages.copy()
            if iteration > 0:
                history_context = "\n\nYour Previous thinking:\n" + "\n".join(
                    f"Iteration {i+1}: {step['reasoning']}" 
                    for i, step in enumerate(thinking_history)
                )
                
                if is_final_iteration:
                    iteration_prompt = (
                        f"{history_context}\n\n"
                        "This is the final iteration. After your thinking process, "
                        "please provide a clear and definitive final answer."
                    )
                else:
                    iteration_prompt = f"{history_context}\n\n{continuation_prompt}"
                
                current_messages.append({"role": "user", "content": iteration_prompt})
            else:
                current_messages.append({"role": "user", "content": current_prompt})
            
            try:
                # Make the API call to Ollama
                response = requests.post(
                    f"{OLLAMA_URL}/api/chat",
                    json={
                        "model": MODEL_NAME,
                        "messages": current_messages,
                        "stream": True
                    },
                    stream=True
                )
                response.raise_for_status()
                
                # Process the streaming response
                for line in response.iter_lines():
                    if line:
                        try:
                            chunk = json.loads(line)
                            if chunk.get("message", {}).get("content"):
                                chunk_content = chunk["message"]["content"]
                                
                                # Check for thinking block markers
                                if "<think>" in chunk_content:
                                    in_thinking_block = True
                                    chunk_content = chunk_content.replace("<think>", "")
                                elif "</think>" in chunk_content:
                                    in_thinking_block = False
                                    chunk_content = chunk_content.replace("</think>", "")
                                    reasoning_stream += chunk_content.strip()
                                    cprint(chunk_content.strip(), 'yellow', flush=True)
                                    if not is_final_iteration:
                                        break
                                
                                # Stream the content
                                if in_thinking_block:
                                    cprint(chunk_content, 'yellow', end="", flush=True)
                                    reasoning_stream += chunk_content
                                elif not in_thinking_block and is_final_iteration:
                                    cprint(chunk_content, 'cyan', end="", flush=True)
                                    final_answer += chunk_content
                                    
                        except json.JSONDecodeError:
                            continue
                            
            except requests.exceptions.RequestException as e:
                cprint(f"\n⚠️ Error calling Ollama API: {str(e)}", "red")
                continue
            
            # Store this iteration's thinking
            if reasoning_stream.strip():
                thinking_history.append({
                    "iteration": iteration + 1,
                    "reasoning": reasoning_stream.strip()
                })
            
            # Print iteration footer
            print("\n" + "-"*50)
            cprint(f"✅ Iteration {iteration + 1} Complete!", "green", attrs=['bold'])
            print("-"*50 + "\n")
            
            await asyncio.sleep(1)
        
        # Save the final answer to chat history
        if final_answer.strip():
            messages.append({"role": "assistant", "content": final_answer.strip()})
            save_chat_history(history_id, messages)
        
        # Print final summary
        cprint("\n📚 Final Thinking History:", "blue", attrs=['bold'])
        for step in thinking_history:
            print("\n" + "-"*30)
            cprint(f"Iteration {step['iteration']}:", "cyan")
            cprint(step['reasoning'], "yellow")
        
        if final_answer.strip():
            print("\n" + "-"*50)
            cprint("🎯 Final Answer:", "green", attrs=['bold'])
            cprint(final_answer.strip(), "cyan")
            print("-"*50)
        
        return {
            "thinking_history": thinking_history,
            "final_answer": final_answer.strip()
        }

    except Exception as e:
        cprint(f"\n⚠️ Error in iterative thinking process: {str(e)}", "red")
        return {"thinking_history": thinking_history if thinking_history else None, "final_answer": ""}

async def chat_console():
    """Interactive chat console with history support"""
    try:
        cprint("\n🤖 Welcome to DeepSeek Chat Console!", "green", attrs=['bold'])
        cprint("Type 'exit' to quit, 'new' to start a new chat, or 'history' to view chat history\n", "yellow")
        
        current_history_id = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        while True:
            cprint("\n💭 You:", "blue", attrs=['bold'])
            user_input = input().strip()
            
            if user_input.lower() == 'exit':
                break
            elif user_input.lower() == 'new':
                current_history_id = datetime.now().strftime("%Y%m%d_%H%M%S")
                cprint("\n🆕 Started new chat session!", "green")
                continue
            elif user_input.lower() == 'history':
                # Show available chat histories
                histories = [f for f in os.listdir(CHAT_HISTORY_DIR) if f.endswith('.json')]
                if histories:
                    cprint("\n📜 Chat History:", "magenta", attrs=['bold'])
                    for hist_file in histories:
                        hist_id = hist_file.replace('.json', '')
                        messages = load_chat_history(hist_id)
                        if messages:
                            cprint(f"\nChat {hist_id}:", "cyan")
                            for msg in messages:
                                role_color = "blue" if msg["role"] == "user" else "green"
                                cprint(f"{msg['role'].title()}: {msg['content'][:100]}...", role_color)
                else:
                    cprint("\nNo chat history found.", "yellow")
                continue
            
            if not user_input:
                continue
                
            # Save user message to history
            messages = load_chat_history(current_history_id)
            messages.append({"role": "user", "content": user_input})
            save_chat_history(current_history_id, messages)
            
            # Process the message
            response = await call_ollama_with_iteration_awareness(
                initial_prompt=user_input,
                history_id=current_history_id,
                iterations=3
            )
            
    except KeyboardInterrupt:
        cprint("\n\n👋 Goodbye!", "green", attrs=['bold'])
    except Exception as e:
        cprint(f"\n⚠️ Error in chat console: {str(e)}", "red")

if __name__ == "__main__":
    asyncio.run(chat_console()) 