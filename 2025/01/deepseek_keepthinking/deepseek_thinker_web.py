import os
from termcolor import cprint
import asyncio
import aiohttp
import json
from typing import List, Dict
from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
import uvicorn

# Constants
MODEL_NAME = "deepseek-r1:7b"
OLLAMA_URL = "http://192.168.0.18:8234"
MAX_ITERATIONS = 5  # Default maximum number of thinking iterations

app = FastAPI()

# Mount static files
app.mount("/static", StaticFiles(directory="2025/01/deepseek_keepthinking/static"), name="static")

# HTML template
HTML_TEMPLATE = """
<!DOCTYPE html>
<html>
<head>
    <title>DeepSeek Reasoning Chat</title>
    <link rel="icon" type="image/x-icon" href="/static/img/favicon.ico">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/katex.min.css" integrity="sha384-n8MVd4RsNIU0tAv4ct0nTaAbDJwPJzDEaqSD1odI+WdtXRGWt2kTvGFasHpSy3SV" crossorigin="anonymous">
    <link rel="stylesheet" href="/static/css/style.css">
    <!-- Use specific version of marked -->
    <script src="https://cdn.jsdelivr.net/npm/marked@4.3.0/marked.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/katex.min.js" integrity="sha384-XjKyOOlGwcjNTAIQHIpgOno0Hl1YQqzUOEleOLALmuqehneUG+vnGctmUb0ZY0l8" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/contrib/auto-render.min.js" integrity="sha384-+VBxd3r6XgURycqtZ117nYw44OOcIax56Z4dCRWbxyPt0Koah1uHoK0o4+/RRE05" crossorigin="anonymous"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/daisyui@4.6.0/dist/full.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/animejs@3.2.1/lib/anime.min.js"></script>
</head>
<body class="bg-gray-900 text-white min-h-screen">
    <div class="container mx-auto p-4 max-w-4xl">
        <h1 class="text-3xl font-bold mb-8 text-center text-purple-400">DeepSeek Reasoning Chat</h1>
        <div id="chat" class="bg-gray-800 rounded-lg p-4 h-[600px] overflow-y-auto mb-4"></div>
        <div class="flex gap-2">
            <input type="text" id="messageInput" class="flex-1 bg-gray-700 text-white rounded px-4 py-2" placeholder="Ask your question...">
            <button id="sendButton" class="bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded">Send</button>
        </div>
    </div>
    <script src="/static/js/app.js"></script>
</body>
</html>
"""

@app.get("/")
async def get():
    return HTMLResponse(HTML_TEMPLATE)

class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        self.heartbeat_interval = 30  # seconds

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)
        asyncio.create_task(self.heartbeat(websocket))

    def disconnect(self, websocket: WebSocket):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)

    async def heartbeat(self, websocket: WebSocket):
        try:
            while True:
                await asyncio.sleep(self.heartbeat_interval)
                try:
                    await websocket.send_json({"type": "ping"})
                except Exception:
                    self.disconnect(websocket)
                    break
        except asyncio.CancelledError:
            pass

manager = ConnectionManager()

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    await manager.connect(websocket)
    try:
        while True:
            try:
                data = await websocket.receive_json()
                print(f"Received data: {data}")  # Debug log
                
                if data["type"] == "pong":
                    continue  # Ignore pong responses
                
                if data["type"] == "message":
                    print(f"Processing message: {data['content']}")  # Debug log
                    
                    try:
                        # Process the message with our iterative thinking
                        response = await call_ollama_with_iteration_awareness(
                            initial_prompt=data["content"],
                            iterations=3,
                            websocket=websocket
                        )
                        
                        if response["final_answer"]:
                            await websocket.send_json({
                                "type": "complete",
                                "final_answer": response["final_answer"]
                            })
                    except Exception as e:
                        error_msg = f"Error processing message: {str(e)}"
                        print(error_msg)
                        await websocket.send_json({
                            "type": "error",
                            "content": error_msg
                        })
                        
            except json.JSONDecodeError as e:
                print(f"JSON decode error: {e}")
                await websocket.send_json({
                    "type": "error",
                    "content": "Invalid message format"
                })
            except WebSocketDisconnect:
                manager.disconnect(websocket)
                print("Client disconnected")
                break
            except Exception as e:
                print(f"Error processing message: {e}")
                try:
                    await websocket.send_json({
                        "type": "error",
                        "content": str(e)
                    })
                except:
                    break
                
    except WebSocketDisconnect:
        manager.disconnect(websocket)
        print("Client disconnected")
    except Exception as e:
        print(f"WebSocket error: {e}")
        try:
            await websocket.send_json({
                "type": "error",
                "content": "Server error occurred"
            })
        except:
            pass
    finally:
        manager.disconnect(websocket)

async def call_ollama_with_iteration_awareness(
    initial_prompt: str,
    iterations: int = MAX_ITERATIONS,
    continuation_prompt: str = "Continue your thinking from where you left off.",
    websocket: WebSocket = None,
    thinking_history: List[Dict[str, str]] = None
) -> Dict[str, any]:
    """
    Calls the Ollama API iteratively, with awareness of which iteration it's on.
    On the final iteration, explicitly tells the model to conclude its thinking and provide a final answer.
    """
    try:
        thinking_history = thinking_history or []
        final_answer = ""
        
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=300)) as session:
            for iteration in range(iterations):
                is_final_iteration = iteration == iterations - 1
                if websocket:
                    await websocket.send_json({
                        "type": "iteration_start",
                        "iteration": iteration + 1,
                        "total": iterations
                    })
                
                # Initialize response streams
                reasoning_stream = ""
                in_thinking_block = False
                current_stream = ""
                
                # Construct the full prompt with history and iteration awareness
                if thinking_history:
                    history_context = "\n\nYour Previous thinking:\n" + "\n".join(
                        f"Iteration {step['iteration']}: {step['reasoning']}" 
                        for step in thinking_history
                    )
                    
                    if is_final_iteration:
                        iteration_prompt = (
                            f"{history_context}\n\n"
                            "This is the final iteration. After your thinking process, "
                            "please provide a clear and definitive final answer."
                        )
                    else:
                        iteration_prompt = f"{history_context}\n\n{continuation_prompt}"
                    
                    full_prompt = iteration_prompt
                else:
                    full_prompt = initial_prompt
                
                try:
                    # Prepare messages for Ollama API
                    messages = [{"role": "user", "content": full_prompt}]
                    
                    # Make the API call
                    async with session.post(
                        f"{OLLAMA_URL}/api/chat",
                        json={
                            "model": MODEL_NAME,
                            "messages": messages,
                            "stream": True
                        },
                        timeout=aiohttp.ClientTimeout(total=60)  # 60 seconds timeout per request
                    ) as response:
                        if response.status != 200:
                            error_msg = f"Ollama API error: {response.status} - {await response.text()}"
                            print(error_msg)
                            if websocket:
                                await websocket.send_json({
                                    "type": "error",
                                    "content": error_msg
                                })
                            continue

                        # Process the streaming response
                        async for line in response.content:
                            if line:
                                try:
                                    chunk = json.loads(line)
                                    if not chunk or not chunk.get("message", {}).get("content"):
                                        continue
                                        
                                    chunk_content = chunk["message"]["content"]
                                    
                                    # Check for thinking block markers
                                    if "<think>" in chunk_content:
                                        in_thinking_block = True
                                        chunk_content = chunk_content.replace("<think>", "")
                                        current_stream = ""
                                    elif "</think>" in chunk_content:
                                        in_thinking_block = False
                                        chunk_content = chunk_content.replace("</think>", "")
                                        reasoning_stream += chunk_content.strip()
                                        current_stream += chunk_content.strip()
                                        
                                        if websocket:
                                            await websocket.send_json({
                                                "type": "iteration_content",
                                                "iteration": iteration + 1,
                                                "content": current_stream
                                            })
                                        
                                        if not is_final_iteration:
                                            break
                                    
                                    # Stream the content
                                    if in_thinking_block:
                                        reasoning_stream += chunk_content
                                        current_stream += chunk_content
                                        if websocket:
                                            await websocket.send_json({
                                                "type": "iteration_content",
                                                "iteration": iteration + 1,
                                                "content": current_stream
                                            })
                                    elif not in_thinking_block and is_final_iteration:
                                        final_answer += chunk_content
                                        if websocket:
                                            await websocket.send_json({
                                                "type": "final_stream",
                                                "content": chunk_content
                                            })
                                except json.JSONDecodeError:
                                    print(f"Failed to decode JSON: {line}")
                                    continue
                                except Exception as e:
                                    print(f"Error processing chunk: {str(e)}")
                                    continue

                except aiohttp.ClientError as e:
                    error_msg = f"Network error during iteration {iteration + 1}: {str(e)}"
                    print(error_msg)
                    if websocket:
                        await websocket.send_json({
                            "type": "error",
                            "content": error_msg
                        })
                    continue
                except Exception as e:
                    error_msg = f"Error during iteration {iteration + 1}: {str(e)}"
                    print(error_msg)
                    if websocket:
                        await websocket.send_json({
                            "type": "error",
                            "content": error_msg
                        })
                    continue
                
                # Store this iteration's thinking
                if reasoning_stream.strip():
                    thinking_history.append({
                        "iteration": iteration + 1,
                        "reasoning": reasoning_stream.strip()
                    })
                
                    if websocket:
                        await websocket.send_json({
                            "type": "iteration_complete",
                            "iteration": iteration + 1
                        })
                
                await asyncio.sleep(1)
        
        if not final_answer.strip() and thinking_history:
            # If we have thinking history but no final answer, use the last thinking as the answer
            final_answer = thinking_history[-1]["reasoning"]
        
        return {
            "thinking_history": thinking_history,
            "final_answer": final_answer.strip()
        }

    except Exception as e:
        error_msg = f"Error in iterative thinking process: {str(e)}"
        print(error_msg)
        if websocket:
            await websocket.send_json({
                "type": "error",
                "content": error_msg
            })
        return {"thinking_history": thinking_history if thinking_history else None, "final_answer": ""}

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8510) 