/* Custom scrollbar styles */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #2d3748;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #4a5568;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #718096;
}

/* Message styles */
.message {
    padding: 1rem;
    margin-bottom: 1rem;
    border-radius: 0.5rem;
    animation: fadeIn 0.3s ease-in;
}

.user-message {
    background-color: #553c9a;
    margin-left: 2rem;
}

.bot-message {
    background-color: #2d3748;
    margin-right: 2rem;
}

.thinking {
    background-color: #4a5568;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    margin: 0.5rem 0;
    font-style: italic;
}

/* Code block styles */
pre {
    background-color: #1a202c;
    padding: 1rem;
    border-radius: 0.5rem;
    overflow-x: auto;
    margin: 1rem 0;
}

code {
    font-family: 'Fira Code', monospace;
    color: #e2e8f0;
}

/* Math rendering styles */
.katex-display {
    margin: 1rem 0;
    overflow-x: auto;
    overflow-y: hidden;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading animation */
.loading {
    display: inline-block;
    position: relative;
    width: 80px;
    height: 13px;
}

.loading div {
    position: absolute;
    width: 13px;
    height: 13px;
    border-radius: 50%;
    background: #a78bfa;
    animation-timing-function: cubic-bezier(0, 1, 1, 0);
}

.loading div:nth-child(1) {
    left: 8px;
    animation: loading1 0.6s infinite;
}

.loading div:nth-child(2) {
    left: 8px;
    animation: loading2 0.6s infinite;
}

.loading div:nth-child(3) {
    left: 32px;
    animation: loading2 0.6s infinite;
}

.loading div:nth-child(4) {
    left: 56px;
    animation: loading3 0.6s infinite;
}

@keyframes loading1 {
    0% {
        transform: scale(0);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes loading2 {
    0% {
        transform: translate(0, 0);
    }
    100% {
        transform: translate(24px, 0);
    }
}

@keyframes loading3 {
    0% {
        transform: scale(1);
    }
    100% {
        transform: scale(0);
    }
}

/* Markdown Styles */
.markdown-content {
    color: inherit;
    line-height: 1.6;
}
.markdown-content h1 { font-size: 1.5em; font-weight: bold; margin: 0.5em 0; }
.markdown-content h2 { font-size: 1.3em; font-weight: bold; margin: 0.5em 0; }
.markdown-content h3 { font-size: 1.1em; font-weight: bold; margin: 0.5em 0; }
.markdown-content p { margin: 0.5em 0; }
.markdown-content code {
    background: rgba(0,0,0,0.2);
    padding: 0.2em 0.4em;
    border-radius: 3px;
    font-family: monospace;
}
.markdown-content pre {
    background: rgba(0,0,0,0.2);
    padding: 1em;
    border-radius: 5px;
    overflow-x: auto;
    margin: 0.5em 0;
}
.markdown-content pre code {
    background: transparent;
    padding: 0;
}
.markdown-content ul, .markdown-content ol {
    margin: 0.5em 0;
    padding-left: 1.5em;
}
.markdown-content ul { list-style-type: disc; }
.markdown-content ol { list-style-type: decimal; }
.markdown-content blockquote {
    border-left: 3px solid #a855f7;
    padding-left: 1em;
    margin: 0.5em 0;
    color: #d1d5db;
}
.markdown-content table {
    border-collapse: collapse;
    margin: 0.5em 0;
    width: 100%;
}
.markdown-content th, .markdown-content td {
    border: 1px solid #4b5563;
    padding: 0.4em 0.8em;
}
.markdown-content th {
    background: rgba(0,0,0,0.2);
}
.katex { color: inherit; } 