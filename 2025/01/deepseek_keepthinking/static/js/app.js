// Wait for all dependencies to load
document.addEventListener('DOMContentLoaded', () => {
    // Configure marked with syntax highlighting and LaTeX support
    marked.setOptions({
        highlight: function(code, lang) {
            return code;
        },
        breaks: true,
        mangle: false,
        headerIds: false
    });

    // Custom renderer to handle LaTeX
    const renderer = {
        paragraph(text) {
            text = renderLaTeX(text);
            return `<p>${text}</p>`;
        },
        text(text) {
            return renderLaTeX(text);
        }
    };

    marked.use({ renderer });

    function renderLaTeX(text) {
        // Store code blocks temporarily
        const codeBlocks = [];
        text = text.replace(/```[\s\S]*?```/g, (match) => {
            codeBlocks.push(match);
            return `CODE_BLOCK_${codeBlocks.length - 1}`;
        });

        // Handle display math first (to avoid conflict with inline math)
        const displayMathRegex = /\$\$([\s\S]+?)\$\$|\\\[([\s\S]+?)\\\]/g;
        text = text.replace(displayMathRegex, (match, latex1, latex2) => {
            try {
                const latex = latex1 || latex2;
                return katex.renderToString(latex.trim(), { 
                    displayMode: true,
                    throwOnError: false,
                    strict: false
                });
            } catch (e) {
                console.error('LaTeX display math error:', e);
                return match;
            }
        });

        // Handle inline math
        const inlineMathRegex = /\$([^\$\n]+?)\$|\\\(([^\)]+?)\\\)/g;
        text = text.replace(inlineMathRegex, (match, latex1, latex2) => {
            try {
                const latex = latex1 || latex2;
                return katex.renderToString(latex.trim(), { 
                    displayMode: false,
                    throwOnError: false,
                    strict: false
                });
            } catch (e) {
                console.error('LaTeX inline math error:', e);
                return match;
            }
        });

        // Handle raw LaTeX commands
        const latexEnvRegex = /\\begin\{([^}]+)\}([\s\S]*?)\\end\{\1\}/g;
        text = text.replace(latexEnvRegex, (match) => {
            try {
                return katex.renderToString(match, { 
                    displayMode: true,
                    throwOnError: false,
                    strict: false
                });
            } catch (e) {
                console.error('Raw LaTeX error:', e);
                return match;
            }
        });

        // Restore code blocks
        text = text.replace(/CODE_BLOCK_(\d+)/g, (match, index) => codeBlocks[parseInt(index)]);

        return text;
    }

    function renderContent(content, isMarkdown = true) {
        if (!isMarkdown) return content;
        
        try {
            // Pre-process LaTeX before markdown
            content = renderLaTeX(content);
            
            // Render markdown
            const rendered = marked.parse(content);
            
            return rendered;
        } catch (e) {
            console.error('Content rendering error:', e);
            return content;
        }
    }

    // Initialize KaTeX auto-render
    renderMathInElement(document.body, {
        delimiters: [
            {left: "$$", right: "$$", display: true},
            {left: "$", right: "$", display: false},
            {left: "\\(", right: "\\)", display: false},
            {left: "\\[", right: "\\]", display: true}
        ],
        throwOnError: false,
        strict: false,
        trust: true,
        macros: {
            "\\frac": "\\displaystyle\\frac",
            "\\sum": "\\displaystyle\\sum",
            "\\int": "\\displaystyle\\int",
            "\\lim": "\\displaystyle\\lim"
        },
        fleqn: false,
        leqno: false,
        maxSize: 500,
        maxExpand: 1000,
        minRuleThickness: 0.05
    });

    let ws = null;
    let reconnectAttempts = 0;
    let maxReconnectAttempts = 5;
    let reconnectTimeout = null;
    let isReconnecting = false;
    let lastMessageTime = Date.now();
    let heartbeatInterval = null;
    let chat = document.getElementById("chat");
    let messageInput = document.getElementById("messageInput");
    let sendButton = document.getElementById("sendButton");
    let currentIterationDiv = null;
    let currentStreamDiv = null;
    let thinkingHistory = [];  // Store thinking history across iterations

    function updateStreamContent(div, content) {
        if (div && div.firstChild) {
            div.firstChild.innerHTML = renderContent(content);
            chat.scrollTop = chat.scrollHeight;
        }
    }

    function addMessage(content, type = 'user') {
        const msgDiv = document.createElement('div');
        msgDiv.className = `mb-4 ${type === 'user' ? 'text-right' : ''}`;
        
        const innerDiv = document.createElement('div');
        innerDiv.className = `inline-block rounded-lg p-3 ${
            type === 'user' ? 'bg-purple-600' : 
            type === 'iteration' ? 'bg-blue-800' : 
            type === 'final' ? 'bg-green-700' : 'bg-gray-700'
        } markdown-content`;
        
        innerDiv.innerHTML = type === 'user' ? content : renderContent(content);
        msgDiv.appendChild(innerDiv);
        chat.appendChild(msgDiv);
        chat.scrollTop = chat.scrollHeight;

        // Store thinking content in history if it's an iteration
        if (type === 'iteration' && content.includes('Iteration')) {
            const iterationMatch = content.match(/Iteration (\d+)/);
            if (iterationMatch) {
                const iterationNum = parseInt(iterationMatch[1]);
                const reasoningContent = content.replace(/^Iteration \d+: /, '');
                thinkingHistory[iterationNum - 1] = {
                    iteration: iterationNum,
                    reasoning: reasoningContent
                };
            }
        }

        anime({
            targets: msgDiv,
            translateY: [20, 0],
            opacity: [0, 1],
            duration: 500,
            easing: 'easeOutElastic'
        });

        return msgDiv;
    }

    function clearChat() {
        const systemMessages = Array.from(chat.children).filter(
            msg => msg.querySelector('div').classList.contains('bg-gray-700')
        );
        chat.innerHTML = '';
        systemMessages.forEach(msg => chat.appendChild(msg));
        currentIterationDiv = null;
        currentStreamDiv = null;
        // Don't clear thinkingHistory here
    }

    function sendMessage() {
        if (!ws || ws.readyState !== WebSocket.OPEN) {
            console.error("WebSocket is not connected");
            addMessage("Error: Not connected to server. Please try again.", 'error');
            return;
        }

        const message = messageInput.value.trim();
        if (message) {
            try {
                messageInput.disabled = true;
                sendButton.disabled = true;
                clearChat();
                thinkingHistory = [];  // Reset thinking history for new conversation
                addMessage(message, 'user');
                ws.send(JSON.stringify({
                    type: "message",
                    content: message,
                    thinking_history: thinkingHistory
                }));
                messageInput.value = '';
            } catch (error) {
                console.error("Error sending message:", error);
                addMessage("Error sending message. Please try again.", 'error');
                messageInput.disabled = false;
                sendButton.disabled = false;
            }
        }
    }

    // Initialize WebSocket connection
    function connectWebSocket() {
        if (ws && ws.readyState === WebSocket.OPEN) {
            console.log("WebSocket already connected");
            return;
        }

        if (isReconnecting) {
            console.log("Already attempting to reconnect");
            return;
        }

        isReconnecting = true;
        clearTimeout(reconnectTimeout);
        
        if (ws) {
            ws.close();
        }

        try {
            ws = new WebSocket("ws://" + window.location.host + "/ws");
            
            ws.onopen = () => {
                console.log("WebSocket connected");
                messageInput.disabled = false;
                sendButton.disabled = false;
                reconnectAttempts = 0;
                isReconnecting = false;
                lastMessageTime = Date.now();
                
                // Start heartbeat
                if (heartbeatInterval) {
                    clearInterval(heartbeatInterval);
                }
                heartbeatInterval = setInterval(() => {
                    if (ws && ws.readyState === WebSocket.OPEN) {
                        try {
                            ws.send(JSON.stringify({ type: "ping" }));
                            lastMessageTime = Date.now();
                        } catch (e) {
                            console.error("Error sending heartbeat:", e);
                            handleConnectionError();
                        }
                    }
                }, 30000); // 30 seconds heartbeat
                
                addMessage("Connected to server", 'system');
            };

            ws.onclose = (event) => {
                console.log("WebSocket disconnected", event);
                handleConnectionError();
            };

            ws.onerror = (error) => {
                console.error("WebSocket error:", error);
                handleConnectionError();
            };

            ws.onmessage = function(event) {
                try {
                    lastMessageTime = Date.now();
                    const data = JSON.parse(event.data);
                    console.log("Received message:", data);
                    
                    if (!data || !data.type) {
                        throw new Error("Invalid message format");
                    }

                    switch (data.type) {
                        case "ping":
                            ws.send(JSON.stringify({ type: "pong" }));
                            break;
                            
                        case "pong":
                            // Heartbeat received
                            break;
                            
                        case 'iteration_start':
                            console.log("Starting iteration:", data.iteration);
                            // Include previous thinking history in the message
                            let historyPrefix = '';
                            if (thinkingHistory.length > 0) {
                                historyPrefix = "Previous thinking:\n" + thinkingHistory
                                    .filter(h => h && h.iteration && h.reasoning)
                                    .map(h => `Iteration ${h.iteration}: ${h.reasoning}`)
                                    .join("\n") + "\n\nContinuing with ";
                            }
                            currentIterationDiv = addMessage(`${historyPrefix}Iteration ${data.iteration}/${data.total}: `, 'iteration');
                            currentStreamDiv = currentIterationDiv;
                            break;
                        
                        case 'iteration_content':
                            console.log("Iteration content:", data.content);
                            if (currentStreamDiv) {
                                const iterationContent = `Iteration ${data.iteration}: ${data.content}`;
                                updateStreamContent(currentStreamDiv, iterationContent);
                                // Update thinking history
                                thinkingHistory[data.iteration - 1] = {
                                    iteration: data.iteration,
                                    reasoning: data.content
                                };
                            } else {
                                console.warn("No current stream div for iteration content");
                            }
                            break;
                        
                        case 'iteration_complete':
                            console.log("Iteration complete:", data.iteration);
                            currentStreamDiv = null;
                            break;
                        
                        case 'final_stream':
                            console.log("Final stream chunk:", data.content);
                            if (!currentStreamDiv) {
                                currentStreamDiv = addMessage('', 'final');
                            }
                            const currentContent = currentStreamDiv.firstChild?.innerHTML || '';
                            const newContent = currentContent + (data.content || '');
                            updateStreamContent(currentStreamDiv, newContent);
                            break;
                        
                        case 'complete':
                            console.log("Processing complete");
                            currentStreamDiv = null;
                            currentIterationDiv = null;
                            messageInput.disabled = false;
                            sendButton.disabled = false;
                            break;
                        
                        case 'error':
                            console.error("Server error:", data.content);
                            addMessage(`Error: ${data.content || 'Unknown error occurred'}`, 'error');
                            // Don't clear thinking history on error
                            messageInput.disabled = false;
                            sendButton.disabled = false;
                            currentStreamDiv = null;
                            currentIterationDiv = null;
                            // Send the current thinking history with the next iteration
                            if (ws && ws.readyState === WebSocket.OPEN) {
                                ws.send(JSON.stringify({
                                    type: "continue",
                                    thinking_history: thinkingHistory.filter(h => h && h.iteration && h.reasoning)
                                }));
                            }
                            break;

                        default:
                            console.log("Unknown message type:", data.type);
                    }
                } catch (error) {
                    console.error("Error processing message:", error);
                    addMessage("Error processing message", 'error');
                }
            };
        } catch (error) {
            console.error("Error creating WebSocket:", error);
            handleConnectionError();
        }
    }

    function handleConnectionError() {
        clearInterval(heartbeatInterval);
        messageInput.disabled = true;
        sendButton.disabled = true;
        currentStreamDiv = null;
        currentIterationDiv = null;
        isReconnecting = false;

        if (reconnectAttempts < maxReconnectAttempts) {
            reconnectAttempts++;
            const delay = Math.min(1000 * Math.pow(2, reconnectAttempts), 30000); // Exponential backoff, max 30s
            addMessage(`Connection lost. Reconnecting in ${delay/1000} seconds... (Attempt ${reconnectAttempts}/${maxReconnectAttempts})`, 'system');
            reconnectTimeout = setTimeout(connectWebSocket, delay);
        } else {
            addMessage("Could not reconnect to server. Please refresh the page.", 'error');
        }
    }

    // Connection monitoring
    setInterval(() => {
        if (ws && ws.readyState === WebSocket.OPEN) {
            const timeSinceLastMessage = Date.now() - lastMessageTime;
            if (timeSinceLastMessage > 60000) { // 60 seconds timeout
                console.log("Connection timeout - no message received for 60 seconds");
                handleConnectionError();
            }
        }
    }, 10000); // Check every 10 seconds

    // Event Listeners
    messageInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter' && !e.shiftKey && !messageInput.disabled) {
            e.preventDefault();
            sendMessage();
        }
    });

    sendButton.addEventListener('click', function() {
        if (!messageInput.disabled) {
            sendMessage();
        }
    });

    // Initial connection
    connectWebSocket();
}); 