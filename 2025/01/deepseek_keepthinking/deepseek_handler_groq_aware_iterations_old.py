import os
from termcolor import cprint
from groq import Groq
import asyncio
from typing import List, Dict

# Constants
MODEL_NAME = "deepseek-r1-distill-llama-70b"
API_KEY = os.getenv("GROQ_API_KEY")
MAX_ITERATIONS = 5  # Default maximum number of thinking iterations

async def call_groq_with_iteration_awareness(
    initial_prompt: str,
    iterations: int = MAX_ITERATIONS,
    continuation_prompt: str = "Continue your thinking from where you left off."
) -> Dict[str, any]:
    """
    Calls the Groq API iteratively, with awareness of which iteration it's on.
    On the final iteration, explicitly tells the model to conclude its thinking and provide a final answer.
    """
    try:
        thinking_history = []
        current_prompt = initial_prompt
        final_answer = ""
        
        for iteration in range(iterations):
            is_final_iteration = iteration == iterations - 1
            cprint(f"\n[Iteration {iteration + 1}/{iterations}] 🔄 Calling Groq API...", "cyan")
            
            # Initialize Groq client
            client = Groq(api_key=API_KEY)
            
            # Print iteration header
            print("\n" + "-"*50)
            cprint(f"🤔 Reasoning Process - Iteration {iteration + 1}:", "yellow", attrs=['bold'])
            print("-"*50 + "\n")
            
            # Initialize response streams
            reasoning_stream = ""
            in_thinking_block = False
            
            # Construct the full prompt with history and iteration awareness
            if iteration > 0:
                history_context = "\n\nYour Previous thinking:\n" + "\n".join(
                    f"Iteration {i+1}: {step['reasoning']}" 
                    for i, step in enumerate(thinking_history)
                )
                
                if is_final_iteration:
                    iteration_prompt = (
                        f"{history_context}\n\n"
                        "This is the final iteration. After your thinking process, "
                        "please provide a clear and definitive final answer."
                    )
                else:
                    iteration_prompt = f"{history_context}\n\n{continuation_prompt}"
                
                full_prompt = iteration_prompt
            else:
                full_prompt = current_prompt
            
            # Make the API call
            completion = client.chat.completions.create(
                model=MODEL_NAME,
                messages=[{"role": "user", "content": full_prompt}],
                stream=True
            )
            
            # Process the streaming response
            for chunk in completion:
                chunk_content = chunk.choices[0].delta.content or ""
                
                # Check for thinking block markers
                if "<think>" in chunk_content:
                    in_thinking_block = True
                    chunk_content = chunk_content.replace("<think>", "")
                elif "</think>" in chunk_content:
                    in_thinking_block = False
                    chunk_content = chunk_content.replace("</think>", "")
                    reasoning_stream += chunk_content.strip()
                    cprint(chunk_content.strip(), 'yellow', flush=True)
                    if not is_final_iteration:
                        break
                
                # Stream the content
                if in_thinking_block:
                    cprint(chunk_content, 'yellow', end="", flush=True)
                    reasoning_stream += chunk_content
                elif not in_thinking_block and is_final_iteration:  # Capture final answer
                    cprint(chunk_content, 'cyan', end="", flush=True)
                    final_answer += chunk_content
            
            # Store this iteration's thinking
            if reasoning_stream.strip():
                thinking_history.append({
                    "iteration": iteration + 1,
                    "reasoning": reasoning_stream.strip()
                })
            
            # Print iteration footer
            print("\n" + "-"*50)
            cprint(f"✅ Iteration {iteration + 1} Complete!", "green", attrs=['bold'])
            print("-"*50 + "\n")
            
            await asyncio.sleep(1)
        
        # Print final summary
        cprint("\n📚 Final Thinking History:", "blue", attrs=['bold'])
        for step in thinking_history:
            print("\n" + "-"*30)
            cprint(f"Iteration {step['iteration']}:", "cyan")
            cprint(step['reasoning'], "yellow")
        
        if final_answer.strip():
            print("\n" + "-"*50)
            cprint("🎯 Final Answer:", "green", attrs=['bold'])
            cprint(final_answer.strip(), "cyan")
            print("-"*50)
        
        return {
            "thinking_history": thinking_history,
            "final_answer": final_answer.strip()
        }

    except Exception as e:
        cprint(f"\n⚠️ Error in iterative thinking process: {str(e)}", "red")
        return {"thinking_history": thinking_history if thinking_history else None, "final_answer": ""}

if __name__ == "__main__":
    async def test():
        try:
            test_prompt = """
            what is 534897 times 99142?
            """
            
            response = await call_groq_with_iteration_awareness(
                initial_prompt=test_prompt,
                iterations=4
            )
            
            if response:
                cprint("\nIterative thinking test complete!", "green")
                if response["final_answer"]:
                    cprint(f"\nFinal calculated answer: {response['final_answer']}", "cyan")
        except Exception as e:
            cprint(f"\n⚠️ Error during test: {str(e)}", "red")

    asyncio.run(test()) 