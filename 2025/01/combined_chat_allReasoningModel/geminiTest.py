import google.generativeai as genai
import os


# Configure the API key
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY","AIzaSyA7eaDvusussmwXjF_cdcY71qoe1v5zp-s")
genai.configure(api_key=GEMINI_API_KEY)

# Create a model and generate content
# Create the model
generation_config = {
  "temperature": 1,
  "top_p": 0.95,
  "top_k": 40,
  "max_output_tokens": 8192,
  "response_mime_type": "text/plain",
}

model = genai.GenerativeModel(
  model_name="gemini-2.0-flash-exp",
  generation_config=generation_config,
)

chat_session = model.start_chat(
  history=[
  ]
)

response = chat_session.send_message("The opposite of hot is")

print(response.text)
