from openai import OpenAI
from termcolor import colored
import sys
import json

# Constants
WELCOME_MESSAGE = "Welcome to the AI Assistant! Type 'exit' to quit."
ERROR_COLOR = "red"
SUCCESS_COLOR = "green"
INFO_COLOR = "cyan"

try:
    client = OpenAI()
except Exception as e:
    print(colored(f"Error initializing OpenAI client: {str(e)}", ERROR_COLOR))
    sys.exit(1)

def add_numbers(number1, number2):
    """Add two numbers together"""
    try:
        print(colored(f"Adding {number1} and {number2}...", INFO_COLOR))
        result = float(number1) + float(number2)
        return f"The sum of {number1} and {number2} is {result}"
    except (ValueError, TypeError) as e:
        return f"Error adding numbers: {str(e)}"

tools = [{
    "type": "function",
    "function": {
        "name": "add_numbers",
        "description": "Add two numbers together",
        "parameters": {
            "type": "object",
            "properties": {
                "number1": {
                    "type": "number",
                    "description": "First number to add"
                },
                "number2": {
                    "type": "number",
                    "description": "Second number to add"
                }
            },
            "required": ["number1", "number2"],
            "additionalProperties": False
        },
        "strict": True
    }
}]

def handle_tool_call(tool_call):
    """Handle individual tool calls"""
    try:
        function_name = tool_call.function.name
        function_args = json.loads(tool_call.function.arguments)
        
        if function_name == "add_numbers":
            result = add_numbers(function_args["number1"], function_args["number2"])
        else:
            result = f"Unknown function: {function_name}"
            
        return result
        
    except Exception as e:
        return f"Error handling tool call: {str(e)}"

def main():
    print(colored(WELCOME_MESSAGE, INFO_COLOR))
    
    while True:
        try:
            user_input = input(colored("\nEnter your question: ", INFO_COLOR))
            
            if user_input.lower() == 'exit':
                print(colored("Goodbye!", SUCCESS_COLOR))
                break
                
            print(colored("Processing your request...", INFO_COLOR))
            
            completion = client.chat.completions.create(
                model="gpt-4o",
                messages=[{"role": "user", "content": user_input}],
                tools=tools, 
                parallel_tool_calls=True
            )
            
            message = completion.choices[0].message
            
            if message.tool_calls:
                print(colored("\nExecuting tool calls...", INFO_COLOR))
                for tool_call in message.tool_calls:
                    result = handle_tool_call(tool_call)
                    print(colored(f"\nResult: {result}", SUCCESS_COLOR))
            else:
                print(colored(f"\nResponse: {message.content}", SUCCESS_COLOR))
            
        except KeyboardInterrupt:
            print(colored("\nExiting gracefully...", INFO_COLOR))
            break
        except Exception as e:
            print(colored(f"An error occurred: {str(e)}", ERROR_COLOR))

if __name__ == "__main__":
    main()