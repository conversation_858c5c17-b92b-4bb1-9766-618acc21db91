# AI Assistant Agents

This repository contains two different implementations of AI assistants using OpenAI's API: a basic agent with parallel tool calling capabilities and an O1 agent with sequential tool calling.

## Features

- Interactive command-line interface
- Mathematical calculations using tool functions
- Colored terminal output for better user experience
- Error handling and graceful exits
- Different tool calling strategies

## Agents Overview

### Basic Agent (`basic_agent.py`)
- Uses GPT-4O model
- Supports parallel tool calls
- Processes multiple function calls simultaneously
- Best for scenarios where tools can be executed independently
- Simple request-response pattern

### O1 Agent (`o1_agent.py`)
- Uses O1 model
- Sequential tool calling with conversation memory
- Maintains context throughout the interaction
- Can chain multiple calculations before providing final response
- More sophisticated conversation flow

## Key Differences

| Feature | Basic Agent | O1 Agent |
|---------|-------------|----------|
| Model | GPT-4O | O1 |
| Tool Calls | Parallel | Sequential |
| Memory | Single turn | Maintains conversation history |
| System Message | No | Yes |
| Tool Processing | All at once | One at a time |

## Usage

1. Make sure you have your OpenAI API key set as an environment variable
2. Install the required packages:
```bash
pip install openai termcolor
```

3. Run either agent:
```bash
python basic_agent.py
# or
python o1_agent.py
```

4. Interact with the agent by typing questions or mathematical operations
5. Type 'exit' to quit

## Example Interactions

### Basic Agent
```
Enter your question: Add 5 and 3, then add the result to 10
# Executes both calculations in parallel if possible
```

### O1 Agent
```
Enter your question: Add 5 and 3, then add the result to 10
# Executes calculations sequentially, maintaining context
```

## Error Handling

Both agents include:
- OpenAI client initialization error handling
- Tool execution error handling
- Graceful exit on keyboard interrupt
- Input validation
- Descriptive error messages

## Technical Details

### Common Features
- Both use termcolor for colored output
- JSON parsing for tool arguments
- Structured tool definitions
- Error color coding (red for errors, green for success, cyan for info)

### Tool Definition
Both agents include an `add_numbers` tool that:
- Takes two numbers as input
- Returns the sum with a descriptive message
- Includes parameter validation
- Handles type conversion errors 