from openai import OpenAI
from termcolor import colored
import sys
import json

# Constants
WELCOME_MESSAGE = "Welcome to the AI Assistant! Type 'exit' to quit."
ERROR_COLOR = "red"
SUCCESS_COLOR = "green"
INFO_COLOR = "cyan"
SYSTEM_MESSAGE = "You are a helpful AI assistant that can perform mathematical calculations and provide clear explanations. When numbers are mentioned, you should use the add_numbers function to perform calculations. you can only call one function at a time. but you can call multiple functions before getting back to user with a regular response"

try:
    client = OpenAI()
except Exception as e:
    print(colored(f"Error initializing OpenAI client: {str(e)}", ERROR_COLOR))
    sys.exit(1)

def add_numbers(number1, number2):
    """Add two numbers together"""
    try:
        print(colored(f"calling tool...Adding {number1} and {number2}...", INFO_COLOR))
        result = float(number1) + float(number2)
        return f"The sum of {number1} and {number2} is {result}"
    except (ValueError, TypeError) as e:
        return f"Error adding numbers: {str(e)}"

tools = [{
    "type": "function",
    "function": {
        "name": "add_numbers",
        "description": "Add two numbers together",
        "parameters": {
            "type": "object",
            "properties": {
                "number1": {
                    "type": "number",
                    "description": "First number to add"
                },
                "number2": {
                    "type": "number",
                    "description": "Second number to add"
                }
            },
            "required": ["number1", "number2"],
            "additionalProperties": False
        },
        "strict": True
    }
}]

def handle_tool_call(tool_call):
    """Handle individual tool calls"""
    try:
        function_name = tool_call.function.name
        function_args = json.loads(tool_call.function.arguments)
        
        if function_name == "add_numbers":
            result = add_numbers(function_args["number1"], function_args["number2"])
        else:
            result = f"Unknown function: {function_name}"
            
        return result
        
    except Exception as e:
        return f"Error handling tool call: {str(e)}"

def main():
    print(colored(WELCOME_MESSAGE, INFO_COLOR))
    
    while True:
        try:
            user_input = input(colored("\nEnter your question: ", INFO_COLOR))
            
            if user_input.lower() == 'exit':
                print(colored("Goodbye!", SUCCESS_COLOR))
                break
                
            print(colored("Processing your request...", INFO_COLOR))
            
            completion = client.chat.completions.create(
                model="o1",
                messages=[
                    {"role": "system", "content": SYSTEM_MESSAGE},
                    {"role": "user", "content": user_input}
                ],
                tools=tools,
            )
            
            message = completion.choices[0].message
            messages = [
                {"role": "system", "content": SYSTEM_MESSAGE},
                {"role": "user", "content": user_input}
            ]

            while True:
                if message.tool_calls:
                    print(colored("\nExecuting tool call...", INFO_COLOR))
                    tool_call = message.tool_calls[0]  # Process one tool call at a time
                    result = handle_tool_call(tool_call)
                    print(colored(f"\nTool Result: {result}", SUCCESS_COLOR))
                    
                    # Add the assistant's tool call and the tool result to the message history
                    messages.append({"role": "assistant", "content": None, "tool_calls": [tool_call.model_dump()]})
                    messages.append({"role": "tool", "tool_call_id": tool_call.id, "content": result})
                    
                    # Get the next action from the model
                    completion = client.chat.completions.create(
                        model="o1",
                        messages=messages,
                        tools=tools,
                    )
                    message = completion.choices[0].message
                else:
                    # No more tool calls, print the final response
                    print(colored(f"\nFinal Response: {message.content}", SUCCESS_COLOR))
                    break
            
        except KeyboardInterrupt:
            print(colored("\nExiting gracefully...", INFO_COLOR))
            break
        except Exception as e:
            print(colored(f"An error occurred: {str(e)}", ERROR_COLOR))

if __name__ == "__main__":
    main()