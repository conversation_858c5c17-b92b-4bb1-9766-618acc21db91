<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ArXiv Search and Summarize Flow Diagram</title>
    <!-- Mermaid JS -->
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <style>
        body {
            background-color: #1a1a1a;
            color: #ffffff;
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        h1 {
            color: #00ff9d;
            text-align: center;
            margin-bottom: 30px;
        }
        #flowchart {
            background-color: #2a2a2a;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.5);
        }
        .loading {
            text-align: center;
            color: #00ff9d;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>ArXiv Paper Search and Summarization Flow</h1>
        <div id="flowchart">
            <pre class="mermaid">
                flowchart TB
                    Start([Start]) --> CheckAPI{Check OpenAI API Key}
                    
                    CheckAPI -->|Missing| Error1[Show API Key Error]
                    Error1 --> End([End])
                    
                    CheckAPI -->|Present| Input[Get Search Query]
                    Input --> Search[Search arXiv Papers]
                    
                    Search --> CheckResults{Papers Found?}
                    CheckResults -->|No| Error2[Show No Papers Error]
                    Error2 --> End
                    
                    CheckResults -->|Yes| Display[Display Paper List]
                    Display --> Selection[Get User Selection]
                    
                    Selection --> ValidSelection{Valid Selection?}
                    ValidSelection -->|No| Error3[Show Invalid Selection Error]
                    Error3 --> End
                    
                    ValidSelection -->|Yes| Download[Download Selected Paper]
                    Download --> ExtractContent{Content Extracted?}
                    
                    ExtractContent -->|No| Error4[Show Extraction Error]
                    Error4 --> End
                    
                    ExtractContent -->|Yes| GPT[Send to GPT-4-O]
                    GPT --> Summary{Summary Generated?}
                    
                    Summary -->|No| Error5[Show Summary Error]
                    Error5 --> End
                    
                    Summary -->|Yes| Display2[Display Summary]
                    Display2 --> End
                    
                    style Start fill:#00ff9d,stroke:#009966
                    style End fill:#ff6b6b,stroke:#cc5555
                    style CheckAPI fill:#4a9eff,stroke:#2d5f99
                    style ValidSelection fill:#4a9eff,stroke:#2d5f99
                    style ExtractContent fill:#4a9eff,stroke:#2d5f99
                    style Summary fill:#4a9eff,stroke:#2d5f99
                    style CheckResults fill:#4a9eff,stroke:#2d5f99
            </pre>
        </div>
    </div>
    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'dark',
            flowchart: {
                curve: 'basis',
                nodeSpacing: 50,
                rankSpacing: 50,
            }
        });
    </script>
</body>
</html> 