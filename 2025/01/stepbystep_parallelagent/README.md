# Research Assistant Tutorial Series

This tutorial series demonstrates the progression from basic research paper analysis to advanced parallel processing and vector database implementations. Each script builds upon the concepts introduced in the previous ones.

## Prerequisites

```bash
pip install -r requirements.txt
```

Make sure to set your OpenAI API key as an environment variable:

```bash
export OPENAI_API_KEY=your_api_key_here
```

## Tutorial Progression

### 1. Basic arXiv Paper Search and Analysis (`1_arxiv_search.py`)

**Introduction to Research Paper Processing**

- Basic arXiv API integration
- Single paper download and processing
- Simple GPT-4 summarization
- Sequential processing
- Basic error handling
- Features:
  - Search arXiv papers
  - Download PDF content
  - Generate single summary using GPT-4

### 2. Parallel Paper Analysis (`2_arxiv_parallel_analysis.py`)

**Advanced Parallel Processing**

- Introduces async/await pattern
- Parallel GPT analysis
- Multiple analysis types (summary, key points, suggestions)
- Enhanced error handling
- Markdown report generation
- Features:
  - Parallel API calls using AsyncOpenAI
  - Multiple analysis perspectives
  - Structured markdown output
  - File sanitization
  - Progress tracking

### 3. Wikipedia Content with Threading (`3_wiki_retriever_with_parallel_thread.py`)

**Multi-threaded Data Collection**

- Queue-based processing
- Worker thread implementation
- Vector database integration (ChromaDB)
- Batch processing
- Features:
  - Parallel content retrieval
  - Queue management
  - Thread safety
  - Progress monitoring
  - Vector embeddings storage

### 4. Vector Database Search (`4_db_search.py`)

**Semantic Search Implementation**

- ChromaDB vector search
- Similarity scoring
- Formatted result display
- Interactive query interface
- Features:
  - Semantic similarity search
  - Top-K results retrieval
  - Relevance scoring
  - Rich content preview

## Key Concepts Covered

1. **API Integration**

   - arXiv API
   - OpenAI API
   - Wikipedia API
2. **Parallel Processing**

   - Async/Await patterns
   - Multi-threading
   - Queue management
3. **Vector Databases**

   - ChromaDB setup
   - Embedding generation
   - Semantic search
4. **Error Handling**

   - Try-except patterns
   - User input validation
   - Resource cleanup
5. **User Interface**

   - Color-coded output
   - Progress tracking
   - Interactive prompts

## Usage Examples

### 1. Basic Paper Search

```bash
python 1_arxiv_search.py
# Follow prompts to search and summarize a paper
```

### 2. Parallel Analysis

```bash
python 2_arxiv_parallel_analysis.py
# Get comprehensive analysis with parallel processing
```

### 3. Build Vector Database

```bash
python 3_wiki_retriever_with_parallel_thread.py
# Automatically builds a knowledge base from Wikipedia
```

### 4. Search Database

```bash
python 4_db_search.py
# Search through collected articles semantically
```

## Best Practices Demonstrated

- Proper error handling
- Resource management
- Thread safety
- Progress monitoring
- User feedback
- Code organization
- Type hinting
- Documentation

## Flow Control

The progression shows increasing complexity in flow control:

1. Simple sequential → Async parallel
2. Single thread → Multi-threaded
3. Direct storage → Queue-based processing
4. Text search → Vector similarity search
