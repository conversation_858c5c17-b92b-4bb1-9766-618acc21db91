from termcolor import colored
import arxiv
import os
import openai
from PyPDF2 import PdfReader
import io
import requests
import sys

# Constants
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
MAX_RESULTS = 5
MAX_TOKENS = 4000

def search_arxiv(query):
    try:
        print(colored("Searching arXiv for papers...", "cyan"))
        search = arxiv.Search(
            query=query,
            max_results=MAX_RESULTS,
            sort_by=arxiv.SortCriterion.Relevance
        )
        return list(search.results())
    except Exception as e:
        print(colored(f"Error searching arXiv: {str(e)}", "red"))
        return []

def get_paper_content(paper):
    try:
        print(colored(f"Downloading paper: {paper.title}", "yellow"))
        response = requests.get(paper.pdf_url)
        pdf_content = io.BytesIO(response.content)
        
        reader = PdfReader(pdf_content)
        text = ""
        for page in reader.pages:
            text += page.extract_text()
        
        return text
    except Exception as e:
        print(colored(f"Error downloading paper: {str(e)}", "red"))
        return ""

def summarize_with_gpt(text):
    try:
        print(colored("Generating summary with GPT-4-O...", "green"))
        client = openai.OpenAI(api_key=OPENAI_API_KEY)
        
        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "system", "content": "You are a helpful research assistant. Summarize the following academic paper in a clear and concise way, highlighting the main contributions and findings."},
                {"role": "user", "content": text[:MAX_TOKENS]}
            ],
            max_tokens=1000
        )
        
        return response.choices[0].message.content
    except Exception as e:
        print(colored(f"Error generating summary: {str(e)}", "red"))
        return ""

def main():
    try:
        if not OPENAI_API_KEY:
            raise ValueError("OpenAI API key not found in environment variables")
        
        query = input(colored("Enter your search query for arXiv: ", "cyan"))
        
        papers = search_arxiv(query)
        if not papers:
            print(colored("No papers found!", "red"))
            return
        
        print(colored("\nFound papers:", "green"))
        for i, paper in enumerate(papers, 1):
            print(colored(f"{i}. {paper.title}", "yellow"))
        
        selection = int(input(colored("\nSelect a paper number to summarize (1-5): ", "cyan"))) - 1
        if not 0 <= selection < len(papers):
            raise ValueError("Invalid paper selection")
        
        selected_paper = papers[selection]
        content = get_paper_content(selected_paper)
        
        if not content:
            print(colored("Could not extract paper content!", "red"))
            return
        
        summary = summarize_with_gpt(content)
        if summary:
            print(colored("\nPaper Summary:", "green"))
            print(colored(summary, "white"))
        
    except ValueError as ve:
        print(colored(f"Error: {str(ve)}", "red"))
    except Exception as e:
        print(colored(f"An unexpected error occurred: {str(e)}", "red"))

if __name__ == "__main__":
    main() 