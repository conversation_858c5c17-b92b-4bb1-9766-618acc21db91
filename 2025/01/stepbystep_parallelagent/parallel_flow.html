<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ArXiv Parallel Analysis Flow Diagram</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <style>
        body {
            background-color: #1a1a1a;
            color: #ffffff;
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        h1 {
            color: #00ff9d;
            text-align: center;
            margin-bottom: 30px;
        }
        #flowchart {
            background-color: #2a2a2a;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.5);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>ArXiv Parallel Analysis Flow</h1>
        <div id="flowchart">
            <pre class="mermaid">
                flowchart TB
                    Start([Start]) --> CheckAPI{Check OpenAI API Key}
                    
                    CheckAPI -->|Missing| Error1[Show API Key Error]
                    Error1 --> End([End])
                    
                    CheckAPI -->|Present| Input[Get Search Query]
                    Input --> Search[Search arXiv Papers]
                    
                    Search --> CheckResults{Papers Found?}
                    CheckResults -->|No| Error2[Show No Papers Error]
                    Error2 --> End
                    
                    CheckResults -->|Yes| Display[Display Paper List]
                    Display --> Selection[Get User Selection]
                    
                    Selection --> ValidSelection{Valid Selection?}
                    ValidSelection -->|No| Error3[Show Invalid Selection Error]
                    Error3 --> End
                    
                    ValidSelection -->|Yes| Download[Download Selected Paper]
                    Download --> ExtractContent{Content Extracted?}
                    
                    ExtractContent -->|No| Error4[Show Extraction Error]
                    Error4 --> End
                    
                    ExtractContent -->|Yes| ParallelProcess{Parallel Processing}
                    
                    ParallelProcess --> Summary[Generate Summary]
                    ParallelProcess --> KeyPoints[Extract Key Points]
                    ParallelProcess --> Suggestions[Generate Research Suggestions]
                    
                    Summary --> Combine((Combine Results))
                    KeyPoints --> Combine
                    Suggestions --> Combine
                    
                    Combine --> FinalReport[Generate Final Report]
                    FinalReport --> SaveMD[Save as Markdown]
                    SaveMD --> End
                    
                    style Start fill:#00ff9d,stroke:#009966
                    style End fill:#ff6b6b,stroke:#cc5555
                    style CheckAPI fill:#4a9eff,stroke:#2d5f99
                    style ValidSelection fill:#4a9eff,stroke:#2d5f99
                    style ExtractContent fill:#4a9eff,stroke:#2d5f99
                    style ParallelProcess fill:#ffa500,stroke:#cc8400
                    style Combine fill:#ffa500,stroke:#cc8400
                    style Summary fill:#9932cc,stroke:#7b2899
                    style KeyPoints fill:#9932cc,stroke:#7b2899
                    style Suggestions fill:#9932cc,stroke:#7b2899
                    style FinalReport fill:#32cd32,stroke:#228b22
                    style SaveMD fill:#32cd32,stroke:#228b22
            </pre>
        </div>
    </div>
    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'dark',
            flowchart: {
                curve: 'basis',
                nodeSpacing: 60,
                rankSpacing: 80,
                useMaxWidth: true
            }
        });
    </script>
</body>
</html> 