```mermaid
flowchart TD
    A[Start] --> B[Initialize ChromaDB]
    B --> C[Create Queue & Stop Event]
    C --> D[Start Worker Thread]
    
    %% Main Thread Flow
    D --> E[Process Search Terms]
    E --> F{More Terms?}
    F -->|Yes| G[Get Wiki Content]
    G --> H{Content Found?}
    H -->|Yes| I[Add to Queue]
    I --> F
    H -->|No| F
    F -->|No| J[Wait for Queue Empty]
    J --> K[Set Stop Event]
    K --> L[Join Worker Thread]
    L --> M[Show Final Count]
    M --> N[End]

    %% Worker Thread Flow
    D --> O[Worker Thread]
    O --> P{Stop Event Set AND Queue Empty?}
    P -->|No| Q[Get Item from Queue]
    Q --> R[Process Article]
    R --> S[Save to VectorDB]
    S --> T[Mark Task Done]
    T --> P
    P -->|Yes| U[Thread Exit]

    %% Queue Operations
    I -.->|Enqueue| V[(Queue)]
    Q -.->|Dequeue| V

    %% Error Handling
    B -->|Error| Z[Exit with Error]
    G -->|Error| F
    S -->|Error| P

    %% Styling
    classDef process fill:#a8d5ff,stroke:#333,stroke-width:2px
    classDef decision fill:#ffb3b3,stroke:#333,stroke-width:2px
    classDef queue fill:#b3ffb3,stroke:#333,stroke-width:2px
    classDef thread fill:#ffecb3,stroke:#333,stroke-width:2px
    
    class A,B,C,G,I,K,L,M,N,R,S,T process
    class F,H,P decision
    class V queue
    class O,U thread
``` 