import wikipedia
from termcolor import colored
import os
import chromadb
from chromadb.utils import embedding_functions
import queue
import threading
import time
from typing import List, Tuple

# Constants
MAX_CHARS = 1000
ENCODING = "utf-8"
DB_DIR = "wiki_vectordb"
COLLECTION_NAME = "wiki_articles"
SEARCH_TERMS = [
    "Artificial Intelligence",
    "Quantum Computing",
    "Machine Learning",
    "Neural Networks",
    "Deep Learning",
    "Blockchain",
    "Cryptocurrency",
    "Internet of Things",
    "Cloud Computing",
    "Cybersecurity",
    "Data Science",
    "Virtual Reality",
    "Augmented Reality",
    "5G Technology",
    "Robotics",
    "Edge Computing",
    "Big Data",
    "DevOps",
    "Microservices",
    "API Development"
]

# Initialize queue for thread communication
article_queue = queue.Queue()
# Event to signal thread termination
stop_event = threading.Event()

# Initialize ChromaDB
try:
    print(colored("Initializing ChromaDB...", "cyan"))
    chroma_client = chromadb.PersistentClient(path=DB_DIR)
    embedding_fn = embedding_functions.DefaultEmbeddingFunction()
    
    collection = chroma_client.get_or_create_collection(
        name=COLLECTION_NAME,
        embedding_function=embedding_fn
    )
    print(colored("ChromaDB initialized successfully", "green"))
except Exception as e:
    print(colored(f"Error initializing ChromaDB: {e}", "red"))
    exit(1)

def get_wiki_content(topic) -> Tuple[str, str, str]:
    try:
        print(colored(f"Searching for article about: {topic}", "cyan"))
        search_results = wikipedia.search(topic)
        if not search_results:
            raise Exception("No results found")
            
        page_title = search_results[0]
        print(colored(f"Found article: {page_title}", "green"))
        
        try:
            page = wikipedia.page(page_title, auto_suggest=False)
        except wikipedia.exceptions.DisambiguationError as e:
            print(colored(f"Multiple matches found. Selecting the most relevant option: {e.options[0]}", "yellow"))
            page = wikipedia.page(e.options[0], auto_suggest=False)
            page_title = e.options[0]
            
        content = page.content[:MAX_CHARS]
        return content, page_title, page.url
        
    except wikipedia.exceptions.PageError:
        print(colored("Article not found", "red"))
        return None, None, None
    except Exception as e:
        print(colored(f"An error occurred: {e}", "red"))
        return None, None, None

def save_to_vectordb(content: str, title: str, url: str) -> bool:
    try:
        existing = collection.get(
            where={"title": title}
        )
        
        if existing and existing['ids']:
            print(colored(f"Article '{title}' already exists in the database", "yellow"))
            return True
            
        # Simulate long processing time
        time.sleep(3)
        
        collection.add(
            documents=[content],
            metadatas=[{"title": title, "url": url}],
            ids=[title]
        )
        print(colored(f"Article '{title}' saved to vector database", "green"))
        return True
    except Exception as e:
        print(colored(f"Error saving to vector database: {e}", "red"))
        return False

def database_worker():
    """Worker thread function to process the queue and add items to the database"""
    while not stop_event.is_set() or not article_queue.empty():
        try:
            # Get item from queue with timeout to check stop_event periodically
            item = article_queue.get(timeout=1)
            content, title, url = item
            
            print(colored(f"\nProcessing article: {title}", "cyan"))
            print(colored(f"Queue size: {article_queue.qsize()} items remaining", "yellow"))
            
            save_to_vectordb(content, title, url)
            
            article_queue.task_done()
            print(colored(f"Queue size after processing: {article_queue.qsize()} items", "yellow"))
            
        except queue.Empty:
            continue
        except Exception as e:
            print(colored(f"Error in worker thread: {e}", "red"))
            continue

def process_search_terms(terms: List[str]):
    """Process the list of search terms and add them to the queue"""
    # Start worker thread
    worker_thread = threading.Thread(target=database_worker)
    worker_thread.start()
    
    try:
        for i, term in enumerate(terms, 1):
            print(colored(f"\nProcessing search term {i}/{len(terms)}: {term}", "cyan"))
            
            content, title, url = get_wiki_content(term)
            if content and title:
                print(colored(f"Adding '{title}' to queue...", "yellow"))
                article_queue.put((content, title, url))
                print(colored(f"Current queue size: {article_queue.qsize()} items", "yellow"))
        
        print(colored("\nAll search terms processed. Waiting for queue to empty...", "cyan"))
        # Wait for all queue items to be processed
        article_queue.join()
        
    except KeyboardInterrupt:
        print(colored("\nReceived interrupt signal. Cleaning up...", "yellow"))
    finally:
        # Signal worker thread to stop
        stop_event.set()
        # Wait for worker thread to finish
        worker_thread.join()
        print(colored("Worker thread stopped", "green"))

def main():
    print(colored("Starting batch processing of Wikipedia articles...", "cyan"))
    process_search_terms(SEARCH_TERMS)
    
    # Show final count
    count = collection.count()
    print(colored(f"\nFinal database status:", "cyan"))
    print(colored(f"Total articles in database: {count}", "green"))
    print(colored("Processing complete!", "green", attrs=["bold"]))

if __name__ == "__main__":
    print(colored("Wikipedia Vector Database - Batch Processor", "cyan", attrs=["bold"]))
    print(colored("This tool will process multiple articles in parallel and save them to a vector database", "cyan"))
    main() 