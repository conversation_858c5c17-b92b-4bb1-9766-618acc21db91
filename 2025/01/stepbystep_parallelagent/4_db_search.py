import chromadb
from chromadb.utils import embedding_functions
from termcolor import colored
import os
from typing import List, Dict, Any

# Constants
DB_DIR = "wiki_vectordb"
COLLECTION_NAME = "wiki_articles"
TOP_K = 3  # Number of results to return

def initialize_db():
    """Initialize connection to the ChromaDB database"""
    try:
        print(colored("Connecting to ChromaDB...", "cyan"))
        chroma_client = chromadb.PersistentClient(path=DB_DIR)
        embedding_fn = embedding_functions.DefaultEmbeddingFunction()
        
        collection = chroma_client.get_or_create_collection(
            name=COLLECTION_NAME,
            embedding_function=embedding_fn
        )
        print(colored("Successfully connected to ChromaDB", "green"))
        return collection
    except Exception as e:
        print(colored(f"Error connecting to database: {e}", "red"))
        exit(1)

def search_articles(collection, query: str) -> Dict[str, List[Any]]:
    """Search for similar articles in the database"""
    try:
        print(colored(f"\nSearching for: {query}", "cyan"))
        
        # Query the collection
        results = collection.query(
            query_texts=[query],
            n_results=TOP_K,
            include=["metadatas", "documents", "distances"]
        )
        
        if not results['ids'][0]:
            print(colored("No results found", "yellow"))
            return None
            
        return results
    except Exception as e:
        print(colored(f"Error searching database: {e}", "red"))
        return None

def display_results(results: Dict[str, List[Any]]):
    """Display search results with formatting"""
    if not results:
        return
        
    print(colored("\nTop 3 Most Similar Articles:", "cyan", attrs=["bold"]))
    print(colored("=" * 80, "cyan"))
    
    for i in range(len(results['ids'][0])):
        # Calculate similarity score (convert distance to similarity)
        similarity = 1 - (results['distances'][0][i] / 2)  # Normalize to 0-1 range
        
        # Get article details
        title = results['metadatas'][0][i]['title']
        url = results['metadatas'][0][i]['url']
        content = results['documents'][0][i][:200] + "..."  # Show first 200 chars
        
        # Display formatted result
        print(colored(f"\n{i+1}. {title}", "green", attrs=["bold"]))
        print(colored(f"Similarity Score: {similarity:.2%}", "yellow"))
        print(colored("URL: ", "cyan") + url)
        print(colored("Preview: ", "cyan") + content)
        print(colored("-" * 80, "cyan"))

def main():
    # Initialize database connection
    collection = initialize_db()
    
    while True:
        # Get search query
        query = input(colored("\nEnter your search query (or 'quit' to exit): ", "cyan"))
        
        if query.lower() == 'quit':
            print(colored("Goodbye!", "yellow"))
            break
            
        if not query.strip():
            print(colored("Please enter a valid search query", "yellow"))
            continue
        
        # Search and display results
        results = search_articles(collection, query)
        if results:
            display_results(results)

if __name__ == "__main__":
    print(colored("Wikipedia Vector Database - Semantic Search", "cyan", attrs=["bold"]))
    print(colored("This tool will find the most similar articles to your query", "cyan"))
    main() 