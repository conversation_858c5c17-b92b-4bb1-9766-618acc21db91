from termcolor import colored
import arxiv
import os
from openai import AsyncOpenAI
from PyPDF2 import PdfReader
import io
import requests
import sys
import asyncio
import aiohttp
from concurrent.futures import ThreadPoolExecutor
import markdown
import re

# Constants
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
MAX_RESULTS = 5
MAX_TOKENS = 4000
MODEL_NAME = "gpt-4o"

def search_arxiv(query):
    try:
        print(colored("Searching arXiv for papers...", "cyan"))
        search = arxiv.Search(
            query=query,
            max_results=MAX_RESULTS,
            sort_by=arxiv.SortCriterion.Relevance
        )
        return list(search.results())
    except Exception as e:
        print(colored(f"Error searching arXiv: {str(e)}", "red"))
        return []

def get_paper_content(paper):
    try:
        print(colored(f"Downloading paper: {paper.title}", "yellow"))
        response = requests.get(paper.pdf_url)
        pdf_content = io.BytesIO(response.content)
        
        reader = PdfReader(pdf_content)
        text = ""
        for page in reader.pages:
            text += page.extract_text()
        
        return text
    except Exception as e:
        print(colored(f"Error downloading paper: {str(e)}", "red"))
        return ""

async def analyze_with_gpt(client: AsyncOpenAI, text: str, task_type: str):
    try:
        print(colored(f"Generating {task_type}...", "green"))
        
        prompts = {
            "summary": "Provide a clear and concise summary of this academic paper.",
            "key_points": "Extract and list the key points and main contributions of this paper.",
            "suggestions": "Based on this paper, suggest related research areas and potential future directions for investigation."
        }
        
        response = await client.chat.completions.create(
            model=MODEL_NAME,
            messages=[
                {"role": "system", "content": prompts[task_type]},
                {"role": "user", "content": text[:MAX_TOKENS]}
            ],
            max_tokens=1000
        )
        
        return {"type": task_type, "content": response.choices[0].message.content}
    except Exception as e:
        print(colored(f"Error generating {task_type}: {str(e)}", "red"))
        return {"type": task_type, "content": f"Error: {str(e)}"}

async def generate_final_report(client: AsyncOpenAI, results):
    try:
        print(colored("Generating final report...", "magenta"))
        
        prompt = f"""
        Create a comprehensive markdown report based on the following analysis:
        
        Summary: {results['summary']['content']}
        
        Key Points: {results['key_points']['content']}
        
        Further Research Suggestions: {results['suggestions']['content']}
        
        Format the response in clean markdown with appropriate headers, bullet points, and sections.
        """
        
        response = await client.chat.completions.create(
            model=MODEL_NAME,
            messages=[
                {"role": "system", "content": "You are a research assistant creating a final markdown report."},
                {"role": "user", "content": prompt}
            ],
            max_tokens=1500
        )
        
        return response.choices[0].message.content
    except Exception as e:
        print(colored(f"Error generating final report: {str(e)}", "red"))
        return ""

def sanitize_filename(filename: str) -> str:
    """Sanitize filename by removing invalid characters and limiting length."""
    try:
        # Remove invalid filename characters and replace with underscore
        sanitized = re.sub(r'[<>:"/\\|?*]', '_', filename)
        # Remove multiple underscores
        sanitized = re.sub(r'_+', '_', sanitized)
        # Remove leading/trailing underscores
        sanitized = sanitized.strip('_')
        # Limit length to 200 characters (including extension)
        return sanitized[:200]
    except Exception as e:
        print(colored(f"Error sanitizing filename: {str(e)}", "red"))
        return "report_default"

async def save_report(report: str, paper_title: str):
    try:
        sanitized_title = sanitize_filename(paper_title)
        filename = f"report_{sanitized_title}.md"
        with open(filename, "w", encoding="utf-8") as f:
            f.write(report)
        print(colored(f"Report saved as {filename}", "green"))
    except Exception as e:
        print(colored(f"Error saving report: {str(e)}", "red"))

async def main():
    try:
        if not OPENAI_API_KEY:
            raise ValueError("OpenAI API key not found in environment variables")
        
        query = input(colored("Enter your search query for arXiv: ", "cyan"))
        
        papers = search_arxiv(query)
        if not papers:
            print(colored("No papers found!", "red"))
            return
        
        print(colored("\nFound papers:", "green"))
        for i, paper in enumerate(papers, 1):
            print(colored(f"{i}. {paper.title}", "yellow"))
        
        selection = int(input(colored("\nSelect a paper number to analyze (1-5): ", "cyan"))) - 1
        if not 0 <= selection < len(papers):
            raise ValueError("Invalid paper selection")
        
        selected_paper = papers[selection]
        content = get_paper_content(selected_paper)
        
        if not content:
            print(colored("Could not extract paper content!", "red"))
            return
        
        print(colored("\nAnalyzing paper in parallel...", "cyan"))
        client = AsyncOpenAI(api_key=OPENAI_API_KEY)
        
        # True parallel processing with AsyncOpenAI
        tasks = [
            analyze_with_gpt(client, content, "summary"),
            analyze_with_gpt(client, content, "key_points"),
            analyze_with_gpt(client, content, "suggestions")
        ]
        
        results = {}
        analysis_results = await asyncio.gather(*tasks)
        for result in analysis_results:
            results[result["type"]] = result
        
        print(colored("\nGenerating final report...", "cyan"))
        final_report = await generate_final_report(client, results)
        
        if final_report:
            await save_report(final_report, selected_paper.title)
            print(colored("\nAnalysis complete! Check the generated markdown file.", "green"))
        
    except ValueError as ve:
        print(colored(f"Error: {str(ve)}", "red"))
    except Exception as e:
        print(colored(f"An unexpected error occurred: {str(e)}", "red"))

if __name__ == "__main__":
    asyncio.run(main()) 