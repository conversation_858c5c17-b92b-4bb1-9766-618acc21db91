import os
import json
import asyncio
from openai import AsyncOpenAI
from termcolor import colored
import datetime
from pathlib import Path

# CONSTANTS
API_KEY = os.getenv("OPENAI_API_KEY")
MODEL = "o3-mini"
REASONING_EFFORT = "high"
NUM_QUESTIONS = 10
OUTPUT_DIR = "research_results"

async def initialize_client():
    try:
        return AsyncOpenAI()
    except Exception as e:
        print(colored(f"Error initializing AsyncOpenAI client: {str(e)}", "red"))
        return None

async def generate_research_questions(client, query):
    try:
        print(colored(f"Generating research questions for: {query}", "cyan"))

        system_prompt = f"""You are a curious researcher. Given a topic, generate exactly {NUM_QUESTIONS} 
        interesting and thought-provoking questions that would be fascinating to explore about that subject. 
        The questions should:
        - Range from fundamental to complex concepts
        - Cover different aspects and perspectives
        - Be specific and actionable for research
        - Encourage critical thinking
        you must return your response in json format with the key "questions" and a list of questions as the value.
        """

        response = await client.chat.completions.create(
            model=MODEL,
            response_format={"type": "json_object"},
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": f"""Generate research questions about: {query}. 
                Return them in this exact format: {{"questions": ["question1", "question2", ...]}}"""}
            ]
        )

        result = json.loads(response.choices[0].message.content)
        return result.get("questions", [])

    except Exception as e:
        print(colored(f"Error generating research questions: {str(e)}", "red"))
        return None

async def generate_answer(client, question):
    try:
        print(colored(f"Researching: {question}", "cyan"))
        
        response = await client.chat.completions.create(
            model=MODEL,
            reasoning_effort=REASONING_EFFORT,
            response_format={"type": "json_object"},
            messages=[
                {"role": "system", "content": """You are a knowledgeable researcher providing detailed answers.
                Format your response as JSON with the following structure:
                {
                    "answer": "main detailed answer",
                    "key_points": ["point1", "point2", ...],
                    "references": ["reference1", "reference2", ...]
                }"""},
                {"role": "user", "content": f"Provide a detailed answer to: {question}"}
            ]
        )

        return json.loads(response.choices[0].message.content)

    except Exception as e:
        print(colored(f"Error generating answer for '{question}': {str(e)}", "red"))
        return None

def display_questions(questions):
    if not questions:
        print(colored("No questions were generated.", "red"))
        return

    print(colored("\n🔍 Research Questions Generated:", "green"))
    for i, question in enumerate(questions, 1):
        print(colored(f"\n{i}. {question}", "yellow"))

def get_selected_questions(questions):
    while True:
        try:
            print(colored("\nEnter the numbers of the questions you want to explore (comma-separated, e.g., 1,3,5): ", "green"))
            selections = input().strip()
            indices = [int(x.strip()) - 1 for x in selections.split(",")]
            
            if all(0 <= i < len(questions) for i in indices):
                return [questions[i] for i in indices]
            else:
                print(colored("Invalid selection. Please enter valid question numbers.", "red"))
        except ValueError:
            print(colored("Invalid input. Please enter numbers separated by commas.", "red"))

def save_results(topic, questions_and_answers):
    try:
        # Create output directory if it doesn't exist
        Path(OUTPUT_DIR).mkdir(exist_ok=True)
        
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{OUTPUT_DIR}/research_{topic.replace(' ', '_')}_{timestamp}.json"
        
        output = {
            "topic": topic,
            "timestamp": timestamp,
            "research_results": questions_and_answers
        }
        
        with open(filename, "w", encoding="utf-8") as f:
            json.dump(output, f, indent=2, ensure_ascii=False)
            
        print(colored(f"\nResults saved to: {filename}", "green"))
        
    except Exception as e:
        print(colored(f"Error saving results: {str(e)}", "red"))

async def main():
    try:
        print(colored("🤔 Deep Research Question Generator and Answering System 🤔", "cyan"))
        print(colored("--------------------------------------------------------", "cyan"))
        
        client = await initialize_client()
        if not client:
            return

        query = input(colored("Enter your research topic: ", "green"))
        print(colored("\nThinking...", "cyan"))
        
        # Generate initial questions
        questions = await generate_research_questions(client, query)
        if not questions:
            return

        # Display questions and get selection
        display_questions(questions)
        selected_questions = get_selected_questions(questions)
        
        print(colored("\nGenerating detailed answers...", "cyan"))
        
        # Generate answers asynchronously
        tasks = [generate_answer(client, question) for question in selected_questions]
        answers = await asyncio.gather(*tasks)
        
        # Combine questions and answers
        research_results = [
            {
                "question": question,
                "research": answer
            }
            for question, answer in zip(selected_questions, answers)
            if answer is not None
        ]
        
        # Save results
        save_results(query, research_results)

    except KeyboardInterrupt:
        print(colored("\nOperation cancelled by user.", "yellow"))
    except Exception as e:
        print(colored(f"An unexpected error occurred: {str(e)}", "red"))

if __name__ == "__main__":
    asyncio.run(main()) 