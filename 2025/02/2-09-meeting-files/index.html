<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>City Simulator</title>
  <style>
    /* Remove margins and ensure the canvas fills the viewport nicely */
    body {
      margin: 0;
      overflow: hidden;
      background: #222;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
    }
    canvas {
      background: #e0e0e0;
      image-rendering: pixelated; /* for a crisp grid look */
    }
  </style>
</head>
<body>
  <canvas id="cityCanvas"></canvas>

  <script>
    // Configuration for the grid
    const gridWidth = 50;
    const gridHeight = 50;
    let grid = [];

    // Get canvas and context; size canvas based on grid dimensions and available viewport.
    const canvas = document.getElementById('cityCanvas');
    const ctx = canvas.getContext('2d');
    const cellSize = Math.min(window.innerWidth / gridWidth, window.innerHeight / gridHeight);
    canvas.width = gridWidth * cellSize;
    canvas.height = gridHeight * cellSize;

    // Cell type definitions:
    // 0 = empty, 1 = road, 2 = residential
    // Initialize grid with all empty cells.
    for (let x = 0; x < gridWidth; x++) {
      grid[x] = [];
      for (let y = 0; y < gridHeight; y++) {
        grid[x][y] = 0;
      }
    }

    // Create initial roads: a horizontal road and a vertical road crossing in the center.
    const midX = Math.floor(gridWidth / 2);
    const midY = Math.floor(gridHeight / 2);
    for (let x = 0; x < gridWidth; x++) {
      grid[x][midY] = 1;
    }
    for (let y = 0; y < gridHeight; y++) {
      grid[midX][y] = 1;
    }

    // Count neighbors in the 8 surrounding cells that are either roads or residential buildings.
    function countNeighbors(x, y) {
      let count = 0;
      for (let dx = -1; dx <= 1; dx++) {
        for (let dy = -1; dy <= 1; dy++) {
          if (dx === 0 && dy === 0) continue;
          const nx = x + dx, ny = y + dy;
          if (nx >= 0 && nx < gridWidth && ny >= 0 && ny < gridHeight) {
            if (grid[nx][ny] === 1 || grid[nx][ny] === 2) {
              count++;
            }
          }
        }
      }
      return count;
    }

    // Update the simulation.
    // For each empty cell that is next to a road or residential cell, there is a chance to develop into a residential building.
    function updateSimulation() {
      // Make a deep copy of the grid so that updates happen simultaneously.
      const newGrid = grid.map(col => col.slice());
      for (let x = 0; x < gridWidth; x++) {
        for (let y = 0; y < gridHeight; y++) {
          if (grid[x][y] === 0) {  // Only consider empty cells.
            const neighbors = countNeighbors(x, y);
            if (neighbors > 0) {
              // The chance to develop increases with the number of neighbors, up to a maximum chance of 50%.
              const chance = Math.min(0.2 * neighbors, 0.5);
              if (Math.random() < chance) {
                newGrid[x][y] = 2; // Develop as a residential building.
              }
            }
          }
        }
      }
      grid = newGrid;
    }

    // Render the grid to the canvas.
    function render() {
      for (let x = 0; x < gridWidth; x++) {
        for (let y = 0; y < gridHeight; y++) {
          let cellType = grid[x][y];
          switch (cellType) {
            case 0:
              ctx.fillStyle = "#e0e0e0"; // empty
              break;
            case 1:
              ctx.fillStyle = "#333333"; // road
              break;
            case 2:
              ctx.fillStyle = "#f4a460"; // residential
              break;
            default:
              ctx.fillStyle = "#e0e0e0";
          }
          ctx.fillRect(x * cellSize, y * cellSize, cellSize, cellSize);
        }
      }
    }

    // Main loop: update the simulation at a set interval, and render continuously.
    let lastUpdate = performance.now();
    const simulationInterval = 200; // milliseconds between simulation updates

    function loop(timestamp) {
      if (timestamp - lastUpdate > simulationInterval) {
        updateSimulation();
        lastUpdate = timestamp;
      }
      render();
      requestAnimationFrame(loop);
    }
    requestAnimationFrame(loop);

    // Allow user interaction: clicking on an empty cell adds a road,
    // clicking on an existing road removes it.
    canvas.addEventListener('click', (e) => {
      const rect = canvas.getBoundingClientRect();
      const mouseX = e.clientX - rect.left;
      const mouseY = e.clientY - rect.top;
      const gridX = Math.floor(mouseX / cellSize);
      const gridY = Math.floor(mouseY / cellSize);
      if (gridX >= 0 && gridX < gridWidth && gridY >= 0 && gridY < gridHeight) {
        // Only allow toggling if the cell is empty or already a road.
        if (grid[gridX][gridY] === 0) {
          grid[gridX][gridY] = 1; // add a road
        } else if (grid[gridX][gridY] === 1) {
          grid[gridX][gridY] = 0; // remove the road
        }
      }
    });
  </script>
</body>
</html>
