import os
from openai import OpenAI
from termcolor import colored
import json

# CONSTANTS
API_KEY = os.getenv("OPENAI_API_KEY")
MODEL = "o3-mini"
REASONING_EFFORT = "medium"
NUM_QUESTIONS = 10

def initialize_client():
    try:
        return OpenAI()
    except Exception as e:
        print(colored(f"Error initializing OpenAI client: {str(e)}", "red"))
        return None

def generate_research_questions(query):
    try:
        print(colored(f"Generating research questions for: {query}", "cyan"))
        
        client = initialize_client()
        if not client:
            return None

        system_prompt = f"""You are a curious researcher. Given a topic, generate exactly {NUM_QUESTIONS} 
        interesting and thought-provoking questions that would be fascinating to explore about that subject. 
        The questions should:
        - Range from fundamental to complex concepts
        - Cover different aspects and perspectives
        - Be specific and actionable for research
        - Encourage critical thinking
        you must return your response in json format with the key "questions" and a list of questions as the value.
        """

        response = client.chat.completions.create(
            model=MODEL,
            response_format={"type": "json_object"},
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": f"""Generate research questions about: {query}. 
                Return them in this exact format: {{"questions": ["question1", "question2", ...]}}"""}
            ]
        )

        # Parse the response as JSON
        try:
            result = json.loads(response.choices[0].message.content)
            return result.get("questions", [])
        except json.JSONDecodeError as e:
            print(colored(f"Error parsing JSON response: {str(e)}", "red"))
            return None

    except Exception as e:
        print(colored(f"Error generating research questions: {str(e)}", "red"))
        return None

def display_questions(questions):
    if not questions:
        print(colored("No questions were generated.", "red"))
        return

    print(colored("\n🔍 Research Questions Generated:", "green"))
    for i, question in enumerate(questions, 1):
        print(colored(f"\n{i}. {question}", "yellow"))

def main():
    try:
        print(colored("🤔 Deep Research Question Generator 🤔", "cyan"))
        print(colored("----------------------------------------", "cyan"))
        
        query = input(colored("Enter your research topic: ", "green"))
        print(colored("\nThinking...", "cyan"))
        
        questions = generate_research_questions(query)
        display_questions(questions)

    except KeyboardInterrupt:
        print(colored("\nOperation cancelled by user.", "yellow"))
    except Exception as e:
        print(colored(f"An unexpected error occurred: {str(e)}", "red"))

if __name__ == "__main__":
    main()