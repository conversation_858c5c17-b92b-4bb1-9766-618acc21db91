import os
from openai import OpenAI
import json
from termcolor import colored

# Constants
MODEL = "o3-mini"
REASONING_EFFORT = "medium"
SYSTEM_PROMPT = "You are a helpful coding assistant. Return your response as valid JSON with a 'code' field containing the implementation."
USER_PROMPT = "please build a simple space shooter game  in pygame"
OUTPUT_FILE = "output.py"

try:
    client = OpenAI()
    
    print(colored("Generating code...", "yellow"))
    
    response = client.chat.completions.create(
        model=MODEL,
        response_format={"type": "json_object"},
        reasoning_effort=REASONING_EFFORT,
        messages=[
            {
                "role": "system",
                "content": SYSTEM_PROMPT
            },
            {
                "role": "user", 
                "content": USER_PROMPT
            }
        ]
    )
    
    # Parse and print the JSON response
    response_json = json.loads(response.choices[0].message.content)

    
    # Save the code to a file
    try:
        with open(OUTPUT_FILE, "w", encoding="utf-8") as f:
            f.write(response_json["code"])
        print(colored(f"\nCode successfully saved to {OUTPUT_FILE}", "green"))
    except IOError as e:
        print(colored(f"Error saving file: {str(e)}", "red"))

except json.JSONDecodeError as e:
    print(colored(f"Error parsing JSON response: {str(e)}", "red"))
except Exception as e:
    print(colored(f"An error occurred: {str(e)}", "red"))