# Auto-Coder Series

A series of increasingly sophisticated Python scripts that leverage OpenAI's API to automatically generate, improve, and debug code.

## Overview

This project consists of three progressive implementations of an automatic code generator:

### 1. auto_coder_1.py

The basic implementation that:

- Makes a single API call to generate code
- Uses the o3-mini model
- Saves the generated code to `output.py`
- Includes basic error handling and colored console output
- Perfect for quick, one-off code generation tasks

### 2. auto_coder_2.py

Builds upon the first version by adding:

- Iterative code improvement capability
- Multiple iterations (default: 3)
- Saves each iteration in a separate file within an `iterations` folder
- Maintains code history between iterations
- Better for generating more refined code through multiple improvements

### 3. auto_coder_3.py

The most advanced implementation featuring:

- All features from previous versions
- Runtime error detection and automatic fixing
- Executes generated code and captures errors
- Makes up to 5 attempts to fix any runtime errors
- Terminates running programs after 5 seconds
- Limits error messages to 500 characters for better API interaction
- Perfect for generating working applications with automatic debugging

## Key Features

- **JSON Response Format**: All versions use JSON formatting for reliable code extraction
- **Error Handling**: Comprehensive error handling with informative colored output
- **UTF-8 Encoding**: Consistent UTF-8 encoding for file operations
- **Separation of Concerns**: Modular code structure in later versions
- **Progress Tracking**: Colored console output for monitoring progress
