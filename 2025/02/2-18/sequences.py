from termcolor import colored

def generate_complex_sequence(seed: int, length: int) -> list:
    """
    Generate a complex sequence of numbers starting from a seed value.
    
    Args:
        seed (int): The starting seed number
        length (int): Desired length of the sequence
        
    Returns:
        list: Generated sequence of numbers
    """
    try:
        print(colored(f"Generating sequence with seed {seed} and length {length}...", "cyan"))
        
        sequence = [seed]
        for i in range(length-1):
            # Generate next number using multiple mathematical operations
            last_num = sequence[-1]
            
            # Complex formula combining multiple operations
            next_num = (
                (last_num * 1.5) + 
                (last_num % 7) * 3 - 
                (last_num // 4) + 
                ((i + 1) * seed % 10) + 1  # Deterministic replacement for random
            )
            
            sequence.append(round(next_num))
            print(colored(f"Generated number {i+2}: {round(next_num)}", "green"))
            
        print(colored("Sequence generation complete!", "yellow"))
        return sequence
        
    except Exception as e:
        print(colored(f"Error generating sequence: {str(e)}", "red"))
        return []

if __name__ == "__main__":
    # Example usage
    sequence = generate_complex_sequence(seed=42, length=10)
    print(colored(f"\nFinal sequence: {sequence}", "magenta"))
