import os
import subprocess
from openai import OpenAI
import json
from termcolor import colored
import sys

# Constants
MODEL = "o3-mini"
REASONING_EFFORT = "medium"
SYSTEM_PROMPT = "You are a helpful coding assistant. Return your response as valid JSON with a 'code' field containing the implementation."
USER_PROMPT = "please build a fully featured calendar and personal management app using tkinter"
IMPROVEMENT_PROMPT = "Improve the following Python code by adding new features, refactoring, or fixing bugs while improving aesthetics(without using any external files). Return the complete improved code in JSON format.\n\nPrevious Code:\n```python\n{}\n```"
ERROR_FIX_PROMPT = "Fix the following Python code that generated this error:\n\nError Message:\n{}\n\nCode:\n```python\n{}\n```\nProvide the complete fixed code."
NUM_ITERATIONS = 20  # Number of iterations for general improvements
MAX_ERROR_FIX_ATTEMPTS = 5  # Maximum number of attempts to fix runtime errors
OUTPUT_FOLDER = "iterations"
OUTPUT_FILE_TEMPLATE = "output_{}.py"

def run_python_code(file_path):
    """Execute a Python file and return any error message. Terminate after 5 seconds if no errors."""
    try:
        print(colored(f"Running {file_path}...", "yellow"))
        # Start the process
        process = subprocess.Popen([sys.executable, file_path], 
                                 stdout=subprocess.PIPE, 
                                 stderr=subprocess.PIPE,
                                 text=True,
                                 encoding='utf-8')
        
        try:
            # Wait for 5 seconds or until process finishes
            stdout, stderr = process.communicate(timeout=5)
            
            if process.returncode != 0:
                # Limit error message to last 500 characters
                return stderr[-500:] if len(stderr) > 500 else stderr
            return None
            
        except subprocess.TimeoutExpired:
            # If timeout occurs (meaning game ran successfully for 5 seconds)
            print(colored("Game ran successfully for 5 seconds, terminating...", "green"))
            process.kill()  # Kill the game process
            process.communicate()  # Clean up the process
            return None
            
    except Exception as e:
        error_str = str(e)
        return error_str[-500:] if len(error_str) > 500 else error_str

def save_code_to_file(code, file_path):
    """Save generated code to a file."""
    try:
        with open(file_path, "w", encoding="utf-8") as f:
            f.write(code)
        print(colored(f"Code saved to {file_path}", "green"))
        return True
    except IOError as e:
        print(colored(f"Error saving file: {str(e)}", "red"))
        return False

def get_improved_code(client, code, error_message=None):
    """Get improved code from the API."""
    try:
        messages = [
            {"role": "system", "content": SYSTEM_PROMPT}
        ]
        
        if error_message:
            messages.append({
                "role": "user",
                "content": ERROR_FIX_PROMPT.format(error_message, code)
            })
        else:
            messages.append({
                "role": "user",
                "content": IMPROVEMENT_PROMPT.format(code)
            })

        response = client.chat.completions.create(
            model=MODEL,
            response_format={"type": "json_object"},
            reasoning_effort=REASONING_EFFORT,
            messages=messages
        )

        response_json = json.loads(response.choices[0].message.content)
        return response_json["code"]
    except Exception as e:
        print(colored(f"Error getting improved code: {str(e)}", "red"))
        return None

def main():
    try:
        client = OpenAI()
        os.makedirs(OUTPUT_FOLDER, exist_ok=True)

        # Initial code generation
        current_prompt = USER_PROMPT
        previous_code = None

        # Regular improvement iterations
        for iteration in range(1, NUM_ITERATIONS + 1):
            output_file = os.path.join(OUTPUT_FOLDER, OUTPUT_FILE_TEMPLATE.format(iteration))
            print(colored(f"\nIteration {iteration}: Generating code...", "yellow"))

            messages = [
                {"role": "system", "content": SYSTEM_PROMPT},
                {"role": "user", "content": current_prompt}
            ]
            
            if previous_code:
                messages.insert(1, {
                    "role": "user",
                    "content": IMPROVEMENT_PROMPT.format(previous_code)
                })
                print(colored("...improving code from previous iteration...", "cyan"))

            try:
                response = client.chat.completions.create(
                    model=MODEL,
                    response_format={"type": "json_object"},
                    reasoning_effort=REASONING_EFFORT,
                    messages=messages
                )

                response_json = json.loads(response.choices[0].message.content)
                generated_code = response_json["code"]

                if save_code_to_file(generated_code, output_file):
                    previous_code = generated_code
                    
                    # After each iteration, try to run the code and fix any errors
                    error_fix_count = 0
                    while error_fix_count < MAX_ERROR_FIX_ATTEMPTS:
                        error_message = run_python_code(output_file)
                        
                        if not error_message:
                            print(colored("Code executed successfully!", "green"))
                            break
                        
                        print(colored(f"Execution error detected: {error_message}", "red"))
                        print(colored(f"Attempting to fix error (attempt {error_fix_count + 1}/{MAX_ERROR_FIX_ATTEMPTS})...", "yellow"))
                        
                        fixed_code = get_improved_code(client, previous_code, error_message)
                        if fixed_code:
                            if save_code_to_file(fixed_code, output_file):
                                previous_code = fixed_code
                        
                        error_fix_count += 1
                    
                    current_prompt = "Improve the code from the previous iteration"

            except json.JSONDecodeError as e:
                print(colored(f"Error parsing JSON response in iteration {iteration}: {str(e)}", "red"))
                break
            except Exception as e:
                print(colored(f"An error occurred in iteration {iteration}: {str(e)}", "red"))
                break

        print(colored("\nCode generation and improvement process completed.", "magenta"))

    except Exception as e:
        print(colored(f"An error occurred during the process: {str(e)}", "red"))

if __name__ == "__main__":
    main() 