import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import calendar
import datetime


class Tooltip:
    """A simple tooltip for a given widget."""
    def __init__(self, widget, text, delay=500):
        self.widget = widget
        self.text = text
        self.delay = delay  # milliseconds
        self.tooltip_window = None
        self.after_id = None
        self.widget.bind('<Enter>', self.schedule)
        self.widget.bind('<Leave>', self.hide)
        self.widget.bind('<ButtonPress>', self.hide)

    def schedule(self, event=None):
        self.unschedule()
        self.after_id = self.widget.after(self.delay, self.show)

    def unschedule(self):
        if self.after_id:
            self.widget.after_cancel(self.after_id)
            self.after_id = None

    def show(self, event=None):
        if self.tooltip_window:
            return
        # Position tooltip near widget
        x = self.widget.winfo_rootx() + 20
        y = self.widget.winfo_rooty() + self.widget.winfo_height() + 2
        self.tooltip_window = tw = tk.Toplevel(self.widget)
        tw.wm_overrideredirect(True)
        tw.wm_geometry(f'+{x}+{y}')
        label = tk.Label(tw, text=self.text, background='lightyellow', relief='solid', borderwidth=1,
                         font=('Arial', 10), padx=2, pady=1)
        label.pack()

    def hide(self, event=None):
        self.unschedule()
        if self.tooltip_window:
            self.tooltip_window.destroy()
            self.tooltip_window = None


class CalendarAndTaskApp:
    def __init__(self, root):
        self.root = root
        self.root.title('Calendar & Personal Management App')
        self.root.geometry('900x650')

        # Setup menubar
        self.create_menubar()

        # Initialize calendar data
        today = datetime.date.today()
        self.today = today
        self.current_year = today.year
        self.current_month = today.month
        # Dictionary for calendar events: key 'YYYY-MM-DD' -> event note
        self.events = {}

        # Initialize task management data
        # Each task is a dict containing: text, completion status, and importance flag
        self.tasks = []
        self.visible_task_indices = []  # mapping from displayed listbox to self.tasks indices
        self.task_filter = tk.StringVar(value='All')

        # Create notebook with two tabs: Calendar and Personal Management
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(expand=True, fill='both')

        self.create_calendar_tab()
        self.create_task_tab()

        # Bind additional shortcuts
        self.bind_shortcuts()

        # Bind window closing event
        self.root.protocol('WM_DELETE_WINDOW', self.on_exit)

    def create_menubar(self):
        menubar = tk.Menu(self.root)

        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        file_menu.add_command(label='Exit', command=self.on_exit, accelerator='Ctrl+Q')
        menubar.add_cascade(label='File', menu=file_menu)

        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        help_menu.add_command(label='About', command=self.show_about, accelerator='F1')
        menubar.add_cascade(label='Help', menu=help_menu)

        self.root.config(menu=menubar)

    def bind_shortcuts(self):
        # Bind shortcuts for calendar navigation
        self.root.bind('<Left>', lambda event: self.prev_month())
        self.root.bind('<Right>', lambda event: self.next_month())
        self.root.bind('t', lambda event: self.go_to_today())
        self.root.bind('<Control-q>', lambda event: self.on_exit())
        self.root.bind('<F1>', lambda event: self.show_about())

        # Bind shortcuts for tasks
        self.root.bind('<Control-Delete>', lambda event: self.remove_task())

    def show_about(self):
        about_text = (
            'Calendar & Personal Management App\n'
            'Version 3.1\n\n'
            'New Features and Improvements:\n'
            '- Keyboard shortcuts for navigation (Left/Right/T, Ctrl+Q, F1)\n'
            '- Task filtering with proper index mapping\n'
            '- Ability to mark tasks as important (via context menu)\n'
            '- Right-click in calendar cells for event details\n'
            '- Clear All Tasks button\n'
            '- Refactoring, improved UI, and code readability\n'
        )
        messagebox.showinfo('About', about_text)

    def on_exit(self):
        if messagebox.askokcancel('Quit', 'Do you really want to exit?'):
            self.root.destroy()

    # ----------------- CALENDAR TAB -----------------
    def create_calendar_tab(self):
        self.calendar_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.calendar_frame, text='Calendar')

        # Navigation header: Previous, Today, Next and Month-Year label
        nav_frame = ttk.Frame(self.calendar_frame)
        nav_frame.pack(pady=10)

        self.prev_button = ttk.Button(nav_frame, text='Previous Month', command=self.prev_month)
        self.prev_button.grid(row=0, column=0, padx=5)

        self.today_button = ttk.Button(nav_frame, text='Today', command=self.go_to_today)
        self.today_button.grid(row=0, column=1, padx=5)

        self.month_year_label = ttk.Label(nav_frame, text='', font=('Arial', 12, 'bold'))
        self.month_year_label.grid(row=0, column=2, padx=10)

        self.next_button = ttk.Button(nav_frame, text='Next Month', command=self.next_month)
        self.next_button.grid(row=0, column=3, padx=5)

        # Frame for calendar grid
        self.days_frame = ttk.Frame(self.calendar_frame)
        self.days_frame.pack(pady=10, fill='both', expand=True)

        self.draw_calendar()

    def draw_calendar(self):
        """Draws the calendar grid with day headers, event indicators, tooltips, and click events."""
        # Clear previous grid
        for widget in self.days_frame.winfo_children():
            widget.destroy()

        # Update header label
        month_name = calendar.month_name[self.current_month]
        self.month_year_label.config(text=f'{month_name} {self.current_year}')

        # Create day headers (Mon to Sun)
        days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
        for idx, day in enumerate(days):
            header = ttk.Label(self.days_frame, text=day, borderwidth=1, relief='raised', width=12, anchor='center')
            header.grid(row=0, column=idx, padx=1, pady=1, sticky='nsew')

        # Create weeks for the month
        # calendar.Calendar uses Monday as the first day by default
        cal = calendar.Calendar(firstweekday=0)
        month_weeks = cal.monthdayscalendar(self.current_year, self.current_month)

        # Create day cells
        for r, week in enumerate(month_weeks, start=1):
            for c, day in enumerate(week):
                if day == 0:
                    day_str = ''
                else:
                    day_str = str(day)
                day_label = ttk.Label(self.days_frame, text=day_str, borderwidth=1, relief='ridge', width=12, anchor='center')

                # Highlight today's date
                if day and (self.current_year == self.today.year and self.current_month == self.today.month and day == self.today.day):
                    day_label.config(background='lightgreen')

                # Check for event on this date
                if day:
                    date_key = f'{self.current_year:04d}-{self.current_month:02d}-{day:02d}'
                    event_note = self.events.get(date_key, '').strip()
                    if event_note:
                        day_label.config(background='lightyellow')
                        Tooltip(day_label, event_note)

                    # Bind left-click for editing/adding event
                    day_label.bind('<Button-1>', lambda e, d=day: self.edit_event(d))

                    # Bind right-click for showing event details
                    day_label.bind('<Button-3>', lambda e, d=day: self.show_event_details(d))

                day_label.grid(row=r, column=c, padx=1, pady=1, sticky='nsew')

        # Make all columns expand equally
        for i in range(7):
            self.days_frame.columnconfigure(i, weight=1)

    def edit_event(self, day):
        """Allows user to add or edit an event note for a given day."""
        date_key = f'{self.current_year:04d}-{self.current_month:02d}-{day:02d}'
        current_note = self.events.get(date_key, '')
        new_note = simpledialog.askstring('Edit Event', f'Enter event note for {date_key}:',
                                          initialvalue=current_note, parent=self.root)
        if new_note is None:
            return  # User cancelled
        self.events[date_key] = new_note.strip()
        self.draw_calendar()

    def show_event_details(self, day):
        """Shows event details in a popup on right-click, if available."""
        date_key = f'{self.current_year:04d}-{self.current_month:02d}-{day:02d}'
        event_note = self.events.get(date_key, '').strip()
        if event_note:
            messagebox.showinfo(f'Event on {date_key}', event_note)
        else:
            messagebox.showinfo('No Event', f'No event is set for {date_key}.\nLeft-click to add one.')

    def prev_month(self):
        if self.current_month == 1:
            self.current_month = 12
            self.current_year -= 1
        else:
            self.current_month -= 1
        self.draw_calendar()

    def next_month(self):
        if self.current_month == 12:
            self.current_month = 1
            self.current_year += 1
        else:
            self.current_month += 1
        self.draw_calendar()

    def go_to_today(self):
        self.current_year = self.today.year
        self.current_month = self.today.month
        self.draw_calendar()

    # --------------- TASK MANAGEMENT TAB ---------------
    def create_task_tab(self):
        self.task_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.task_frame, text='Personal Management')

        header_label = ttk.Label(self.task_frame, text='Your Tasks:', font=('Arial', 14, 'bold'))
        header_label.pack(pady=10)

        # Frame for new task entry
        task_entry_frame = ttk.Frame(self.task_frame)
        task_entry_frame.pack(pady=5)

        self.task_entry = ttk.Entry(task_entry_frame, width=60)
        self.task_entry.pack(side='left', padx=5)
        self.task_entry.bind('<Return>', lambda event: self.add_task())

        add_button = ttk.Button(task_entry_frame, text='Add Task', command=self.add_task)
        add_button.pack(side='left', padx=5)

        # Task filter dropdown
        filter_frame = ttk.Frame(self.task_frame)
        filter_frame.pack(pady=5)
        ttk.Label(filter_frame, text='Filter Tasks:').pack(side='left', padx=5)
        filter_options = ['All', 'Pending', 'Completed']
        filter_menu = ttk.Combobox(filter_frame, values=filter_options, textvariable=self.task_filter,
                                    state='readonly', width=10)
        filter_menu.pack(side='left')
        filter_menu.bind('<<ComboboxSelected>>', lambda e: self.update_task_list())

        # Frame for the Listbox and its scrollbar
        list_frame = ttk.Frame(self.task_frame)
        list_frame.pack(pady=5, fill='both', expand=True)

        self.task_listbox = tk.Listbox(list_frame, font=('Arial', 12), selectmode='extended')
        self.task_listbox.pack(side='left', fill='both', expand=True, padx=5, pady=5)

        scrollbar = ttk.Scrollbar(list_frame, orient='vertical', command=self.task_listbox.yview)
        scrollbar.pack(side='right', fill='y')
        self.task_listbox.config(yscrollcommand=scrollbar.set)

        # Bind double-click to toggle task completion
        self.task_listbox.bind('<Double-Button-1>', self.toggle_task_completion_event)

        # Bind Shift+Double-Click to toggle importance
        self.task_listbox.bind('<Shift-Double-Button-1>', self.shift_toggle_importance)

        # Bind right-click for context menu
        self.task_listbox.bind('<Button-3>', self.show_task_context_menu)

        # Task control buttons frame
        btn_frame = ttk.Frame(self.task_frame)
        btn_frame.pack(pady=10)

        remove_button = ttk.Button(btn_frame, text='Remove Selected Task(s)', command=self.remove_task)
        remove_button.grid(row=0, column=0, padx=5, pady=2)

        clear_completed_button = ttk.Button(btn_frame, text='Clear Completed Tasks', command=self.clear_completed_tasks)
        clear_completed_button.grid(row=0, column=1, padx=5, pady=2)

        clear_all_button = ttk.Button(btn_frame, text='Clear All Tasks', command=self.clear_all_tasks)
        clear_all_button.grid(row=0, column=2, padx=5, pady=2)

        edit_button = ttk.Button(btn_frame, text='Edit Task', command=self.edit_task)
        edit_button.grid(row=0, column=3, padx=5, pady=2)

    def add_task(self):
        task_text = self.task_entry.get().strip()
        if not task_text:
            messagebox.showwarning('Input Error', 'Please enter a task.')
            return
        # Append a new task with default values
        self.tasks.append({'text': task_text, 'completed': False, 'important': False})
        self.task_entry.delete(0, 'end')
        self.update_task_list()

    def update_task_list(self):
        """Refreshes the task listBox and maintains mapping to the internal tasks list."""
        self.task_listbox.delete(0, 'end')
        self.visible_task_indices = []
        filter_option = self.task_filter.get()
        for idx, task in enumerate(self.tasks):
            if filter_option == 'Pending' and task['completed']:
                continue
            if filter_option == 'Completed' and not task['completed']:
                continue
            display_text = task['text']
            if task['important']:
                display_text = '(!) ' + display_text
            if task['completed']:
                display_text = '[Completed] ' + display_text
            self.task_listbox.insert('end', display_text)
            self.visible_task_indices.append(idx)

    def get_selected_task_index(self):
        """Maps the selected listbox index to the corresponding index in self.tasks."""
        selection = self.task_listbox.curselection()
        if not selection:
            return None
        listbox_index = selection[0]
        try:
            task_index = self.visible_task_indices[listbox_index]
            return task_index
        except IndexError:
            return None

    def toggle_task_completion_event(self, event=None):
        """Toggles the completed status for the selected task (via double-click)."""
        task_index = self.get_selected_task_index()
        if task_index is None:
            return
        self.tasks[task_index]['completed'] = not self.tasks[task_index]['completed']
        self.update_task_list()
        self.reselect_task(task_index)

    def shift_toggle_importance(self, event=None):
        """Toggles the importance of a task using Shift+Double-Click."""
        task_index = self.get_selected_task_index()
        if task_index is None:
            return
        self.tasks[task_index]['important'] = not self.tasks[task_index]['important']
        self.update_task_list()
        self.reselect_task(task_index)

    def reselect_task(self, task_index):
        """Reselects a task in the listbox if it is still visible after an update."""
        if task_index in self.visible_task_indices:
            new_index = self.visible_task_indices.index(task_index)
            self.task_listbox.select_set(new_index)

    def edit_task(self):
        """Allows editing of a selected task. Only one task can be edited at a time."""
        selection = self.task_listbox.curselection()
        if not selection or len(selection) != 1:
            messagebox.showwarning('Selection Error', 'Select a single task to edit.')
            return
        task_index = self.get_selected_task_index()
        if task_index is None:
            return
        current_text = self.tasks[task_index]['text']
        new_text = simpledialog.askstring('Edit Task', 'Modify your task:',
                                          initialvalue=current_text, parent=self.root)
        if new_text is None or new_text.strip() == '':
            return
        self.tasks[task_index]['text'] = new_text.strip()
        self.update_task_list()
        self.reselect_task(task_index)

    def remove_task(self):
        selections = list(self.task_listbox.curselection())
        if not selections:
            messagebox.showwarning('Selection Error', 'Select at least one task to remove.')
            return
        # Remove tasks based on visible indices mapping (in reverse order)
        indices_to_remove = [self.visible_task_indices[i] for i in selections]
        for task_idx in sorted(indices_to_remove, reverse=True):
            try:
                del self.tasks[task_idx]
            except IndexError:
                continue
        self.update_task_list()

    def clear_completed_tasks(self):
        self.tasks = [task for task in self.tasks if not task['completed']]
        self.update_task_list()

    def clear_all_tasks(self):
        if messagebox.askyesno('Clear All', 'Are you sure you want to clear all tasks?'):
            self.tasks.clear()
            self.update_task_list()

    def show_task_context_menu(self, event):
        try:
            # Select task under pointer
            index = self.task_listbox.nearest(event.y)
            self.task_listbox.selection_clear(0, 'end')
            self.task_listbox.selection_set(index)
            task_index = self.visible_task_indices[index]
            context_menu = tk.Menu(self.root, tearoff=0)
            context_menu.add_command(label='Toggle Completed', command=lambda: self.toggle_task_completion_event())
            context_menu.add_command(label='Toggle Importance', command=lambda: self.toggle_task_importance(task_index))
            context_menu.add_command(label='Edit Task', command=self.edit_task)
            context_menu.add_command(label='Delete Task', command=self.remove_task)
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            if 'context_menu' in locals():
                context_menu.grab_release()

    def toggle_task_importance(self, task_index):
        if task_index is None:
            return
        self.tasks[task_index]['important'] = not self.tasks[task_index]['important']
        self.update_task_list()
        self.reselect_task(task_index)


def main():
    root = tk.Tk()
    # Optionally set a default font for the application
    default_font = ('Arial', 11)
    root.option_add('*Font', default_font)
    app = CalendarAndTaskApp(root)
    root.mainloop()


if __name__ == '__main__':
    main()