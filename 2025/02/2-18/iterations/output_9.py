import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import calendar
import datetime
from dataclasses import dataclass, field
from typing import List, Dict


class Tooltip:
    """A simple tooltip for a given widget."""

    def __init__(self, widget: tk.Widget, text: str, delay: int = 500) -> None:
        self.widget = widget
        self.text = text
        self.delay = delay  # milliseconds
        self.tooltip_window = None
        self.after_id = None
        self.widget.bind('<Enter>', self.schedule)
        self.widget.bind('<Leave>', self.hide)
        self.widget.bind('<ButtonPress>', self.hide)

    def schedule(self, event=None) -> None:
        self.unschedule()
        self.after_id = self.widget.after(self.delay, self.show)

    def unschedule(self) -> None:
        if self.after_id:
            self.widget.after_cancel(self.after_id)
            self.after_id = None

    def show(self, event=None) -> None:
        if self.tooltip_window:
            return
        # Position tooltip near widget
        x = self.widget.winfo_rootx() + 20
        y = self.widget.winfo_rooty() + self.widget.winfo_height() + 2
        self.tooltip_window = tw = tk.Toplevel(self.widget)
        tw.wm_overrideredirect(True)
        tw.wm_geometry(f'+{x}+{y}')
        label = tk.Label(tw, text=self.text, background='lightyellow', relief='solid', borderwidth=1,
                         font=('Arial', 10), padx=2, pady=1)
        label.pack()

    def hide(self, event=None) -> None:
        self.unschedule()
        if self.tooltip_window:
            self.tooltip_window.destroy()
            self.tooltip_window = None


@dataclass
class Task:
    text: str
    completed: bool = False
    important: bool = False


class CalendarAndTaskApp:
    def __init__(self, root: tk.Tk) -> None:
        self.root = root
        self.root.title('Calendar & Personal Management App')
        self.root.geometry('900x650')

        # Default light mode, with ability to toggle dark mode
        self.dark_mode = False
        self.style = ttk.Style()
        self.update_theme()

        # Setup menubar
        self.create_menubar()

        # Initialize calendar data
        self.today = datetime.date.today()
        self.current_year = self.today.year
        self.current_month = self.today.month
        # Dictionary for calendar events: key 'YYYY-MM-DD' -> event note
        self.events: Dict[str, str] = {}

        # Initialize task management data
        self.tasks: List[Task] = []
        self.visible_task_indices: List[int] = []  # Mapping for listbox indices to self.tasks indices
        self.task_filter = tk.StringVar(value='All')
        self.search_var = tk.StringVar(value='')
        self.search_var.trace_add('write', lambda *args: self.update_task_list())

        # Create notebook with two tabs: Calendar and Personal Management
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(expand=True, fill='both')

        self.create_calendar_tab()
        self.create_task_tab()

        # Bind additional shortcuts
        self.bind_shortcuts()

        # Handle window close event
        self.root.protocol('WM_DELETE_WINDOW', self.on_exit)

    def update_theme(self) -> None:
        """Updates the UI theme based on dark_mode flag."""
        if self.dark_mode:
            bg_color = '#2e2e2e'
            fg_color = '#ffffff'
            self.style.theme_use('clam')  # Using clam as base for dark mode
            self.style.configure('.', background=bg_color, foreground=fg_color, fieldbackground=bg_color)
            self.root.configure(bg=bg_color)
        else:
            bg_color = '#f0f0f0'
            fg_color = '#000000'
            self.style.theme_use('default')
            self.style.configure('.', background=bg_color, foreground=fg_color, fieldbackground=bg_color)
            self.root.configure(bg=bg_color)

    def create_menubar(self) -> None:
        menubar = tk.Menu(self.root)

        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        file_menu.add_command(label='Exit', command=self.on_exit, accelerator='Ctrl+Q')
        menubar.add_cascade(label='File', menu=file_menu)

        # View menu for theme toggle
        view_menu = tk.Menu(menubar, tearoff=0)
        view_menu.add_command(label='Toggle Dark Mode', command=self.toggle_dark_mode)
        menubar.add_cascade(label='View', menu=view_menu)

        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        help_menu.add_command(label='About', command=self.show_about, accelerator='F1')
        menubar.add_cascade(label='Help', menu=help_menu)

        self.root.config(menu=menubar)

    def toggle_dark_mode(self) -> None:
        self.dark_mode = not self.dark_mode
        self.update_theme()
        self.draw_calendar()
        self.update_task_list()

    def bind_shortcuts(self) -> None:
        # Calendar navigation shortcuts
        self.root.bind('<Left>', lambda event: self.prev_month())
        self.root.bind('<Right>', lambda event: self.next_month())
        self.root.bind('t', lambda event: self.go_to_today())
        self.root.bind('<Control-q>', lambda event: self.on_exit())
        self.root.bind('<F1>', lambda event: self.show_about())

        # Task management shortcut
        self.root.bind('<Control-Delete>', lambda event: self.remove_task())

    def show_about(self) -> None:
        about_text = (
            'Calendar & Personal Management App\n'
            'Version 5.0\n\n'
            'New Features and Improvements:\n'
            '- Keyboard shortcuts for navigation (Left/Right/T, Ctrl+Q, F1)\n'
            '- Enhanced calendar context menu for events (View, Edit, Delete, Add)\n'
            '- Live search and filtering for tasks with count display\n'
            '- Mark tasks as important via context menu or Shift+Double-Click\n'
            '- Improved UI theming (Dark/Light modes)\n'
            '- Clear Completed and Clear All Tasks buttons added\n'
            '- Added Clear Calendar Events button\n'
            '- Refactored code with dataclasses for Task management\n'
            '- Code refactoring for better readability and maintainability\n'
        )
        messagebox.showinfo('About', about_text)

    def on_exit(self) -> None:
        if messagebox.askokcancel('Quit', 'Do you really want to exit?'):
            self.root.destroy()

    # ------------------ CALENDAR TAB -------------------
    def create_calendar_tab(self) -> None:
        self.calendar_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.calendar_frame, text='Calendar')

        # Navigation header with Previous, Today, Next buttons and month-year label
        nav_frame = ttk.Frame(self.calendar_frame)
        nav_frame.pack(pady=10)

        self.prev_button = ttk.Button(nav_frame, text='Previous Month', command=self.prev_month)
        self.prev_button.grid(row=0, column=0, padx=5)

        self.today_button = ttk.Button(nav_frame, text='Today', command=self.go_to_today)
        self.today_button.grid(row=0, column=1, padx=5)

        self.month_year_label = ttk.Label(nav_frame, text='', font=('Arial', 12, 'bold'))
        self.month_year_label.grid(row=0, column=2, padx=10)

        self.next_button = ttk.Button(nav_frame, text='Next Month', command=self.next_month)
        self.next_button.grid(row=0, column=3, padx=5)

        # Additional Calendar Control: Clear All Events
        clear_events_button = ttk.Button(nav_frame, text='Clear All Events', command=self.clear_all_events)
        clear_events_button.grid(row=0, column=4, padx=5)

        # Calendar grid frame
        self.days_frame = ttk.Frame(self.calendar_frame)
        self.days_frame.pack(pady=10, fill='both', expand=True)

        self.draw_calendar()

    def draw_calendar(self) -> None:
        """Draws the calendar grid with day headers, event indicators, and context menu support for events."""
        for widget in self.days_frame.winfo_children():
            widget.destroy()

        # Update header label
        month_name = calendar.month_name[self.current_month]
        self.month_year_label.config(text=f'{month_name} {self.current_year}')

        # Create day headers (Mon through Sun)
        headers = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
        for idx, header_text in enumerate(headers):
            header = ttk.Label(self.days_frame, text=header_text, borderwidth=1, relief='raised', width=12, anchor='center')
            header.grid(row=0, column=idx, padx=1, pady=1, sticky='nsew')

        # Build calendar grid for the month
        cal = calendar.Calendar(firstweekday=0)  # Monday as first day
        month_weeks = cal.monthdayscalendar(self.current_year, self.current_month)

        for row, week in enumerate(month_weeks, start=1):
            for col, day in enumerate(week):
                text = str(day) if day != 0 else ''
                day_label = ttk.Label(self.days_frame, text=text, borderwidth=1, relief='ridge', width=12, anchor='center')

                if day:
                    date_key = f'{self.current_year:04d}-{self.current_month:02d}-{day:02d}'
                    event_note = self.events.get(date_key, '').strip()

                    # Highlight today's date
                    if (self.current_year == self.today.year and self.current_month == self.today.month and day == self.today.day):
                        day_label.config(background='lightgreen')

                    # If an event exists, change background and add tooltip
                    if event_note:
                        day_label.config(background='lightyellow')
                        Tooltip(day_label, event_note)

                    # Bind left-click to add/edit event
                    day_label.bind('<Button-1>', lambda event, d=day: self.edit_event(d))
                    # Bind right-click to open a context menu for event actions
                    day_label.bind('<Button-3>', lambda event, d=day: self.calendar_context_menu(event, d))

                day_label.grid(row=row, column=col, padx=1, pady=1, sticky='nsew')

        # Ensure columns expand equally
        for col in range(7):
            self.days_frame.columnconfigure(col, weight=1)

    def calendar_context_menu(self, event, day: int) -> None:
        """Displays a context menu on right-click in a calendar cell with various event actions."""
        date_key = f'{self.current_year:04d}-{self.current_month:02d}-{day:02d}'
        event_note = self.events.get(date_key, '').strip()
        menu = tk.Menu(self.root, tearoff=0)
        if event_note:
            menu.add_command(label='View Event', command=lambda d=day: self.show_event_details(d))
            menu.add_command(label='Edit Event', command=lambda d=day: self.edit_event(d))
            menu.add_command(label='Delete Event', command=lambda d=day: self.delete_event(d))
        else:
            menu.add_command(label='Add Event', command=lambda d=day: self.edit_event(d))
        try:
            menu.tk_popup(event.x_root, event.y_root)
        finally:
            menu.grab_release()

    def edit_event(self, day: int) -> None:
        """Allows user to add or edit an event note for a given day. An empty note deletes the event."""
        date_key = f'{self.current_year:04d}-{self.current_month:02d}-{day:02d}'
        current_note = self.events.get(date_key, '')
        new_note = simpledialog.askstring('Edit Event', f'Enter event note for {date_key}:',
                                          initialvalue=current_note, parent=self.root)
        if new_note is None:
            return  # Cancelled by user
        new_note = new_note.strip()
        if new_note:
            self.events[date_key] = new_note
        else:
            self.events.pop(date_key, None)
        self.draw_calendar()

    def delete_event(self, day: int) -> None:
        """Deletes an event for the given day after confirmation."""
        date_key = f'{self.current_year:04d}-{self.current_month:02d}-{day:02d}'
        if date_key in self.events:
            if messagebox.askyesno('Delete Event', f'Are you sure you want to delete the event for {date_key}?'):
                del self.events[date_key]
                self.draw_calendar()
        else:
            messagebox.showinfo('No Event', f'No event exists for {date_key}.')

    def show_event_details(self, day: int) -> None:
        """Shows event details in a popup."""
        date_key = f'{self.current_year:04d}-{self.current_month:02d}-{day:02d}'
        event_note = self.events.get(date_key, '').strip()
        if event_note:
            messagebox.showinfo(f'Event on {date_key}', event_note)
        else:
            messagebox.showinfo('No Event', f'No event is set for {date_key}.')

    def clear_all_events(self) -> None:
        """Clears all calendar events after confirmation."""
        if messagebox.askyesno('Clear Events', 'Are you sure you want to clear all calendar events?'):
            self.events.clear()
            self.draw_calendar()

    def prev_month(self) -> None:
        """Navigate to the previous month."""
        if self.current_month == 1:
            self.current_month = 12
            self.current_year -= 1
        else:
            self.current_month -= 1
        self.draw_calendar()

    def next_month(self) -> None:
        """Navigate to the next month."""
        if self.current_month == 12:
            self.current_month = 1
            self.current_year += 1
        else:
            self.current_month += 1
        self.draw_calendar()

    def go_to_today(self) -> None:
        """Sets the calendar view to today's date."""
        self.current_year = self.today.year
        self.current_month = self.today.month
        self.draw_calendar()

    # ------------------ TASK MANAGEMENT TAB -------------------
    def create_task_tab(self) -> None:
        self.task_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.task_frame, text='Personal Management')

        header_label = ttk.Label(self.task_frame, text='Your Tasks:', font=('Arial', 14, 'bold'))
        header_label.pack(pady=10)

        # Frame for new task entry
        task_entry_frame = ttk.Frame(self.task_frame)
        task_entry_frame.pack(pady=5)
        
        self.task_entry = ttk.Entry(task_entry_frame, width=60)
        self.task_entry.pack(side='left', padx=5)
        self.task_entry.bind('<Return>', lambda event: self.add_task())

        add_button = ttk.Button(task_entry_frame, text='Add Task', command=self.add_task)
        add_button.pack(side='left', padx=5)

        # Search field for tasks
        search_frame = ttk.Frame(self.task_frame)
        search_frame.pack(pady=5)
        ttk.Label(search_frame, text='Search Tasks:').pack(side='left', padx=5)
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=30)
        search_entry.pack(side='left')

        # Task filter dropdown
        filter_frame = ttk.Frame(self.task_frame)
        filter_frame.pack(pady=5)
        ttk.Label(filter_frame, text='Filter Tasks:').pack(side='left', padx=5)
        filter_options = ['All', 'Pending', 'Completed']
        filter_menu = ttk.Combobox(filter_frame, values=filter_options, textvariable=self.task_filter,
                                    state='readonly', width=10)
        filter_menu.pack(side='left')
        filter_menu.bind('<<ComboboxSelected>>', lambda e: self.update_task_list())

        # Frame for the Listbox and scrollbar
        list_frame = ttk.Frame(self.task_frame)
        list_frame.pack(pady=5, fill='both', expand=True)

        self.task_listbox = tk.Listbox(list_frame, font=('Arial', 12), selectmode='extended')
        self.task_listbox.pack(side='left', fill='both', expand=True, padx=5, pady=5)

        scrollbar = ttk.Scrollbar(list_frame, orient='vertical', command=self.task_listbox.yview)
        scrollbar.pack(side='right', fill='y')
        self.task_listbox.config(yscrollcommand=scrollbar.set)

        # Bind events for task interactions
        self.task_listbox.bind('<Double-Button-1>', self.toggle_task_completion_event)
        self.task_listbox.bind('<Shift-Double-Button-1>', self.shift_toggle_importance)
        self.task_listbox.bind('<Button-3>', self.show_task_context_menu)

        # Label to show task count
        self.task_count_label = ttk.Label(self.task_frame, text='Total Tasks: 0  |  Visible: 0')
        self.task_count_label.pack(pady=5)

        # Control buttons frame for tasks
        btn_frame = ttk.Frame(self.task_frame)
        btn_frame.pack(pady=10)

        remove_button = ttk.Button(btn_frame, text='Remove Selected Task(s)', command=self.remove_task)
        remove_button.grid(row=0, column=0, padx=5, pady=2)

        clear_completed_button = ttk.Button(btn_frame, text='Clear Completed Tasks', command=self.clear_completed_tasks)
        clear_completed_button.grid(row=0, column=1, padx=5, pady=2)

        clear_all_button = ttk.Button(btn_frame, text='Clear All Tasks', command=self.clear_all_tasks)
        clear_all_button.grid(row=0, column=2, padx=5, pady=2)

        edit_button = ttk.Button(btn_frame, text='Edit Task', command=self.edit_task)
        edit_button.grid(row=0, column=3, padx=5, pady=2)

    def add_task(self) -> None:
        task_text = self.task_entry.get().strip()
        if not task_text:
            messagebox.showwarning('Input Error', 'Please enter a task.')
            return
        # Append a new task with default values
        self.tasks.append(Task(text=task_text))
        self.task_entry.delete(0, 'end')
        self.update_task_list()

    def update_task_list(self) -> None:
        """Refreshes the task listbox based on current filters and search criteria, and updates task count."""
        self.task_listbox.delete(0, 'end')
        self.visible_task_indices = []
        filter_option = self.task_filter.get()
        search_text = self.search_var.get().strip().lower()
        
        for idx, task in enumerate(self.tasks):
            # Apply status filtering
            if filter_option == 'Pending' and task.completed:
                continue
            if filter_option == 'Completed' and not task.completed:
                continue
            # Apply search filter
            if search_text and search_text not in task.text.lower():
                continue
            display_text = task.text
            if task.important:
                display_text = '(!) ' + display_text
            if task.completed:
                display_text = '[Completed] ' + display_text
            self.task_listbox.insert('end', display_text)
            self.visible_task_indices.append(idx)
        
        self.task_count_label.config(text=f'Total Tasks: {len(self.tasks)}  |  Visible: {len(self.visible_task_indices)}')

    def get_selected_task_index(self) -> int:
        """Maps the selected listbox index to the corresponding index in self.tasks."""
        selection = self.task_listbox.curselection()
        if not selection:
            return -1
        listbox_index = selection[0]
        try:
            return self.visible_task_indices[listbox_index]
        except IndexError:
            return -1

    def toggle_task_completion_event(self, event=None) -> None:
        """Toggles the completion status for the selected task."""
        task_index = self.get_selected_task_index()
        if task_index == -1:
            return
        self.tasks[task_index].completed = not self.tasks[task_index].completed
        self.update_task_list()
        self.reselect_task(task_index)

    def shift_toggle_importance(self, event=None) -> None:
        """Toggles the importance flag for the selected task."""
        task_index = self.get_selected_task_index()
        if task_index == -1:
            return
        self.tasks[task_index].important = not self.tasks[task_index].important
        self.update_task_list()
        self.reselect_task(task_index)

    def reselect_task(self, task_index: int) -> None:
        """Reselects a task in the listbox if it is still visible after an update."""
        if task_index in self.visible_task_indices:
            new_index = self.visible_task_indices.index(task_index)
            self.task_listbox.selection_set(new_index)

    def edit_task(self) -> None:
        """Allows editing of the selected task. Only one task can be edited at a time."""
        selection = self.task_listbox.curselection()
        if not selection or len(selection) != 1:
            messagebox.showwarning('Selection Error', 'Select a single task to edit.')
            return
        task_index = self.get_selected_task_index()
        if task_index == -1:
            return
        current_text = self.tasks[task_index].text
        new_text = simpledialog.askstring('Edit Task', 'Modify your task:',
                                          initialvalue=current_text, parent=self.root)
        if new_text is None or new_text.strip() == '':
            return
        self.tasks[task_index].text = new_text.strip()
        self.update_task_list()
        self.reselect_task(task_index)

    def remove_task(self) -> None:
        """Removes selected tasks from the task list."""
        selections = list(self.task_listbox.curselection())
        if not selections:
            messagebox.showwarning('Selection Error', 'Select at least one task to remove.')
            return
        indices_to_remove = [self.visible_task_indices[i] for i in selections]
        # Remove tasks starting from the highest index to maintain integrity
        for idx in sorted(indices_to_remove, reverse=True):
            try:
                del self.tasks[idx]
            except IndexError:
                continue
        self.update_task_list()

    def clear_completed_tasks(self) -> None:
        """Clears all tasks marked as completed."""
        self.tasks = [task for task in self.tasks if not task.completed]
        self.update_task_list()

    def clear_all_tasks(self) -> None:
        """Clears all tasks after user confirmation."""
        if messagebox.askyesno('Clear All', 'Are you sure you want to clear all tasks?'):
            self.tasks.clear()
            self.update_task_list()

    def show_task_context_menu(self, event) -> None:
        """Displays a context menu for tasks with options to toggle completion, toggle importance, edit, or delete."""
        try:
            index = self.task_listbox.nearest(event.y)
            self.task_listbox.selection_clear(0, 'end')
            self.task_listbox.selection_set(index)
            task_index = self.visible_task_indices[index]
            context_menu = tk.Menu(self.root, tearoff=0)
            context_menu.add_command(label='Toggle Completed', command=lambda: self.toggle_task_completion_event())
            context_menu.add_command(label='Toggle Importance', command=lambda: self.toggle_task_importance(task_index))
            context_menu.add_command(label='Edit Task', command=self.edit_task)
            context_menu.add_command(label='Delete Task', command=self.remove_task)
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            if 'context_menu' in locals():
                context_menu.grab_release()

    def toggle_task_importance(self, task_index: int) -> None:
        """Toggles the importance flag for the specified task."""
        if task_index == -1:
            return
        self.tasks[task_index].important = not self.tasks[task_index].important
        self.update_task_list()
        self.reselect_task(task_index)


def main() -> None:
    root = tk.Tk()
    # Optionally set a default font for consistency
    default_font = ('Arial', 11)
    root.option_add('*Font', default_font)
    app = CalendarAndTaskApp(root)
    root.mainloop()


if __name__ == '__main__':
    main()