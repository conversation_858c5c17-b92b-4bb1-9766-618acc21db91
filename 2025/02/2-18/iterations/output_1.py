import tkinter as tk
from tkinter import ttk, messagebox
import calendar
import datetime


class CalendarAndTaskApp:
    def __init__(self, root):
        self.root = root
        self.root.title('Calendar and Personal Management App')
        self.root.geometry('800x600')

        # Create a Notebook for tabs
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(expand=True, fill='both')

        # Variables for calendar
        today = datetime.date.today()
        self.current_year = today.year
        self.current_month = today.month

        # Create frames for tabs
        self.create_calendar_tab()
        self.create_task_tab()

    def create_calendar_tab(self):
        self.calendar_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.calendar_frame, text='Calendar')

        # Header for navigation
        nav_frame = ttk.Frame(self.calendar_frame)
        nav_frame.pack(pady=10)

        self.prev_button = ttk.Button(nav_frame, text='Previous Month', command=self.prev_month)
        self.prev_button.grid(row=0, column=0, padx=5)

        self.month_year_label = ttk.Label(nav_frame, text='')
        self.month_year_label.grid(row=0, column=1, padx=10)

        self.next_button = ttk.Button(nav_frame, text='Next Month', command=self.next_month)
        self.next_button.grid(row=0, column=2, padx=5)

        # Frame for displaying calendar
        self.days_frame = ttk.Frame(self.calendar_frame)
        self.days_frame.pack(pady=10)

        self.draw_calendar()

    def draw_calendar(self):
        # Clear previous calendar
        for widget in self.days_frame.winfo_children():
            widget.destroy()

        # Update the month year label
        self.month_year_label.config(text=f'{calendar.month_name[self.current_month]} {self.current_year}')

        # Display day headers
        days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
        for idx, day in enumerate(days):
            header = ttk.Label(self.days_frame, text=day, borderwidth=1, relief='raised', width=10, anchor='center')
            header.grid(row=0, column=idx, padx=1, pady=1, sticky='nsew')

        # Get month calendar as a matrix where each week is a list of ints (0 if day not in month)
        cal = calendar.Calendar(firstweekday=0)  # Monday is 0
        month_days = cal.monthdayscalendar(self.current_year, self.current_month)

        # Display days in grid
        for r, week in enumerate(month_days, start=1):
            for c, day in enumerate(week):
                if day == 0:
                    day_str = ''
                else:
                    day_str = str(day)
                day_label = ttk.Label(self.days_frame, text=day_str, borderwidth=1, relief='ridge', width=10, anchor='center')

                # Highlight today's date if in current month and year
                if (day == datetime.date.today().day and
                    self.current_month == datetime.date.today().month and
                    self.current_year == datetime.date.today().year):
                    day_label.config(background='lightgreen')

                day_label.grid(row=r, column=c, padx=1, pady=1, sticky='nsew')

    def prev_month(self):
        # Decrement month and adjust year if necessary
        if self.current_month == 1:
            self.current_month = 12
            self.current_year -= 1
        else:
            self.current_month -= 1
        self.draw_calendar()

    def next_month(self):
        # Increment month and adjust year if necessary
        if self.current_month == 12:
            self.current_month = 1
            self.current_year += 1
        else:
            self.current_month += 1
        self.draw_calendar()

    def create_task_tab(self):
        self.task_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.task_frame, text='Personal Management')

        # Label for tasks
        label = ttk.Label(self.task_frame, text='Your Tasks:', font=('Arial', 14))
        label.pack(pady=10)

        # Frame for task entry and button
        task_entry_frame = ttk.Frame(self.task_frame)
        task_entry_frame.pack(pady=5)

        # Entry for new task
        self.task_entry = ttk.Entry(task_entry_frame, width=50)
        self.task_entry.pack(side='left', padx=5)
        self.task_entry.bind('<Return>', lambda event: self.add_task())

        # Add Task Button
        add_button = ttk.Button(task_entry_frame, text='Add Task', command=self.add_task)
        add_button.pack(side='left', padx=5)

        # Listbox to display tasks with scroll
        list_frame = ttk.Frame(self.task_frame)
        list_frame.pack(pady=5, fill='both', expand=True)

        self.task_listbox = tk.Listbox(list_frame, font=('Arial', 12), selectmode='extended')
        self.task_listbox.pack(side='left', fill='both', expand=True, padx=5)

        # Bind double-click event to mark task as completed
        self.task_listbox.bind('<Double-Button-1>', self.toggle_task_completion)

        # Scrollbar for the listbox
        scrollbar = ttk.Scrollbar(list_frame, orient='vertical', command=self.task_listbox.yview)
        scrollbar.pack(side='right', fill='y')
        self.task_listbox.config(yscrollcommand=scrollbar.set)

        # Button to remove selected tasks
        remove_button = ttk.Button(self.task_frame, text='Remove Selected Task(s)', command=self.remove_task)
        remove_button.pack(pady=10)

        # Internal structure to hold tasks and completion status
        # We'll use a dictionary mapping listbox index to completion state
        self.task_completion = {}

    def add_task(self):
        task_text = self.task_entry.get().strip()
        if not task_text:
            messagebox.showwarning('Input Error', 'Please enter a task.')
            return
        # Add task to listbox and record its completion status as False (incomplete)
        self.task_listbox.insert('end', task_text)
        # Record the task as not completed
        self.task_completion[self.task_listbox.size() - 1] = False
        self.task_entry.delete(0, 'end')

    def remove_task(self):
        selected_indices = list(self.task_listbox.curselection())
        if not selected_indices:
            messagebox.showwarning('Selection Error', 'Please select at least one task to remove.')
            return
        # Remove tasks starting from the highest index to avoid index shifting
        selected_indices.sort(reverse=True)
        for idx in selected_indices:
            self.task_listbox.delete(idx)
            # Remove from the completion dictionary
            if idx in self.task_completion:
                del self.task_completion[idx]

        # Rebuild the task_completion mapping because indices have changed
        new_mapping = {}
        for new_idx in range(self.task_listbox.size()):
            # Here we simply set to False for simplicity; you could store more info if needed
            new_mapping[new_idx] = False
        self.task_completion = new_mapping

    def toggle_task_completion(self, event):
        # Toggle the completion status of a task when double-clicked
        idx = self.task_listbox.curselection()
        if not idx:
            return
        idx = idx[0]
        # Get current text
        task_text = self.task_listbox.get(idx)
        # Check if task is marked as completed by a simple prefix (you can enhance this logic)
        if self.task_completion.get(idx, False):
            # Mark as not completed, remove strikethrough effect
            new_text = task_text.replace('[Completed] ', '')
            self.task_listbox.delete(idx)
            self.task_listbox.insert(idx, new_text)
            self.task_completion[idx] = False
        else:
            # Mark as completed by adding a prefix
            new_text = '[Completed] ' + task_text
            self.task_listbox.delete(idx)
            self.task_listbox.insert(idx, new_text)
            self.task_completion[idx] = True
        # Keep the selection on the same index
        self.task_listbox.select_set(idx)


def main():
    root = tk.Tk()
    app = CalendarAndTaskApp(root)
    root.mainloop()


if __name__ == '__main__':
    main()