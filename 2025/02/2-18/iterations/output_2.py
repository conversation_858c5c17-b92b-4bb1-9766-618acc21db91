import tkinter as tk
from tkinter import ttk, messagebox
import calendar
import datetime


class CalendarAndTaskApp:
    def __init__(self, root):
        self.root = root
        self.root.title('Calendar and Personal Management App')
        self.root.geometry('800x600')

        # Create a Notebook for tabs
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(expand=True, fill='both')

        # Calendar variables
        today = datetime.date.today()
        self.today = today
        self.current_year = today.year
        self.current_month = today.month

        # Task management list: each task is a dict with 'text' and 'completed'
        self.tasks = []

        # Create UI tabs
        self.create_calendar_tab()
        self.create_task_tab()

    # ---------------- CALENDAR TAB ----------------
    def create_calendar_tab(self):
        self.calendar_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.calendar_frame, text='Calendar')

        # Navigation header
        nav_frame = ttk.Frame(self.calendar_frame)
        nav_frame.pack(pady=10)

        self.prev_button = ttk.Button(nav_frame, text='Previous Month', command=self.prev_month)
        self.prev_button.grid(row=0, column=0, padx=5)

        self.today_button = ttk.Button(nav_frame, text='Today', command=self.go_to_today)
        self.today_button.grid(row=0, column=1, padx=5)

        self.month_year_label = ttk.Label(nav_frame, text='', font=('Arial', 12, 'bold'))
        self.month_year_label.grid(row=0, column=2, padx=10)

        self.next_button = ttk.Button(nav_frame, text='Next Month', command=self.next_month)
        self.next_button.grid(row=0, column=3, padx=5)

        # Frame for displaying the calendar grid
        self.days_frame = ttk.Frame(self.calendar_frame)
        self.days_frame.pack(pady=10)

        self.draw_calendar()

    def draw_calendar(self):
        """Clear and draw the calendar for the current month and year."""
        # Remove previous calendar widgets
        for widget in self.days_frame.winfo_children():
            widget.destroy()

        # Update the label with current month and year
        month_name = calendar.month_name[self.current_month]
        self.month_year_label.config(text=f'{month_name} {self.current_year}')

        # Display day headers
        days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
        for idx, day in enumerate(days):
            header = ttk.Label(self.days_frame, text=day, borderwidth=1, relief='raised', width=10, anchor='center')
            header.grid(row=0, column=idx, padx=1, pady=1, sticky='nsew')

        # Calculate month calendar structure; note: Monday=0
        cal = calendar.Calendar(firstweekday=0)
        month_days = cal.monthdayscalendar(self.current_year, self.current_month)

        # Create the calendar day grid
        for r, week in enumerate(month_days, start=1):
            for c, day in enumerate(week):
                day_str = str(day) if day != 0 else ''
                day_label = ttk.Label(self.days_frame, text=day_str, borderwidth=1, relief='ridge', width=10, anchor='center')

                # Highlight today's date if it matches
                if (day and self.current_year == self.today.year and self.current_month == self.today.month 
                        and day == self.today.day):
                    day_label.config(background='lightgreen')

                day_label.grid(row=r, column=c, padx=1, pady=1, sticky='nsew')

    def prev_month(self):
        """Navigate to the previous month."""
        if self.current_month == 1:
            self.current_month = 12
            self.current_year -= 1
        else:
            self.current_month -= 1
        self.draw_calendar()

    def next_month(self):
        """Navigate to the next month."""
        if self.current_month == 12:
            self.current_month = 1
            self.current_year += 1
        else:
            self.current_month += 1
        self.draw_calendar()

    def go_to_today(self):
        """Reset the calendar view to the current month and year."""
        self.current_year = self.today.year
        self.current_month = self.today.month
        self.draw_calendar()

    # ---------------- TASK MANAGEMENT TAB ----------------
    def create_task_tab(self):
        self.task_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.task_frame, text='Personal Management')

        header_label = ttk.Label(self.task_frame, text='Your Tasks:', font=('Arial', 14, 'bold'))
        header_label.pack(pady=10)

        # Frame for entering new tasks
        task_entry_frame = ttk.Frame(self.task_frame)
        task_entry_frame.pack(pady=5)

        self.task_entry = ttk.Entry(task_entry_frame, width=50)
        self.task_entry.pack(side='left', padx=5)
        self.task_entry.bind('<Return>', lambda event: self.add_task())

        add_button = ttk.Button(task_entry_frame, text='Add Task', command=self.add_task)
        add_button.pack(side='left', padx=5)

        # Frame for listbox and scroll
        list_frame = ttk.Frame(self.task_frame)
        list_frame.pack(pady=5, fill='both', expand=True)

        self.task_listbox = tk.Listbox(list_frame, font=('Arial', 12), selectmode='extended')
        self.task_listbox.pack(side='left', fill='both', expand=True, padx=5)
        self.task_listbox.bind('<Double-Button-1>', self.toggle_task_completion)

        scrollbar = ttk.Scrollbar(list_frame, orient='vertical', command=self.task_listbox.yview)
        scrollbar.pack(side='right', fill='y')
        self.task_listbox.config(yscrollcommand=scrollbar.set)

        # Frame for task control buttons
        btn_frame = ttk.Frame(self.task_frame)
        btn_frame.pack(pady=10)

        remove_button = ttk.Button(btn_frame, text='Remove Selected Task(s)', command=self.remove_task)
        remove_button.grid(row=0, column=0, padx=5)

        clear_button = ttk.Button(btn_frame, text='Clear Completed Tasks', command=self.clear_completed_tasks)
        clear_button.grid(row=0, column=1, padx=5)

    def add_task(self):
        """Add a new task to the task list."""
        task_text = self.task_entry.get().strip()
        if not task_text:
            messagebox.showwarning('Input Error', 'Please enter a task.')
            return
        # Append new task as a dictionary
        self.tasks.append({'text': task_text, 'completed': False})
        self.task_entry.delete(0, 'end')
        self.update_task_list()

    def remove_task(self):
        """Remove selected tasks from the list."""
        selected_indices = list(self.task_listbox.curselection())
        if not selected_indices:
            messagebox.showwarning('Selection Error', 'Please select at least one task to remove.')
            return
        # Remove tasks in reverse order to avoid index issues
        for index in sorted(selected_indices, reverse=True):
            try:
                del self.tasks[index]
            except IndexError:
                continue
        self.update_task_list()

    def toggle_task_completion(self, event):
        """Toggle the completion status of the double-clicked task."""
        selection = self.task_listbox.curselection()
        if not selection:
            return
        index = selection[0]
        # Toggle completion
        self.tasks[index]['completed'] = not self.tasks[index]['completed']
        self.update_task_list()
        # Reselect the toggled task
        self.task_listbox.select_set(index)

    def clear_completed_tasks(self):
        """Remove all tasks that are marked as completed."""
        self.tasks = [task for task in self.tasks if not task['completed']]
        self.update_task_list()

    def update_task_list(self):
        """Refresh the Listbox content based on the task list."""
        self.task_listbox.delete(0, 'end')
        for task in self.tasks:
            display_text = task['text']
            if task['completed']:
                display_text = '[Completed] ' + display_text
            self.task_listbox.insert('end', display_text)


def main():
    root = tk.Tk()
    app = CalendarAndTaskApp(root)
    root.mainloop()


if __name__ == '__main__':
    main()