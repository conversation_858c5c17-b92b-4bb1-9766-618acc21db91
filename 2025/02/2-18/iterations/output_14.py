import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import calendar
import datetime
import logging
from dataclasses import dataclass
from typing import List, Dict, Optional, Callable

# Setup basic logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


class Tooltip:
    """A simple tooltip for a given widget."""

    def __init__(self, widget: tk.Widget, text: str, delay: int = 500) -> None:
        self.widget = widget
        self.text = text
        self.delay = delay  # milliseconds
        self.tooltip_window: Optional[tk.Toplevel] = None
        self.after_id: Optional[str] = None
        self.widget.bind('<Enter>', self.schedule)
        self.widget.bind('<Leave>', self.hide)
        self.widget.bind('<ButtonPress>', self.hide)

    def schedule(self, event: Optional[tk.Event] = None) -> None:
        self.unschedule()
        self.after_id = self.widget.after(self.delay, self.show)

    def unschedule(self) -> None:
        if self.after_id:
            self.widget.after_cancel(self.after_id)
            self.after_id = None

    def show(self, event: Optional[tk.Event] = None) -> None:
        if self.tooltip_window:
            return
        x = self.widget.winfo_rootx() + 20
        y = self.widget.winfo_rooty() + self.widget.winfo_height() + 2
        self.tooltip_window = tw = tk.Toplevel(self.widget)
        tw.wm_overrideredirect(True)
        tw.wm_geometry(f'+{x}+{y}')
        label = tk.Label(tw, text=self.text, background='lightyellow', relief='solid', borderwidth=1,
                         font=('Arial', 10), padx=2, pady=1)
        label.pack()

    def hide(self, event: Optional[tk.Event] = None) -> None:
        self.unschedule()
        if self.tooltip_window:
            self.tooltip_window.destroy()
            self.tooltip_window = None


@dataclass
class Task:
    text: str
    completed: bool = False
    important: bool = False


class ManagementApp:
    def __init__(self, root: tk.Tk) -> None:
        self.root = root
        self.root.title('Calendar & Personal Management App')
        self.root.geometry('900x650')

        # UI Theme
        self.dark_mode = False
        self.style = ttk.Style()
        self.update_theme()
        logging.info('Initialized theme: Dark mode is %s', self.dark_mode)

        # Data
        self.today = datetime.date.today()
        self.current_year = self.today.year
        self.current_month = self.today.month
        self.events: Dict[str, str] = {}  # 'YYYY-MM-DD' -> event note
        self.tasks: List[Task] = []
        self.visible_task_indices: List[int] = []  # Maps displayed tasks to index in self.tasks

        # Task filtering and search
        self.task_filter = tk.StringVar(value='All')
        self.search_var = tk.StringVar(value='')
        self.search_var.trace_add('write', lambda *args: self.update_task_list())

        # Setup menubar and shortcuts
        self.create_menubar()
        self.bind_shortcuts()

        # Notebook Tabs
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(expand=True, fill='both')
        self.create_calendar_tab()
        self.create_task_tab()

        # Global binding: Escape clears task selection
        self.root.bind('<Escape>', lambda event: self.task_listbox.selection_clear(0, 'end'))

        self.root.protocol('WM_DELETE_WINDOW', self.on_exit)
        logging.info('Application started.')

    def update_theme(self) -> None:
        """Update UI theme for dark or light mode."""
        if self.dark_mode:
            bg_color = '#2e2e2e'
            fg_color = '#ffffff'
            self.style.theme_use('clam')  # clam suits dark mode
        else:
            bg_color = '#f0f0f0'
            fg_color = '#000000'
            self.style.theme_use('default')
        self.style.configure('.', background=bg_color, foreground=fg_color, fieldbackground=bg_color)
        self.root.configure(bg=bg_color)
        logging.info('Theme updated: Dark mode is %s', self.dark_mode)

    def create_menubar(self) -> None:
        """Creates the menubar with File, View, and Help menus."""
        menubar = tk.Menu(self.root)

        # File Menu
        file_menu = tk.Menu(menubar, tearoff=0)
        file_menu.add_command(label='Reset Data', command=self.reset_data, accelerator='Ctrl+R')
        file_menu.add_command(label='Exit', command=self.on_exit, accelerator='Ctrl+Q')
        menubar.add_cascade(label='File', menu=file_menu)

        # View Menu
        view_menu = tk.Menu(menubar, tearoff=0)
        view_menu.add_command(label='Toggle Dark Mode', command=self.toggle_dark_mode)
        menubar.add_cascade(label='View', menu=view_menu)

        # Additional Sort Menu
        sort_menu = tk.Menu(menubar, tearoff=0)
        sort_menu.add_command(label='Sort Tasks by Importance', command=self.sort_tasks_by_importance)
        menubar.add_cascade(label='Sort', menu=sort_menu)

        # Help Menu
        help_menu = tk.Menu(menubar, tearoff=0)
        help_menu.add_command(label='Shortcuts', command=self.show_shortcuts, accelerator='F2')
        help_menu.add_command(label='About', command=self.show_about, accelerator='F1')
        menubar.add_cascade(label='Help', menu=help_menu)

        self.root.config(menu=menubar)

    def bind_shortcuts(self) -> None:
        """Binds keyboard shortcuts to enhance user experience."""
        bindings: Dict[str, Callable] = {
            '<Left>': self.prev_month,
            '<Right>': self.next_month,
            't': self.go_to_today,
            '<Control-q>': lambda event: self.on_exit(),
            '<F1>': lambda event: self.show_about(),
            '<F2>': lambda event: self.show_shortcuts(),
            '<Control-Delete>': lambda event: self.remove_task(),
            '<Control-n>': lambda event: self.task_entry.focus_set(),
            '<Control-Up>': lambda event: self.move_task_up(),
            '<Control-Down>': lambda event: self.move_task_down(),
            '<Control-r>': lambda event: self.reset_data(),
        }
        for key, func in bindings.items():
            self.root.bind(key, func)

    def reset_data(self) -> None:
        """Resets tasks and events after user confirmation."""
        if messagebox.askyesno('Reset Data', 'Are you sure you want to reset all tasks and calendar events?'):
            self.tasks.clear()
            self.events.clear()
            self.update_task_list()
            self.draw_calendar()
            logging.info('All data has been reset.')

    def toggle_dark_mode(self) -> None:
        """Switches dark mode on or off and refreshes UI."""
        self.dark_mode = not self.dark_mode
        self.update_theme()
        self.draw_calendar()
        self.update_task_list()
        logging.info('Dark mode toggled to %s', self.dark_mode)

    def show_about(self) -> None:
        """Displays information about the application."""
        about_text = ("Calendar & Personal Management App\nVersion 7.2\n\n"
                      "Enhancements: Refactoring, new sort and shortcuts display, improved tooltips,\n"
                      "and additional UI improvements.\n\n"
                      "For more details, refer to the built-in documentation.")
        messagebox.showinfo('About', about_text)

    def show_shortcuts(self) -> None:
        """Displays a list of keyboard shortcuts."""
        shortcuts = (
            "Keyboard Shortcuts:\n\n"
            "Left Arrow: Previous Month\n"
            "Right Arrow: Next Month\n"
            "t: Go to Today\n"
            "Ctrl+Q: Quit Application\n"
            "Ctrl+R: Reset Data\n"
            "F1: About\n"
            "F2: Display Shortcuts\n"
            "Ctrl+Delete: Remove Selected Task(s)\n"
            "Ctrl+N: Focus on Task Entry\n"
            "Ctrl+Up/Down: Move Task Up/Down\n"
            "Double-Click Task: Toggle Completion\n"
            "Shift+Double-Click Task: Toggle Importance\n"
        )
        messagebox.showinfo('Shortcuts', shortcuts)

    def on_exit(self) -> None:
        """Exits the application after confirmation."""
        if messagebox.askokcancel('Quit', 'Do you really want to exit?'):
            logging.info('Application exiting.')
            self.root.destroy()

    def get_date_key(self, year: int, month: int, day: int) -> str:
        """Formats year, month, day into a YYYY-MM-DD string."""
        return f'{year:04d}-{month:02d}-{day:02d}'

    # --------------------- CALENDAR TAB ---------------------
    def create_calendar_tab(self) -> None:
        """Creates the Calendar UI tab."""
        self.calendar_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.calendar_frame, text='Calendar')

        # Navigation Frame
        nav_frame = ttk.Frame(self.calendar_frame)
        nav_frame.pack(pady=10, fill='x')
        
        self.prev_button = ttk.Button(nav_frame, text='Previous Month', command=self.prev_month)
        self.prev_button.grid(row=0, column=0, padx=5)

        self.today_button = ttk.Button(nav_frame, text='Today', command=self.go_to_today)
        self.today_button.grid(row=0, column=1, padx=5)

        self.month_year_label = ttk.Label(nav_frame, text='', font=('Arial', 12, 'bold'))
        self.month_year_label.grid(row=0, column=2, padx=10)

        self.next_button = ttk.Button(nav_frame, text='Next Month', command=self.next_month)
        self.next_button.grid(row=0, column=3, padx=5)

        clear_events_button = ttk.Button(nav_frame, text='Clear All Events', command=self.clear_all_events)
        clear_events_button.grid(row=0, column=4, padx=5)

        # Tooltip for Clear button
        Tooltip(clear_events_button, 'Remove all events from the calendar')

        # Calendar Grid Frame
        self.days_frame = ttk.Frame(self.calendar_frame)
        self.days_frame.pack(pady=10, fill='both', expand=True)
        
        self.draw_calendar()

    def draw_calendar(self) -> None:
        """Draws the calendar grid, event markers, and day headers."""
        for widget in self.days_frame.winfo_children():
            widget.destroy()

        month_name = calendar.month_name[self.current_month]
        self.month_year_label.config(text=f'{month_name} {self.current_year}')

        # Day headers
        headers = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
        for idx, header_text in enumerate(headers):
            header = ttk.Label(self.days_frame, text=header_text, borderwidth=1, relief='raised', width=12, anchor='center')
            header.grid(row=0, column=idx, padx=1, pady=1, sticky='nsew')

        # Calendar days
        cal = calendar.Calendar(firstweekday=0)
        month_weeks = cal.monthdayscalendar(self.current_year, self.current_month)
        for row, week in enumerate(month_weeks, start=1):
            for col, day in enumerate(week):
                display_text = str(day) if day != 0 else ''
                day_label = ttk.Label(self.days_frame, text=display_text, borderwidth=1, relief='ridge', width=12, anchor='center')
                if day:
                    date_key = self.get_date_key(self.current_year, self.current_month, day)
                    event_note = self.events.get(date_key, '').strip()
                    
                    # Highlight today's date
                    if (self.current_year == self.today.year and self.current_month == self.today.month and day == self.today.day):
                        day_label.config(background='lightgreen')

                    # Mark cell if event exists
                    if event_note:
                        day_label.config(background='lightyellow')
                        Tooltip(day_label, event_note)

                    # Bind left-click to edit/add event
                    day_label.bind('<Button-1>', lambda event, d=day: self.edit_event(d))
                    # Bind right-click for event context menu
                    day_label.bind('<Button-3>', lambda event, d=day: self.calendar_context_menu(event, d))

                day_label.grid(row=row, column=col, padx=1, pady=1, sticky='nsew')

        for col in range(7):
            self.days_frame.columnconfigure(col, weight=1)
        logging.debug('Calendar redrawn for %s-%s', self.current_year, self.current_month)

    def calendar_context_menu(self, event: tk.Event, day: int) -> None:
        """Shows a context menu for a specific calendar day."""
        date_key = self.get_date_key(self.current_year, self.current_month, day)
        event_note = self.events.get(date_key, '').strip()
        menu = tk.Menu(self.root, tearoff=0)
        if event_note:
            menu.add_command(label='View Event', command=lambda d=day: self.show_event_details(d))
            menu.add_command(label='Edit Event', command=lambda d=day: self.edit_event(d))
            menu.add_command(label='Delete Event', command=lambda d=day: self.delete_event(d))
        else:
            menu.add_command(label='Add Event', command=lambda d=day: self.edit_event(d))
        try:
            menu.tk_popup(event.x_root, event.y_root)
        finally:
            menu.grab_release()

    def edit_event(self, day: int) -> None:
        """Add or edit an event for a specific day. Empty value clears the event."""
        date_key = self.get_date_key(self.current_year, self.current_month, day)
        current_note = self.events.get(date_key, '')
        new_note = simpledialog.askstring('Edit Event', f'Enter event note for {date_key}:',
                                          initialvalue=current_note, parent=self.root)
        if new_note is None:
            return
        new_note = new_note.strip()
        if new_note:
            self.events[date_key] = new_note
            logging.info('Set event for %s', date_key)
        else:
            self.events.pop(date_key, None)
            logging.info('Cleared event for %s', date_key)
        self.draw_calendar()

    def delete_event(self, day: int) -> None:
        """Deletes the event for a given day after confirmation."""
        date_key = self.get_date_key(self.current_year, self.current_month, day)
        if date_key in self.events:
            if messagebox.askyesno('Delete Event', f'Are you sure you want to delete the event for {date_key}?'):
                self.events.pop(date_key)
                logging.info('Deleted event for %s', date_key)
                self.draw_calendar()
        else:
            messagebox.showinfo('No Event', f'No event exists for {date_key}.')

    def show_event_details(self, day: int) -> None:
        """Shows details of the event on a given day."""
        date_key = self.get_date_key(self.current_year, self.current_month, day)
        event_note = self.events.get(date_key, '').strip()
        if event_note:
            messagebox.showinfo(f'Event on {date_key}', event_note)
        else:
            messagebox.showinfo('No Event', f'No event set for {date_key}.')

    def clear_all_events(self) -> None:
        """Clears all events after user confirmation."""
        if messagebox.askyesno('Clear Events', 'Are you sure you want to clear all calendar events?'):
            self.events.clear()
            self.draw_calendar()
            logging.info('Cleared all calendar events.')

    def prev_month(self, event: Optional[tk.Event] = None) -> None:
        """Moves the calendar view to the previous month."""
        if self.current_month == 1:
            self.current_month = 12
            self.current_year -= 1
        else:
            self.current_month -= 1
        self.draw_calendar()
        logging.info('Moved to previous month: %s-%s', self.current_year, self.current_month)

    def next_month(self, event: Optional[tk.Event] = None) -> None:
        """Moves the calendar view to the next month."""
        if self.current_month == 12:
            self.current_month = 1
            self.current_year += 1
        else:
            self.current_month += 1
        self.draw_calendar()
        logging.info('Moved to next month: %s-%s', self.current_year, self.current_month)

    def go_to_today(self, event: Optional[tk.Event] = None) -> None:
        """Resets the calendar view to today's date."""
        self.current_year = self.today.year
        self.current_month = self.today.month
        self.draw_calendar()
        logging.info('Reset calendar to today: %s', self.today)

    # --------------------- TASK MANAGEMENT TAB ---------------------
    def create_task_tab(self) -> None:
        """Creates the Task Management UI tab."""
        self.task_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.task_frame, text='Personal Management')

        header_label = ttk.Label(self.task_frame, text='Your Tasks:', font=('Arial', 14, 'bold'))
        header_label.pack(pady=10)

        # Task entry section
        task_entry_frame = ttk.Frame(self.task_frame)
        task_entry_frame.pack(pady=5)

        self.task_entry = ttk.Entry(task_entry_frame, width=60)
        self.task_entry.pack(side='left', padx=5)
        self.task_entry.bind('<Return>', lambda event: self.add_task())

        add_button = ttk.Button(task_entry_frame, text='Add Task', command=self.add_task)
        add_button.pack(side='left', padx=5)
        Tooltip(add_button, 'Click to add a new task')

        export_button = ttk.Button(task_entry_frame, text='Export Tasks', command=self.export_tasks)
        export_button.pack(side='left', padx=5)
        Tooltip(export_button, 'Click to view tasks summary')

        # Search and Clear Search
        search_frame = ttk.Frame(self.task_frame)
        search_frame.pack(pady=5)
        ttk.Label(search_frame, text='Search Tasks:').pack(side='left', padx=5)
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=30)
        search_entry.pack(side='left')
        clear_search_btn = ttk.Button(search_frame, text='Clear', command=lambda: self.search_var.set(''))
        clear_search_btn.pack(side='left', padx=5)
        Tooltip(clear_search_btn, 'Clear search text')

        # Filter dropdown
        filter_frame = ttk.Frame(self.task_frame)
        filter_frame.pack(pady=5)
        ttk.Label(filter_frame, text='Filter Tasks:').pack(side='left', padx=5)
        filter_options = ['All', 'Pending', 'Completed']
        filter_menu = ttk.Combobox(filter_frame, values=filter_options, textvariable=self.task_filter,
                                    state='readonly', width=10)
        filter_menu.pack(side='left')
        filter_menu.bind('<<ComboboxSelected>>', lambda e: self.update_task_list())

        # Task list
        list_frame = ttk.Frame(self.task_frame)
        list_frame.pack(pady=5, fill='both', expand=True)

        self.task_listbox = tk.Listbox(list_frame, font=('Arial', 12), selectmode='extended')
        self.task_listbox.pack(side='left', fill='both', expand=True, padx=5, pady=5)
        scrollbar = ttk.Scrollbar(list_frame, orient='vertical', command=self.task_listbox.yview)
        scrollbar.pack(side='right', fill='y')
        self.task_listbox.config(yscrollcommand=scrollbar.set)

        self.task_listbox.bind('<Double-Button-1>', self.toggle_task_completion_event)
        self.task_listbox.bind('<Shift-Double-Button-1>', self.shift_toggle_importance)
        self.task_listbox.bind('<Button-3>', self.show_task_context_menu)

        self.task_count_label = ttk.Label(self.task_frame, text='Total Tasks: 0  |  Visible: 0')
        self.task_count_label.pack(pady=5)

        # Control buttons
        btn_frame = ttk.Frame(self.task_frame)
        btn_frame.pack(pady=10)
        remove_button = ttk.Button(btn_frame, text='Remove Selected Task(s)', command=self.remove_task)
        remove_button.grid(row=0, column=0, padx=5, pady=2)
        clear_completed_button = ttk.Button(btn_frame, text='Clear Completed Tasks', command=self.clear_completed_tasks)
        clear_completed_button.grid(row=0, column=1, padx=5, pady=2)
        clear_all_button = ttk.Button(btn_frame, text='Clear All Tasks', command=self.clear_all_tasks)
        clear_all_button.grid(row=0, column=2, padx=5, pady=2)
        edit_button = ttk.Button(btn_frame, text='Edit Task', command=self.edit_task)
        edit_button.grid(row=0, column=3, padx=5, pady=2)
        move_up_button = ttk.Button(btn_frame, text='Move Up', command=self.move_task_up)
        move_up_button.grid(row=1, column=0, padx=5, pady=2)
        move_down_button = ttk.Button(btn_frame, text='Move Down', command=self.move_task_down)
        move_down_button.grid(row=1, column=1, padx=5, pady=2)

    def add_task(self) -> None:
        """Adds a new task from the entry field."""
        task_text = self.task_entry.get().strip()
        if not task_text:
            messagebox.showwarning('Input Error', 'Please enter a task.')
            return
        self.tasks.append(Task(text=task_text))
        self.task_entry.delete(0, 'end')
        self.update_task_list()
        logging.info('Task added: %s', task_text)

    def update_task_list(self) -> None:
        """Updates the task list based on search and filter criteria."""
        self.task_listbox.delete(0, 'end')
        self.visible_task_indices.clear()
        filter_option = self.task_filter.get()
        search_text = self.search_var.get().strip().lower()

        for idx, task in enumerate(self.tasks):
            if filter_option == 'Pending' and task.completed:
                continue
            if filter_option == 'Completed' and not task.completed:
                continue
            if search_text and search_text not in task.text.lower():
                continue
            display_text = task.text
            if task.important:
                display_text = '(!) ' + display_text
            if task.completed:
                display_text = '[Completed] ' + display_text
            self.task_listbox.insert('end', display_text)
            self.visible_task_indices.append(idx)

        self.task_count_label.config(text=f'Total Tasks: {len(self.tasks)}  |  Visible: {len(self.visible_task_indices)}')
        logging.debug('Task list updated: %d visible out of %d', len(self.visible_task_indices), len(self.tasks))

    def get_selected_task_index(self) -> int:
        """Gets the absolute index of the first selected task."""
        selection = self.task_listbox.curselection()
        if not selection:
            return -1
        try:
            return self.visible_task_indices[selection[0]]
        except IndexError:
            return -1

    def toggle_task_completion_event(self, event: Optional[tk.Event] = None) -> None:
        """Toggles the completion status of the selected task."""
        task_index = self.get_selected_task_index()
        if task_index == -1:
            return
        self.tasks[task_index].completed = not self.tasks[task_index].completed
        self.update_task_list()
        self.reselect_task(task_index)
        logging.info('Toggled completion for task at index %d', task_index)

    def shift_toggle_importance(self, event: Optional[tk.Event] = None) -> None:
        """Toggles the importance flag via Shift+Double-Click."""
        task_index = self.get_selected_task_index()
        if task_index == -1:
            return
        self.tasks[task_index].important = not self.tasks[task_index].important
        self.update_task_list()
        self.reselect_task(task_index)
        logging.info('Toggled importance for task at index %d', task_index)

    def reselect_task(self, task_index: int) -> None:
        """Reselects a task in the listbox, if still visible, after an update."""
        if task_index in self.visible_task_indices:
            new_index = self.visible_task_indices.index(task_index)
            self.task_listbox.selection_set(new_index)

    def edit_task(self) -> None:
        """Edits the currently selected task."""
        selection = self.task_listbox.curselection()
        if not selection or len(selection) != 1:
            messagebox.showwarning('Selection Error', 'Select a single task to edit.')
            return
        task_index = self.get_selected_task_index()
        if task_index == -1:
            return
        current_text = self.tasks[task_index].text
        new_text = simpledialog.askstring('Edit Task', 'Modify your task:',
                                          initialvalue=current_text, parent=self.root)
        if new_text is None or new_text.strip() == '':
            return
        self.tasks[task_index].text = new_text.strip()
        self.update_task_list()
        self.reselect_task(task_index)
        logging.info('Edited task at index %d', task_index)

    def remove_task(self) -> None:
        """Removes the selected task(s) after confirmation."""
        selections = list(self.task_listbox.curselection())
        if not selections:
            messagebox.showwarning('Selection Error', 'Select at least one task to remove.')
            return
        if not messagebox.askyesno('Remove Task', 'Are you sure you want to remove the selected task(s)?'):
            return
        indices_to_remove = [self.visible_task_indices[i] for i in selections]
        for idx in sorted(indices_to_remove, reverse=True):
            try:
                del self.tasks[idx]
            except IndexError:
                continue
        self.update_task_list()
        logging.info('Removed tasks at indices: %s', indices_to_remove)

    def clear_completed_tasks(self) -> None:
        """Clears all tasks that are marked completed."""
        self.tasks = [task for task in self.tasks if not task.completed]
        self.update_task_list()
        logging.info('Cleared completed tasks.')

    def clear_all_tasks(self) -> None:
        """Clears all tasks after user confirmation."""
        if messagebox.askyesno('Clear All', 'Are you sure you want to clear all tasks?'):
            self.tasks.clear()
            self.update_task_list()
            logging.info('Cleared all tasks.')

    def export_tasks(self) -> None:
        """Exports a summary of all tasks in a popup."""
        if not self.tasks:
            messagebox.showinfo('Export Tasks', 'No tasks to export.')
            return
        export_str = 'Tasks Summary:\n\n'
        for idx, task in enumerate(self.tasks, start=1):
            status = '✓' if task.completed else '✗'
            important = ' (!) ' if task.important else ' '
            export_str += f"{idx}. {task.text}{important}[{status}]\n"
        messagebox.showinfo('Exported Tasks', export_str)
        logging.info('Exported tasks summary.')

    def show_task_context_menu(self, event: tk.Event) -> None:
        """Displays a context menu for task actions."""
        try:
            index = self.task_listbox.nearest(event.y)
            self.task_listbox.selection_clear(0, 'end')
            self.task_listbox.selection_set(index)
            task_index = self.visible_task_indices[index]
            context_menu = tk.Menu(self.root, tearoff=0)
            context_menu.add_command(label='Toggle Completed', command=self.toggle_task_completion_event)
            context_menu.add_command(label='Toggle Importance', command=lambda: self.toggle_task_importance(task_index))
            context_menu.add_command(label='Edit Task', command=self.edit_task)
            context_menu.add_command(label='Delete Task', command=self.remove_task)
            context_menu.add_separator()
            context_menu.add_command(label='Move Up', command=self.move_task_up)
            context_menu.add_command(label='Move Down', command=self.move_task_down)
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            if 'context_menu' in locals():
                context_menu.grab_release()

    def toggle_task_importance(self, task_index: int) -> None:
        """Toggles the importance flag for a given task."""
        if task_index == -1:
            return
        self.tasks[task_index].important = not self.tasks[task_index].important
        self.update_task_list()
        self.reselect_task(task_index)
        logging.info('Toggled importance for task at index %d', task_index)

    def move_task_up(self) -> None:
        """Moves the selected task up, if possible."""
        selection = self.task_listbox.curselection()
        if not selection or len(selection) != 1:
            return
        visible_index = selection[0]
        task_index = self.visible_task_indices[visible_index]
        if task_index <= 0:
            return
        self.tasks[task_index], self.tasks[task_index - 1] = self.tasks[task_index - 1], self.tasks[task_index]
        self.update_task_list()
        if task_index - 1 in self.visible_task_indices:
            new_visible_index = self.visible_task_indices.index(task_index - 1)
            self.task_listbox.selection_set(new_visible_index)
        logging.info('Moved task at index %d up', task_index)

    def move_task_down(self) -> None:
        """Moves the selected task down, if possible."""
        selection = self.task_listbox.curselection()
        if not selection or len(selection) != 1:
            return
        visible_index = selection[0]
        task_index = self.visible_task_indices[visible_index]
        if task_index >= len(self.tasks) - 1:
            return
        self.tasks[task_index], self.tasks[task_index + 1] = self.tasks[task_index + 1], self.tasks[task_index]
        self.update_task_list()
        if task_index + 1 in self.visible_task_indices:
            new_visible_index = self.visible_task_indices.index(task_index + 1)
            self.task_listbox.selection_set(new_visible_index)
        logging.info('Moved task at index %d down', task_index)

    def sort_tasks_by_importance(self) -> None:
        """Sorts tasks so that important and pending tasks come first, then by text."""
        self.tasks.sort(key=lambda task: ((not task.important), task.completed, task.text.lower()))
        self.update_task_list()
        logging.info('Tasks sorted by importance and status.')


def main() -> None:
    root = tk.Tk()
    default_font = ('Arial', 11)
    root.option_add('*Font', default_font)
    app = ManagementApp(root)
    root.mainloop()


if __name__ == '__main__':
    main()