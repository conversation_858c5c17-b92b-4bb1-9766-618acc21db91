import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import calendar
import datetime


class CalendarAndTaskApp:
    def __init__(self, root):
        self.root = root
        self.root.title('Calendar & Personal Management App')
        self.root.geometry('900x650')

        # Add menubar
        self.create_menubar()

        # Data and state
        today = datetime.date.today()
        self.today = today
        self.current_year = today.year
        self.current_month = today.month
        self.tasks = []  # list of tasks as dicts: {'text': str, 'completed': bool}
        self.events = {}  # calendar events mapping 'YYYY-MM-DD' -> event note

        # Create main notebook
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(expand=True, fill='both')

        # Create UI tabs
        self.create_calendar_tab()
        self.create_task_tab()

        # Bind closing protocol
        self.root.protocol('WM_DELETE_WINDOW', self.on_exit)

    def create_menubar(self):
        self.menubar = tk.Menu(self.root)
        # File menu
        file_menu = tk.Menu(self.menubar, tearoff=0)
        file_menu.add_command(label='Exit', command=self.on_exit)
        self.menubar.add_cascade(label='File', menu=file_menu)
        
        # Help menu
        help_menu = tk.Menu(self.menubar, tearoff=0)
        help_menu.add_command(label='About', command=self.show_about)
        self.menubar.add_cascade(label='Help', menu=help_menu)
        
        self.root.config(menu=self.menubar)

    def show_about(self):
        messagebox.showinfo('About', 'Calendar & Personal Management App\nVersion 1.1\n\nImproved features: task context menu, calendar events, and exit confirmation.')

    def on_exit(self):
        if messagebox.askokcancel('Quit', 'Do you really want to exit?'):
            self.root.destroy()

    # ---------------- CALENDAR TAB ----------------
    def create_calendar_tab(self):
        self.calendar_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.calendar_frame, text='Calendar')

        # Navigation header
        nav_frame = ttk.Frame(self.calendar_frame)
        nav_frame.pack(pady=10)

        self.prev_button = ttk.Button(nav_frame, text='Previous Month', command=self.prev_month)
        self.prev_button.grid(row=0, column=0, padx=5)

        self.today_button = ttk.Button(nav_frame, text='Today', command=self.go_to_today)
        self.today_button.grid(row=0, column=1, padx=5)

        self.month_year_label = ttk.Label(nav_frame, text='', font=('Arial', 12, 'bold'))
        self.month_year_label.grid(row=0, column=2, padx=10)

        self.next_button = ttk.Button(nav_frame, text='Next Month', command=self.next_month)
        self.next_button.grid(row=0, column=3, padx=5)

        # Frame for displaying the calendar grid
        self.days_frame = ttk.Frame(self.calendar_frame)
        self.days_frame.pack(pady=10)

        self.draw_calendar()

    def draw_calendar(self):
        '''Draw the calendar for the current year and month, with clickable days for events.'''
        for widget in self.days_frame.winfo_children():
            widget.destroy()

        # Update month-year label
        month_name = calendar.month_name[self.current_month]
        self.month_year_label.config(text=f'{month_name} {self.current_year}')

        # Day headers (Monday to Sunday)
        days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
        for idx, day in enumerate(days):
            header = ttk.Label(self.days_frame, text=day, borderwidth=1, relief='raised', width=12, anchor='center')
            header.grid(row=0, column=idx, padx=1, pady=1, sticky='nsew')

        # Calendar days
        cal = calendar.Calendar(firstweekday=0)  # Monday is first day
        month_days = cal.monthdayscalendar(self.current_year, self.current_month)
        
        for r, week in enumerate(month_days, start=1):
            for c, day in enumerate(week):
                if day == 0:
                    day_str = ''
                else:
                    day_str = str(day)
                day_label = ttk.Label(self.days_frame, text=day_str, borderwidth=1, relief='ridge', width=12, anchor='center')

                # Check if this day is today
                if day and self.current_year == self.today.year and self.current_month == self.today.month and day == self.today.day:
                    day_label.config(background='lightgreen')

                # Check if an event exists for this day
                if day:
                    date_key = f'{self.current_year:04d}-{self.current_month:02d}-{day:02d}'
                    if date_key in self.events and self.events[date_key].strip() != '':
                        # Mark event days with a different background
                        day_label.config(background='lightyellow')

                    # Make day clickable to add/edit event
                    day_label.bind('<Button-1>', lambda e, d=day: self.edit_event(d))

                day_label.grid(row=r, column=c, padx=1, pady=1, sticky='nsew')

    def edit_event(self, day):
        '''Open a dialog to add or edit an event note for a specific day.'''
        date_key = f'{self.current_year:04d}-{self.current_month:02d}-{day:02d}'
        current_note = self.events.get(date_key, '')
        new_note = simpledialog.askstring('Edit Event', f'Enter event note for {date_key}:', initialvalue=current_note, parent=self.root)
        if new_note is None:
            return  # Cancelled
        self.events[date_key] = new_note.strip()
        self.draw_calendar()

    def prev_month(self):
        if self.current_month == 1:
            self.current_month = 12
            self.current_year -= 1
        else:
            self.current_month -= 1
        self.draw_calendar()

    def next_month(self):
        if self.current_month == 12:
            self.current_month = 1
            self.current_year += 1
        else:
            self.current_month += 1
        self.draw_calendar()

    def go_to_today(self):
        self.current_year = self.today.year
        self.current_month = self.today.month
        self.draw_calendar()

    # ---------------- TASK MANAGEMENT TAB ----------------
    def create_task_tab(self):
        self.task_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.task_frame, text='Personal Management')

        header_label = ttk.Label(self.task_frame, text='Your Tasks:', font=('Arial', 14, 'bold'))
        header_label.pack(pady=10)

        # Frame for entering new tasks
        task_entry_frame = ttk.Frame(self.task_frame)
        task_entry_frame.pack(pady=5)

        self.task_entry = ttk.Entry(task_entry_frame, width=60)
        self.task_entry.pack(side='left', padx=5)
        self.task_entry.bind('<Return>', lambda event: self.add_task())

        add_button = ttk.Button(task_entry_frame, text='Add Task', command=self.add_task)
        add_button.pack(side='left', padx=5)

        # Frame that contains the Listbox and Scrollbar
        list_frame = ttk.Frame(self.task_frame)
        list_frame.pack(pady=5, fill='both', expand=True)

        self.task_listbox = tk.Listbox(list_frame, font=('Arial', 12), selectmode='extended')
        self.task_listbox.pack(side='left', fill='both', expand=True, padx=5, pady=5)
        
        # Bind double-click to toggle completion
        self.task_listbox.bind('<Double-Button-1>', self.toggle_task_completion)
        
        # Bind right-click for context menu
        self.task_listbox.bind('<Button-3>', self.show_task_context_menu)

        scrollbar = ttk.Scrollbar(list_frame, orient='vertical', command=self.task_listbox.yview)
        scrollbar.pack(side='right', fill='y')
        self.task_listbox.config(yscrollcommand=scrollbar.set)

        # Task control buttons
        btn_frame = ttk.Frame(self.task_frame)
        btn_frame.pack(pady=10)

        remove_button = ttk.Button(btn_frame, text='Remove Selected Task(s)', command=self.remove_task)
        remove_button.grid(row=0, column=0, padx=5)

        clear_button = ttk.Button(btn_frame, text='Clear Completed Tasks', command=self.clear_completed_tasks)
        clear_button.grid(row=0, column=1, padx=5)

        edit_button = ttk.Button(btn_frame, text='Edit Task', command=self.edit_task)
        edit_button.grid(row=0, column=2, padx=5)

    def add_task(self):
        task_text = self.task_entry.get().strip()
        if not task_text:
            messagebox.showwarning('Input Error', 'Please enter a task.')
            return
        self.tasks.append({'text': task_text, 'completed': False})
        self.task_entry.delete(0, 'end')
        self.update_task_list()

    def remove_task(self):
        selected_indices = list(self.task_listbox.curselection())
        if not selected_indices:
            messagebox.showwarning('Selection Error', 'Please select at least one task to remove.')
            return
        for index in sorted(selected_indices, reverse=True):
            try:
                del self.tasks[index]
            except IndexError:
                continue
        self.update_task_list()

    def toggle_task_completion(self, event=None):
        selection = self.task_listbox.curselection()
        if not selection:
            return
        index = selection[0]
        self.tasks[index]['completed'] = not self.tasks[index]['completed']
        self.update_task_list()
        self.task_listbox.select_set(index)

    def edit_task(self):
        selection = self.task_listbox.curselection()
        if not selection or len(selection) > 1:
            messagebox.showwarning('Selection Error', 'Select a single task to edit.')
            return
        index = selection[0]
        current_text = self.tasks[index]['text']
        new_text = simpledialog.askstring('Edit Task', 'Modify your task:', initialvalue=current_text, parent=self.root)
        if new_text is None or new_text.strip() == '':
            return
        self.tasks[index]['text'] = new_text.strip()
        self.update_task_list()
        self.task_listbox.select_set(index)

    def clear_completed_tasks(self):
        self.tasks = [task for task in self.tasks if not task['completed']]
        self.update_task_list()

    def update_task_list(self):
        self.task_listbox.delete(0, 'end')
        # Show incomplete tasks first
        for task in self.tasks:
            display_text = task['text']
            if task['completed']:
                display_text = '[Completed] ' + display_text
            self.task_listbox.insert('end', display_text)

    def show_task_context_menu(self, event):
        try:
            index = self.task_listbox.nearest(event.y)
            self.task_listbox.selection_clear(0, 'end')
            self.task_listbox.selection_set(index)
            # Create context menu
            context_menu = tk.Menu(self.root, tearoff=0)
            context_menu.add_command(label='Toggle Completed', command=lambda: self.toggle_task_completion())
            context_menu.add_command(label='Edit Task', command=self.edit_task)
            context_menu.add_command(label='Delete Task', command=self.remove_task)
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu.grab_release()


def main():
    root = tk.Tk()
    app = CalendarAndTaskApp(root)
    root.mainloop()


if __name__ == '__main__':
    main()
