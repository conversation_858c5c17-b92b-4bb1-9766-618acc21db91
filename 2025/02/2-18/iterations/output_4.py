import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import calendar
import datetime


class Tooltip:
    """A simple tooltip for a given widget."""
    def __init__(self, widget, text, delay=500):
        self.widget = widget
        self.text = text
        self.delay = delay  # in milliseconds
        self.tooltip_window = None
        self.id = None
        self.widget.bind('<Enter>', self.schedule)
        self.widget.bind('<Leave>', self.hide)
        self.widget.bind('<ButtonPress>', self.hide)  # hide on click

    def schedule(self, event=None):
        self.unschedule()
        self.id = self.widget.after(self.delay, self.show)

    def unschedule(self):
        if self.id:
            self.widget.after_cancel(self.id)
            self.id = None

    def show(self, event=None):
        if self.tooltip_window:
            return
        x = self.widget.winfo_rootx() + 20
        y = self.widget.winfo_rooty() + self.widget.winfo_height() + 1
        self.tooltip_window = tw = tk.Toplevel(self.widget)
        tw.wm_overrideredirect(True)
        tw.wm_geometry(f'+{x}+{y}')
        label = tk.Label(tw, text=self.text, background='yellow', relief='solid', borderwidth=1, font=('Arial', 10))
        label.pack(ipadx=2)

    def hide(self, event=None):
        self.unschedule()
        if self.tooltip_window:
            self.tooltip_window.destroy()
            self.tooltip_window = None


class CalendarAndTaskApp:
    def __init__(self, root):
        self.root = root
        self.root.title('Calendar & Personal Management App')
        self.root.geometry('900x650')

        # Menubar setup
        self.create_menubar()

        # Initialize data
        today = datetime.date.today()
        self.today = today
        self.current_year = today.year
        self.current_month = today.month
        # Tasks contain: text, completed status, and importance flag
        self.tasks = []
        # Calendar events: mapping 'YYYY-MM-DD' -> event note
        self.events = {}

        # Task filter (All, Pending, or Completed)
        self.task_filter = tk.StringVar(value='All')

        # Create main notebook with two tabs
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(expand=True, fill='both')
        self.create_calendar_tab()
        self.create_task_tab()

        # Bind close window protocol
        self.root.protocol('WM_DELETE_WINDOW', self.on_exit)

    def create_menubar(self):
        menubar = tk.Menu(self.root)

        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        file_menu.add_command(label='Exit', command=self.on_exit)
        menubar.add_cascade(label='File', menu=file_menu)

        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        help_menu.add_command(label='About', command=self.show_about)
        menubar.add_cascade(label='Help', menu=help_menu)

        self.root.config(menu=menubar)

    def show_about(self):
        messagebox.showinfo('About', 'Calendar & Personal Management App\nVersion 2.0\n\nAdded features: Task filtering, importance marking, tooltips for events and improved UI.')

    def on_exit(self):
        if messagebox.askokcancel('Quit', 'Do you really want to exit?'):
            self.root.destroy()

    # ---------------- CALENDAR TAB ----------------
    def create_calendar_tab(self):
        self.calendar_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.calendar_frame, text='Calendar')

        # Header navigation frame
        nav_frame = ttk.Frame(self.calendar_frame)
        nav_frame.pack(pady=10)

        self.prev_button = ttk.Button(nav_frame, text='Previous Month', command=self.prev_month)
        self.prev_button.grid(row=0, column=0, padx=5)

        self.today_button = ttk.Button(nav_frame, text='Today', command=self.go_to_today)
        self.today_button.grid(row=0, column=1, padx=5)

        self.month_year_label = ttk.Label(nav_frame, text='', font=('Arial', 12, 'bold'))
        self.month_year_label.grid(row=0, column=2, padx=10)

        self.next_button = ttk.Button(nav_frame, text='Next Month', command=self.next_month)
        self.next_button.grid(row=0, column=3, padx=5)

        # Frame for calendar grid
        self.days_frame = ttk.Frame(self.calendar_frame)
        self.days_frame.pack(pady=10, fill='both', expand=True)
        self.draw_calendar()

    def draw_calendar(self):
        '''Draw the calendar grid with event indicators and tooltips. '''
        for widget in self.days_frame.winfo_children():
            widget.destroy()

        # Update header label with month and year
        month_name = calendar.month_name[self.current_month]
        self.month_year_label.config(text=f'{month_name} {self.current_year}')

        # Day headers (Mon to Sun)
        days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
        for idx, day in enumerate(days):
            header = ttk.Label(self.days_frame, text=day, borderwidth=1, relief='raised', width=12, anchor='center')
            header.grid(row=0, column=idx, padx=1, pady=1, sticky='nsew')

        cal = calendar.Calendar(firstweekday=0)  # Monday as first day
        month_days = cal.monthdayscalendar(self.current_year, self.current_month)

        for r, week in enumerate(month_days, start=1):
            for c, day in enumerate(week):
                day_str = str(day) if day != 0 else ''
                day_label = ttk.Label(self.days_frame, text=day_str, borderwidth=1, relief='ridge', width=12, anchor='center')

                # Highlight today's date
                if day and (self.current_year == self.today.year and self.current_month == self.today.month and day == self.today.day):
                    day_label.config(background='lightgreen')

                # If there is an event note, change background and attach tooltip
                if day:
                    date_key = f'{self.current_year:04d}-{self.current_month:02d}-{day:02d}'
                    if date_key in self.events and self.events[date_key].strip() != '':
                        day_label.config(background='lightyellow')
                        Tooltip(day_label, self.events[date_key])

                    # Bind left-click for adding/editing events
                    day_label.bind('<Button-1>', lambda e, d=day: self.edit_event(d))

                day_label.grid(row=r, column=c, padx=1, pady=1, sticky='nsew')

    def edit_event(self, day):
        '''Edit or add an event note for the selected day.'''
        date_key = f'{self.current_year:04d}-{self.current_month:02d}-{day:02d}'
        current_note = self.events.get(date_key, '')
        new_note = simpledialog.askstring('Edit Event', f'Enter event note for {date_key}:', initialvalue=current_note, parent=self.root)
        if new_note is None:
            return  # Cancelled
        self.events[date_key] = new_note.strip()
        self.draw_calendar()

    def prev_month(self):
        if self.current_month == 1:
            self.current_month = 12
            self.current_year -= 1
        else:
            self.current_month -= 1
        self.draw_calendar()

    def next_month(self):
        if self.current_month == 12:
            self.current_month = 1
            self.current_year += 1
        else:
            self.current_month += 1
        self.draw_calendar()

    def go_to_today(self):
        self.current_year = self.today.year
        self.current_month = self.today.month
        self.draw_calendar()

    # ---------------- TASK MANAGEMENT TAB ----------------
    def create_task_tab(self):
        self.task_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.task_frame, text='Personal Management')

        header_label = ttk.Label(self.task_frame, text='Your Tasks:', font=('Arial', 14, 'bold'))
        header_label.pack(pady=10)

        # Frame for new task entry
        task_entry_frame = ttk.Frame(self.task_frame)
        task_entry_frame.pack(pady=5)

        self.task_entry = ttk.Entry(task_entry_frame, width=60)
        self.task_entry.pack(side='left', padx=5)
        self.task_entry.bind('<Return>', lambda event: self.add_task())

        add_button = ttk.Button(task_entry_frame, text='Add Task', command=self.add_task)
        add_button.pack(side='left', padx=5)

        # Task filter dropdown
        filter_frame = ttk.Frame(self.task_frame)
        filter_frame.pack(pady=5)
        ttk.Label(filter_frame, text='Filter Tasks:').pack(side='left', padx=5)
        filter_options = ['All', 'Pending', 'Completed']
        filter_menu = ttk.Combobox(filter_frame, values=filter_options, textvariable=self.task_filter, state='readonly', width=10)
        filter_menu.pack(side='left')
        filter_menu.bind('<<ComboboxSelected>>', lambda e: self.update_task_list())

        # Frame containing the Listbox and Scrollbar
        list_frame = ttk.Frame(self.task_frame)
        list_frame.pack(pady=5, fill='both', expand=True)

        self.task_listbox = tk.Listbox(list_frame, font=('Arial', 12), selectmode='extended')
        self.task_listbox.pack(side='left', fill='both', expand=True, padx=5, pady=5)

        # Bind double-click to toggle task completion
        self.task_listbox.bind('<Double-Button-1>', self.toggle_task_completion)
        # Bind right-click for context menu
        self.task_listbox.bind('<Button-3>', self.show_task_context_menu)

        scrollbar = ttk.Scrollbar(list_frame, orient='vertical', command=self.task_listbox.yview)
        scrollbar.pack(side='right', fill='y')
        self.task_listbox.config(yscrollcommand=scrollbar.set)

        # Task control buttons
        btn_frame = ttk.Frame(self.task_frame)
        btn_frame.pack(pady=10)

        remove_button = ttk.Button(btn_frame, text='Remove Selected Task(s)', command=self.remove_task)
        remove_button.grid(row=0, column=0, padx=5)

        clear_button = ttk.Button(btn_frame, text='Clear Completed Tasks', command=self.clear_completed_tasks)
        clear_button.grid(row=0, column=1, padx=5)

        edit_button = ttk.Button(btn_frame, text='Edit Task', command=self.edit_task)
        edit_button.grid(row=0, column=2, padx=5)

    def add_task(self):
        task_text = self.task_entry.get().strip()
        if not task_text:
            messagebox.showwarning('Input Error', 'Please enter a task.')
            return
        # Add a new task (not completed, not marked as important by default)
        self.tasks.append({'text': task_text, 'completed': False, 'important': False})
        self.task_entry.delete(0, 'end')
        self.update_task_list()

    def remove_task(self):
        selected_indices = list(self.task_listbox.curselection())
        if not selected_indices:
            messagebox.showwarning('Selection Error', 'Please select at least one task to remove.')
            return
        # Remove tasks in reverse order
        for index in sorted(selected_indices, reverse=True):
            try:
                del self.tasks[index]
            except IndexError:
                continue
        self.update_task_list()

    def toggle_task_completion(self, event=None):
        selection = self.task_listbox.curselection()
        if not selection:
            return
        index = selection[0]
        self.tasks[index]['completed'] = not self.tasks[index]['completed']
        self.update_task_list()
        self.task_listbox.select_set(index)

    def toggle_task_importance(self, index):
        self.tasks[index]['important'] = not self.tasks[index]['important']
        self.update_task_list()
        self.task_listbox.select_set(index)

    def edit_task(self):
        selection = self.task_listbox.curselection()
        if not selection or len(selection) > 1:
            messagebox.showwarning('Selection Error', 'Select a single task to edit.')
            return
        index = selection[0]
        current_text = self.tasks[index]['text']
        new_text = simpledialog.askstring('Edit Task', 'Modify your task:', initialvalue=current_text, parent=self.root)
        if new_text is None or new_text.strip() == '':
            return
        self.tasks[index]['text'] = new_text.strip()
        self.update_task_list()
        self.task_listbox.select_set(index)

    def clear_completed_tasks(self):
        self.tasks = [task for task in self.tasks if not task['completed']]
        self.update_task_list()

    def update_task_list(self):
        self.task_listbox.delete(0, 'end')
        filter_option = self.task_filter.get()
        for idx, task in enumerate(self.tasks):
            if filter_option == 'Pending' and task['completed']:
                continue
            if filter_option == 'Completed' and not task['completed']:
                continue
            display_text = task['text']
            if task['important']:
                display_text = '(!) ' + display_text
            if task['completed']:
                display_text = '[Completed] ' + display_text
            self.task_listbox.insert('end', display_text)

    def show_task_context_menu(self, event):
        try:
            index = self.task_listbox.nearest(event.y)
            self.task_listbox.selection_clear(0, 'end')
            self.task_listbox.selection_set(index)
            context_menu = tk.Menu(self.root, tearoff=0)
            context_menu.add_command(label='Toggle Completed', command=lambda: self.toggle_task_completion())
            context_menu.add_command(label='Toggle Importance', command=lambda: self.toggle_task_importance(index))
            context_menu.add_command(label='Edit Task', command=self.edit_task)
            context_menu.add_command(label='Delete Task', command=self.remove_task)
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu.grab_release()


def main():
    root = tk.Tk()
    app = CalendarAndTaskApp(root)
    root.mainloop()


if __name__ == '__main__':
    main()
