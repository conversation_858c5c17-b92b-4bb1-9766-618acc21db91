import os
from openai import OpenAI
import json
from termcolor import colored

# Constants
MODEL = "o3-mini"
REASONING_EFFORT = "medium"
SYSTEM_PROMPT = "You are a helpful coding assistant. Return your response as valid JSON with a 'code' field containing the implementation."
USER_PROMPT = "please build a simple space shooter game  in pygame"
IMPROVEMENT_PROMPT = "Improve the following Python code by adding new features, refactoring, or fixing bugs. Return the complete improved code in JSON format.\n\nPrevious Code:\n```python\n{}\n```"
NUM_ITERATIONS = 3  # Number of iterations for code improvement
OUTPUT_FOLDER = "iterations"  # Single folder for all iterations
OUTPUT_FILE_TEMPLATE = "output_{}.py"  # Template for enumerated files

try:
    client = OpenAI()

    # Create the iterations folder if it doesn't exist
    os.makedirs(OUTPUT_FOLDER, exist_ok=True)

    # Initial code generation
    current_prompt = USER_PROMPT
    previous_code = None

    for iteration in range(1, NUM_ITERATIONS + 1):
        output_file = os.path.join(OUTPUT_FOLDER, OUTPUT_FILE_TEMPLATE.format(iteration))

        print(colored(f"\nIteration {iteration}: Generating code...", "yellow"))

        messages = [
            {
                "role": "system",
                "content": SYSTEM_PROMPT
            },
            {
                "role": "user",
                "content": current_prompt
            }
        ]
        if previous_code:
            messages.insert(1, {  # Insert improvement prompt after system prompt
                "role": "user",
                "content": IMPROVEMENT_PROMPT.format(previous_code)
            })
            print(colored("  ...improving code from previous iteration...", "cyan"))

        response = client.chat.completions.create(
            model=MODEL,
            response_format={"type": "json_object"},
            reasoning_effort=REASONING_EFFORT,
            messages=messages
        )

        try:
            response_json = json.loads(response.choices[0].message.content)
            generated_code = response_json["code"]

            print(colored(f"  Code generated for iteration {iteration}:", "green"))
            # print(colored(generated_code, "white")) # No need to print to console every iteration

            # Save the code to a file
            try:
                with open(output_file, "w", encoding="utf-8") as f:
                    f.write(generated_code)
                print(colored(f"  Code saved to {output_file}", "green"))
            except IOError as e:
                print(colored(f"Error saving file: {str(e)}", "red"))

            previous_code = generated_code  # Update previous_code for next iteration
            current_prompt = "Improve the code from the previous iteration" # Generic prompt for improvement

        except json.JSONDecodeError as e:
            print(colored(f"Error parsing JSON response in iteration {iteration}: {str(e)}", "red"))
            break # Stop iterations if JSON error occurs
        except Exception as e:
            print(colored(f"An error occurred in iteration {iteration}: {str(e)}", "red"))
            break # Stop iterations if other error occurs


    print(colored("\nCode generation and improvement process completed.", "magenta"))

except Exception as e:
    print(colored(f"An error occurred during the process: {str(e)}", "red")) 