import os
import re
from termcolor import cprint, colored
from google import genai
from google.genai import types

# Major variables
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
MODEL_NAME = 'models/gemini-2.0-flash'
YOUTUBE_VIDEO_URL = "https://www.youtube.com/watch?v=9hE5-98ZeCg" # Full YouTube URL
# Use the same video URI as in the original script for the API call
VIDEO_FILE_URI = YOUTUBE_VIDEO_URL

def parse_timestamp_to_seconds(timestamp_str: str) -> int:
    """Converts HH:MM:SS or MM:SS timestamp string to seconds."""
    try:
        parts = list(map(int, timestamp_str.split(':')))
        if len(parts) == 3:  # HH:MM:SS
            return parts[0] * 3600 + parts[1] * 60 + parts[2]
        elif len(parts) == 2:  # MM:SS
            return parts[0] * 60 + parts[1]
        else:
            cprint(f"Invalid timestamp format encountered: {timestamp_str}", "red")
            raise ValueError("Timestamp format must be HH:MM:SS or MM:SS")
    except ValueError as e:
        # Re-raise for the caller to handle if needed, or log and return 0/None
        cprint(f"Error parsing timestamp '{timestamp_str}': {e}", "red")
        raise

def format_and_print_summary(summary_text: str, full_video_url: str):
    """Parses summary text, creates clickable YouTube links, and prints in color."""
    cprint("Processing summary output...", "yellow")
    if not summary_text:
        cprint("Received empty summary text.", "yellow")
        return

    lines = summary_text.strip().split('\n') # Handle escaped newlines if API returns them
    if len(lines) == 1 and '\n' not in summary_text: # if no escaped newlines, try splitting by actual newlines
        lines = summary_text.strip().split('\n')


    # Regex to capture [HH:MM:SS], [MM:SS] or [M:SS] and the rest of the line
    timestamp_pattern = re.compile(r"\[?(\d{1,2}:\d{2}(?::\d{2})?)\]?:?\s*(.*)", re.IGNORECASE)


    if not lines or (len(lines) == 1 and not lines[0].strip()):
        cprint("No lines to process after splitting summary text.", "yellow")
        cprint(f"Original summary text was: '{summary_text}'", "magenta")
        return

    for line_number, line in enumerate(lines):
        line = line.strip()
        if not line:
            continue
        
        try:
            match = timestamp_pattern.match(line)
            if match:
                timestamp_str = match.group(1)
                summary_content = match.group(2)
                
                seconds = parse_timestamp_to_seconds(timestamp_str)
                # Use the full_video_url directly and append the time parameter
                clickable_url = f"{full_video_url}&t={seconds}s"
                
                cprint(f"\[{timestamp_str}\]", "cyan", end="")
                cprint(f": {summary_content} ", "white", end="")
                cprint(f"(Watch: {clickable_url})", "green")
            else:
                # If line doesn't match, print it as is, maybe in a different color
                cprint(f"Line {line_number+1} (no timestamp match): {line}", "grey")
        except ValueError: # Catch errors from parse_timestamp_to_seconds
             cprint(f"Skipping line due to timestamp parse error: '{line}'", "red")
        except Exception as e:
            cprint(f"Error processing line: '{line}'. Error: {e}", "red")
            cprint(line, "grey") # Print original line if error during processing

def main():
    """Main function to run the Gemini API call and process the output."""
    cprint("Script started.", "magenta")

    if not GEMINI_API_KEY:
        cprint("Error: GEMINI_API_KEY environment variable not set.", "red")
        cprint("Please set the GEMINI_API_KEY environment variable to your API key.", "red")
        return

    cprint("Initializing Gemini Client...", "yellow")
    try:
        client = genai.Client(api_key=GEMINI_API_KEY)
        cprint("Gemini Client initialized successfully.", "green")
    except Exception as e:
        cprint(f"Failed to initialize Gemini Client: {e}", "red")
        return

    cprint(f"Requesting timestamped summary for video: {VIDEO_FILE_URI}", "yellow")
    
    try:
        cprint("Calling Gemini API...", "blue")
        # The API call format is preserved as per original script and user request
        response = client.models.generate_content(
            model=MODEL_NAME,
            contents=types.Content(
                parts=[
                    types.Part(
                        file_data=types.FileData(file_uri=VIDEO_FILE_URI)
                    ),
                    types.Part(text='timestamps with summaries') # This prompt is key
                ]
            )
        )
        cprint("API call successful.", "green")
        
        if response.text:
            cprint("Received response from API:", "green")
            # For debugging, print the raw response text before formatting
            # cprint(f"Raw API response text: '{response.text}'", "magenta")
            format_and_print_summary(response.text, YOUTUBE_VIDEO_URL) # Pass the full URL
        else:
            cprint("No text content received from API.", "red")
            if hasattr(response, 'prompt_feedback') and response.prompt_feedback:
                 cprint(f"Prompt feedback: {response.prompt_feedback}", "magenta")
            if hasattr(response, 'candidates') and response.candidates:
                candidate = response.candidates[0]
                if hasattr(candidate, 'finish_reason') and candidate.finish_reason:
                    cprint(f"Finish Reason: {candidate.finish_reason}", "magenta")
                if hasattr(candidate, 'safety_ratings') and candidate.safety_ratings:
                    cprint(f"Safety Ratings: {candidate.safety_ratings}", "magenta")
            else:
                cprint(f"Full response object: {response}", "magenta")


    except types.generation_types.BlockedPromptException as e:
        cprint(f"API call failed: Prompt was blocked.", "red")
        cprint(f"Details: {e}", "red")
        if hasattr(response, 'prompt_feedback') and response.prompt_feedback:
            cprint(f"Prompt Feedback: {response.prompt_feedback}", "magenta")
    except types.generation_types.StopCandidateException as e:
        cprint(f"API call failed: Candidate generation stopped.", "red")
        cprint(f"Details: {e}", "red")
        if hasattr(response, 'candidates') and response.candidates and len(response.candidates) > 0:
            candidate = response.candidates[0]
            if hasattr(candidate, 'finish_reason'):
                cprint(f"Candidate Finish Reason: {candidate.finish_reason}", "magenta")
            if hasattr(candidate, 'safety_ratings'):
                cprint(f"Candidate Safety Ratings: {candidate.safety_ratings}", "magenta")
    except Exception as e:
        cprint(f"An error occurred during the API call or processing: {e}", "red")
        import traceback
        cprint(f"Traceback:\n{traceback.format_exc()}", "red")

    cprint("Script finished.", "magenta")

if __name__ == "__main__":
    main()