# Song with Generated Musings Mixer

This Python script (`mix_musing_into_song.py`) dynamically generates philosophical musings on a chosen topic using OpenAI's GPT models, converts these musings to speech using OpenAI's TTS API, and then mixes them into a background music track at random points with various audio effects.

## Features

*   **AI-Generated Content:** Creates unique, short, philosophical musings using OpenAI's chat completion API (`gpt-4o` by default).
*   **Text-to-Speech:** Converts generated text into audio using OpenAI's TTS API (`gpt-4o-mini-tts` by default) with configurable voice and instructions.
*   **Randomized Selection:** Can automatically select a random background song from the `songs/` directory, a random musing topic from a predefined list, and a random TTS voice.
*   **Audio Mixing:** Uses `pydub` to overlay the generated speech onto the background track at random intervals.
*   **Audio Effects:** Applies configurable audio effects (filtering, pitch shifting, echo, normalization) to the speech overlays.
*   **Configurable Parameters:** Many aspects of the process can be customized via variables at the top of the script (e.g., number of musings, insertion points, gain levels, panning, effect parameters).
*   **Asynchronous TTS:** Generates TTS files concurrently for faster processing.
*   **Output:** Exports the final mixed audio to an MP3 file (`song_with_generated_musings.mp3` by default) and optionally saves the generated musings text to a JSON file (`generated_musings.json`).

## Prerequisites

1.  **Python 3.7+:** Ensure you have a compatible Python version installed.
2.  **OpenAI API Key:** You need an API key from OpenAI. Set it as an environment variable:
    ```bash
    export OPENAI_API_KEY='your_api_key_here'
    # On Windows (Command Prompt)
    set OPENAI_API_KEY=your_api_key_here
    # On Windows (PowerShell)
    $env:OPENAI_API_KEY='your_api_key_here'
    ```
3.  **FFmpeg:** The `pydub` library relies on FFmpeg for handling various audio formats. You **must** install FFmpeg and ensure it's available in your system's PATH.
    *   **Installation Guide:** [How to install FFmpeg on Linux, Windows, and macOS](https://www.hostinger.com/tutorials/how-to-install-ffmpeg)
4.  **Python Packages:** Install the required packages using pip:
    ```bash
    pip install -r requirements.txt
    ```

## Directory Structure

*   `songs/`: Place your background music files (MP3, WAV, FLAC, etc.) in this directory. The script can pick one randomly if no specific file is set.
*   `generated_musings/` (Temporary): This directory is created automatically to store the intermediate TTS audio files. It's usually deleted after the script finishes (controlled by `cleanup_temp_audio`).
*   `mix_musing_into_song.py`: The main script file.
*   `requirements.txt`: Lists Python dependencies.
*   `README.md`: This file.
*   `song_with_generated_musings.mp3` (Output): The final mixed audio file.
*   `generated_musings.json` (Optional Output): Contains the generated musing texts if `save_musings_json` is `True`.

## Configuration

Open `mix_musing_into_song.py` and adjust the variables in the "Configuration" section near the top:

*   `songs_dir`: Path to your background music directory.
*   `background_track_file`: Specify a path to a specific song, or leave empty `""` to select randomly from `songs_dir`.
*   `output_file`: Name of the final output MP3 file.
*   `musing_topic_list`: Add or remove potential topics for the AI to muse about.
*   `selected_musing_topic`: Specify a topic, or leave empty `""` to select randomly from the list.
*   `openai_tts_voice_list`: List of available OpenAI TTS voices.
*   `selected_openai_tts_voice`: Specify a voice (e.g., `"echo"`), or leave empty `""` to select randomly.
*   `num_musings_to_generate`: How many distinct musing sentences to request from the AI.
*   `openai_chat_model`, `openai_tts_model`: Specify the OpenAI models to use.
*   `openai_tts_instructions`: Detailed instructions for the TTS voice's accent, tone, pacing, etc.
*   `temp_audio_dir`: Path for temporary TTS files.
*   `cleanup_temp_audio`: Set to `False` to keep the generated TTS files.
*   `save_musings_json`: Set to `True` to save the generated texts.
*   `musings_json_file`: Filename for the saved musings.
*   Audio Effect Parameters (`low_pass_cutoff`, `high_pass_cutoff`, `apply_intercom_effect`, `pitch_shift_semitones`, `echo_delay_ms`, etc.): Fine-tune the sound of the overlays.
*   Insertion Parameters (`num_insertions`, `first_insertion_min_time_sec`, `min_gain_db`, `max_gain_db`, `min_pan`, `max_pan`, `min_gap_factor`): Control how many overlays are added and where/how they are placed.

## How it Works

1.  **Initialization:** Reads configuration, sets up OpenAI clients.
2.  **Selection:** Determines the background song, musing topic, and TTS voice (randomly, if not specified).
3.  **Musing Generation:** Calls the OpenAI Chat API to generate the requested number of musings on the selected topic, expecting a JSON response.
4.  **TTS Generation:** Calls the OpenAI TTS API asynchronously to convert each musing text into an MP3 audio file using the selected voice and instructions. These are saved temporarily.
5.  **Background Loading:** Loads the selected background music track using `pydub`.
6.  **Mixing Loop:**
    *   Iterates for the configured `num_insertions`.
    *   Randomly selects one of the generated TTS audio files.
    *   Applies the configured audio effects (filters, pitch shift, echo, normalization) to the selected TTS audio.
    *   Attempts to find a random insertion point (timestamp) in the background track that doesn't overlap too closely with previous insertions.
    *   Applies random gain (volume) and panning (left/right balance) to the processed TTS audio.
    *   Overlays the adjusted TTS audio onto the background track at the chosen insertion point.
7.  **Export:** Saves the final combined audio track as an MP3 file.
8.  **Cleanup:** Deletes the temporary TTS audio directory if configured.
9.  **Report:** Prints details about the successfully placed insertions.

## Usage

1.  Ensure all prerequisites are met (Python, API key, FFmpeg, installed packages).
2.  Place background music files in the `songs/` directory.
3.  (Optional) Customize parameters in `mix_musing_into_song.py`.
4.  Run the script from your terminal:
    ```bash
    python mix_musing_into_song.py
    ```
5.  Wait for the process to complete. Check the terminal for progress and any errors.
6.  Find the final output in `song_with_generated_musings.mp3` (or your configured output filename). 