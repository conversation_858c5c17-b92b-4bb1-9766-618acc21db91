import asyncio
import os
import random
import shutil
import json
from pathlib import Path
from termcolor import colored
from pydub import AudioSegment
from pydub.effects import normalize
from openai import OpenAI, AsyncOpenAI

# --- Configuration ---

# Input Files / Selection
songs_dir = Path("./songs") # Directory containing background songs
background_track_file = "" # Leave empty to select a random song from songs_dir

output_file = "song_with_generated_musings.mp3"

# --- OpenAI Configuration ---
# List of potential topics
musing_topic_list = [
    "the subtle beauty of decaying leaves in autumn",
    "the power of a single moment in time",
    "awe inspiring universe",
    "the beauty of a simple act of kindness",
    "the magic of a child's imagination",
    "unreasonable effectiveness of mathematics",
    "the beauty of a sunset over a calm ocean",
    "the magic of a snowflake",
]
selected_musing_topic = "" # Leave empty to select a random topic from musing_topic_list

# TTS Voice Selection
openai_tts_voice_list = ["alloy", "ash", "ballad", "coral", "echo", "fable", "onyx", "nova", "sage", "shimmer", "verse"]
selected_openai_tts_voice = "" # Leave empty to select a random voice from the list

num_musings_to_generate = 5
openai_chat_model = "gpt-4o"
openai_tts_model = "gpt-4o-mini-tts"
# openai_tts_voice = "ash" # Replaced by selected_openai_tts_voice
openai_tts_instructions = "Accent/Affect: slight French accent; sophisticated yet friendly, clearly understandable with a charming touch of French intonation. Tone: Warm and a little snooty. Speak with pride and knowledge for the art being presented. Pacing: Moderate, with deliberate pauses at key observations to allow listeners to appreciate details. Emotion: Calm, knowledgeable enthusiasm; show genuine reverence and fascination for the artwork. Pronunciation: Clearly articulate French words (e.g., 'Mes amis,' 'incroyable') in French and artist names (e.g., 'Leonardo da Vinci') with authentic French pronunciation. Personality Affect: Cultured, engaging, and refined, guiding visitors with a blend of artistic passion and welcoming charm."

temp_audio_dir = Path("./generated_musings")
cleanup_temp_audio = True

save_musings_json = True
musings_json_file = "generated_musings.json"

# --- Audio Effect Parameters ---
low_pass_cutoff = 500
high_pass_cutoff = 80
apply_intercom_effect = False
intercom_low_pass = 3000
intercom_high_pass = 300
intercom_pre_gain_db = 3
pitch_shift_semitones = -1.5
echo_delay_ms = 150
echo_decay_db = -10
echo2_delay_ms = 75
echo2_decay_db = -14
normalize_processed_overlay = True

# --- Automatic Insertion Parameters ---
num_insertions = 15
first_insertion_min_time_sec = 5
min_gain_db = -7
max_gain_db = -1
min_pan = -1.0
max_pan = 1.0
min_gap_factor = 0.8
max_placement_attempts = 50

# --- OpenAI Clients ---
if not os.getenv("OPENAI_API_KEY"):
    print(colored("Error: OPENAI_API_KEY environment variable not set.", "red"))
    exit()

client = OpenAI()
async_client = AsyncOpenAI()

# --- Helper Functions ---

def generate_musings(topic, count):
    print(colored(f"Generating {count} musings about '{topic}' using {openai_chat_model} in JSON mode...", "cyan"))
    try:
        response = client.chat.completions.create(
            model=openai_chat_model,
            response_format={"type": "json_object"},
            messages=[
                {"role": "system", "content": f"You are a profound, slightly melancholic philosopher. Generate short, single-sentence musings. Respond ONLY with a JSON object containing a single key 'musings' which is a list of exactly {count} distinct strings (the musings)."},
                {"role": "user", "content": f"Generate {count} distinct, short, profound, single-sentence musings about: {topic}"}
            ],
            n=1,
            temperature=0.8,
        )
        content = response.choices[0].message.content
        data = json.loads(content)
        musings = data.get("musings", [])

        if not isinstance(musings, list) or not all(isinstance(m, str) for m in musings):
            print(colored("Error: Model did not return a valid JSON list of strings under the 'musings' key.", "red"))
            print(f"Received: {content}")
            return []

        print(colored(f"Successfully generated {len(musings)} musings from JSON response.", "green"))
        if len(musings) < count:
            print(colored(f"Warning: Generated fewer musings ({len(musings)}) than requested ({count}). Check model output or prompt.", "yellow"))
        return musings[:count]
    except json.JSONDecodeError as e:
        print(colored(f"Error decoding JSON response from OpenAI: {e}", "red"))
        print(f"Received content: {content}")
        return []
    except Exception as e:
        print(colored(f"Error generating musings: {e}", "red"))
        return []

async def generate_tts_for_musing(musing_text, index, output_path):
    try:
        speech_file_path = output_path / f"musing_{index}.mp3"
        async with async_client.audio.speech.with_streaming_response.create(
            model=openai_tts_model,
            voice=selected_openai_tts_voice,
            input=musing_text,
            instructions=openai_tts_instructions,
        ) as response:
            await response.stream_to_file(speech_file_path)
        return str(speech_file_path)
    except Exception as e:
        print(colored(f"Error generating TTS for musing {index+1} (voice: {selected_openai_tts_voice}): {e}", "red"))
        return None

async def generate_all_tts(musings, output_dir):
    print(colored(f"Generating TTS for {len(musings)} musings in parallel...", "cyan"))
    output_dir.mkdir(parents=True, exist_ok=True)
    tasks = [
        generate_tts_for_musing(text, i, output_dir)
        for i, text in enumerate(musings)
    ]
    results = await asyncio.gather(*tasks)
    successful_files = [f for f in results if f is not None]
    color = "green" if len(successful_files) == len(musings) else "yellow"
    print(colored(f"Successfully generated {len(successful_files)} out of {len(musings)} requested TTS files.", color))
    return successful_files

def apply_audio_effects(audio_segment):
    processed = audio_segment

    if apply_intercom_effect and intercom_pre_gain_db != 0:
        print(f"    Applying intercom pre-gain ({intercom_pre_gain_db}dB)...")
        processed = processed + intercom_pre_gain_db

    print("    Applying filters...")
    if apply_intercom_effect:
        print(f"      (Using Intercom filter: HPF={intercom_high_pass}Hz, LPF={intercom_low_pass}Hz)")
        processed = processed.low_pass_filter(intercom_low_pass)
        processed = processed.high_pass_filter(intercom_high_pass)
    else:
        print(f"      (Using General filter: HPF={high_pass_cutoff}Hz, LPF={low_pass_cutoff}Hz)")
        processed = processed.low_pass_filter(low_pass_cutoff)
        processed = processed.high_pass_filter(high_pass_cutoff)

    if pitch_shift_semitones != 0:
        print("    Applying pitch shift...")
        new_sample_rate = int(processed.frame_rate * (2.0 ** (pitch_shift_semitones / 12.0)))
        processed = processed._spawn(processed.raw_data, overrides={'frame_rate': new_sample_rate})
        processed = processed.set_frame_rate(audio_segment.frame_rate)

    print("    Applying echo effect...")
    delay1 = AudioSegment.silent(duration=echo_delay_ms)
    echo1 = processed - abs(echo_decay_db)
    processed = processed.overlay(delay1 + echo1, position=0)
    delay2 = AudioSegment.silent(duration=echo2_delay_ms)
    echo2 = processed - abs(echo2_decay_db)
    processed = processed.overlay(delay2 + echo2, position=0)

    if normalize_processed_overlay:
        print("    Normalizing processed overlay...")
        processed = normalize(processed)

    return processed

# --- Main Execution ---
async def main():
    # --- Determine Song, Topic, and Voice --- (Order matters slightly)
    global background_track_file, selected_musing_topic, selected_openai_tts_voice

    # Select random song if needed
    if not background_track_file:
        print(colored(f"No background track specified. Selecting random song from '{songs_dir}'...", "magenta"))
        if not songs_dir.is_dir():
            print(colored(f"Error: Songs directory '{songs_dir}' not found.", "red"))
            return
        try:
            audio_extensions = (".mp3", ".wav", ".flac", ".ogg", ".aac", ".m4a")
            potential_songs = [f for f in songs_dir.iterdir() if f.is_file() and f.suffix.lower() in audio_extensions]
            if not potential_songs:
                 print(colored(f"Error: No audio files found in '{songs_dir}'.", "red"))
                 return
            chosen_song_path = random.choice(potential_songs)
            background_track_file = str(chosen_song_path)
            print(colored(f"Selected song: {background_track_file}", "magenta"))
        except Exception as e:
             print(colored(f"Error listing or selecting songs from '{songs_dir}': {e}", "red"))
             return

    # Select random topic if needed
    if not selected_musing_topic:
        if not musing_topic_list:
            print(colored("Error: Musing topic list is empty and no topic was selected.", "red"))
            return
        print(colored("No musing topic specified. Selecting random topic...", "magenta"))
        selected_musing_topic = random.choice(musing_topic_list)
        print(colored(f"Selected topic: '{selected_musing_topic}'", "magenta"))

    # Select random voice if needed
    if not selected_openai_tts_voice:
        if not openai_tts_voice_list:
             print(colored("Error: TTS Voice list is empty and no voice was selected.", "red"))
             return
        print(colored("No TTS voice specified. Selecting random voice...", "magenta"))
        selected_openai_tts_voice = random.choice(openai_tts_voice_list)
        print(colored(f"Selected voice: '{selected_openai_tts_voice}'", "magenta"))
    elif selected_openai_tts_voice not in openai_tts_voice_list:
         print(colored(f"Warning: Selected TTS voice '{selected_openai_tts_voice}' is not in the known list: {openai_tts_voice_list}", "yellow"))
         # Allow it anyway, maybe OpenAI added a new one?

    # -------------------------------------

    # 1. Generate Musing Texts (using the determined topic)
    musings = generate_musings(selected_musing_topic, num_musings_to_generate)
    if not musings:
        print(colored("Failed to generate musings. Exiting.", "red"))
        return

    # Save Musings to JSON
    if save_musings_json:
        try:
            # Use the *selected* topic in the saved JSON
            with open(musings_json_file, 'w', encoding='utf-8') as f:
                json.dump({"topic": selected_musing_topic, "musings": musings}, f, ensure_ascii=False, indent=4)
            print(colored(f"Successfully saved generated musings to {musings_json_file}", "green"))
        except Exception as e:
            print(colored(f"Warning: Failed to save musings to {musings_json_file}: {e}", "yellow"))

    # 2. Generate TTS Audio Files
    generated_audio_files = await generate_all_tts(musings, temp_audio_dir)
    if not generated_audio_files:
        print(colored("Failed to generate any TTS audio. Exiting.", "red"))
        if cleanup_temp_audio and temp_audio_dir.exists():
             shutil.rmtree(temp_audio_dir)
             print(colored(f"Cleaned up temporary directory: {temp_audio_dir}", "magenta"))
        return

    # 3. Load Background Track (using the determined file path)
    print(colored(f"Loading background track: {background_track_file}...", "cyan"))
    if not os.path.exists(background_track_file):
        # This check might be redundant now but kept as a safeguard
        print(colored(f"Error: Background track '{background_track_file}' not found.", "red"))
        return
    try:
        background_track = AudioSegment.from_file(background_track_file) # Use from_file for broader format support
    except Exception as e:
        print(colored(f"Error loading background track '{background_track_file}': {e}", "red"))
        print(colored("Ensure ffmpeg is installed and accessible in your PATH.", "red"))
        return

    background_duration_ms = len(background_track)
    first_insertion_min_time_ms = int(first_insertion_min_time_sec * 1000)
    print(f"Background duration: {background_duration_ms / 1000:.2f}s")

    # 4. Generate Random Insertion Points & Mix
    successful_insertions = []
    chosen_start_times = []
    final_mix = background_track

    print(colored(f"Attempting to generate and mix {num_insertions} insertions...", "cyan"))

    for i in range(num_insertions):
        if not generated_audio_files: # Check if TTS failed entirely
            print(colored("Skipping mixing loop as no TTS files were generated.", "yellow"))
            break
        selected_audio_path = random.choice(generated_audio_files)
        try:
            overlay_audio = AudioSegment.from_mp3(selected_audio_path)
        except Exception as e:
            print(colored(f"  Warning: Error loading {selected_audio_path}: {e}. Skipping this insertion.", "yellow"))
            continue

        processed_overlay_sound = apply_audio_effects(overlay_audio)
        overlay_duration_ms = len(processed_overlay_sound)
        min_gap_ms = int(overlay_duration_ms * min_gap_factor)

        placed = False
        for attempt in range(max_placement_attempts):
            min_possible_start = 0
            if not chosen_start_times:
                 min_possible_start = first_insertion_min_time_ms
            max_possible_start = background_duration_ms - overlay_duration_ms
            if max_possible_start < min_possible_start:
                break
            potential_start_time = random.randint(min_possible_start, max_possible_start)
            collision = False
            for existing_start_time in chosen_start_times:
                if abs(potential_start_time - existing_start_time) < min_gap_ms:
                    collision = True
                    break
            if not collision:
                chosen_start_times.append(potential_start_time)
                gain = random.uniform(min_gain_db, max_gain_db)
                pan = random.uniform(min_pan, max_pan)
                insertion_details = {
                    "time_ms": potential_start_time,
                    "gain_db": round(gain, 1),
                    "pan": round(pan, 2),
                    "source_file": selected_audio_path
                }
                successful_insertions.append(insertion_details)
                adjusted_overlay = processed_overlay_sound + insertion_details['gain_db']
                adjusted_overlay = adjusted_overlay.pan(insertion_details['pan'])
                final_mix = final_mix.overlay(adjusted_overlay, position=potential_start_time)
                placed = True
                break
        if not placed and max_possible_start >= min_possible_start:
             print(colored(f"  Warning: Could not place insertion {i+1} without collision after {max_placement_attempts} attempts.", "yellow"))

    print(colored(f"Successfully generated and mixed {len(successful_insertions)} insertions.", "green"))

    # 5. Export Final Mix
    print(colored(f"\nExporting final mix to: {output_file}", "cyan"))
    try:
        # Export as MP3 with a high quality bitrate
        final_mix.export(output_file, format="mp3", bitrate="320k")
        print(colored("Mixing complete.", "green"))
    except Exception as e:
        print(colored(f"Error exporting file: {e}", "red"))

    # 6. Cleanup Temporary Files
    if cleanup_temp_audio and temp_audio_dir.exists():
        try:
            shutil.rmtree(temp_audio_dir)
            print(colored(f"Cleaned up temporary directory: {temp_audio_dir}", "magenta"))
        except Exception as e:
            print(colored(f"Warning: Failed to cleanup temporary directory {temp_audio_dir}: {e}", "yellow"))

    # 7. Print Successful Insertion Times
    if successful_insertions:
        print(colored("\n--- Final Insertion Times (seconds) ---", "blue"))
        successful_insertions.sort(key=lambda x: x['time_ms'])
        for idx, insertion in enumerate(successful_insertions):
            print(f"  {idx+1}: {colored(f'{insertion["time_ms"] / 1000:.2f}s', 'white')} (Source: {Path(insertion['source_file']).name})")
        print(colored("-------------------------------------", "blue"))

if __name__ == "__main__":
    asyncio.run(main()) 