from pydub import AudioSegment
import os

# --- Parameters for Audio Effects ---

# Input and Output Files
input_file = "musing.wav"
output_file = "musing_profound.wav"

# Low-Pass Filter Cutoff (Hz)
# Removes frequencies above this value. Lower values create a more muffled, "older" sound.
# Cranked up effect: Using a relatively low cutoff.
low_pass_cutoff = 1500

# Pitch Shift (Semitones)
# How many musical semitones to shift the pitch down. Negative values lower the pitch, making it sound deeper.
# Cranked up effect: Shifting down significantly.
pitch_shift_semitones = -1

# Echo/Reverb Simulation
# Simple echo can give a sense of space or grandeur.
# Cranked up effect: Noticeable delay and moderate decay.
echo_delay_ms = 150  # Delay of the echo in milliseconds
echo_decay_db = -10   # How much quieter the echo is (negative dB). More negative = faster decay.

# Gain Adjustment (dB)
# Adjust overall volume after processing if needed. 0 means no change.
output_gain_db = 0

# --- Audio Processing ---

# Check if input file exists
if not os.path.exists(input_file):
    print(f"Error: Input file '{input_file}' not found.")
    exit()

print(f"Loading audio file: {input_file}")
try:
    # Load the audio file
    audio = AudioSegment.from_wav(input_file)

    print("Applying low-pass filter...")
    # Apply low-pass filter
    audio_filtered = audio.low_pass_filter(low_pass_cutoff)

    print("Applying pitch shift...")
    # Apply pitch shift
    # Formula: new_rate = old_rate * (2 ** (semitones / 12.0))
    new_sample_rate = int(audio_filtered.frame_rate * (2.0 ** (pitch_shift_semitones / 12.0)))
    audio_pitched = audio_filtered._spawn(audio_filtered.raw_data, overrides={'frame_rate': new_sample_rate})
    # Resample back to original sample rate to avoid playback speed issues,
    # keeping the pitch change.
    audio_pitched = audio_pitched.set_frame_rate(audio.frame_rate)


    print("Applying echo effect...")
    # Create a silent segment for the delay
    delay_segment = AudioSegment.silent(duration=echo_delay_ms)
    # Create the echo by overlaying a delayed, quieter version
    # Note: More sophisticated reverb requires dedicated libraries or plugins.
    echo_version = audio_pitched - abs(echo_decay_db) # Make the echo quieter
    audio_with_echo = audio_pitched.overlay(delay_segment + echo_version, position=0)


    print("Adjusting gain...")
    # Adjust final gain
    final_audio = audio_with_echo + output_gain_db

    print(f"Exporting processed audio to: {output_file}")
    # Export the final audio
    final_audio.export(output_file, format="wav")

    print("Processing complete.")

except Exception as e:
    print(f"An error occurred during processing: {e}")
    print("Please ensure ffmpeg is installed and in your system's PATH.")
    print("You can install pydub via: pip install pydub") 