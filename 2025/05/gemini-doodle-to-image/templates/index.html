<!DOCTYPE html>
<html lang="en" data-theme="night">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON> Doodle to Image</title>
    <link href="https://cdn.jsdelivr.net/npm/daisyui@4.10.2/dist/full.min.css" rel="stylesheet" type="text/css" />
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="{{ url_for('static', path='/css/style.css') }}">
    <style>
        /* Basic loading spinner */
        .loader {
            border: 8px solid #f3f3f3; /* Light grey */
            border-top: 8px solid #3498db; /* Blue */
            border-radius: 50%;
            width: 60px;
            height: 60px;
            animation: spin 2s linear infinite;
            position: absolute;
            top: 50%;
            left: 50%;
            margin-top: -30px;
            margin-left: -30px;
            z-index: 100;
            display: none; /* Hidden by default */
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        body {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            margin: 0;
        }
        .main-content {
            flex-grow: 1;
            display: grid;
            grid-template-columns: 1fr 2fr 2fr;
            gap: 1rem;
            padding: 1rem;
            max-width: 1400px;
            margin-left: auto;
            margin-right: auto;
            width: 100%;
        }
        .area {
            border: 1px solid #444;
            padding: 1rem;
            border-radius: 0.5rem;
            background-color: #2A303C; /* DaisyUI base-200 for night theme */
            min-height: 400px;
        }
        #doodleCanvas {
            border: 1px dashed #666;
            cursor: crosshair;
            touch-action: none; /* Important for touch devices */
        }
        #generatedImage {
            max-width: 100%;
            max-height: 500px;
            object-fit: contain;
            border-radius: 0.25rem;
        }
    </style>
</head>
<body class="bg-base-300 text-base-content">
    <div class="loader" id="loadingSpinner"></div>

    <header class="navbar bg-primary text-primary-content shadow-lg">
        <div class="flex-1">
            <a class="btn btn-ghost normal-case text-xl">Gemini Doodle-to-Image Editor</a>
        </div>
    </header>

    <main class="main-content">
        <!-- Settings Area -->
        <section id="settingsArea" class="area">
            <h2 class="text-2xl font-bold mb-4">Settings</h2>
            
            <div class="form-control w-full mb-4">
                <label class="label">
                    <span class="label-text">Enter your Prompt</span>
                </label>
                <textarea id="promptInput" class="textarea textarea-bordered h-24" placeholder="e.g., A futuristic city skyline at sunset"></textarea>
            </div>

            <div class="form-control w-full mb-4">
                <label class="label">
                    <span class="label-text">Image Style</span>
                </label>
                <select id="stylePreset" class="select select-bordered w-full">
                    <option value="none">Default (No specific style)</option>
                    <option value="photorealistic">Photorealistic</option>
                    <option value="cartoon">Cartoon</option>
                    <option value="sketch">Sketch</option>
                    <option value="impressionist">Impressionist Painting</option>
                    <option value="surreal">Surreal</option>
                    <option value="fantasy">Fantasy Art</option>
                    <option value="cyberpunk">Cyberpunk</option>
                    <option value="steampunk">Steampunk</option>
                    <option value="pixel_art">Pixel Art</option>
                    <option value="watercolor">Watercolor</option>
                    <option value="custom">Custom Style</option>
                </select>
            </div>

            <div class="form-control w-full mb-4 hidden" id="customStyleContainer">
                <label class="label">
                    <span class="label-text">Custom Style Prompt</span>
                </label>
                <input type="text" id="customStyleInput" placeholder="e.g., in the style of Van Gogh" class="input input-bordered w-full" />
            </div>
        </section>

        <!-- Doodling Area -->
        <section id="doodleArea" class="area flex flex-col items-center">
            <h2 class="text-2xl font-bold mb-4">Doodle Here</h2>
            <canvas id="doodleCanvas" width="400" height="400" class="bg-white"></canvas>
            <div class="mt-4 space-x-2">
                <button id="clearCanvasBtn" class="btn btn-outline btn-sm">Clear</button>
                <!-- Add more controls like color picker, brush size later if needed -->
            </div>
        </section>

        <!-- Image Generation Area -->
        <section id="imageGenArea" class="area flex flex-col items-center justify-center">
            <h2 class="text-2xl font-bold mb-4">Generated Image</h2>
            <div id="imageResultContainer" class="w-full h-full flex flex-col items-center justify-center">
                <img id="generatedImage" src="#" alt="Generated image will appear here" style="display:none;"/>
                <p id="imagePlaceholderText" class="text-gray-500">Your generated image will appear here.</p>
                <p id="apiTextResponse" class="mt-2 text-sm text-gray-400"></p>
            </div>
        </section>
    </main>

    <footer class="footer footer-center p-4 bg-base-200 text-base-content">
        <div>
            <button id="generateBtn" class="btn btn-primary btn-lg">Generate Image</button>
        </div>
    </footer>

    <script src="{{ url_for('static', path='/js/script.js') }}"></script>
    <!-- Anime.js CDN (optional, for later use) -->
    <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js"></script> -->
</body>
</html> 