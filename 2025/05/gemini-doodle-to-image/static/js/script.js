document.addEventListener('DOMContentLoaded', () => {
    const canvas = document.getElementById('doodleCanvas');
    const ctx = canvas.getContext('2d');
    const generateBtn = document.getElementById('generateBtn');
    const clearCanvasBtn = document.getElementById('clearCanvasBtn');
    const promptInput = document.getElementById('promptInput');
    const stylePreset = document.getElementById('stylePreset');
    const customStyleContainer = document.getElementById('customStyleContainer');
    const customStyleInput = document.getElementById('customStyleInput');
    const generatedImage = document.getElementById('generatedImage');
    const imagePlaceholderText = document.getElementById('imagePlaceholderText');
    const apiTextResponse = document.getElementById('apiTextResponse');
    const loadingSpinner = document.getElementById('loadingSpinner');

    let drawing = false;
    let lastPos = { x: 0, y: 0 };

    // Initialize canvas background to white (important for transparent PNG export)
    ctx.fillStyle = 'white';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    function getMousePos(canvasDom, event) {
        const rect = canvasDom.getBoundingClientRect();
        const scaleX = canvasDom.width / rect.width;
        const scaleY = canvasDom.height / rect.height;
        return {
            x: (event.clientX - rect.left) * scaleX,
            y: (event.clientY - rect.top) * scaleY
        };
    }

    function getTouchPos(canvasDom, touchEvent) {
        const rect = canvasDom.getBoundingClientRect();
        const scaleX = canvasDom.width / rect.width;
        const scaleY = canvasDom.height / rect.height;
        return {
            x: (touchEvent.touches[0].clientX - rect.left) * scaleX,
            y: (touchEvent.touches[0].clientY - rect.top) * scaleY
        };
    }

    function startDrawing(e) {
        drawing = true;
        lastPos = e.type.startsWith('touch') ? getTouchPos(canvas, e) : getMousePos(canvas, e);
        // Prevent page scrolling when drawing on touch devices
        if (e.type.startsWith('touch')) {
            e.preventDefault();
        }
    }

    function draw(e) {
        if (!drawing) return;
        const currentPos = e.type.startsWith('touch') ? getTouchPos(canvas, e) : getMousePos(canvas, e);
        ctx.beginPath();
        ctx.moveTo(lastPos.x, lastPos.y);
        ctx.lineTo(currentPos.x, currentPos.y);
        ctx.strokeStyle = '#000000'; // Black color for drawing
        ctx.lineWidth = 3;
        ctx.lineCap = 'round';
        ctx.stroke();
        ctx.closePath();
        lastPos = currentPos;
        if (e.type.startsWith('touch')) {
            e.preventDefault();
        }
    }

    function stopDrawing() {
        drawing = false;
    }

    // Mouse events
    canvas.addEventListener('mousedown', startDrawing);
    canvas.addEventListener('mousemove', draw);
    canvas.addEventListener('mouseup', stopDrawing);
    canvas.addEventListener('mouseout', stopDrawing); // Stop drawing if mouse leaves canvas

    // Touch events
    canvas.addEventListener('touchstart', startDrawing);
    canvas.addEventListener('touchmove', draw);
    canvas.addEventListener('touchend', stopDrawing);
    canvas.addEventListener('touchcancel', stopDrawing);

    clearCanvasBtn.addEventListener('click', () => {
        ctx.fillStyle = 'white';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        console.log('Canvas cleared');
    });

    stylePreset.addEventListener('change', () => {
        if (stylePreset.value === 'custom') {
            customStyleContainer.classList.remove('hidden');
        } else {
            customStyleContainer.classList.add('hidden');
        }
    });

    generateBtn.addEventListener('click', async () => {
        const prompt = promptInput.value.trim();
        let style = stylePreset.value;

        if (style === 'custom') {
            style = customStyleInput.value.trim();
        }
        if (!style || style === 'none') {
            style = 'default'; // Backend will handle 'default' or empty style appropriately
        }

        loadingSpinner.style.display = 'block';
        generatedImage.style.display = 'none';
        imagePlaceholderText.style.display = 'block';
        apiTextResponse.textContent = '';

        // Get doodle image as Blob
        canvas.toBlob(async (blob) => {
            if (!blob) {
                alert('Could not get image from canvas.');
                loadingSpinner.style.display = 'none';
                return;
            }

            const formData = new FormData();
            formData.append('prompt', prompt);
            formData.append('style', style);
            formData.append('doodle_image', blob, 'doodle.png');

            try {
                console.log('Sending generation request (prompt can be empty)...');
                const response = await fetch('/generate-image', {
                    method: 'POST',
                    body: formData,
                });

                loadingSpinner.style.display = 'none';

                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({ error: 'Failed to parse error response.' }));
                    console.error('Error from server:', errorData);
                    alert(`Error generating image: ${errorData.error || response.statusText}`);
                    imagePlaceholderText.textContent = `Error: ${errorData.error || response.statusText}`;
                    return;
                }

                const result = await response.json();
                console.log('Received response:', result);

                if (result.image_data && result.image_data !== "path/to/your/placeholder_image.png") {
                    if (result.image_data.startsWith('data:image')) { 
                        generatedImage.src = result.image_data;
                    } else { 
                        generatedImage.src = result.image_data; 
                    }
                    generatedImage.style.display = 'block';
                    imagePlaceholderText.style.display = 'none';
                } else if (result.image_url) { 
                    generatedImage.src = result.image_url;
                    generatedImage.style.display = 'block';
                    imagePlaceholderText.style.display = 'none';
                } else {
                    imagePlaceholderText.textContent = 'No image data received, or placeholder was returned.';
                    console.warn('No valid image data/URL in response or it was the placeholder:', result);
                }

                apiTextResponse.textContent = result.text_response || '';

            } catch (error) {
                loadingSpinner.style.display = 'none';
                console.error('Error during fetch:', error);
                alert('An unexpected error occurred. Check the console for details.');
                imagePlaceholderText.textContent = 'Client-side error during request.';
            }
        }, 'image/png');
    });
}); 