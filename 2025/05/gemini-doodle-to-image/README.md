# Gemini Doodle-to-Image Editor

This web application allows users to create doodles, provide text prompts (optional), and select image styles to generate images using Google's Gemini API for image editing/generation.

## Features

*   **Doodle Canvas:** A simple canvas for users to draw an initial image.
*   **Text Prompts:** Users can input text prompts to guide the image generation process. This field is optional.
*   **Style Presets:** A selection of predefined image styles (e.g., Photorealistic, Cartoon, Sketch) plus a custom style option.
*   **Image Generation:** Utilizes the Google Gemini API to edit the doodle based on the prompt and style.
*   **Dark Mode UI:** Built with Tailwind CSS and DaisyUI for a modern, dark-themed interface.

## Technologies Used

*   **Backend:** Python, FastAPI
*   **Frontend:** HTML, CSS, JavaScript
*   **Styling:** Tailwind CSS, DaisyUI
*   **Image Generation:** Google Gemini API (via `google-genai` library, specifically targeting the user-provided API structure)
*   **Image Handling:** Pillow (PIL)
*   **Real-time Logging:** Termcolor

## Project Structure

```
gemini-doodle-to-image/
├── main.py               # FastAPI backend logic and Gemini API interaction
├── requirements.txt      # Python dependencies
├── static/
│   ├── css/style.css     # Custom CSS styles
│   └── js/script.js      # Frontend JavaScript for canvas, API calls
├── templates/
│   └── index.html        # Main HTML page with Jinja2 templating
└── README.md             # This file
```

## Setup and Installation

### 1. Prerequisites

*   Python 3.8 or higher
*   A Google API Key with access to the Gemini API (specifically the model used, e.g., `gemini-2.0-flash-preview-image-generation`).

### 2. Clone the Repository (if applicable)

If this project were in a Git repository:
```bash
git clone <repository-url>
cd gemini-doodle-to-image
```

### 3. Create a Virtual Environment (Recommended)

```bash
python -m venv venv
```
Activate the virtual environment:
*   Windows: `.\venv\Scripts\activate`
*   macOS/Linux: `source venv/bin/activate`

### 4. Install Dependencies

```bash
pip install -r requirements.txt
```
This will install FastAPI, Uvicorn, `google-genai` (the library for Gemini), Pillow, python-multipart, Jinja2, and Termcolor.

### 5. Configure Google API Key

The application expects the Google API Key to be available as an environment variable named `GEMINI_API_KEY`.

*   **Linux/macOS:**
    ```bash
    export GEMINI_API_KEY="YOUR_ACTUAL_API_KEY"
    ```
    You can add this line to your `~/.bashrc` or `~/.zshrc` for persistence.

*   **Windows (PowerShell):**
    ```powershell
    $env:GEMINI_API_KEY="YOUR_ACTUAL_API_KEY"
    ```
    For persistence, you can set it through the System Properties > Environment Variables.

Alternatively, you can directly replace the placeholder in `main.py` (line 15):
```python
GOOGLE_API_KEY = os.getenv("GEMINI_API_KEY", "YOUR_API_KEY_HERE") 
# Replace "YOUR_API_KEY_HERE" with your actual key if not using environment variables.
```
**Note:** Using environment variables is highly recommended for security.

**Important Library Note:**
This project is configured to use the Gemini API via imports like `from google import genai` and `genai.Client()`. This specific structure relies on the version of the `google-genai` library that supports this. If `pip install -U google-genai` installs the `google-generativeai` library (which is common), `genai.Client()` might not be available. The application includes checks for this at startup. If you face issues, ensure your `google-genai` installation provides the `genai.Client` interface or adapt the client initialization in `main.py` to the `google-generativeai` pattern (`genai.configure(api_key=...)` and `model = genai.GenerativeModel(...)`).

## Running the Application

1.  Ensure your `GEMINI_API_KEY` is set correctly.
2.  Make sure your virtual environment is activated.
3.  Run the FastAPI application using Uvicorn:

    ```bash
    python main.py
    ```
    The server will start, and you should see output in your terminal, including `termcolor` logs:
    ```
    INFO:     Uvicorn running on http://127.0.0.1:8000 (Press CTRL+C to quit)
    ...
    Google GenAI client initialized successfully using genai.Client(). (or an error if not)
    ...
    ```

4.  Open your web browser and navigate to `http://127.0.0.1:8000`.

## How to Use

1.  **Doodle:** Use your mouse or touch to draw on the canvas in the "Doodle Here" section.
2.  **Prompt (Optional):** Enter a text description in the "Enter your Prompt" field to guide the image generation. You can leave this empty.
3.  **Style:** Select an image style from the dropdown. If you choose "Custom Style", an additional input field will appear for your custom style description.
4.  **Generate:** Click the "Generate Image" button.
5.  **View:** The generated image and any text response from the API will appear in the "Generated Image" section. A loading spinner will be shown during processing.

## Error Handling

*   The application includes `termcolor` logging in the backend for observing the request flow and potential errors.
*   API errors and client-side issues are reported via alerts or messages on the UI.
*   If the `GenAI Client` fails to initialize (e.g., due to API key issues or library mismatch), critical errors will be printed to the console on startup.

---

Enjoy creating images with your doodles! 