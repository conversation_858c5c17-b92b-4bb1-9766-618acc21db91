import os
import io
import base64 # For encoding image data
from fastapi import <PERSON><PERSON><PERSON>, Request, UploadFile, File, Form
from fastapi.responses import H<PERSON>LR<PERSON>ponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from PIL import Image
from termcolor import cprint

# --- Google GenAI Imports as per user specification ---
from google import genai # Assuming this is the intended library
from google.genai import types # Assuming this is the intended types import

# --- Constants ---
# For a real application, use environment variables for API keys
GOOGLE_API_KEY = os.getenv("GEMINI_API_KEY", "YOUR_API_KEY_HERE") 
# Using the exact model name specified by the user
MODEL_NAME = "gemini-2.0-flash-preview-image-generation" 

DEFAULT_PROMPT = "Edit this image based on the visual input provided."

# --- App Initialization ---
app = FastAPI()

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")

# Setup Jinja2 templates
templates = Jinja2Templates(directory="templates")

# --- GenAI Client Initialization (as per user's example) ---
CLIENT = None
if GOOGLE_API_KEY and GOOGLE_API_KEY != "YOUR_API_KEY_HERE":
    try:
        # Assuming genai.Client() is the correct way to initialize with the user-specified library
        CLIENT = genai.Client(api_key=GOOGLE_API_KEY) 
        cprint("Google GenAI client initialized successfully using genai.Client().", "green")
    except AttributeError:
        cprint("Error: genai.Client() not found. This suggests a mismatch with the 'google-generativeai' library.", "red")
        cprint("If you installed 'google-genai' (which is 'google-generativeai'), the API is genai.configure(api_key=...) and genai.GenerativeModel(...)", "red")
        CLIENT = None # Ensure client is None if initialization fails
    except Exception as e:
        cprint(f"Error initializing Google GenAI client with genai.Client(): {e}", "red")
        CLIENT = None # Ensure client is None if initialization fails
else:
    cprint("GOOGLE_API_KEY not found or is placeholder. API calls will fail.", "yellow")

# --- API Endpoints ---
@app.get("/", response_class=HTMLResponse)
async def read_root(request: Request):
    """Serves the main HTML page."""
    cprint("Serving index.html", "green")
    return templates.TemplateResponse("index.html", {"request": request})

@app.post("/generate-image")
async def generate_image_endpoint(
    request: Request,
    prompt: str = Form(None), # Prompt is now optional
    style: str = Form(...),
    doodle_image: UploadFile = File(...)
):
    actual_prompt = prompt if prompt and prompt.strip() else DEFAULT_PROMPT
    cprint(f"Received image generation request. Actual Prompt: {actual_prompt}, Style: {style}", "cyan")

    if not CLIENT:
        cprint("GenAI Client not initialized. Cannot process request.", "red")
        return JSONResponse(status_code=500, content={"error": "GenAI Client not initialized on server. Check API key and library setup."})

    try:
        doodle_bytes = await doodle_image.read()
        if not doodle_bytes:
            cprint("Received empty doodle image.", "red")
            return JSONResponse(status_code=400, content={"error": "Doodle image is empty."})
        cprint(f"Doodle image '{doodle_image.filename}' received, size: {len(doodle_bytes)} bytes.", "yellow")

        try:
            input_pil_image = Image.open(io.BytesIO(doodle_bytes))
            if input_pil_image.mode == 'RGBA':
                cprint("Converting RGBA image to RGB by pasting on white background", "yellow")
                background = Image.new('RGB', input_pil_image.size, (255, 255, 255))
                background.paste(input_pil_image, (0,0), input_pil_image)
                input_pil_image = background
            elif input_pil_image.mode != 'RGB':
                cprint(f"Converting image from mode {input_pil_image.mode} to RGB", "yellow")
                input_pil_image = input_pil_image.convert('RGB')
        except Exception as e:
            cprint(f"Error processing uploaded image: {e}", "red")
            return JSONResponse(status_code=400, content={"error": f"Invalid image format or error processing image: {e}"})

        final_text_input = actual_prompt
        if style and style.lower() != "default" and style.lower() != "none":
            final_text_input += f". Apply a {style} style."
        
        cprint(f"Final text input for Gemini: {final_text_input}", "magenta")

        # Using the API call structure provided by the user
        # contents should be a list: [text_input, image]
        # The image part for this API structure usually needs to be PIL.Image directly
        contents_for_api = [final_text_input, input_pil_image]

        cprint(f"Sending to Gemini model '{MODEL_NAME}': Text='{final_text_input[:50]}...', Image_mode='{input_pil_image.mode}', Image_size='{input_pil_image.size}'", "yellow")
        
        # Using client.models.generate_content as per user's original example
        response = CLIENT.models.generate_content(
            model=MODEL_NAME,
            contents=contents_for_api,
            # Using the config structure from the user's example
            config=types.GenerateContentConfig(
              response_modalities=['TEXT', 'IMAGE']
            )
            # safety_settings are often configured at client level or not used in this specific call structure
        )
        
        cprint("Received response from Gemini API.", "green")

        generated_text = ""
        generated_image_b64 = None

        if response.candidates and response.candidates[0].content and response.candidates[0].content.parts:
            for part in response.candidates[0].content.parts:
                if hasattr(part, 'text') and part.text is not None:
                    generated_text += part.text + "\n"
                    cprint(f"Gemini text part: {part.text}", "cyan")
                elif hasattr(part, 'inline_data') and part.inline_data is not None and hasattr(part.inline_data, 'data') and part.inline_data.data:
                    cprint(f"Gemini image part received (mime_type: {part.inline_data.mime_type}).", "cyan")
                    img_bytes = part.inline_data.data
                    generated_image_b64 = base64.b64encode(img_bytes).decode('utf-8')
                    # Assuming we want the first image found
                    break 
        else:
            cprint("Response structure does not match expected parts format.", "yellow")

        if not generated_image_b64:
            cprint("No image data found in Gemini response. Text (if any): " + generated_text, "red")
            error_message = "Failed to generate image. No image data in response."
            if generated_text.strip():
                 error_message += f" API message: {generated_text.strip()}"
            return JSONResponse(status_code=500, content={"error": error_message, "text_response": generated_text.strip()})

        cprint("Image successfully generated and encoded to base64.", "green")
        return JSONResponse(content={
            "message": "Image generated successfully!",
            "text_response": generated_text.strip(),
            "image_data": f"data:{part.inline_data.mime_type if hasattr(part, 'inline_data') and part.inline_data else 'image/png'};base64,{generated_image_b64}"
        })

    # Specific exceptions from google.generativeai (if this was the library)
    # except genai.types.BlockedPromptException as e: # This type may not exist in the 'google.genai' user is referring to
    #     cprint(f"Gemini API call failed due to blocked prompt: {e}", "red")
    #     return JSONResponse(status_code=400, content={"error": f"Request blocked by API safety filters: {e}", "text_response": str(e)})
    # except genai.types.StopCandidateException as e: # Similarly, may not exist
    #     cprint(f"Gemini API call failed because generation stopped: {e}", "red")
    #     return JSONResponse(status_code=500, content={"error": f"Image generation stopped unexpectedly: {e}", "text_response": str(e)})
    except Exception as e:
        cprint(f"Error in image generation endpoint: {type(e).__name__} - {str(e)}", "red")
        import traceback
        traceback.print_exc()
        return JSONResponse(status_code=500, content={"error": f"An unexpected error occurred: {type(e).__name__} - {str(e)}"})

# --- Main Execution ---
if __name__ == "__main__":
    import uvicorn
    cprint("Starting FastAPI server with Uvicorn...", "blue")
    if not CLIENT:
        cprint("CRITICAL: GenAI Client FAILED to initialize. Check API Key and library setup.", "red")
        cprint("The application might not function correctly if the client uses 'from google import genai' and 'genai.Client()'.", "red")
        cprint("Ensure you have the correct Google library installed that supports this specific API structure.", "red")
    
    uvicorn.run("main:app", host="127.0.0.1", port=8000, reload=True) 