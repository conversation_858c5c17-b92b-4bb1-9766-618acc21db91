# Explanation Tournament

This project generates multiple explanations for a given topic using an OpenAI model and then runs an Elo-based tournament among these explanations, using another OpenAI model as an AI judge. The process and results are saved to a JSON file, and a real-time, aesthetic HTML visualization is generated and updated as the tournament progresses.

## Features

-   Generate multiple diverse explanations for a configurable topic.
-   Run an Elo rating tournament to rank the explanations based on AI judge comparisons.
-   Save tournament progress, including match results and Elo changes, to a JSON file (`tournament_results.json`).
-   Generate and update a colorful, dark-mode HTML visualization (`tournament.html`) in real-time.
-   Automatically open the HTML visualization in your default web browser.
-   Show ranking movement (up/down arrows) in the HTML visualization.

## Setup

1.  **Clone the repository** (or download the files).
2.  **Install dependencies:** Make sure you have Python installed. Install the required libraries:

    ```bash
    pip install openai termcolor
    ```

3.  **Set up OpenAI API Key:** The script requires an OpenAI API key to generate explanations and run the tournament. Set the `OPENAI_API_KEY` environment variable with your key.

    -   **Windows:**
        ```cmd
        set OPENAI_API_KEY=your_api_key_here
        ```
    -   **macOS/Linux:**
        ```bash
        export OPENAI_API_KEY=your_api_key_here
        ```
    Replace `your_api_key_here` with your actual OpenAI API key.

## How to Run

Navigate to the project directory in your terminal and run the Python script:

```bash
python generate_explanations.py
```

The script will generate explanations, run the tournament, print updates to the console, save results to `tournament_results.json`, and automatically open `tournament.html` in your default web browser. The HTML page will auto-refresh to show the real-time progress.

## Configuration

You can modify the following variables at the top of `generate_explanations.py`:

-   `MODEL`: The OpenAI model used to generate the initial explanations (e.g., `o4-mini`, `gpt-4`).
-   `TOURNAMENT_MODEL`: The OpenAI model used as the AI judge for comparisons.
-   `N_EXPLANATIONS`: The number of explanations to generate and include in the tournament.
-   `TOPIC`: The topic for which explanations will be generated.

## Output Files

-   `explanations.json`: Contains the initial list of generated explanations.
-   `tournament_results.json`: Contains the complete tournament data, including initial explanations, a log of all matches with Elo changes, current Elo ratings, and the ranked results after each match. The ranked results are placed at the top of the file.
-   `tournament.html`: The HTML visualization of the tournament progress. This file is updated in real-time as the tournament runs and includes current rankings, Elo scores, ranking movements, and a log of matches. 