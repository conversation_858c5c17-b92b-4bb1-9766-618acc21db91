import openai
from termcolor import colored
import os
import json
import random
import math
import time # Import time for potential delays
from collections import OrderedDict # Import OrderedDict to help maintain key order
import html # Import html module for escaping HTML special characters
import webbrowser # Import webbrowser to open the HTML file

# VARIABLES
MODEL = "o4-mini"
TOURNAMENT_MODEL = "o4-mini" # Model for comparison
N_EXPLANATIONS = 5  # Change as needed
TOPIC = "Consciousness"  # Change as needed
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
OUTPUT_FILE = "tournament_results.json" # Changed output file name to reflect contents
HTML_OUTPUT_FILE = "tournament.html" # HTML file for visualization
INITIAL_ELO = 1500
K_FACTOR = 32 # Elo rating constant

# --- HTML Template ---
# Moved from tournament.html
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Explanation Tournament Results</title>
    <meta http-equiv="refresh" content="2"> <!-- Auto-refresh every 2 seconds -->
    <style>
        body {
            font-family: sans-serif;
            background-color: #1e1e1e; /* Dark background */
            color: #ffffff; /* White text */
            line-height: 1.6;
            margin: 20px;
        }

        h1, h2 {
            color: #4CAF50; /* Green accent */
        }

        #ranked-results h2 {
            color: #2196F3; /* Blue accent */
        }

        #ranked-results table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }

        #ranked-results th, #ranked-results td {
            border: 1px solid #3a3a3a; /* Darker border */
            padding: 12px;
            text-align: left;
        }

        #ranked-results th {
            background-color: #333333; /* Slightly lighter dark */
        }

        #ranked-results tr:nth-child(even) {
            background-color: #2a2a2a; /* Alternate row color */
        }

        #ranked-results tr:hover {
            background-color: #444444; /* Hover effect */
        }

        .explanation-text {
            font-style: italic;
            color: #cccccc; /* Lighter gray for explanations */
        }

        #match-updates h2 {
            color: #FF9800; /* Orange accent */
            margin-top: 30px;
        }

        .match-item {
            border: 1px solid #3a3a3a;
            padding: 15px;
            margin-bottom: 15px;
            background-color: #2a2a2a;
            border-radius: 5px;
        }

        .match-outcome {
            font-weight: bold;
            color: #FFEB3B; /* Yellow accent */
        }

        .elo-change {
            font-family: monospace;
            color: #8BC34A; /* Light green */
        }

         /* Specific colors for explanations */
        .exp-color-1 { color: #F44336; } /* Red */
        .exp-color-2 { color: #E91E63; } /* Pink */
        .exp-color-3 { color: #9C27B0; } /* Purple */
        .exp-color-4 { color: #673AB7; } /* Deep Purple */
        .exp-color-5 { color: #3F51B5; } /* Indigo */
        .exp-color-6 { color: #2196F3; } /* Blue */
        .exp-color-7 { color: #03A9F4; } /* Light Blue */
        .exp-color-8 { color: #00BCD4; } /* Cyan */
        .exp-color-9 { color: #009688; } /* Teal */
        .exp-color-10 { color: #4CAF50; } /* Green */

    </style>
</head>
<body>

    <h1>Explanation Tournament Progress</h1>

    <div id="ranked-results">
        <!-- Ranked results will be inserted here by the script -->
        <h2>Current Rankings</h2>
        <table>
            <thead>
                <tr>
                    <th>Rank</th>
                    <th>Explanation #</th>
                    <th>Current Elo</th>
                    <th>Explanation Text</th>
                </tr>
            </thead>
            <tbody>
                <!-- Ranking rows will be added here -->
            </tbody>
        </table>
    </div>

    <div id="match-updates">
        <!-- Match updates will be inserted here by the script -->
        <h2>Match Log</h2>
        <!-- Match entries will be added here -->
    </div>

</body>
</html>
"""
# --- End HTML Template ---

openai.api_key = OPENAI_API_KEY

def get_explanations(topic, n, model):
    print(colored(f"\nGenerating {n} explanations for '{topic}' using {model}...", 'green'))
    response = openai.chat.completions.create(
        model=model,
        messages=[
            {"role": "system", "content": "You are an expert explainer. Provide clear, concise explanations. Provide a variety of explanations, ranging from esoteric to scientific. Respond in JSON as a list of explanations. in this format: {'explanations': ['explanation1', 'explanation2', 'explanation3', 'explanation4', 'explanation5']}"},
            {"role": "user", "content": f"Give me {n} different explanations for the topic: '{topic}'."}
        ],
        response_format={"type": "json_object"}
    )
    explanations = response.choices[0].message.content
    try:
        explanations_list = json.loads(explanations)
        # Handle cases where the model might return a dict with a list inside
        if isinstance(explanations_list, dict) and 'explanations' in explanations_list:
            explanations_list = explanations_list['explanations']
        # Ensure it's a list, even if the model deviates slightly
        if not isinstance(explanations_list, list):
             explanations_list = [explanations_list] if explanations_list else []
    except json.JSONDecodeError:
        print(colored("Warning: Model did not return valid JSON. Treating response as a single explanation.", 'red'))
        explanations_list = [explanations]
    except Exception as e:
        print(colored(f"An error occurred parsing explanations: {e}", 'red'))
        explanations_list = [explanations]
        
    # Ensure we have the requested number of explanations, or pad if necessary (simple case)
    while len(explanations_list) < n:
        explanations_list.append(f"[Placeholder explanation {len(explanations_list) + 1}]") # Pad with placeholders

    return explanations_list[:n] # Return exactly n explanations

def calculate_elo_change(player_rating, opponent_rating, outcome, k_factor):
    # Outcome: 1 for win, 0 for loss, 0.5 for draw
    expected_outcome = 1 / (1 + math.pow(10, (opponent_rating - player_rating) / 400))
    return k_factor * (outcome - expected_outcome)

def compare_explanations_with_openai(topic, exp1, exp2, model):
    print(colored("  Asking AI judge...", 'blue'))
    try:
        response = openai.chat.completions.create(
            model=model,
            messages=[
                {"role": "system", "content": "You are an AI judge comparing two explanations. Read Explanation A and Explanation B carefully. Determine which explanation is better based on clarity, conciseness, and how well it explains the topic. Respond only with 'A', 'B', or 'Draw'."},
                {"role": "user", "content": f"Compare the following two explanations for the topic '{topic}':\n\nExplanation A: {exp1}\n\nExplanation B: {exp2}\n\nWhich is better? Respond only with 'A', 'B', or 'Draw'."}
            ],
        )
        decision = response.choices[0].message.content.strip()
        print(colored(f"  Judge's decision: {decision}", 'blue'))
        if decision == 'A':
            return 1 # Exp1 wins
        elif decision == 'B':
            return 0 # Exp1 loses (Exp2 wins)
        elif decision == 'Draw':
            return 0.5 # Draw
        else:
            print(colored(f"  Warning: Unexpected response from judge: {decision}. Treating as a draw.", 'red'))
            return 0.5 # Default to draw on unexpected response
    except Exception as e:
        print(colored(f"  Error during AI comparison: {e}. Treating as a draw.", 'red'))
        return 0.5 # Default to draw on error

def save_tournament_data(data, filename):
    try:
        # Create a new dictionary with desired order for saving, without modifying the original data
        ordered_data = OrderedDict()

        # Place ranked_results first if it exists
        if "ranked_results" in data:
             ordered_data["ranked_results"] = data["ranked_results"]

        # Add other keys in a logical order
        if "current_elo_ratings" in data:
            ordered_data["current_elo_ratings"] = data["current_elo_ratings"]
        if "matches" in data:
            ordered_data["matches"] = data["matches"]
        if "initial_explanations" in data:
            ordered_data["initial_explanations"] = data["initial_explanations"]

        # Add any remaining keys (shouldn't be any with current structure but good for robustness)
        for key, value in data.items():
            if key not in ordered_data:
                ordered_data[key] = value

        with open(filename, 'w') as f:
            json.dump(ordered_data, f, indent=4)
        # print(colored(f"Tournament data saved to {filename}.", 'green')) # Optional: noisy for real-time saves
    except Exception as e:
        print(colored(f"Error saving tournament data to {filename}: {e}", 'red'))

# New function to generate HTML for the tournament visualization
def generate_tournament_html(tournament_data, topic, explanations):
    # Use the internal HTML_TEMPLATE
    html_template = HTML_TEMPLATE
    
    # Keep track of previous rankings to show movement
    previous_rankings = {}
    if "matches" in tournament_data and len(tournament_data["matches"]) > 1:
        # Try to reconstruct previous rankings based on the state before the last match
        # This is a simplification; a more robust way would be to store historical rankings
        # For this implementation, we'll just compare the current ranking to the *previous* match's result's impacted explanations
        # A simpler approach for showing movement is to compare current rank to initial rank or rank in the *immediately* previous data save.
        # Let's use the immediately previous save's rankings for a simpler implementation.
        # This requires reading the last saved data, which might be slow.
        # Alternative: store previous rankings in tournament_data itself.

        # Let's try storing previous ranks in tournament_data
        if "previous_ranked_results" in tournament_data and tournament_data["previous_ranked_results"]:
             for result in tournament_data["previous_ranked_results"]:
                 previous_rankings[result["original_index"]] = result["rank"]

    # Generate ranked results table rows
    ranked_results_html = ""
    if "ranked_results" in tournament_data and tournament_data["ranked_results"]:
        for result in tournament_data["ranked_results"]:
            # Get color class based on original index (cycling if more than 10 explanations)
            color_class = f"exp-color-{(result['original_index'] % 10) + 1}"
            
            # Escape any HTML in the explanation to prevent injection and fix encoding issues
            # Trim the explanation to avoid excessively long rows
            safe_explanation = html.escape(result["explanation"])
            
            # Determine if rank changed (up/down arrow)
            rank_indicator = ""
            original_idx = result["original_index"]
            current_rank = result["rank"]
            
            if original_idx in previous_rankings:
                prev_rank = previous_rankings[original_idx]
                if current_rank < prev_rank:  # Lower rank number = higher position
                    rank_indicator = '<span style="color: #4CAF50; font-weight: bold;">↑</span>'  # Green up arrow
                elif current_rank > prev_rank:
                    rank_indicator = '<span style="color: #F44336; font-weight: bold;">↓</span>'  # Red down arrow
            
            # Create the table row
            ranked_results_html += f"""
            <tr>
                <td>{result['rank']} {rank_indicator}</td>
                <td class="{color_class}">Explanation {result['original_index'] + 1}</td>
                <td>{result['current_elo']:.0f}</td>
                <td class="explanation-text">{safe_explanation[:150]}{'...' if len(safe_explanation) > 150 else ''}</td>
            </tr>
            """
    
    # Generate match updates log
    match_updates_html = ""
    if "matches" in tournament_data and tournament_data["matches"]:
        # Most recent matches first (reverse order)
        for match in reversed(tournament_data["matches"]):
            # Extract data for easier reference
            match_num = match["match_number"]
            exp_a_idx = match["explanation_a"]["original_index"]
            exp_a_before = match["explanation_a"]["elo_before"]
            exp_a_after = match["explanation_a"]["elo_after"]
            exp_a_delta = exp_a_after - exp_a_before
            
            exp_b_idx = match["explanation_b"]["original_index"]
            exp_b_before = match["explanation_b"]["elo_before"]
            exp_b_after = match["explanation_b"]["elo_after"]
            exp_b_delta = exp_b_after - exp_b_before
            
            outcome = match["outcome"]
            
            # Color classes for the explanations
            color_a = f"exp-color-{(exp_a_idx % 10) + 1}"
            color_b = f"exp-color-{(exp_b_idx % 10) + 1}"
            
            # Delta indicators with arrows
            delta_a_indicator = f"<span style=\"color: {'#4CAF50' if exp_a_delta > 0 else '#F44336'}\">({exp_a_delta:+.0f} {'↑' if exp_a_delta > 0 else '↓'})</span>"
            delta_b_indicator = f"<span style=\"color: {'#4CAF50' if exp_b_delta > 0 else '#F44336'}\">({exp_b_delta:+.0f} {'↑' if exp_b_delta > 0 else '↓'})</span>"
            
            # Simplified explanation display in match logs
            safe_a_text = ""
            safe_b_text = ""
            
            # Find the actual explanation text using the original index
            exp_a_text_full = ""
            exp_b_text_full = ""
            for i, exp in enumerate(explanations):
                if i == exp_a_idx:
                    exp_a_text_full = exp
                if i == exp_b_idx:
                    exp_b_text_full = exp
            
            a_text = exp_a_text_full[:70] + ('...' if len(exp_a_text_full) > 70 else '')
            safe_a_text = html.escape(a_text)
            
            b_text = exp_b_text_full[:70] + ('...' if len(exp_b_text_full) > 70 else '')
            safe_b_text = html.escape(b_text)
            
            # Create match item HTML
            match_updates_html += f"""
            <div class="match-item">
                <h3>Match {match_num}</h3>
                <p class="match-outcome">
                    <span class="{color_a}">Explanation {exp_a_idx + 1}</span> vs 
                    <span class="{color_b}">Explanation {exp_b_idx + 1}</span> - 
                    Winner: <strong class="{color_a if outcome == 'A' else color_b if outcome == 'B' else ''}">{outcome}</strong>
                </p>
                <p class="elo-change">
                    <span class="{color_a}">Explanation {exp_a_idx + 1}</span>: {exp_a_before:.0f} → {exp_a_after:.0f} {delta_a_indicator}<br>
                    <span class="{color_b}">Explanation {exp_b_idx + 1}</span>: {exp_b_before:.0f} → {exp_b_after:.0f} {delta_b_indicator}
                </p>
                <div class="explanation-preview">
                    <div class="{color_a}"><small>Explanation {exp_a_idx + 1}:</small> <span class="explanation-text">{safe_a_text}</span></div>
                    <div class="{color_b}"><small>Explanation {exp_b_idx + 1}:</small> <span class="explanation-text">{safe_b_text}</span></div>
                </div>
            </div>
            """
    
    # Get the index of the closing tbody tag
    tbody_start = html_template.find("<tbody>") + len("<tbody>")
    tbody_end = html_template.find("</tbody>")
    
    # Get the index of the match updates section
    match_updates_start = html_template.find('<div id="match-updates">')
    match_updates_end = html_template.find('<h2>Match Log</h2>', match_updates_start) + len('<h2>Match Log</h2>')
    
    # Replace the parts of the template with our generated content
    html_content = (
        html_template[:tbody_start] + 
        ranked_results_html + 
        html_template[tbody_end:match_updates_end] + 
        match_updates_html + 
        html_template[html_template.find('</div>', match_updates_end):]
    )
    
    # Replace the title with the topic
    html_content = html_content.replace("<title>Explanation Tournament Results</title>", 
                                        f"<title>{topic} - Explanation Tournament</title>")
    html_content = html_content.replace("<h1>Explanation Tournament Progress</h1>", 
                                        f"<h1>{topic} - Explanation Tournament</h1>")
    
    # Add additional CSS for explanation previews (if not already present)
    # A more robust way would be to ensure the template has a placeholder for this
    # For now, we'll just append if it's not there.
    css_additions = """
        .explanation-preview {
            margin-top: 10px;
            padding: 8px;
            background-color: #2c2c2c;
            border-radius: 4px;
        }
        
        .explanation-preview div {
            margin-bottom: 8px;
        }
    """
    
    if css_additions not in html_content:
        html_content = html_content.replace("</style>", css_additions + "\n    </style>")
    
    return html_content

# Function to save HTML content to file
def save_tournament_html(html_content, filename):
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(html_content)
        # print(colored(f"Tournament HTML saved to {filename}.", 'green')) # Optional: noisy for real-time saves
    except Exception as e:
        print(colored(f"Error saving tournament HTML to {filename}: {e}", 'red'))

def run_tournament(topic, explanations, tournament_model, output_filename, html_filename):
    elo_ratings = {explanation: INITIAL_ELO for explanation in explanations}
    # Create a list of (initial_index, explanation) tuples to keep track of original numbers and make lookups easier
    explanation_tuples = []
    for i, exp in enumerate(explanations):
         elo_ratings[exp] = INITIAL_ELO # Ensure initial Elo is set for all
         explanation_tuples.append((i, exp))

    # Data structure to hold tournament results
    tournament_data = {
        "initial_explanations": [{"original_index": i, "explanation": exp, "initial_elo": INITIAL_ELO} for i, exp in explanation_tuples],
        "matches": [],
        "current_elo_ratings": {}, # Renamed to current_elo_ratings
        "ranked_results": [],
        "previous_ranked_results": [] # Store previous ranks for movement indication
    }

    # Initial save of HTML with just the header and empty sections
    initial_html_content = generate_tournament_html(tournament_data, topic, explanations)
    save_tournament_html(initial_html_content, html_filename)

    print(colored("\n--- Starting Explanation Tournament! ---", 'cyan'))
    print(colored(f"Total explanations: {len(explanations)}", 'cyan'))

    match_count = 0
    # Simple round robin tournament
    for i in range(len(explanation_tuples)):
        for j in range(i + 1, len(explanation_tuples)):
            match_count += 1
            exp1_original_index, exp1 = explanation_tuples[i]
            exp2_original_index, exp2 = explanation_tuples[j]

            rating1_before = elo_ratings[exp1]
            rating2_before = elo_ratings[exp2]

            print(colored(f"\n--- Match {match_count} --- ", 'yellow'))
            print(colored(f"  Explanation {exp1_original_index + 1} (Elo: {rating1_before:.0f}) vs Explanation {exp2_original_index + 1} (Elo: {rating2_before:.0f})", 'yellow'))

            # Call OpenAI API to compare explanations
            outcome1_vs_2 = compare_explanations_with_openai(topic, exp1, exp2, tournament_model)
            outcome2_vs_1 = 1 - outcome1_vs_2

            delta1 = calculate_elo_change(rating1_before, rating2_before, outcome1_vs_2, K_FACTOR)
            delta2 = calculate_elo_change(rating2_before, rating1_before, outcome2_vs_1, K_FACTOR)

            elo_ratings[exp1] += delta1
            elo_ratings[exp2] += delta2

            print(colored(f"  Result: Explanation {exp1_original_index + 1} gets {outcome1_vs_2} points, Explanation {exp2_original_index + 1} gets {outcome2_vs_1} points.", 'green'))
            print(colored(f"  Elo Change: Exp {exp1_original_index + 1}: {delta1:+.0f} -> {elo_ratings[exp1]:.0f} | Exp {exp2_original_index + 1}: {delta2:+.0f} -> {elo_ratings[exp2]:.0f}", 'green'))

            # Record match results
            match_data = {
                "match_number": match_count,
                "explanation_a": {"original_index": exp1_original_index, "elo_before": rating1_before, "elo_after": elo_ratings[exp1]},
                "explanation_b": {"original_index": exp2_original_index, "elo_before": rating2_before, "elo_after": elo_ratings[exp2]},
                "outcome": "A" if outcome1_vs_2 == 1 else ("B" if outcome1_vs_2 == 0 else "Draw")
            }
            tournament_data["matches"].append(match_data)

            # Before updating ranked_results, store the current one as previous
            tournament_data["previous_ranked_results"] = tournament_data["ranked_results"][:]

            # Update current Elo ratings and recalculate rankings after each match
            tournament_data["current_elo_ratings"] = {f"Explanation {idx+1}": elo_ratings[exp] for idx, exp in explanation_tuples}

            ranked_explanations = []
            for idx, exp in explanation_tuples:
                 # Ensure the explanation exists in elo_ratings before appending
                 if exp in elo_ratings:
                     ranked_explanations.append((idx, exp, elo_ratings[exp]))
                 else:
                      # This case should ideally not happen if elo_ratings is initialized correctly
                      print(colored(f"Warning: Explanation at index {idx} not found in elo_ratings.", 'red'))
                      ranked_explanations.append((idx, exp, INITIAL_ELO)) # Default to initial Elo

            sorted_rankings = sorted(ranked_explanations, key=lambda item: item[2], reverse=True)

            tournament_data["ranked_results"] = [{
                "rank": rank + 1,
                "original_index": original_index,
                "explanation": explanation,
                "current_elo": rating # Use current_elo for clarity
            } for rank, (original_index, explanation, rating) in enumerate(sorted_rankings)]

            # Save JSON data after each match (real-time update with ranked results at top)
            save_tournament_data(tournament_data, output_filename)
            
            # Generate and save HTML visualization
            html_content = generate_tournament_html(tournament_data, topic, explanations)
            save_tournament_html(html_content, html_filename)

            # Optional: Add a small delay between API calls to avoid rate limits
            # time.sleep(0.5) # Consider increasing this for more explanations

    print(colored("\n--- Tournament Complete! ---", 'cyan'))

    # The final ranked_results and current_elo_ratings are already in tournament_data
    # and saved after the last match, so no need to re-populate/re-save here.

    return elo_ratings # Return final ratings

def save_explanations_to_json(explanations, filename):
    # This function is now less critical as tournament_results.json will contain explanations
    # but keeping it to potentially save just the initial list if needed elsewhere.
    # Or we can remove it if it's redundant. Let's keep it for now but change the default name back.
    print(colored(f"Saving initial explanations list to explanations.json...", 'green'))
    try:
        with open("explanations.json", 'w') as f: # Save to original explanations.json
            json.dump({'explanations': explanations}, f, indent=4)
        print(colored("Initial explanations list saved.", 'green'))
    except Exception as e:
        print(colored(f"Error saving initial explanations list: {e}", 'red'))

def print_colorful_explanations(explanations):
    colors = ['red', 'green', 'yellow', 'blue', 'magenta', 'cyan', 'white']
    print(colored("\n--- Generated Explanations ---", 'cyan'))
    for i, explanation in enumerate(explanations):
        color = colors[i % len(colors)]
        print(colored(f"Explanation {i+1}:\n", color) + explanation)

def print_elo_rankings(explanations, elo_ratings):
    # This function now prints from the final state. Can be adapted to read from file.
    print(colored("\n--- Final Elo Rankings ---", 'cyan'))
    print(colored("Rank | Explanation # | Final Elo", 'cyan'))
    print(colored("-------------------------------------", 'cyan'))

    # Rebuilding the ranked list for printing based on final elo_ratings
    # This logic is slightly redundant with the saving logic but keeps print separate.
    # Ideally, print_elo_rankings would read from the final saved file.
    explanation_tuples = list(enumerate(explanations))
    ranked_explanations = []
    for i, exp in explanation_tuples:
         if exp in elo_ratings: # Should always be true now
             ranked_explanations.append((i, exp, elo_ratings[exp]))
         else:
              # This should not happen with correct logic
              ranked_explanations.append((i, exp, INITIAL_ELO))

    sorted_rankings = sorted(ranked_explanations, key=lambda item: item[2], reverse=True)

    for rank, (original_index, explanation, rating) in enumerate(sorted_rankings):
        print(colored(f"{rank + 1:<4} | {original_index + 1:<13} | {rating:.0f}", 'green'))


if __name__ == "__main__":
    # Ensure OPENAI_API_KEY is set
    if not OPENAI_API_KEY:
        print(colored("Error: OPENAI_API_KEY environment variable not set.", 'red'))
        print(colored("Please set the OPENAI_API_KEY environment variable before running the script.", 'red'))
    else:
        explanations = get_explanations(TOPIC, N_EXPLANATIONS, MODEL)
        save_explanations_to_json(explanations, "explanations.json") # Save initial list
        print_colorful_explanations(explanations)
        
        # Proceed to tournament only if explanations were generated
        if explanations:
            # Open the HTML file in the default web browser
            try:
                webbrowser.open(HTML_OUTPUT_FILE)
                print(colored(f"\nOpening {HTML_OUTPUT_FILE} in web browser...", 'cyan'))
            except Exception as e:
                print(colored(f"Error opening web browser: {e}", 'red'))
                print(colored(f"Please open {HTML_OUTPUT_FILE} manually to view the tournament results.", 'yellow'))

            # Run tournament and save results incrementally to tournament_results.json and tournament.html
            # Initial HTML save is now handled inside run_tournament before the loop
            final_elo_ratings = run_tournament(TOPIC, explanations, TOURNAMENT_MODEL, OUTPUT_FILE, HTML_OUTPUT_FILE)
            print_elo_rankings(explanations, final_elo_ratings)

            print(colored(f"\nHTML visualization saved to {HTML_OUTPUT_FILE}", 'cyan'))
            # The message to open manually is now only shown if the automatic open fails.
            # print(colored("Open this file in a web browser to view the tournament results.", 'cyan'))
        else:
            print(colored("No explanations generated, skipping tournament and ranking.", 'yellow')) 