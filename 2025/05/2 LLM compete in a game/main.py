import pygame
import sys
import random
import math
import json
import time
import threading
import asyncio
from typing import List, Tuple, Dict, Union, Optional, Any

# Import configurations, LLM controller, game objects, and UI
import config
from llm_controller import get_controller, LLMController
from game_objects import Player, Food, Offspring
from ui import HUD # Import HUD class

# Initialize pygame
pygame.init()
screen = pygame.display.set_mode((config.WIDTH, config.HEIGHT), pygame.RESIZABLE)
pygame.display.set_caption("LLM Circle Game")
clock = pygame.time.Clock()
# font = pygame.font.SysFont('Arial', 24) # Font initialization is now in HUD

# Define action types
CONTINUOUS_ACTIONS = ["move_to_food", "rest"]
ONE_SHOT_ACTIONS = ["create_offspring", "launch_offspring"]

class Game:
    def __init__(self):
        self.player1 = Player(
            player_id=1, 
            x=config.WIDTH * 0.25,
            y=config.HEIGHT // 2,
            color=config.PLAYER_1_COLOR,
            offspring_color=config.PLAYER_1_OFFSPRING_COLOR,
            initial_aggressiveness=config.PLAYER_1_INITIAL_AGGRESSIVENESS
        )
        self.player2 = Player(
            player_id=2, 
            x=config.WIDTH * 0.75, 
            y=config.HEIGHT // 2, 
            color=config.PLAYER_2_COLOR,
            offspring_color=config.PLAYER_2_OFFSPRING_COLOR,
            initial_aggressiveness=config.PLAYER_2_INITIAL_AGGRESSIVENESS
        )
        self.players = [self.player1, self.player2]

        # Both players will now use the OpenAI controller
        self.player1.controller = get_controller(player_id=1, controller_type="openai")
        self.player2.controller = get_controller(player_id=2, controller_type="openai") # Changed from "random"

        self.foods: List[Food] = []
        self.spawn_food(config.FOOD_COUNT)
        
        self.last_llm_call_times = {1: 0.0, 2: 0.0} 
        self.player_actions_from_llm: Dict[int, Optional[Dict[str, str]]] = {1: None, 2: None}
        # Stores the current continuous action each player should be performing
        self.player_current_behavior: Dict[int, str] = {1: "rest", 2: "rest"} 
        self.game_winner: Optional[int] = None 
        self.hud = HUD()
        self.action_lock = threading.Lock()

    def reset(self): 
        print("Resetting game...")
        # Reset player 1
        self.player1 = Player(
            player_id=1, 
            x=config.WIDTH * 0.25,
            y=config.HEIGHT // 2,
            color=config.PLAYER_1_COLOR,
            offspring_color=config.PLAYER_1_OFFSPRING_COLOR,
            initial_aggressiveness=config.PLAYER_1_INITIAL_AGGRESSIVENESS
        )
        # Reset player 2
        self.player2 = Player(
            player_id=2, 
            x=config.WIDTH * 0.75, 
            y=config.HEIGHT // 2, 
            color=config.PLAYER_2_COLOR,
            offspring_color=config.PLAYER_2_OFFSPRING_COLOR,
            initial_aggressiveness=config.PLAYER_2_INITIAL_AGGRESSIVENESS
        )
        self.players = [self.player1, self.player2]

        # Re-assign controllers (they are stateless for now, but good practice if they become stateful)
        self.player1.controller = get_controller(player_id=1, controller_type="openai")
        self.player2.controller = get_controller(player_id=2, controller_type="openai")

        # Reset food
        self.foods.clear()
        self.spawn_food(config.FOOD_COUNT)
        
        # Reset game state variables
        self.last_llm_call_times = {1: 0.0, 2: 0.0}
        self.player_actions_from_llm = {1: None, 2: None}
        self.player_current_behavior = {1: "rest", 2: "rest"}
        self.game_winner = None
        # HUD doesn't need explicit reset, it redraws based on game state
        # Action lock is fine as is

    def spawn_food(self, count: int):
        for _ in range(count):
            x = random.randint(config.FOOD_RADIUS, config.WIDTH - config.FOOD_RADIUS)
            y = random.randint(config.FOOD_RADIUS, config.HEIGHT - config.FOOD_RADIUS)
            self.foods.append(Food(x, y))
    
    def get_player_by_id(self, player_id: int) -> Optional[Player]:
        return next((p for p in self.players if p.player_id == player_id), None)

    def get_game_state_for_player(self, player: Player) -> Dict[str, Any]:
        opponent = self.player2 if player.player_id == 1 else self.player1
        
        nearest_food_obj = None
        min_dist_sq = float('inf')
        if player.is_alive:
            for food in self.foods:
                dist_sq = (player.x - food.x)**2 + (player.y - food.y)**2
                if dist_sq < min_dist_sq:
                    min_dist_sq = dist_sq
                    nearest_food_obj = food
        
        nearest_food_info = {}
        if nearest_food_obj:
            nearest_food_info = {
                "x": nearest_food_obj.x,
                "y": nearest_food_obj.y,
                "distance": math.sqrt(min_dist_sq) if min_dist_sq != float('inf') else float('inf')
            }

        # Get available actions for the current player
        available_actions = player.get_available_actions(opponent_is_alive=opponent.is_alive)

        player_data = {
            "id": player.player_id,
            "radius": player.radius,
            "x": player.x,
            "y": player.y,
            "offspring_count": len([off for off in player.offspring_list if not off.is_launched]),
            "is_alive": player.is_alive,
            "distance_to_opponent": math.sqrt((player.x - opponent.x)**2 + (player.y - opponent.y)**2) if opponent.is_alive else float('inf'),
            "nearest_food": nearest_food_info,
            "available_actions": available_actions,
            "aggressiveness": player.aggressiveness
        }
        opponent_data = {
            "id": opponent.player_id,
            "radius": opponent.radius,
            "x": opponent.x,
            "y": opponent.y,
            "offspring_count": len([off for off in opponent.offspring_list if not off.is_launched]),
            "is_alive": opponent.is_alive
        }
        game_info = {
            "min_radius": config.MIN_PLAYER_RADIUS,
            "food_count": len(self.foods),
            "width": config.WIDTH,
            "height": config.HEIGHT,
            "offspring_creation_cost": config.OFFSPRING_CREATION_COST,
            "offspring_max_count": config.OFFSPRING_MAX_COUNT,
            "offspring_damage": config.OFFSPRING_DAMAGE
        }
        return {"player": player_data, "opponent": opponent_data, "game_info": game_info}

    def _ask_llm_thread(self, player: Player):
        if not player.is_alive or self.game_winner is not None: return
        current_time = time.time()
        if current_time - self.last_llm_call_times.get(player.player_id, 0) < config.LLM_CALL_INTERVAL:
            return 
        self.last_llm_call_times[player.player_id] = current_time
        try:
            print(f"Requesting action for Player {player.player_id} ({player.controller.name})...")
            game_state_for_llm = self.get_game_state_for_player(player)
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            action_result = loop.run_until_complete(player.controller.get_action(game_state_for_llm))
            loop.close()
            with self.action_lock:
                self.player_actions_from_llm[player.player_id] = action_result
            player.last_action = action_result.get("action", "error_parsing_action")
            print(f"Player {player.player_id} received action: {player.last_action}")
        except Exception as e:
            print(f"Error in P{player.player_id} _ask_llm_thread: {str(e)}")
            with self.action_lock:
                self.player_actions_from_llm[player.player_id] = None 
            player.last_action = "error_in_thread"

    def process_player_actions(self):
        for player in self.players:
            if not player.is_alive or self.game_winner is not None: continue
            
            chosen_action_name = self.player_current_behavior.get(player.player_id, "rest")
            llm_directive_processed_this_tick = False

            with self.action_lock:
                if self.player_actions_from_llm[player.player_id] is not None:
                    chosen_action_name = self.player_actions_from_llm[player.player_id].get("action", "rest")
                    self.player_actions_from_llm[player.player_id] = None # Consume the action
                    llm_directive_processed_this_tick = True
            
            opponent = self.player2 if player.player_id == 1 else self.player1
            available_action_names = [act_dict["name"] for act_dict in player.get_available_actions(opponent.is_alive)]

            if chosen_action_name not in available_action_names and chosen_action_name != "rest":
                if llm_directive_processed_this_tick:
                    print(f"P{player.player_id} LLM chose '{chosen_action_name}', but it became invalid. Valid: {available_action_names}. Forcing rest.")
                else:
                    print(f"P{player.player_id} current behavior '{chosen_action_name}' became invalid. Valid: {available_action_names}. Forcing rest.")
                chosen_action_name = "rest"
            
            player.last_action = chosen_action_name 

            if chosen_action_name == "move_to_food":
                game_state_for_player_pov = self.get_game_state_for_player(player)
                nearest_food_info = game_state_for_player_pov['player']['nearest_food']
                if nearest_food_info and nearest_food_info.get('x') is not None:
                    player.move_towards(nearest_food_info['x'], nearest_food_info['y'])
                    self.player_current_behavior[player.player_id] = "move_to_food" # Persist
                else:
                    player.rest()
                    self.player_current_behavior[player.player_id] = "rest" # No food, so rest
            elif chosen_action_name == "create_offspring":
                player.create_offspring()
                self.player_current_behavior[player.player_id] = "rest" # After a one-shot, default to rest
            elif chosen_action_name == "launch_offspring":
                if opponent.is_alive:
                    player.launch_offspring(opponent)
                    self.player_current_behavior[player.player_id] = "rest" # Default to rest after launch attempt
                else:
                    player.last_action = "opponent_defeated_no_launch"
                    player.rest() # Opponent gone, just rest if tried to launch
            elif chosen_action_name == "rest":
                player.rest()
                self.player_current_behavior[player.player_id] = "rest" # Persist rest
            else: 
                player.rest()
                player.last_action = f"unknown_action_fallback_rest: {chosen_action_name}"

    def update_collisions_and_interactions(self):
        if self.game_winner is not None: return

        # Player vs Player collision
        p1 = self.player1
        p2 = self.player2
        if p1.is_alive and p2.is_alive:
            dx = p2.x - p1.x
            dy = p2.y - p1.y
            distance_sq = dx*dx + dy*dy
            sum_radii = p1.radius + p2.radius
            
            if distance_sq < sum_radii * sum_radii and distance_sq > 0: # Check distance_sq > 0 to avoid division by zero if perfectly overlapped
                distance = math.sqrt(distance_sq)
                overlap = sum_radii - distance
                
                # Normalize the difference vector
                nx = dx / distance
                ny = dy / distance
                
                # Move players apart
                p1.x -= nx * overlap * 0.5
                p1.y -= ny * overlap * 0.5
                p2.x += nx * overlap * 0.5
                p2.y += ny * overlap * 0.5

                # Ensure players stay within bounds after collision resolution
                # Player.move() already handles this, but we might need to re-clamp if vx/vy are zero
                # For simplicity, we can call a simplified clamp here or rely on the next frame's Player.move()
                # A direct clamp is safer if players might not have velocity after collision.
                p1.x = max(p1.radius, min(config.WIDTH - p1.radius, p1.x))
                p1.y = max(p1.radius, min(config.HEIGHT - p1.radius, p1.y))
                p2.x = max(p2.radius, min(config.WIDTH - p2.radius, p2.x))
                p2.y = max(p2.radius, min(config.HEIGHT - p2.radius, p2.y))

            elif distance_sq == 0: # Players are exactly on top of each other
                # Slightly nudge player 1 to avoid being stuck
                p1.x += 1 
                p1.x = max(p1.radius, min(config.WIDTH - p1.radius, p1.x)) # Clamp after nudge

        offspring_to_remove_from_game: List[Offspring] = [] 

        for attacker_player in self.players:
            if not attacker_player.is_alive: continue
            opponent_player = self.player2 if attacker_player.player_id == 1 else self.player1

            current_attacker_offspring_to_remove: List[Offspring] = []
            for launched_off in attacker_player.offspring_list:
                if not launched_off.is_launched or launched_off in offspring_to_remove_from_game:
                    continue

                # 1. Launched vs Opponent's Orbiting Offspring (Shield)
                hit_shield = False
                if opponent_player.is_alive:
                    current_opponent_offspring_to_remove: List[Offspring] = []
                    for defending_off in opponent_player.offspring_list:
                        if not defending_off.is_launched and defending_off not in offspring_to_remove_from_game:
                            dist_sq = (launched_off.x - defending_off.x)**2 + (launched_off.y - defending_off.y)**2
                            if dist_sq < (launched_off.radius + defending_off.radius)**2:
                                current_attacker_offspring_to_remove.append(launched_off)
                                current_opponent_offspring_to_remove.append(defending_off)
                                print(f"P{attacker_player.player_id}'s L-offspring vs P{opponent_player.player_id}'s O-offspring. Both removed.")
                                hit_shield = True
                                break 
                    for off in current_opponent_offspring_to_remove:
                        if off not in offspring_to_remove_from_game: offspring_to_remove_from_game.append(off)
                
                if hit_shield: continue # Attacking offspring was destroyed by a shield

                # 2. Launched vs Opponent's Parent Circle
                if opponent_player.is_alive:
                    dist_sq_parent = (launched_off.x - opponent_player.x)**2 + (launched_off.y - opponent_player.y)**2
                    if dist_sq_parent < (launched_off.radius + opponent_player.radius)**2:
                        opponent_player.take_damage(config.OFFSPRING_DAMAGE)
                        if launched_off not in offspring_to_remove_from_game: offspring_to_remove_from_game.append(launched_off)
                        print(f"P{attacker_player.player_id}'s L-offspring hit P{opponent_player.player_id} parent. Damage dealt.")
                        # Offspring is consumed upon hitting parent
            
            for off in current_attacker_offspring_to_remove:
                 if off not in offspring_to_remove_from_game: offspring_to_remove_from_game.append(off)

        # Remove all marked offspring from their respective parent lists globally
        if offspring_to_remove_from_game:
            unique_offspring_to_remove = set(offspring_to_remove_from_game)
            for p in self.players:
                p.offspring_list = [off for off in p.offspring_list if off not in unique_offspring_to_remove]
        
        # Player vs Food - food is shared
        new_food_list: List[Food] = []
        for food_item in self.foods:
            eaten_this_cycle = False
            for player in self.players:
                if player.is_alive:
                    dist_sq = (player.x - food_item.x)**2 + (player.y - food_item.y)**2
                    if dist_sq < (player.radius + food_item.radius)**2:
                        player.radius += config.FOOD_GROWTH_RATE
                        # player.last_action could be updated here, but might be too noisy
                        eaten_this_cycle = True
                        break 
            if not eaten_this_cycle:
                new_food_list.append(food_item)
        self.foods = new_food_list

    def check_game_over(self):
        if self.game_winner is not None: return True # Game already decided

        p1_alive = self.player1.is_alive
        p2_alive = self.player2.is_alive

        if not p1_alive and not p2_alive:
            self.game_winner = 0 # Draw
            print("Game Over - DRAW!")
            return True
        if not p1_alive:
            self.game_winner = 2 # Player 2 wins
            print("Game Over - Player 2 WINS!")
            return True
        if not p2_alive:
            self.game_winner = 1 # Player 1 wins
            print("Game Over - Player 1 WINS!")
            return True
        return False

    def update(self, delta_time: float):
        if self.check_game_over(): return

        # Request LLM actions in threads (non-blocking)
        for player in self.players:
            if player.is_alive:
                 # Using threading directly; manage llm call frequency within _ask_llm_thread
                threading.Thread(target=self._ask_llm_thread, args=(player,)).start()

        self.process_player_actions()

        for player in self.players:
            player.move()
            player.shrink(delta_time)
            player.update_offspring()
            # player.eat(self.foods) # Eat logic moved to update_collisions for shared food

        self.update_collisions_and_interactions()

        if len(self.foods) < config.FOOD_COUNT:
            self.spawn_food(config.FOOD_COUNT - len(self.foods))

    def draw(self):
        screen.fill(config.BACKGROUND_COLOR)
        for food in self.foods:
            food.draw(screen)
        for player in self.players:
            player.draw(screen) # Player draw method now handles its offspring
        
        p1_data = self.get_hud_data_for_player(self.player1)
        p2_data = self.get_hud_data_for_player(self.player2)
        self.hud.draw(screen, p1_data, p2_data, 
                      self.player1, self.player2, # Pass full player objects
                      self.game_winner)
        
        pygame.display.flip()

    def get_hud_data_for_player(self, player: Player) -> Dict[str, Union[str, float, List[Dict[str, float]]]]:
        return {
            "id": player.player_id,
            "radius": player.radius,
            "last_action": player.last_action,
            "offspring_count": len([off for off in player.offspring_list if not off.is_launched]),
            "color": player.color,
            "controller_name": player.controller.name if hasattr(player, 'controller') else 'N/A',
            "is_alive": player.is_alive
        }

def main():
    game = Game()
    running_main_loop = True
    
    while running_main_loop:
        delta_time = clock.tick(config.FPS) / 1000.0 # seconds

        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running_main_loop = False
            if event.type == pygame.VIDEORESIZE:
                config.WIDTH, config.HEIGHT = event.size
                pygame.display.set_mode((config.WIDTH, config.HEIGHT), pygame.RESIZABLE)
            
            # Pass events to HUD to handle slider interactions
            action_from_hud = game.hud.handle_event(event, game.player1, game.player2)
            if action_from_hud == "restart_game":
                game.reset()
        
        game.update(delta_time)
        game.draw()

    pygame.quit()
    sys.exit()

if __name__ == "__main__":
    main() 