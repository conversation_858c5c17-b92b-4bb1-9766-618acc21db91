import os

# Window settings
WIDTH, HEIGHT = 1000, 800
FPS = 60

# Colors
BACKGROUND_COLOR = (0, 0, 0)
# Player 1
PLAYER_1_COLOR = (0, 0, 255)  # Blue
PLAYER_1_OFFSPRING_COLOR = (100, 100, 255) # Light Blue
# Player 2
PLAYER_2_COLOR = (255, 0, 0)  # Red
PLAYER_2_OFFSPRING_COLOR = (255, 100, 100) # Light Red

FOOD_COLOR = (0, 255, 0)    # Green

# Game mechanics
PLAYER_INITIAL_RADIUS = 20
FOOD_RADIUS = 8
PLAYER_SPEED = 2.5
FOOD_COUNT = 10 # Total food on screen
SIZE_DECAY_RATE = 0.3  # How much the player circle radius decreases per second
FOOD_GROWTH_RATE = 1    # How much the player grows when eating food
MIN_PLAYER_RADIUS = 5  # Game over threshold for parent circle

# Offspring Mechanics
OFFSPRING_RADIUS = 6
OFFSPRING_CREATION_COST = 3 # Radius lost by parent to create one offspring
OFFSPRING_MAX_COUNT = 5     # Max offspring per player
OFFSPRING_LAUNCH_SPEED = 4.0
OFFSPRING_DAMAGE = 15        # Radius opponent's parent loses if hit by an offspring
OFFSPRING_ORBIT_DISTANCE = 10 # Distance offspring orbit their parent
OFFSPRING_TRACKING_STRENGTH = 0.1 # Value from 0 (no tracking) to 1 (perfect tracking). Controls how quickly launched offspring adjust to target's movement.

# LLM settings
API_KEY = os.getenv("OPENAI_API_KEY")
DEFAULT_MODEL = "gpt-4.1" # More advanced model
LLM_CALL_INTERVAL = 3   # Seconds between LLM calls for each player

# Player specific initial settings
PLAYER_1_INITIAL_AGGRESSIVENESS = 0.5 # Value from 0.0 (passive) to 1.0 (very aggressive)
PLAYER_2_INITIAL_AGGRESSIVENESS = 0.5 # Value from 0.0 (passive) to 1.0 (very aggressive) 