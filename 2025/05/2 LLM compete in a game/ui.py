import pygame
import config
from typing import Optional, Dict, Any, TYPE_CHECKING

if TYPE_CHECKING:
    from game_objects import Player # For type hinting

class HUD:
    def __init__(self, font_name: str = 'Arial', font_size: int = 18, slider_font_size: int = 16): # Adjusted font size
        self.font = pygame.font.SysFont(font_name, font_size)
        self.slider_font = pygame.font.SysFont(font_name, slider_font_size)
        self.text_color = (255, 255, 255) # White
        self.game_over_color = (255, 0, 0) # Red
        self.winner_color = (0, 255, 0) # Green
        self.slider_track_color = (100, 100, 100) # Dark grey for slider track
        self.slider_thumb_color_default = (200, 200, 200) # Light grey for thumb (will be overridden by player color)
        self.slider_width = 100
        self.slider_height = 10
        self.slider_thumb_width = 8
        self.slider_thumb_height = 16

        # Store slider Rects for interaction
        self.p1_slider_rect: Optional[pygame.Rect] = None
        self.p2_slider_rect: Optional[pygame.Rect] = None
        self.dragging_p1_slider = False
        self.dragging_p2_slider = False
        self.restart_button_rect: Optional[pygame.Rect] = None
        self.button_color = (50, 50, 150) # Dull blue for button
        self.button_text_color = (255, 255, 255)

    def _render_player_stats(self, surface, player_data: Dict[str, Any], aggressiveness: float, start_y: int, is_left_side: bool):
        if not player_data.get('is_alive', True):
            status_text = self.font.render(f"Player {player_data['id']} - DEFEATED", True, self.game_over_color)
            x_pos = 10 if is_left_side else config.WIDTH - status_text.get_width() - 10
            surface.blit(status_text, (x_pos, start_y))
            return start_y + 30

        # Simplified player info line
        info_line = f"P{player_data['id']} ({player_data.get('controller_name', 'N/A')}): R={player_data['radius']:.0f} O={player_data['offspring_count']}"
        info_text = self.font.render(info_line, True, player_data['color'])
        
        action_line = f"Action: {player_data['last_action']}"
        action_text = self.font.render(action_line, True, self.text_color)

        # Aggressiveness display
        aggro_percentage = int(aggressiveness * 100)
        aggro_label_text = self.slider_font.render(f"Aggro: {aggro_percentage}%", True, self.text_color)

        y_offset = start_y
        text_spacing = 22 # Adjusted spacing
        slider_spacing = 5 # Space between text and slider
        current_slider_rect: Optional[pygame.Rect] = None

        if is_left_side:
            x_base = 10
            surface.blit(info_text, (x_base, y_offset)); y_offset += text_spacing
            surface.blit(action_text, (x_base, y_offset)); y_offset += text_spacing
            surface.blit(aggro_label_text, (x_base, y_offset))
            # Slider
            slider_x = x_base + aggro_label_text.get_width() + slider_spacing + 5 # Align next to "Aggro: X%"
            slider_y = y_offset + (aggro_label_text.get_height() - self.slider_height) // 2 # Center slider vertically with text
            current_slider_rect = pygame.Rect(slider_x, slider_y, self.slider_width, self.slider_height)
            self.p1_slider_rect = current_slider_rect # Store P1 slider rect
        else:
            # Align to the right
            x_base = config.WIDTH - 10
            surface.blit(info_text, (x_base - info_text.get_width(), y_offset)); y_offset += text_spacing
            surface.blit(action_text, (x_base - action_text.get_width(), y_offset)); y_offset += text_spacing
            
            # For right side, slider first then text
            slider_x = x_base - self.slider_width - aggro_label_text.get_width() - slider_spacing - 5
            slider_y = y_offset + (aggro_label_text.get_height() - self.slider_height) // 2
            surface.blit(aggro_label_text, (slider_x + self.slider_width + slider_spacing, y_offset))
            current_slider_rect = pygame.Rect(slider_x, slider_y, self.slider_width, self.slider_height)
            self.p2_slider_rect = current_slider_rect # Store P2 slider rect

        # Draw slider track
        pygame.draw.rect(surface, self.slider_track_color, current_slider_rect, border_radius=3)
        
        # Draw slider thumb
        thumb_x = current_slider_rect.x + int(aggressiveness * (self.slider_width - self.slider_thumb_width))
        thumb_y = current_slider_rect.y + (self.slider_height - self.slider_thumb_height) // 2
        pygame.draw.rect(surface, player_data['color'], (thumb_x, thumb_y, self.slider_thumb_width, self.slider_thumb_height), border_radius=3)
        
        return start_y + text_spacing * 2 + 30 # Total height for this player's info block

    def handle_event(self, event: pygame.event.Event, player1: 'Player', player2: 'Player') -> Optional[str]: # Return an action string
        mouse_pos = pygame.mouse.get_pos()
        action_taken: Optional[str] = None

        if event.type == pygame.MOUSEBUTTONDOWN:
            if event.button == 1: # Left click
                if self.p1_slider_rect and self.p1_slider_rect.collidepoint(mouse_pos):
                    self.dragging_p1_slider = True
                elif self.p2_slider_rect and self.p2_slider_rect.collidepoint(mouse_pos):
                    self.dragging_p2_slider = True
                elif self.restart_button_rect and self.restart_button_rect.collidepoint(mouse_pos):
                    action_taken = "restart_game"
        
        elif event.type == pygame.MOUSEBUTTONUP:
            if event.button == 1:
                self.dragging_p1_slider = False
                self.dragging_p2_slider = False
        
        elif event.type == pygame.MOUSEMOTION:
            if self.dragging_p1_slider and self.p1_slider_rect:
                relative_x = mouse_pos[0] - self.p1_slider_rect.x
                new_aggro = relative_x / self.slider_width
                player1.aggressiveness = max(0.0, min(1.0, new_aggro))
            elif self.dragging_p2_slider and self.p2_slider_rect:
                relative_x = mouse_pos[0] - self.p2_slider_rect.x
                new_aggro = relative_x / self.slider_width
                player2.aggressiveness = max(0.0, min(1.0, new_aggro))
        return action_taken # Return None or "restart_game"

    def draw(self, surface, player1_data: Dict[str, Any], player2_data: Dict[str, Any], 
             player1: 'Player', player2: 'Player', # Pass full player objects for aggressiveness update
             game_winner: Optional[int] = None):
        """Draws all HUD elements on the given surface."""
        next_y = 10
        # Pass aggressiveness from player objects directly
        self._render_player_stats(surface, player1_data, player1.aggressiveness, next_y, True)
        self._render_player_stats(surface, player2_data, player2.aggressiveness, next_y, False)

        if game_winner is not None:
            if game_winner == 0: # Draw
                winner_message = self.font.render(f"GAME OVER - IT'S A DRAW!", True, self.game_over_color)
            else:
                winner_message = self.font.render(f"GAME OVER - PLAYER {game_winner} WINS!", True, self.winner_color)
            
            text_rect = winner_message.get_rect(center=(config.WIDTH // 2, config.HEIGHT // 2))
            surface.blit(winner_message, text_rect)

            # Draw Restart Button
            button_text_content = "Restart Game"
            button_font = self.font # Use existing font or a dedicated button font
            button_text_surface = button_font.render(button_text_content, True, self.button_text_color)
            button_width = button_text_surface.get_width() + 40
            button_height = button_text_surface.get_height() + 20
            button_x = config.WIDTH // 2 - button_width // 2
            button_y = config.HEIGHT // 2 + text_rect.height // 2 + 20 # Below winner message
            
            self.restart_button_rect = pygame.Rect(button_x, button_y, button_width, button_height)
            pygame.draw.rect(surface, self.button_color, self.restart_button_rect, border_radius=5)
            surface.blit(button_text_surface, (button_x + (button_width - button_text_surface.get_width()) // 2, 
                                              button_y + (button_height - button_text_surface.get_height()) // 2))
        else:
            self.restart_button_rect = None # No button if game is not over 