# LLM Circle Arena

A competitive game where two circles, controlled by Large Language Models (LLMs), battle for survival by creating and launching offspring.

## Game Overview

Two LLM-controlled players (Player 1 - <PERSON>, Player 2 - Red) compete in an arena. The core objective is to eliminate the opponent by reducing their radius to zero.

### Key Features:

-   **Dual LLM Control:** Both players are independently controlled by LLMs (e.g., GPT-4 Turbo).
-   **Survival & Growth:** Players' circles constantly shrink. They must eat food (green dots) to grow their radius and gain resources.
-   **Offspring Mechanics:**
    -   **Creation:** Players can spend a portion of their radius to create smaller "offspring" circles.
    -   **Orbit & Defense:** Offspring orbit their parent, acting as shields that intercept and destroy incoming enemy offspring.
    -   **Launch Attack:** Players can launch their orbiting offspring at the opponent.
    -   **Damage:** If a launched offspring hits the opponent's main circle, it deals damage, reducing the opponent's radius.
    -   **Imperfect Tracking:** Launched offspring have a slight homing capability, making them more likely to hit a moving target but still dodgeable.
-   **Aggressiveness Control:**
    -   Each LLM has an "aggressiveness" level, adjustable via on-screen sliders (0% to 100%).
    -   This setting dynamically alters the LLM's decision-making process, influencing its tendency to attack, defend, or gather resources.
-   **Dynamic Action Space:** LLMs are only presented with currently valid actions, preventing wasted turns on impossible moves.
-   **Collision System:** Players cannot occupy the same space and will be pushed apart if they collide.
-   **Game End & Restart:** The game ends when one player's radius drops below a minimum threshold. A "Restart Game" button allows for immediate replay.

## Project Structure

The game is organized into the following Python files:

-   **`main.py`**: Contains the main game loop, the `Game` class orchestrating game logic, and event handling.
-   **`config.py`**: Centralized configuration for game parameters (speeds, sizes, colors, LLM settings, initial aggressiveness, offspring mechanics, etc.).
-   **`game_objects.py`**: Defines the `Player`, `Offspring`, and `Food` classes, encapsulating their attributes and behaviors.
-   **`llm_controller.py`**: Manages LLM integration. Includes the base `LLMController` class and the `OpenAIController` which interacts with the OpenAI API and dynamically generates prompts based on game state and aggressiveness.
-   **`ui.py`**: Handles the Heads-Up Display (HUD), rendering player stats, aggressiveness sliders, and game over/restart messages.

## How It Works

### Core Game Loop (`main.py`)

1.  **Initialization:** Sets up Pygame, creates player instances, spawns initial food.
2.  **Event Handling:** Processes user input (quitting, window resizing, slider interaction, restart button).
3.  **LLM Action Requests (`_ask_llm_thread`):**
    -   Periodically (defined by `LLM_CALL_INTERVAL`), for each active player, a separate thread:
        -   Gathers the current `game_state` (player stats, opponent stats, food locations, available actions, aggressiveness level).
        -   Calls the player's `controller.get_action()` method.
4.  **Action Processing (`process_player_actions`):**
    -   Retrieves actions chosen by LLMs (or defaults to "rest").
    -   Validates the action against the current available actions.
    -   Executes the chosen action (move, rest, create offspring, launch offspring).
5.  **Updates:**
    -   Players move, shrink over time, and update their offspring positions (orbiting or launched).
    -   Collision detection and resolution:
        -   Player vs. Player.
        -   Launched Offspring vs. Opponent's Orbiting Offspring (shielding).
        -   Launched Offspring vs. Opponent Parent.
        -   Player vs. Food.
    -   Food replenishment.
6.  **Game Over Check:** Determines if a player has been defeated or if it's a draw.
7.  **Rendering (`draw`):** Draws all game elements (players, food, offspring) and the HUD.

### LLM Integration (`llm_controller.py`)

-   The `OpenAIController` constructs a detailed prompt for the LLM.
-   **Dynamic Prompt Generation:** The prompt includes:
    -   Current game state (radii, offspring counts, distances, food availability).
    -   Explicit guidance based on the player's current `aggressiveness` level, suggesting different strategic priorities.
    -   A list of currently `available_actions` with descriptions and their JSON format for the expected response.
-   The LLM is instructed to return its chosen action in JSON format (e.g., `{"action": "launch_offspring"}`).
-   Error handling and fallbacks (e.g., to a random valid action if the LLM response is invalid) are included.

### UI System (`ui.py`)

-   The `HUD` class is responsible for all on-screen text and interactive elements.
-   Displays simplified stats for each player (radius, offspring count, last action, controller type).
-   Renders and handles mouse interaction for the aggressiveness sliders, directly updating player aggressiveness values.
-   Shows game over messages and a clickable "Restart Game" button.

## Requirements

-   Python 3.7+
-   Pygame: `pip install pygame`
-   OpenAI Python SDK: `pip install openai`
-   Termcolor (for colored console output): `pip install termcolor`
-   An OpenAI API key with access to a suitable model (e.g., GPT-4 Turbo, as configured in `config.py`).

## Setup

1.  Clone this repository.
2.  Install dependencies:
    ```bash
    pip install -r requirements.txt
    ```
3.  Set your OpenAI API key as an environment variable:
    -   **Windows (Command Prompt):**
        ```cmd
        set OPENAI_API_KEY=your_api_key_here
        ```
    -   **Windows (PowerShell):**
        ```powershell
        $env:OPENAI_API_KEY="your_api_key_here"
        ```
    -   **macOS/Linux (Bash/Zsh):**
        ```bash
        export OPENAI_API_KEY=your_api_key_here
        ```
        (You might want to add this to your `.bashrc`, `.zshrc`, or shell profile for persistence.)
4.  Run the game:
    ```bash
    python main.py
    ```

## Customization (`config.py`)

Many aspects of the game can be tuned by modifying `config.py`:

-   Screen `WIDTH` and `HEIGHT`.
-   `FPS` (Frames Per Second).
-   Colors for players, offspring, and food.
-   Player mechanics: `PLAYER_INITIAL_RADIUS`, `PLAYER_SPEED`, `SIZE_DECAY_RATE`, `FOOD_GROWTH_RATE`, `MIN_PLAYER_RADIUS`.
-   Food mechanics: `FOOD_COUNT`, `FOOD_RADIUS`.
-   Offspring mechanics: `OFFSPRING_RADIUS`, `OFFSPRING_CREATION_COST`, `OFFSPRING_MAX_COUNT`, `OFFSPRING_LAUNCH_SPEED`, `OFFSPRING_DAMAGE`, `OFFSPRING_ORBIT_DISTANCE`, `OFFSPRING_TRACKING_STRENGTH`.
-   LLM settings: `API_KEY` (though preferably set via environment variable), `DEFAULT_MODEL`, `LLM_CALL_INTERVAL`.
-   Initial aggressiveness: `PLAYER_1_INITIAL_AGGRESSIVENESS`, `PLAYER_2_INITIAL_AGGRESSIVENESS`.

## Extending the Game

The modular design allows for various extensions:

-   **New Actions:** Add abilities to the `Player` class and update `get_available_actions` and prompt generation.
-   **Different LLM Controllers:** Implement new classes derived from `LLMController` for other models or APIs.
-   **More Complex AI:** Enhance the `RandomController` or create new non-LLM AI behaviors.
-   **Interactive Elements:** Add more UI controls for game settings.
-   **Game Modes:** Introduce different objectives or arena setups.
