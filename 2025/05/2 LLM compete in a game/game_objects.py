import pygame
import math
import config
from typing import List, TYPE_CHECKING, Optional, Dict, Any

# Forward declaration for type hinting to avoid circular import
if TYPE_CHECKING:
    # No longer need to import <PERSON> from main, as it's defined in this file.
    # However, Offspring uses Player and Player uses Offspring in type hints.
    # We can resolve this by putting Offspring class definition before Player, or careful quoting.
    # For simplicity here, we'll ensure Offspring is defined before Player uses it in lists.
    from llm_controller import LLMController

class Offspring:
    # Ensure 'Player' type hint for parent uses quotes if Player class is defined later
    # or ensure Player is defined before Offspring if Offspring uses <PERSON> directly.
    # Current setup: Offspring is defined first, Player uses List[Offspring]
    def __init__(self, parent: 'Player', color: tuple):
        self.parent = parent
        self.radius = config.OFFSPRING_RADIUS
        self.color = color
        self.angle = 0 
        self.x = parent.x + (parent.radius + config.OFFSPRING_ORBIT_DISTANCE + self.radius) * math.cos(self.angle) # Initial position based on dynamic orbit
        self.y = parent.y + (parent.radius + config.OFFSPRING_ORBIT_DISTANCE + self.radius) * math.sin(self.angle)
        
        self.is_launched = False
        self.target_player: Optional['Player'] = None # Store target player object for tracking
        self.vx = 0.0
        self.vy = 0.0

    def update_position(self):
        if self.is_launched and self.target_player:
            if self.target_player.is_alive:
                # Calculate vector towards the target player's current position
                target_dx = self.target_player.x - self.x
                target_dy = self.target_player.y - self.y
                distance_to_target = max(0.001, math.sqrt(target_dx*target_dx + target_dy*target_dy))
                
                # Normalized direction vector to target
                desired_vx = (target_dx / distance_to_target) * config.OFFSPRING_LAUNCH_SPEED
                desired_vy = (target_dy / distance_to_target) * config.OFFSPRING_LAUNCH_SPEED
                
                # Interpolate current velocity towards desired velocity (imperfect tracking)
                tracking_strength = config.OFFSPRING_TRACKING_STRENGTH
                self.vx = (1 - tracking_strength) * self.vx + tracking_strength * desired_vx
                self.vy = (1 - tracking_strength) * self.vy + tracking_strength * desired_vy
                
                # Normalize final velocity to maintain consistent speed (optional, but good for predictability)
                current_speed = math.sqrt(self.vx*self.vx + self.vy*self.vy)
                if current_speed > 0: # Avoid division by zero
                    self.vx = (self.vx / current_speed) * config.OFFSPRING_LAUNCH_SPEED
                    self.vy = (self.vy / current_speed) * config.OFFSPRING_LAUNCH_SPEED

            # Move offspring based on (potentially updated) velocity
            self.x += self.vx
            self.y += self.vy
            
            # Despawn if it goes way off screen (basic boundary check)
            # More robust removal would be in Game class based on collision or screen exit
            if not (0 - self.radius < self.x < config.WIDTH + self.radius and 
                    0 - self.radius < self.y < config.HEIGHT + self.radius):
                # Mark for removal, actual removal handled by Game class to avoid modifying list while iterating
                # For now, we can't directly remove it here. The Game class needs to check and remove.
                # We could add a flag like self.to_be_removed = True
                pass 
        else:
            # Orbit parent
            if hasattr(self.parent, 'x') and hasattr(self.parent, 'y') and self.parent.is_alive:
                self.angle += 0.05 # Simple orbit speed
                # Dynamic orbit distance: parent's radius + a fixed buffer (e.g., config.OFFSPRING_ORBIT_DISTANCE as buffer)
                # Ensure offspring radius is also considered to prevent visual overlap if buffer is small
                orbit_radius = self.parent.radius + config.OFFSPRING_ORBIT_DISTANCE + self.radius 
                self.x = self.parent.x + orbit_radius * math.cos(self.angle)
                self.y = self.parent.y + orbit_radius * math.sin(self.angle)
            else: # Parent might be gone or invalid, offspring stops actively orbiting
                pass 

    def launch(self, target_player_obj: 'Player'): # Changed to accept Player object
        self.is_launched = True
        self.target_player = target_player_obj # Store the target player object
        
        # Initial velocity calculation (as before, towards target's position at launch)
        dx = self.target_player.x - self.x
        dy = self.target_player.y - self.y
        distance = max(0.001, math.sqrt(dx*dx + dy*dy))
        self.vx = (dx / distance) * config.OFFSPRING_LAUNCH_SPEED
        self.vy = (dy / distance) * config.OFFSPRING_LAUNCH_SPEED

    def draw(self, surface):
        pygame.draw.circle(surface, self.color, (int(self.x), int(self.y)), self.radius)

class Food:
    def __init__(self, x: float, y: float):
        self.x = x
        self.y = y
        self.radius = config.FOOD_RADIUS
    
    def draw(self, surface):
        pygame.draw.circle(surface, config.FOOD_COLOR, (int(self.x), int(self.y)), self.radius)

class Player:
    def __init__(self, player_id: int, x: float, y: float, color: tuple, offspring_color: tuple, initial_aggressiveness: float = 0.5):
        self.player_id = player_id
        self.x = x
        self.y = y
        self.radius = config.PLAYER_INITIAL_RADIUS
        self.color = color
        self.offspring_color = offspring_color
        self.vx = 0
        self.vy = 0
        self.last_action = "rest"
        self.offspring_list: List[Offspring] = []
        self.is_alive = True
        self.aggressiveness = initial_aggressiveness # Value between 0.0 (passive) and 1.0 (aggressive)
        
        if TYPE_CHECKING:
            self.controller: LLMController 

    def get_available_actions(self, opponent_is_alive: bool) -> List[Dict[str, str]]:
        """Returns a list of dictionaries, each representing an available action with a description."""
        if not self.is_alive: 
            return []

        actions = []
        actions.append({"name": "move_to_food", "description": "Move towards the closest green food item."}) 
        actions.append({"name": "rest", "description": "Stay in place."})

        # Check for create_offspring
        can_create_offspring = (
            len(self.offspring_list) < config.OFFSPRING_MAX_COUNT and 
            self.radius > config.OFFSPRING_CREATION_COST + config.MIN_PLAYER_RADIUS
        )
        if can_create_offspring:
            actions.append({
                "name": "create_offspring", 
                "description": f"Cost: {config.OFFSPRING_CREATION_COST} radius. Result: +1 orbiting offspring (max {config.OFFSPRING_MAX_COUNT})."
            })

        # Check for launch_offspring
        can_launch_offspring = (
            any(not off.is_launched for off in self.offspring_list) and 
            opponent_is_alive
        )
        if can_launch_offspring:
            actions.append({
                "name": "launch_offspring", 
                "description": f"Cost: 1 orbiting offspring. Result: Launch at opponent (deals {config.OFFSPRING_DAMAGE} damage if hit)."
            })
        
        return actions

    def move(self):
        if not self.is_alive: return
        self.x += self.vx
        self.y += self.vy
        
        self.x = max(self.radius, min(config.WIDTH - self.radius, self.x))
        self.y = max(self.radius, min(config.HEIGHT - self.radius, self.y))

    def move_towards(self, target_x: float, target_y: float):
        if not self.is_alive: return
        dx = target_x - self.x
        dy = target_y - self.y
        distance = max(0.001, math.sqrt(dx*dx + dy*dy))
        self.vx = (dx / distance) * config.PLAYER_SPEED
        self.vy = (dy / distance) * config.PLAYER_SPEED
        
    def rest(self):
        if not self.is_alive: return
        self.vx = 0
        self.vy = 0
    
    def shrink(self, delta_time: float):
        if not self.is_alive: return
        self.radius -= config.SIZE_DECAY_RATE * delta_time
        if self.radius < config.MIN_PLAYER_RADIUS:
            self.radius = config.MIN_PLAYER_RADIUS # Prevent going below min
            self.is_alive = False # Player is out
            print(f"Player {self.player_id} has run out of radius!")

    def eat(self, food_list: List[Food]) -> List[Food]:
        if not self.is_alive: return food_list
        remaining_food: List[Food] = []
        for food in food_list:
            distance = math.sqrt((self.x - food.x)**2 + (self.y - food.y)**2)
            if distance < self.radius + food.radius:
                self.radius += config.FOOD_GROWTH_RATE
            else:
                remaining_food.append(food)
        return remaining_food

    def create_offspring(self):
        if not self.is_alive: return
        if len(self.offspring_list) < config.OFFSPRING_MAX_COUNT and self.radius > config.OFFSPRING_CREATION_COST + config.MIN_PLAYER_RADIUS:
            self.radius -= config.OFFSPRING_CREATION_COST
            new_offspring = Offspring(self, self.offspring_color)
            # Distribute angle for new offspring to avoid overlap initially
            # Ensure there's always at least 1 slot for division, or use a sensible default angle.
            active_offspring_count = len([off for off in self.offspring_list if not off.is_launched])
            
            # Spread new offspring around existing orbiting ones.
            if config.OFFSPRING_MAX_COUNT > 0:
                 new_offspring.angle = (2 * math.pi / config.OFFSPRING_MAX_COUNT) * active_offspring_count
            else:
                 new_offspring.angle = 0 # Default angle if max_count is 0 (should not happen)

            # Initial position update is critical AFTER angle is set.
            # The Offspring.__init__ calculates an initial x,y based on angle=0 and fixed orbit.
            # We must call update_position() here to correctly place it based on its assigned angle and dynamic radius.
            new_offspring.update_position() 
            self.offspring_list.append(new_offspring)
            self.last_action = "created offspring"
            print(f"Player {self.player_id} created an offspring. Radius: {self.radius:.1f}, Offspring: {len(self.offspring_list)}")
        else:
            print(f"Player {self.player_id} failed to create offspring. Radius: {self.radius:.1f}, Offspring: {len(self.offspring_list)}")
            self.last_action = "failed create offspring"

    def launch_offspring(self, target_player: 'Player'): # Use quotes for Player type hint
        if not self.is_alive or not target_player.is_alive: return
        # Launch the first available (oldest) non-launched offspring
        orbiting_offspring = [off for off in self.offspring_list if not off.is_launched]
        if orbiting_offspring:
            offspring_to_launch = orbiting_offspring[0]
            offspring_to_launch.launch(target_player)
            self.last_action = f"launched offspring at P{target_player.player_id}"
            print(f"Player {self.player_id} launched offspring at Player {target_player.player_id}")
        else:
            self.last_action = "failed launch (no offspring)"
            print(f"Player {self.player_id} failed to launch offspring (none available or all launched).")

    def update_offspring(self):
        if not self.is_alive: 
            # If parent is dead, maybe offspring should also be removed or stop moving
            # For now, they just keep their last command if launched, or stop orbiting logic
            # This needs refinement based on desired game behavior for orphaned offspring
            for offspring in self.offspring_list:
                if offspring.is_launched:
                    offspring.update_position()
            return

        for offspring in self.offspring_list:
            offspring.update_position()

    def take_damage(self, amount: float):
        if not self.is_alive: return
        self.radius -= amount
        print(f"Player {self.player_id} took {amount} damage. Radius: {self.radius:.1f}")
        if self.radius < config.MIN_PLAYER_RADIUS:
            self.radius = config.MIN_PLAYER_RADIUS
            self.is_alive = False
            print(f"Player {self.player_id} defeated by damage!")

    def draw(self, surface):
        if not self.is_alive:
            # Optionally draw a different state for a defeated player (e.g., smaller, greyed out)
            # pygame.draw.circle(surface, (100,100,100), (int(self.x), int(self.y)), int(config.MIN_PLAYER_RADIUS))
            return # For now, just don't draw if not alive

        pygame.draw.circle(surface, self.color, (int(self.x), int(self.y)), int(self.radius))
        for offspring in self.offspring_list:
            offspring.draw(surface) 