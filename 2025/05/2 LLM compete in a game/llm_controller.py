import json
import random
import async<PERSON>
from typing import Dict, Any, Optional, List
from openai import AsyncOpenAI
import config
from termcolor import colored

# Assuming game_objects.py is in the same directory or accessible in PYTHONPATH
# We need Player type hint for game_state structure, but avoid circular import for runtime
from typing import TYPE_CHECKING
if TYPE_CHECKING:
    from game_objects import Player # For type hinting game_state structure

class LLMController:
    def __init__(self, player_id: int):
        self.player_id = player_id
        self.name = f"Base LLM Controller P{player_id}"
    
    async def get_action(self, game_state: Dict[str, Any]) -> Dict[str, str]:
        """Get an action from the LLM based on the current game state"""
        raise NotImplementedError("Subclasses must implement get_action")

class RandomController(LLMController):
    """A controller that chooses random actions (fallback when no API key)"""
    def __init__(self, player_id: int):
        super().__init__(player_id)
        self.name = f"Random Controller P{player_id}"
    
    async def get_action(self, game_state: Dict[str, Any]) -> Dict[str, str]:
        player_info = game_state.get('player', {})
        available_actions = player_info.get('available_actions', [])
        
        if not available_actions:
            # This case should ideally not happen if player is alive and has default actions
            # but as a fallback, ensure 'rest' is always an option if list is empty.
            return {"action": "rest"}

        # Convert list of dicts to list of action names for random.choice
        action_names = [action_dict["name"] for action_dict in available_actions]
        
        # Slightly more intelligent random: prioritize launch if possible and has offspring
        if player_info.get('offspring_count', 0) > 0 and "launch_offspring" in action_names and random.random() < 0.6:
            chosen_action_name = "launch_offspring"
        elif "create_offspring" in action_names and random.random() < 0.4:
            chosen_action_name = "create_offspring"
        else:
            # Choose from remaining available actions, or default to rest if somehow empty
            non_special_actions = [name for name in action_names if name not in ["launch_offspring", "create_offspring"]]
            if non_special_actions:
                chosen_action_name = random.choice(non_special_actions)
            elif action_names: # Fallback to any available if non_special is empty but action_names is not
                chosen_action_name = random.choice(action_names)
            else: # Should not be reached if available_actions always includes at least 'rest'
                chosen_action_name = "rest"

        return {"action": chosen_action_name}

class OpenAIController(LLMController):
    """Controller that uses OpenAI's API to make decisions"""
    def __init__(self, player_id: int, api_key: Optional[str] = None, model: str = config.DEFAULT_MODEL):
        super().__init__(player_id)
        self.name = f"OpenAI ({model}) P{player_id}"
        self.api_key = api_key or config.API_KEY
        self.model = model
        
        if self.api_key:
            self.client = AsyncOpenAI(api_key=self.api_key)
        else:
            self.client = None
            print(f"Warning: No API key provided for OpenAIController P{player_id}")
    
    async def get_action(self, game_state: Dict[str, Any]) -> Dict[str, str]:
        """Get an action from the OpenAI API based on the current game state"""
        if not self.client:
            # Fallback to a new RandomController instance if OpenAI client isn't initialized
            return await RandomController(self.player_id).get_action(game_state)
        
        try:
            prompt = self._create_prompt(game_state)
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                response_format={"type": "json_object"}
            )
            content = response.choices[0].message.content
            print(colored(f"P{self.player_id} LLM raw response: {content}", "cyan"))
            action_data = json.loads(content)
            
            # Validate that the chosen action is one of the currently available actions
            player_state_info = game_state.get('player', {})
            available_action_names = [act["name"] for act in player_state_info.get('available_actions', [])]
            
            if not available_action_names: # Should not happen if player is alive
                 print(colored(f"P{self.player_id} LLM Error: No available actions listed in game_state. Defaulting to rest.", "red"))
                 return {"action": "rest"}

            if action_data.get("action") not in available_action_names:
                print(colored(f"P{self.player_id} LLM returned an unavailable action: '{action_data.get('action')}'. Valid: {available_action_names}. Defaulting to random available action.", "yellow"))
                # Choose a random valid action as a recovery mechanism
                return {"action": random.choice(available_action_names) if available_action_names else "rest"}
            return action_data
            
        except Exception as e:
            print(colored(f"Error in P{self.player_id} OpenAI API call: {str(e)}", "red"))
            # Fallback to random action using the game_state to ensure it picks from available actions
            return await RandomController(self.player_id).get_action(game_state)
    
    def _create_prompt(self, state: Dict[str, Any]) -> str:
        """Create a prompt for the LLM based on the game state"""
        player_s = state['player']
        opponent_s = state['opponent']
        game_s = state['game_info']
        available_actions = player_s.get('available_actions', [])
        player_aggressiveness = player_s.get('aggressiveness', 0.5) 
        aggro_percentage_str = f"{player_aggressiveness*100:.0f}"

        actions_description_list = []
        for i, action_info in enumerate(available_actions):
            actions_description_list.append(f"{i+1}. {action_info['name']} (`{{'action': '{action_info['name']}'}}`): {action_info['description']}")
        actions_prompt_part = "\n".join(actions_description_list)
        if not actions_prompt_part: 
            actions_prompt_part = "No actions currently available. (This is likely an error in game state)"

        nearest_food_info = player_s.get('nearest_food', {})
        nearest_food_dist = nearest_food_info.get('distance')
        nearest_food_distance_str = f"{nearest_food_dist:.1f}" if nearest_food_dist is not None and nearest_food_dist != float('inf') else 'N/A'

        # Adjusted Aggressiveness guidance for more proactive play
        aggro_level_desc = "ERROR: UNSET AGGRO DESC"
        if player_aggressiveness <= 0.20: # 0% - 20% (Was <= 0.25, Very Cautious)
            aggro_level_desc = "very cautious. Prioritize survival and food. Create offspring mainly for defense. Only launch if a very clear and safe opportunity arises."
        elif player_aggressiveness <= 0.45: # 21% - 45% (Was <= 0.5, Cautious)
            aggro_level_desc = "cautiously aggressive. Balance food gathering with building offspring. Start looking for chances to launch offspring to apply some pressure."
        elif player_aggressiveness <= 0.75: # 46% - 75% (Was <= 0.75, Aggressive - similar range, stronger wording)
            aggro_level_desc = "decidedly aggressive while maintaining size. Focus on creating and launching offspring to actively pressure and damage your opponent. Food is secondary to maintaining offensive momentum."
        else: # 76% - 100% (Was > 0.75, Very Aggressive - stronger wording)
            aggro_level_desc = "relentlessly aggressive. Your main goal is to overwhelm the opponent with constant offspring attacks. Create and launch at every opportunity. Take significant risks with your radius to secure a kill."

        return f"""
        You are Player {self.player_id} controlling a circle in a competitive game against Player {opponent_s['id']}.
        Your goal: Defeat Player {opponent_s['id']} by launching offspring, while surviving by eating food.
        Your current strategic approach is: {aggro_level_desc} (Aggressiveness: {aggro_percentage_str}%)

        Game State:
        - Your (P{self.player_id}) Radius: {player_s['radius']:.1f} (Min: {game_s['min_radius']} to survive)
        - Your Orbiting Offspring: {player_s['offspring_count']} / {config.OFFSPRING_MAX_COUNT}
        - Opponent (P{opponent_s['id']}) Radius: {opponent_s['radius']:.1f}
        - Opponent Orbiting Offspring: {opponent_s['offspring_count']}
        - Distance to Opponent: {player_s['distance_to_opponent']:.1f}
        - Nearest Food Distance: {nearest_food_distance_str}
        - Total Food Items on map: {game_s['food_count']}

        Offspring Info:
        - Creating an offspring costs {game_s['offspring_creation_cost']} radius.
        - Launching an offspring at opponent can deal {game_s['offspring_damage']} damage if it hits their main circle.
        - Orbiting offspring act as shields: they intercept incoming enemy offspring (both are destroyed).

        Available Actions for Player {self.player_id} (Choose one by returning its JSON format):
        {actions_prompt_part}

        Strategic Considerations (inline with your aggressiveness: {aggro_percentage_str}%):
        - Your radius constantly shrinks. You need food to survive and to create offspring.
        - Based on your aggressiveness, how should you balance creating offspring vs. eating food?
        - If aggressive: Is your opponent vulnerable? Do they have few offspring? Can you create an offspring and still be safe to attack?
        - If cautious: Is food nearby and critical? Should you build up more offspring for defense before considering an attack?

        Respond with ONLY a JSON object for ONE of the available actions listed above. Example: {{"action": "move_to_food"}}
        Your current radius: {player_s['radius']:.1f}. Your offspring: {player_s['offspring_count']}. Aggressiveness: {aggro_percentage_str}%
        Choose your action wisely, Player {self.player_id}!
        """

def get_controller(player_id: int, controller_type: str = "openai", api_key: Optional[str] = None, model: str = config.DEFAULT_MODEL) -> LLMController:
    """Factory function to create the appropriate controller"""
    if controller_type.lower() == "openai":
        return OpenAIController(player_id, api_key, model)
    elif controller_type.lower() == "random":
        return RandomController(player_id)
    else:
        print(colored(f"Warning: Unknown controller type '{controller_type}' for P{player_id}. Defaulting to RandomController.", "yellow"))
        return RandomController(player_id) 