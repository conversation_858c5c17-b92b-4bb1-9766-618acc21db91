import json
import random
import async<PERSON>
from typing import Dict, Any, Optional
from openai import AsyncOpenAI
import config
from termcolor import colored

class LLMController:
    """Base class for LLM-based game controllers"""
    def __init__(self):
        self.name = "Base LLM Controller"
    
    async def get_action(self, game_state: Dict[str, Any]) -> Dict[str, str]:
        """Get an action from the LLM based on the current game state"""
        raise NotImplementedError("Subclasses must implement get_action")

class RandomController(LLMController):
    """A controller that chooses random actions (fallback when no API key)"""
    def __init__(self):
        super().__init__()
        self.name = "Random Controller"
    
    async def get_action(self, game_state: Dict[str, Any]) -> Dict[str, str]:
        """Choose a random action"""
        action = random.choice(["move_to_food", "rest"])
        return {"action": action}

class OpenAIController(LLMController):
    """Controller that uses OpenAI's API to make decisions"""
    def __init__(self, api_key: Optional[str] = None, model: str = config.DEFAULT_MODEL):
        super().__init__()
        self.name = f"OpenAI ({model})"
        self.api_key = api_key or config.API_KEY
        self.model = model
        
        if self.api_key:
            self.client = AsyncOpenAI(api_key=self.api_key)
        else:
            self.client = None
            print("Warning: No API key provided for OpenAIController")
    
    async def get_action(self, game_state: Dict[str, Any]) -> Dict[str, str]:
        """Get an action from the OpenAI API based on the current game state"""
        if not self.client:
            # Fall back to random controller if no API key
            return await RandomController().get_action(game_state)
        
        try:
            # Prepare prompt for the LLM
            prompt = self._create_prompt(game_state)
            
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                response_format={"type": "json_object"}
            )
            
            content = response.choices[0].message.content
            print(colored(f"LLM response: {content}", "green"))
            return json.loads(content)
            
        except Exception as e:
            print(f"Error in OpenAI API call: {str(e)}")
            # Fall back to random action on error
            return await RandomController().get_action(game_state)
    
    def _create_prompt(self, state: Dict[str, Any]) -> str:
        """Create a prompt for the LLM based on the game state"""
        return f"""
            You are controlling a circle in a simple game. Your goal is to survive by eating food.
            - If your radius falls below {state['game_info']['min_radius']}, you lose.
            - Your circle constantly shrinks over time.
            - Eating food makes you larger.
            
            Current state:
            - Your radius: {state['player']['radius']:.2f}
            - Food remaining: {state['game_info']['food_count']}
            
            You can only respond with one of these actions in JSON format:
            1. {{"action": "move_to_food"}} - Move towards the nearest food
            2. {{"action": "rest"}} - Stay in place (conserve energy)
            
            Choose wisely based on your current state.
            """

def get_controller(controller_type: str = "openai", api_key: Optional[str] = None, model: str = config.DEFAULT_MODEL) -> LLMController:
    """Factory function to create the appropriate controller"""
    if controller_type.lower() == "openai":
        return OpenAIController(api_key, model)
    elif controller_type.lower() == "random":
        return RandomController()
    else:
        raise ValueError(f"Unknown controller type: {controller_type}") 