import os

# Window settings
WIDTH, HEIGHT = 800, 600
FPS = 60

# Colors
BACKGROUND_COLOR = (0, 0, 0)
PLAYER_COLOR = (0, 0, 255)  # Blue
FOOD_COLOR = (0, 255, 0)    # Green

# Game mechanics
PLAYER_INITIAL_RADIUS = 20
FOOD_RADIUS = 8
PLAYER_SPEED = 2.5
FOOD_COUNT = 5
SIZE_DECAY_RATE = 0.5  # How much the player circle radius decreases per second
FOOD_GROWTH_RATE = 1    # How much the player grows when eating food
MIN_PLAYER_RADIUS = 10  # Game over threshold
LLM_CALL_INTERVAL = 3   # Seconds between LLM calls

# LLM settings
API_KEY = os.getenv("OPENAI_API_KEY")
DEFAULT_MODEL = "gpt-4.1" 