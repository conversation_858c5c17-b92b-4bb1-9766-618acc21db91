# LLM Circle Game

A simple game where a circle controlled by a Large Language Model (LLM) must eat food to survive.

## Game Overview

In this game:

- You have a blue player circle that constantly shrinks over time
- The circle is controlled by an LLM (GPT-4.1) making decisions every few seconds
- Green food dots appear on the screen that the player can eat to grow
- The goal is to survive as long as possible

The LLM must decide between two actions:

1. **Move toward food** - Travel to the nearest food dot
2. **Rest** - Stay in place (potentially conserving energy)

If the player's radius falls below a certain threshold, the game ends.

## Project Structure

The code is modularly organized into several files:

- **`main.py`**: The entry point containing the main game loop and Game class
- **`config.py`**: Game configuration settings (sizes, speeds, colors, etc.)
- **`game_objects.py`**: Classes for game entities (Player and Food)
- **`llm_controller.py`**: LLM integration for AI decision making
- **`ui.py`**: UI components for rendering game information

## How It Works

### Core Game Mechanics

1. The player circle loses size over time (configured by `SIZE_DECAY_RATE`)
2. Every few seconds, the LLM is asked what action to take
3. The player can eat food to increase its size (configured by `FOOD_GROWTH_RATE`)
4. If the player's size falls below `MIN_PLAYER_RADIUS`, the game ends

### LLM Integration

The game uses OpenAI's GPT-4 to control the player. The LLM:

- Is called at regular intervals (every `LLM_CALL_INTERVAL` seconds)
- Receives information about the player's size and available food
- Returns a decision in JSON format with an "action" key
- Has two possible actions: "move_to_food" or "rest"

The game also includes a fallback random controller if no API key is provided.

### Technical Architecture

#### The `Game` Class

- Manages the game state: player, food, and LLM actions
- Spawns food, handles player movement, and detects collisions
- Communicates with the LLM controller to get actions

#### The LLM Controllers

- Base `LLMController` class defines the interface
- `OpenAIController` implements GPT-4 API calls
- `RandomController` provides a fallback with random actions
- Factory function `get_controller()` creates the appropriate controller

#### The UI System

- `HUD` class handles rendering game information
- Displays player radius, current action, food count, and game over message

## Requirements

- Python 3.7+
- Pygame
- OpenAI Python SDK
- An OpenAI API key with access to GPT-4

## Setup

1. Clone this repository
2. Install dependencies:
   ```
   pip install -r requirements.txt
   ```
3. Set your OpenAI API key as an environment variable:
   ```
   # On Windows
   set OPENAI_API_KEY=your_api_key_here

   # On macOS/Linux
   export OPENAI_API_KEY=your_api_key_here
   ```
4. Run the game:
   ```
   python main.py
   ```

## Customization

You can customize various aspects of the game by modifying `config.py`:

- Change window size and colors
- Adjust player speed, size, and decay rate
- Modify food count and growth value
- Change the LLM call frequency
- Switch to a different model

## Extending the Game

### Adding New LLM Controllers

To add a new LLM controller:

1. Create a new class that extends `LLMController` in `llm_controller.py`
2. Implement the `get_action` method
3. Update the `get_controller` factory function to include your new controller
4. Use it by specifying the controller type when creating a Game instance

### Adding Game Features

The modular nature of the code makes it easy to add new features:

- Add new player abilities in the `Player` class
- Create new types of food in `game_objects.py`
- Implement different game modes in `main.py`
- Enhance the HUD with more information in `ui.py`
