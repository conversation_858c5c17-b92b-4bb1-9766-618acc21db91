import pygame
import math
import config
from typing import List

class Food:
    def __init__(self, x: float, y: float):
        self.x = x
        self.y = y
        self.radius = config.FOOD_RADIUS
    
    def draw(self, surface):
        pygame.draw.circle(surface, config.FOOD_COLOR, (int(self.x), int(self.y)), self.radius)

class Player:
    def __init__(self, x: float, y: float):
        self.x = x
        self.y = y
        self.radius = config.PLAYER_INITIAL_RADIUS
        self.vx = 0
        self.vy = 0
        self.last_action = "rest"
    
    def move(self):
        self.x += self.vx
        self.y += self.vy
        
        # Keep player within bounds
        self.x = max(self.radius, min(config.WIDTH - self.radius, self.x))
        self.y = max(self.radius, min(config.HEIGHT - self.radius, self.y))
    
    def move_towards(self, target_x: float, target_y: float):
        # Calculate direction vector
        dx = target_x - self.x
        dy = target_y - self.y
        
        # Normalize
        distance = max(0.001, math.sqrt(dx*dx + dy*dy))  # Avoid division by zero
        dx /= distance
        dy /= distance
        
        # Set velocity
        self.vx = dx * config.PLAYER_SPEED
        self.vy = dy * config.PLAYER_SPEED
        
    def rest(self):
        self.vx = 0
        self.vy = 0
    
    def draw(self, surface):
        pygame.draw.circle(surface, config.PLAYER_COLOR, (int(self.x), int(self.y)), int(self.radius))
    
    def shrink(self, delta_time: float):
        self.radius -= config.SIZE_DECAY_RATE * delta_time
    
    def eat(self, food_list: List[Food]) -> List[Food]:
        remaining_food: List[Food] = []
        for food in food_list:
            distance = math.sqrt((self.x - food.x)**2 + (self.y - food.y)**2)
            if distance < self.radius + food.radius:
                # Collision detected - eat the food
                self.radius += config.FOOD_GROWTH_RATE
            else:
                remaining_food.append(food)
        return remaining_food 