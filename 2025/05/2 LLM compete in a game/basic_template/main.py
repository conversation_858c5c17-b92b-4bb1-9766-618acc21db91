import pygame
import sys
import random
import math
import json
import time
import threading
import asyncio
from typing import List, Tuple, Dict, Union, Optional

# Import configurations, LLM controller, game objects, and UI
import config
from llm_controller import get_controller, LLMController
from game_objects import Player, Food
from ui import HUD # Import HUD class

# Initialize pygame
pygame.init()
screen = pygame.display.set_mode((config.WIDTH, config.HEIGHT), pygame.RESIZABLE)
pygame.display.set_caption("LLM Circle Game")
clock = pygame.time.Clock()
# font = pygame.font.SysFont('Arial', 24) # Font initialization is now in HUD

class Game:
    def __init__(self, controller_type: str = "openai"):
        self.player = Player(config.WIDTH // 2, config.HEIGHT // 2)
        self.foods: List[Food] = [] # Type hint for clarity
        self.spawn_food(config.FOOD_COUNT)
        self.last_llm_call_time = 0
        self.running = True
        self.lock = threading.Lock()
        self.game_state = {"action": "rest"}
        
        # Initialize the controller
        self.controller = get_controller(controller_type)
        print(f"Using controller: {self.controller.name}")
        
        # Initialize HUD
        self.hud = HUD() # Create an instance of the HUD
    
    def spawn_food(self, count: int):
        for _ in range(count):
            x = random.randint(config.FOOD_RADIUS, config.WIDTH - config.FOOD_RADIUS)
            y = random.randint(config.FOOD_RADIUS, config.HEIGHT - config.FOOD_RADIUS)
            self.foods.append(Food(x, y))
    
    def find_nearest_food(self) -> Optional[Food]:
        if not self.foods:
            return None
        
        nearest = min(
            self.foods, 
            key=lambda food: math.sqrt((self.player.x - food.x)**2 + (self.player.y - food.y)**2)
        )
        return nearest
    
    def get_game_state(self) -> Dict[str, Union[float, List[Dict[str, float]]]]:
        nearest_food = self.find_nearest_food()
        nearest_food_position = {}
        if nearest_food:
            nearest_food_position = {
                "x": nearest_food.x,
                "y": nearest_food.y,
                "distance": math.sqrt((self.player.x - nearest_food.x)**2 + (self.player.y - nearest_food.y)**2)
            }
        
        return {
            "player": {
                "radius": self.player.radius,
                "x": self.player.x,
                "y": self.player.y
            },
            "game_info": {
                "min_radius": config.MIN_PLAYER_RADIUS,
                "food_count": len(self.foods),
                "width": config.WIDTH,
                "height": config.HEIGHT
            },
            "nearest_food": nearest_food_position
        }
    
    def ask_llm(self):
        """Queries the LLM for the next action in a separate thread."""
        threading.Thread(target=self._ask_llm_thread).start()
    
    def _ask_llm_thread(self):
        try:
            # Create and run a new event loop for the async function
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            result = loop.run_until_complete(self._async_ask_llm())
            loop.close()
            
            with self.lock:
                self.game_state = result
                
        except Exception as e:
            print(f"Error querying LLM: {str(e)}")
            # Fallback to rest action
            with self.lock:
                self.game_state = {"action": "rest"}
    
    async def _async_ask_llm(self):
        state = self.get_game_state()
        return await self.controller.get_action(state)
    
    def update(self, delta_time: float):
        current_time = time.time()
        
        # Call LLM every few seconds
        if current_time - self.last_llm_call_time >= config.LLM_CALL_INTERVAL:
            self.ask_llm()
            self.last_llm_call_time = current_time
        
        # Get action from LLM
        with self.lock:
            action = self.game_state.get("action", "rest")
        
        # Execute action
        if action == "move_to_food":
            nearest_food = self.find_nearest_food()
            if nearest_food:
                self.player.move_towards(nearest_food.x, nearest_food.y)
                self.player.last_action = "moving to food"
            else:
                self.player.rest()
                self.player.last_action = "resting (no food found)"
        else:  # rest
            self.player.rest()
            self.player.last_action = "resting"
        
        # Update player
        self.player.move()
        self.player.shrink(delta_time)
        
        # Check for food consumption
        self.foods = self.player.eat(self.foods)
        
        # Respawn food if needed
        if len(self.foods) < config.FOOD_COUNT:
            self.spawn_food(config.FOOD_COUNT - len(self.foods))
        
        # Check game over condition
        if self.player.radius < config.MIN_PLAYER_RADIUS:
            self.running = False
    
    def draw(self):
        # Clear screen
        screen.fill(config.BACKGROUND_COLOR)
        
        # Draw food
        for food in self.foods:
            food.draw(screen)
        
        # Draw player
        self.player.draw(screen)
        
        # Draw HUD elements using the HUD class
        self.hud.draw(
            surface=screen,
            player_radius=self.player.radius,
            last_action=self.player.last_action,
            food_count=len(self.foods),
            controller_name=self.controller.name,
            is_game_over=not self.running
        )
        
        pygame.display.flip()

def main():
    # You can change the controller type here (e.g., "openai", "random")
    game = Game(controller_type="openai")
    
    last_time = time.time()
    while True:
        # Calculate delta time
        current_time = time.time()
        delta_time = current_time - last_time
        last_time = current_time
        
        # Process events
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                pygame.quit()
                sys.exit()
            elif event.type == pygame.VIDEORESIZE:
                # global WIDTH, HEIGHT # No longer needed as config handles this
                config.WIDTH, config.HEIGHT = event.size
                screen = pygame.display.set_mode((config.WIDTH, config.HEIGHT), pygame.RESIZABLE)
        
        # Update and draw game
        if game.running:
            game.update(delta_time)
        game.draw()
        
        # Cap the frame rate
        clock.tick(config.FPS)

if __name__ == "__main__":
    main() 