import pygame
import config

class HUD:
    def __init__(self, font_name: str = 'Arial', font_size: int = 24):
        self.font = pygame.font.SysFont(font_name, font_size)
        self.text_color = (255, 255, 255) # White
        self.game_over_color = (255, 0, 0) # Red

    def draw(self, surface, player_radius: float, last_action: str, food_count: int, controller_name: str, is_game_over: bool):
        """Draws all HUD elements on the given surface."""
        # Display game stats
        radius_text = self.font.render(f"Radius: {player_radius:.1f}", True, self.text_color)
        action_text = self.font.render(f"Action: {last_action}", True, self.text_color)
        food_text = self.font.render(f"Food: {food_count}", True, self.text_color)
        controller_text = self.font.render(f"Controller: {controller_name}", True, self.text_color)

        surface.blit(radius_text, (10, 10))
        surface.blit(action_text, (10, 40))
        surface.blit(food_text, (10, 70))
        surface.blit(controller_text, (10, 100))

        # Display game over message if applicable
        if is_game_over:
            game_over_text_surface = self.font.render("GAME OVER", True, self.game_over_color)
            # Center the text
            text_rect = game_over_text_surface.get_rect(center=(config.WIDTH // 2, config.HEIGHT // 2))
            surface.blit(game_over_text_surface, text_rect) 