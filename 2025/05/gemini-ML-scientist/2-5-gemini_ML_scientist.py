import subprocess
import json
import pandas as pd
import re
from termcolor import colored
import os
import shutil
from google import genai
from google.genai import types
import signal
from datetime import datetime
from collections import deque

# Constants
ITERATIONS = 10
ERROR_HISTORY_SIZE = 3  # Keep track of last 3 errors
SOLUTION_HISTORY_SIZE = 3  # Keep track of last 3 solutions
error_history = deque(maxlen=ERROR_HISTORY_SIZE)
solution_history = deque(maxlen=SOLUTION_HISTORY_SIZE)  # Store tuples of (solution_code, progress_report)
MODEL = "gemini-2.5-pro-preview-05-06"
MAX_TOKENS = 64000
MAX_RUNTIME_MINUTES = 15  # Maximum runtime in minutes
MAX_API_CALL_ATTEMPTS = 3  # Number of attempts for the API call
OPTIONAL_INSTRUCTIONS = "think carefully about the features we are using. Focus more on feature engineering before scaling up the compute part of the process. if you break 82 83 percent accuracy then dig deeper into scaling the solution deeper. include any information in the report json because these will be used by you in future iterations to improve the solution. be creative and resourceful as the machine only has 8 gigs of vram and 30 gig regular RAM. You must achive over 90 percent accuracy, keep the training-test split at 60-40"

SYSTEM_MESSAGE_SOLUTION_GENERATION = """You are an expert ML scientist. Your primary task is to generate Python code.
CRITICAL: You MUST adhere to the following output format STRICTLY:
1. Return ONLY the complete Python code solution.
2. The ENTIRE response text MUST be wrapped in a single pair of ```python and ``` markers.
3. NO text, explanations, or any other characters should appear before the opening ```python marker or after the closing ``` marker.
4. Ensure the generated Python code is complete, including all necessary imports and functions.
Failure to follow this format will render the output unusable."""

SYSTEM_MESSAGE_TIMEOUT_IMPROVEMENT = """You are an expert Python performance optimizer. Your task is to optimize Python code for speed.
CRITICAL: You MUST adhere to the following output format STRICTLY:
1. Return ONLY the complete optimized Python code.
2. The ENTIRE response text MUST be wrapped in a single pair of ```python and ``` markers.
3. NO text, explanations, or any other characters should appear before the opening ```python marker or after the closing ``` marker.
4. Ensure the generated Python code is complete, including all necessary imports and functions.
Failure to follow this format will render the output unusable."""

SYSTEM_MESSAGE_FIX_ERROR = """You are an expert Python developer. Your task is to fix code errors.
CRITICAL: You MUST adhere to the following output format STRICTLY:
1. Return ONLY the complete fixed Python code.
2. The ENTIRE response text MUST be wrapped in a single pair of ```python and ``` markers.
3. NO text, explanations, or any other characters should appear before the opening ```python marker or after the closing ``` marker.
4. Ensure the generated Python code is complete, including all necessary imports and functions.
Failure to follow this format will render the output unusable."""

print(colored("Phase 1: Setting up API Client", "cyan"))
client = genai.Client(
    api_key=os.getenv("GEMINI_API_KEY"),
)

# Clear execution outputs file at start
output_file = "execution_outputs.txt"
with open(output_file, 'w', encoding='utf-8') as f:
    f.write(f"=== New Run Started at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} ===\n\n")
print(colored("Cleared previous execution outputs", "green"))

class TimeoutException(Exception):
    pass

def timeout_handler(signum, frame):
    raise TimeoutException("Script execution timed out")

def run_with_timeout(cmd, timeout_minutes):
    """Run command with timeout"""
    start_time = datetime.now()
    process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
    try:
        stdout, stderr = process.communicate(timeout=timeout_minutes * 60)
        execution_time = (datetime.now() - start_time).total_seconds()
        return stdout, stderr, process.returncode, execution_time
    except subprocess.TimeoutExpired:
        process.kill()
        return "", "Execution timed out after {} minutes".format(timeout_minutes), -1, timeout_minutes * 60

def get_text_from_response(response):
    """Extract text content from a Gemini response"""
    return response.text if hasattr(response, 'text') else ""

def get_timeout_improvement(code, train_sample, additional_info, execution_time, max_runtime):
    """Get improvements for timeout issues"""
    print(colored("Getting performance improvement suggestions...", "yellow"))
    try:
        # Format solution history for context
        solution_history_text = "\n\nPrevious solutions and their performance:\n"
        for idx, (prev_code, prev_progress) in enumerate(solution_history, 1):
            solution_history_text += f"\nSolution {idx} Progress:\n{json.dumps(prev_progress, indent=2)}\n"
            solution_history_text += f"\nSolution {idx} Code:\n```python\n{prev_code}\n```\n"

        contents = [
            types.Content(
                role="user",
                parts=[
                    types.Part.from_text(
                        text=f"""The machine learning code is taking too long to execute. It ran for {execution_time:.2f} seconds but needs to complete within {max_runtime * 60} seconds.
IMPORTANT: Optimize the code to run faster while maintaining accuracy. Consider:
1. Reducing model complexity without sacrificing too much accuracy
2. Using more efficient data processing
3. Optimizing hyperparameters for speed
4. Using faster algorithms if possible
5. Reducing cross-validation folds if necessary
6. DO NOT remove core functionality or important features
7. Keep GPU utilization if present
8. Maintain progress_report.json creation and all essential metrics
9. Always ensure you split the training set into train and test sets to validate the model

Here's the training data sample for context:
{train_sample}

Additional information about the task:
{additional_info}

{solution_history_text}

Current code:
```python
{code}

{OPTIONAL_INSTRUCTIONS}
```

Return ONLY the complete optimized code wrapped in ```python and ``` markers."""
                    ),
                ],
            ),
        ]
        
        response = client.models.generate_content(
            model=MODEL,
            contents=contents,
            config=types.GenerateContentConfig(
                response_mime_type="text/plain",
                max_output_tokens=MAX_TOKENS,
                system_instruction=SYSTEM_MESSAGE_TIMEOUT_IMPROVEMENT,
            ),
        )
        
        return response.text
    except Exception as e:
        print(colored(f"Error getting timeout improvements: {str(e)}", "red"))
        return None

def is_actual_error(stderr_text):
    """Check if stderr contains actual errors and not just warnings"""
    error_indicators = [
        "Traceback (most recent call last)",
        "Error:",
        "Exception:",
        "SyntaxError:",
        "NameError:",
        "TypeError:",
        "ValueError:",
        "ImportError:",
        "ModuleNotFoundError:",
        "IndexError:",
        "KeyError:",
        "AttributeError:",
        "IndentationError:"
    ]
    return any(indicator in stderr_text for indicator in error_indicators)

def get_gemini_fix(error_message, code, train_sample, additional_info):
    """Get code fix for the error"""
    print(colored("Getting fix for error...", "yellow"))
    try:
        # Get the last 5000 characters of the error message
        truncated_error = error_message[-5000:] if len(error_message) > 5000 else error_message
        if len(error_message) > 5000:
            truncated_error = "...(earlier error output truncated)...\n" + truncated_error
        
        # Format error history for context
        error_history_text = "\n\nPrevious errors encountered:\n"
        for idx, prev_error in enumerate(error_history, 1):
            error_history_text += f"\nError {idx}:\n{prev_error}\n"
            
        system_message = """You are an expert Python developer. Your task is to fix code errors.
IMPORTANT: You must follow these rules:
1. Return ONLY the complete fixed code
2. Always wrap the entire code in ```python and ``` markers
3. Do not include any explanations or comments outside the code block
4. Return the FULL corrected code, not just the fixed part
5. Make sure all imports and functions are included
6. If using ML frameworks, include GPU checks and utilize GPU when available
7. For ModuleNotFoundError or ImportError:
   - Add necessary pip install commands at the start of the script
   - Use subprocess.check_call to run pip install
   - Keep the rest of the code unchanged unless there are other errors
   - Example:
     ```python
     import subprocess
     try:
         import missing_module
     except ImportError:
         subprocess.check_call(['pip', 'install', 'missing_module'])
         import missing_module

     ```
8. make sure that the script is creating and saving a progress_report.json file with the accuracy and other useful information
9. Review the error history to avoid cyclical fixes and address root causes"""
            
        contents = [
            types.Content(
                role="user",
                parts=[
                    types.Part.from_text(
                        text=f"""Fix this Python code error. Return ONLY the complete fixed code wrapped in ```python and ``` markers.
If there are missing imports (ModuleNotFoundError or ImportError), add pip install commands at the start of the script but keep the main machine learning code logic unchanged.
Make sure to check for GPU availability and use it when possible for ML frameworks.

Here's the training data sample for context:
{train_sample}

Additional information about the task:
{additional_info}

Current Error:
{truncated_error}
{error_history_text}

Code:
{code}"""
                    ),
                ],
            ),
        ]
        
        response = client.models.generate_content(
            model=MODEL,
            contents=contents,
            config=types.GenerateContentConfig(
                response_mime_type="text/plain",
                max_output_tokens=MAX_TOKENS,
                system_instruction=SYSTEM_MESSAGE_FIX_ERROR,
            ),
        )
        
        fixed_code = response.text
        if not fixed_code or not fixed_code.strip().startswith("```python") or not fixed_code.strip().endswith("```"):
            print(colored("Warning: Response is not properly formatted with code blocks", "red"))
            return None
        return fixed_code
    except Exception as e:
        print(colored(f"Error getting fix: {str(e)}", "red"))
        return None

print(colored("Phase 2: Data Loading and Preparation", "cyan"))
# Read the first 10 rows of train.csv
train_data = pd.read_csv('train.csv', nrows=10)
train_sample = train_data.to_string(index=False)

# Read additional info
with open('additional_info.txt', 'r', encoding='utf-8') as file:
    additional_info = file.read()

print(colored("Phase 3: ML Solution Generation and Execution", "cyan"))
previous_code = ""
output_file = "execution_outputs.txt"

for i in range(ITERATIONS):
    print(colored(f"Iteration {i+1}/{ITERATIONS}", "yellow"))
    
    # Move old solution, progress report, and submission to older_solutions folder
    if i > 0:
        older_solutions_dir = "older_solutions"
        os.makedirs(older_solutions_dir, exist_ok=True)
        
        # Move files with iteration number
        files_to_move = {
            "solution.py": f"solution_{i}.py",
            "progress_report.json": f"progress_report_{i}.json",
            "submission.csv": f"submission_{i}.csv"
        }
        
        for src, dst in files_to_move.items():
            if os.path.exists(src):
                shutil.move(src, os.path.join(older_solutions_dir, dst))
                print(colored(f"Moved {src} to {older_solutions_dir}/{dst}", "green"))

    initial_prompt = f"""You are an expert ML scientist. Your task is to write excellent Python code to solve a difficult data challenge.
IMPORTANT: You must follow these rules:
1. Return ONLY the complete code solution
2. Always wrap the entire code in ```python and ``` markers
3. Do not include any explanations or text outside the code block
4. Include all necessary imports at the top of the code
5. Include descriptive comments within the code
6. Check for GPU availability and use it when possible
7. Print clear messages about whether GPU or CPU is being used
8. make sure that the script is creating and saving a progress_report.json file with the accuracy and other useful information as well as the submission file to a file called submission.csv
9. keep the scripts fast and efficient but very accurate
10. script should be able to run and complete in {MAX_RUNTIME_MINUTES} minutes
11. always ensure you split the training set into train and test sets to validate the model

Here's the information about the challenge:

{additional_info}

Here are the first 10 rows of the training data:

{train_sample}

The data files available are train.csv and test.csv. Pay specific attention to column names.

Based on this information, write complete Python code that:
1. Load and preprocess both train.csv and test.csv
2. Perform exploratory data analysis without using any plots
3. Engineer relevant features
4. Select and train an appropriate machine learning model using the training data
   - Check for GPU availability and use it if available
   - Fall back to CPU if GPU is not available
   - Print clear message about which device is being used
5. Evaluate the model's performance using cross-validation. clearly print the accuracy every so often
6. Make predictions on the test set and print the accuracy
- save the progress report and the final accuracy to a file called progress_report.json as well as the submission file to a file called submission.csv
7. Prepare the submission file in the required format
- do not use any plots
- you can print any analysis or information necessary as this will be passed back to you for the next iteration to improve the code
- make sure you use utf-8 encoding when writing to files
- do not use logging instead print the information necessary

{OPTIONAL_INSTRUCTIONS}

Return ONLY the complete code solution wrapped in ```python and ``` markers."""

    if i == 0:
        contents = [
            types.Content(
                role="user",
                parts=[types.Part.from_text(text=initial_prompt)],
            ),
        ]
    else:
        # Read the previous progress report
        prev_progress_path = os.path.join(older_solutions_dir, f"progress_report_{i}.json")
        with open(prev_progress_path, 'r', encoding='utf-8') as f:
            prev_progress = json.load(f)
            
        # Format solution history for context
        solution_history_text = "\n\nPrevious solutions and their performance:\n"
        for idx, (prev_code, prev_prog) in enumerate(solution_history, 1):
            solution_history_text += f"\nSolution {idx} Progress:\n{json.dumps(prev_prog, indent=2)}\n"
            solution_history_text += f"\nSolution {idx} Code:\n```python\n{prev_code}\n```\n"
            
        contents = [
            types.Content(
                role="user",
                parts=[types.Part.from_text(text=initial_prompt)],
            ),
            types.Content(
                role="model",
                parts=[types.Part.from_text(text=f"Here's the previous code I generated:\n\n```python\n{previous_code}\n```")],
            ),
            types.Content(
                role="user",
                parts=[
                    types.Part.from_text(
                        text=f"""Improve this code based on the previous results. Your goal is to improve the accuracy as much as you can. Return ONLY the complete improved code wrapped in ```python and ``` markers.
Make sure to maintain or add GPU support with proper availability checks and fallback to CPU.
make sure that the script is creating and saving a progress_report.json file with the accuracy and other useful information
keep the scripts fast and efficient but very accurate
11. always ensure you split the training set into train and test sets to validate the model

Here's the training data sample for context:
{train_sample}

Additional information about the task:
{additional_info}

{solution_history_text}

Previous model performance:
{json.dumps(prev_progress, indent=2)}

{OPTIONAL_INSTRUCTIONS}
"""
                    )
                ],
            ),
        ]

    response = None
    solution_content = None
    code_blocks = None
    api_call_successful = False

    for attempt in range(MAX_API_CALL_ATTEMPTS):
        print(colored(f"Attempting API call {attempt + 1}/{MAX_API_CALL_ATTEMPTS} for iteration {i+1}", "blue"))
        try:
            response = client.models.generate_content(
                model=MODEL,
                contents=contents,
                config=types.GenerateContentConfig(
                    response_mime_type="text/plain",
                    max_output_tokens=MAX_TOKENS,
                    system_instruction=SYSTEM_MESSAGE_SOLUTION_GENERATION,
                ),
            )

            print(colored("ML Scientist's Solution generated", "cyan"))
            solution_content = get_text_from_response(response) # Use the helper function

            if not solution_content:
                print(colored(f"Attempt {attempt + 1}: Error: Could not extract text content from response. Retrying...", "red"))
                if attempt < MAX_API_CALL_ATTEMPTS - 1:
                    continue # Go to next attempt
                else:
                    print(colored("All API attempts failed to get content. Breaking iteration.", "red"))
                    break # Breaks from the retry loop, will then hit the check outside

            code_blocks = re.findall(r'```python(.*?)```', solution_content, re.DOTALL)
            if not code_blocks:
                print(colored(f"Attempt {attempt + 1}: Error: No properly formatted Python code blocks found. Retrying...", "red"))
                print(colored("Raw response:", "yellow"))
                print(solution_content)
                if attempt < MAX_API_CALL_ATTEMPTS - 1:
                    continue # Go to next attempt
                else:
                    print(colored("All API attempts failed to get formatted code. Breaking iteration.", "red"))
                    break # Breaks from the retry loop
            
            api_call_successful = True # If we reach here, it's a success
            break # Break from the retry loop

        except Exception as e:
            print(colored(f"Attempt {attempt + 1}: Exception during API call or processing: {str(e)}", "red"))
            if attempt < MAX_API_CALL_ATTEMPTS - 1:
                print(colored("Retrying API call...", "yellow"))
                continue
            else:
                print(colored("All API attempts failed due to exceptions. Breaking iteration.", "red"))
                break # Breaks from the retry loop

    if not api_call_successful: # If all attempts failed
        print(colored("Failed to get a valid solution from API after multiple attempts. Moving to next iteration or ending.", "red"))
        break # This break exits the main 'for i in range(ITERATIONS):' loop

    current_code = '\\n'.join(code_blocks).strip()
    has_error = True
    
    while has_error:  # Keep trying until no errors
        with open('solution.py', 'w', encoding='utf-8') as file:
            file.write(current_code)
        print(colored("Solution saved to solution.py", "green"))

        # Execute the generated code with timeout
        print(colored("Executing generated code:", "cyan"))
        stdout, stderr, returncode, execution_time = run_with_timeout(['python', 'solution.py'], MAX_RUNTIME_MINUTES)
        
        print(colored("Execution output:", "green"))
        print(stdout)
        
        if returncode == -1:  # Timeout occurred
            print(colored(f"Execution timed out after {MAX_RUNTIME_MINUTES} minutes", "red"))
            
            # Get optimization suggestions for timeout
            optimized_code = get_timeout_improvement(
                current_code, 
                train_sample, 
                additional_info,
                execution_time,
                MAX_RUNTIME_MINUTES
            )
            
            if optimized_code and "```python" in optimized_code:
                code_blocks = re.findall(r'```python(.*?)```', optimized_code, re.DOTALL)
                if code_blocks:
                    current_code = code_blocks[0].strip()
                    print(colored("Applied performance optimizations. Retrying...", "green"))
                    continue
                else:
                    print(colored("Error: Optimization response was not properly formatted", "red"))
                    break
            else:
                print(colored("Could not get optimization suggestions. Moving to next iteration.", "red"))
                break
        
        if stderr:
            # Check if stderr contains actual errors or just warnings
            if is_actual_error(stderr):
                print(colored("Execution errors:", "red"))
                print(stderr)
                
                # Add current error to history
                error_history.append(stderr)
                
                # Try to get fix for the error
                fixed_code = get_gemini_fix(stderr, current_code, train_sample, additional_info)
                
                if fixed_code and "```python" in fixed_code:
                    # Extract code from markdown if present
                    code_blocks = re.findall(r'```python(.*?)```', fixed_code, re.DOTALL)
                    if code_blocks:
                        current_code = code_blocks[0].strip()
                        print(colored("Applied code fix. Retrying...", "green"))
                    else:
                        print(colored("Error: Fix was not properly formatted", "red"))
                        break
                else:
                    print(colored("Could not get fix for error. Moving to next iteration.", "red"))
                    break
            else:
                print(colored("Warnings (non-critical):", "yellow"))
                print(stderr)
                has_error = False  # Continue if only warnings
        else:
            has_error = False  # No errors or warnings
        
        # Save execution output to file
        with open(output_file, 'a', encoding='utf-8') as f:
            f.write(f"Iteration {i+1}/{ITERATIONS}\n")
            f.write("Execution output:\n")
            f.write(stdout)
            f.write("\nExecution errors:\n")
            f.write(stderr)
            if returncode == -1:
                f.write(f"\nExecution timed out after {MAX_RUNTIME_MINUTES} minutes")
            f.write("\n" + "="*50 + "\n\n")
        
        # If execution was successful, add to solution history
        if not has_error and returncode != -1:
            try:
                with open('progress_report.json', 'r', encoding='utf-8') as f:
                    current_progress = json.load(f)
                solution_history.append((current_code, current_progress))
                print(colored("Added current solution to history", "green"))
            except Exception as e:
                print(colored(f"Warning: Could not add solution to history: {str(e)}", "yellow"))
    
    # Update previous_code for the next iteration
    previous_code = current_code

print(colored("ML Scientist process completed.", "cyan"))
print(f"All execution outputs have been saved to {output_file}")
print(colored("All solutions, progress reports, and submissions have been saved with iteration numbers in the older_solutions directory.", "green"))
