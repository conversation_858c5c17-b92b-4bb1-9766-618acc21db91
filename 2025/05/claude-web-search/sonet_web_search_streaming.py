import json
import anthropic
import os
from termcolor import colored
import warnings

# Major variables/constants at the top
MODEL_NAME = "claude-3-7-sonnet-latest"
MAX_TOKENS = 1024
PROMPT = "when was claude shannon born? how much is nvidia at now?"
TOOLS = [{
    "type": "web_search_20250305",
    "name": "web_search",
    "max_uses": 2
}]

# Initialize client at the top, but handle missing API key
try:
    API_KEY = os.getenv("ANTHROPIC_API_KEY")
    if not API_KEY:
        raise ValueError("ANTHROPIC_API_KEY environment variable not set.")
    CLIENT = anthropic.Anthropic(api_key=API_KEY)
    print(colored("Anthropic client initialized successfully.", "green"))
except Exception as e:
    print(colored(f"Failed to initialize Anthropic client: {e}", "red"))
    CLIENT = None

warnings.filterwarnings("ignore", category=UserWarning)

def call_anthropic(client, prompt):
    try:
        print(colored("Preparing to call Anthropic API...", "cyan"))
        response = client.messages.create(
            model=MODEL_NAME,
            max_tokens=MAX_TOKENS,
            messages=[
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            tools=TOOLS,
            stream=True
        )
        print(colored("Anthropic API call initiated, stream object received.", "green"))
        return response
    except Exception as e:
        print(colored(f"Error during Anthropic API call initiation: {e}", "red"))
        raise

def skip_encrypted_content(obj):
    SKIP_KEYS = {"encrypted_content", "encrypted_index"}
    if isinstance(obj, dict):
        new_dict = {}
        for k, v in obj.items():
            if k in SKIP_KEYS:
                # print(colored(f"Removing '{k}' key from dict.", "yellow"))
                continue
            new_dict[k] = skip_encrypted_content(v)
        return new_dict
    elif isinstance(obj, list):
        return [skip_encrypted_content(item) for item in obj]
    else:
        return obj

def save_filtered_response(filtered_response, filename="filtered_response.json"):
    try:
        print(colored(f"Saving filtered response to {filename}...", "cyan"))
        with open(filename, "w", encoding="utf-8") as f:
            json.dump(filtered_response, f, indent=4, ensure_ascii=False)
        print(colored(f"Filtered response saved to {filename}.", "green"))
    except Exception as e:
        print(colored(f"Error saving filtered response: {e}", "red"))

def print_pretty_assistant_responses(content_list):
    """
    Print assistant's text responses in green and citations in yellow (with clickable URLs if possible).
    Only prints items of type 'text'.
    """
    for item in content_list:
        if item.get("type") == "text" and item.get("text"):
            # Print the assistant's response in green
            print(colored(item["text"], "green"))
            # Print citations if present
            citations = item.get("citations")
            if citations:
                for citation in citations:
                    url = citation.get("url")
                    title = citation.get("title") or url
                    if url:
                        # ANSI escape sequence for creating a clickable link in the terminal
                        clickable_link = f"\033]8;;{url}\033\\{title}\033]8;;\033\\"
                        print(colored(f"  [Citation] {clickable_link}", "yellow"))
                    else:
                        print(colored(f"[Citation] {title}", "yellow"))

def main():
    if CLIENT is None:
        print(colored("Anthropic client is not initialized. Exiting.", "red"))
        return

    try:
        print(colored(f"Calling Anthropic API with prompt: '{PROMPT}'", "cyan"))
        stream = call_anthropic(CLIENT, PROMPT)
    except Exception as e:
        print(colored(f"Failed to initiate API call: {e}", "red"))
        return

    active_block_types = {}
    current_tool_inputs = {}
    last_printed_was_text_delta = False

    try:
        for event in stream:
            # For debugging event structure:
            # print(colored(f"Raw Event: type='{event.type}' data='{event}'", "grey"))

            if event.type == "message_start":
                print(colored("Message stream started.", "blue"))
                last_printed_was_text_delta = False

            elif event.type == "content_block_start":
                if last_printed_was_text_delta:
                    print() 
                last_printed_was_text_delta = False
                
                active_block_types[event.index] = event.content_block.type
                if event.content_block.type == "text":
                    pass # Text is printed by text_delta
                elif event.content_block.type == "server_tool_use":
                    print(colored(f"Model wants to use tool: {event.content_block.name} (ID: {event.content_block.id}, Index: {event.index})", "blue"))
                    current_tool_inputs[event.index] = "" 
                elif event.content_block.type == "web_search_tool_result":
                    print(colored(f"Received search results (for tool use ID: {event.content_block.tool_use_id}, Index: {event.index}):", "blue"))
                    if hasattr(event.content_block, 'content'):
                        search_content = event.content_block.content # Use a clearer variable name
                        if isinstance(search_content, list):
                            for item_dict in search_content: # Iterate assuming items are dictionaries
                                # Check if the item is a dictionary and has the correct type
                                if isinstance(item_dict, dict) and item_dict.get("type") == "web_search_result":
                                    url = item_dict.get("url")
                                    # Only proceed if a URL is actually found in the dictionary
                                    if url:
                                        title = item_dict.get("title")
                                        # Use the title for the link text; if no title, use the URL itself
                                        display_text = title if title else url
                                        
                                        # ANSI escape sequence for creating a clickable link in the terminal
                                        clickable_link = f"\033]8;;{url}\033\\{title}\033]8;;\033\\"
                                        print(colored(f"  [Citation] {clickable_link}", "yellow"))
                                # If item_dict is not a dict, or not type 'web_search_result', or has no URL, it's skipped.
                        # If search_content is not a list (e.g., a string or None), nothing will be printed,
                        # adhering to the request to only print specific links.
                    else:
                        # This message is useful if the 'content' attribute itself is missing from the content_block
                        print(colored("  Search result block has no 'content' attribute.", "magenta"))

            elif event.type == "content_block_delta":
                block_type = active_block_types.get(event.index)
                if block_type == "text" and hasattr(event.delta, 'type') and event.delta.type == "text_delta":
                    print(colored(event.delta.text, "green"), end="", flush=True)
                    last_printed_was_text_delta = True
                elif block_type == "server_tool_use" and hasattr(event.delta, 'type') and event.delta.type == "input_json_delta":
                    if event.index in current_tool_inputs:
                        current_tool_inputs[event.index] += event.delta.partial_json
                    else:
                        print(colored(f"Warning: Received input_json_delta for unknown tool index {event.index}", "orange"))
            
            elif event.type == "content_block_stop":
                block_type = active_block_types.pop(event.index, None)
                if block_type == "text":
                    if last_printed_was_text_delta:
                        print()
                    last_printed_was_text_delta = False
                elif block_type == "server_tool_use":
                    if event.index in current_tool_inputs:
                        tool_input_str = current_tool_inputs.pop(event.index)
                        if tool_input_str:
                            try:
                                parsed_input = json.loads(tool_input_str)
                                query = parsed_input.get("query", "N/A")
                                print(colored(f"\\nTool executed with query: '{query}'", "cyan"))
                            except json.JSONDecodeError:
                                print(colored(f"\\nTool executed. (Could not parse input JSON: {tool_input_str[:100]}...)", "red"))
                        else:
                            print(colored(f"\\nTool (index {event.index}) execution finished (no input JSON).", "blue"))
                    else:
                         print(colored(f"\\nTool (index {event.index}, type: {block_type}) execution finished.", "blue"))

            elif event.type == "message_delta":
                # Useful for stop_reason and usage if needed.
                # print(colored(f"Message delta: Stop Reason: {event.delta.stop_reason}, Usage: {event.usage}", "grey"))
                pass

            elif event.type == "message_stop":
                if last_printed_was_text_delta:
                    print()
                print(colored("Message stream ended.", "blue"))
                # Anthropic's message_stop event itself doesn't usually carry final usage directly in Python SDK,
                # it's often part of the message_delta just before stop, or on the final message object if not streaming.
                # For streaming, check the last message_delta if final usage is critical.

            elif event.type == "ping":
                # print(colored("Ping event received.", "grey"))
                pass

            elif event.type == "error":
                print(colored(f"\\nERROR in stream: Type: {getattr(event.error, 'type', 'Unknown')}, Message: {getattr(event.error, 'message', 'No message')}", "red"))
                break 

        if last_printed_was_text_delta: # Ensure newline if loop ends after partial text output
            print()
        print(colored("\\nStream processing finished.", "green"))

    except anthropic.APIConnectionError as e:
        print(colored(f"\\nConnection Error: {e.__cause__ if e.__cause__ else e}", "red"))
    except anthropic.RateLimitError as e:
        print(colored(f"\\nRate Limit Exceeded: {e}", "red"))
    except anthropic.APIStatusError as e:
        print(colored(f"\\nAPI Status Error {e.status_code}: {e.response}", "red"))
    except KeyboardInterrupt:
        if last_printed_was_text_delta: # Ensure newline if interrupted
            print()
        print(colored("\\nStream processing interrupted by user.", "yellow"))
    except Exception as e:
        if last_printed_was_text_delta: # Ensure newline on other errors
            print()
        print(colored(f"\\nAn unexpected error occurred: {e}", "red"))
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
