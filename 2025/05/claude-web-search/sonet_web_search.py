import json
import anthropic
import os
from termcolor import colored
import warnings

# Major variables/constants at the top
MODEL_NAME = "claude-3-7-sonnet-latest"
MAX_TOKENS = 1024
PROMPT = "when was claude shannon born? how much is nvidia at now?"
TOOLS = [{
    "type": "web_search_20250305",
    "name": "web_search",
    "max_uses": 2
}]

# Initialize client at the top, but handle missing API key
try:
    API_KEY = os.getenv("ANTHROPIC_API_KEY")
    if not API_KEY:
        raise ValueError("ANTHROPIC_API_KEY environment variable not set.")
    CLIENT = anthropic.Anthropic(api_key=API_KEY)
    print(colored("Anthropic client initialized successfully.", "green"))
except Exception as e:
    print(colored(f"Failed to initialize Anthropic client: {e}", "red"))
    CLIENT = None

warnings.filterwarnings("ignore", category=UserWarning)

def call_anthropic(client, prompt):
    try:
        print(colored("Preparing to call Anthropic API...", "cyan"))
        response = client.messages.create(
            model=MODEL_NAME,
            max_tokens=MAX_TOKENS,
            messages=[
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            tools=TOOLS
        )
        print(colored("Anthropic API call completed.", "green"))
        return response
    except Exception as e:
        print(colored(f"Error during Anthropic API call: {e}", "red"))
        raise

def skip_encrypted_content(obj):
    SKIP_KEYS = {"encrypted_content", "encrypted_index"}
    if isinstance(obj, dict):
        new_dict = {}
        for k, v in obj.items():
            if k in SKIP_KEYS:
                # print(colored(f"Removing '{k}' key from dict.", "yellow"))
                continue
            new_dict[k] = skip_encrypted_content(v)
        return new_dict
    elif isinstance(obj, list):
        return [skip_encrypted_content(item) for item in obj]
    else:
        return obj

def save_filtered_response(filtered_response, filename="filtered_response.json"):
    try:
        print(colored(f"Saving filtered response to {filename}...", "cyan"))
        with open(filename, "w", encoding="utf-8") as f:
            json.dump(filtered_response, f, indent=4, ensure_ascii=False)
        print(colored(f"Filtered response saved to {filename}.", "green"))
    except Exception as e:
        print(colored(f"Error saving filtered response: {e}", "red"))

def print_pretty_assistant_responses(content_list):
    """
    Print assistant's text responses in green and citations in yellow (with clickable URLs if possible).
    Only prints items of type 'text'.
    """
    for item in content_list:
        if item.get("type") == "text" and item.get("text"):
            # Print the assistant's response in green
            print(colored(item["text"], "green"))
            # Print citations if present
            citations = item.get("citations")
            if citations:
                for citation in citations:
                    url = citation.get("url")
                    title = citation.get("title") or url
                    if url:
                        # ANSI escape for clickable link (supported in some terminals)
                        clickable = f"\033]8;;{url}\033\\{title}\033]8;;\033\\"
                        print(colored(f"[Citation] {clickable}", "yellow"))
                    else:
                        print(colored(f"[Citation] {title}", "yellow"))

def main():
    if CLIENT is None:
        print(colored("Anthropic client is not initialized. Exiting.", "red"))
        return

    try:
        print(colored("Calling Anthropic API...", "cyan"))
        response = call_anthropic(CLIENT, PROMPT)
        print(colored("API call successful. Processing response...", "green"))
    except Exception as e:
        print(colored(f"Error during API call: {e}", "red"))
        return

    # Convert response to dict if needed
    try:
        print(colored("Converting response to serializable dict...", "cyan"))
        # Try to use .model_dump() if available (pydantic models), else fallback to __dict__ or json
        if hasattr(response, "model_dump"):
            response_dict = response.model_dump()
        elif hasattr(response, "__dict__"):
            response_dict = json.loads(json.dumps(response.__dict__, default=str))
        else:
            response_dict = json.loads(json.dumps(response, default=str))
        print(colored("Response converted to dict successfully.", "green"))
    except Exception as e:
        print(colored(f"Error parsing response: {e}", "red"))
        return

    # Process and print, skipping any result with 'encrypted_content'
    try:
        print(colored("Filtering out encrypted_content from response...", "cyan"))
        filtered_response = skip_encrypted_content(response_dict)
        # print(colored("Filtered response (skipping encrypted_content):", "cyan"))
        # print(json.dumps(filtered_response, indent=4, ensure_ascii=False))
        # Pretty print assistant's responses
        if "content" in filtered_response:
            print(colored("\nAssistant's responses:", "magenta"))
            print_pretty_assistant_responses(filtered_response["content"])
    except Exception as e:
        print(colored(f"Error filtering response: {e}", "red"))
        return

    save_filtered_response(filtered_response)

if __name__ == "__main__":
    main()
