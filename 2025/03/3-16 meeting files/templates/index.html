<!DOCTYPE html>
<html lang="en" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF Analyzer</title>
    <link href="https://cdn.jsdelivr.net/npm/daisyui@3.9.4/dist/full.css" rel="stylesheet" type="text/css" />
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js"></script>
    <link rel="stylesheet" href="{{ url_for('static', path='/css/styles.css') }}">
</head>
<body class="min-h-screen bg-base-300">
    <div class="drawer lg:drawer-open">
        <input id="history-drawer" type="checkbox" class="drawer-toggle" />
        
        <div class="drawer-content flex flex-col items-center justify-center p-4">
            <!-- Page content here -->
            <div class="container mx-auto px-4 py-8">
                <div class="flex lg:hidden justify-between items-center mb-4">
                    <label for="history-drawer" class="btn btn-primary drawer-button lg:hidden">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
                        </svg>
                        History
                    </label>
                </div>
                
                <div class="card bg-base-100 shadow-xl max-w-3xl mx-auto">
                    <div class="card-body">
                        <h1 class="text-3xl font-bold text-center text-primary mb-6">PDF Analyzer</h1>
                        <p class="text-center mb-8">Upload a PDF or provide a URL to analyze with Claude 3.7 Sonnet</p>
                        
                        <form action="/analyze" method="post" enctype="multipart/form-data" class="space-y-6">
                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text">Upload PDF</span>
                                </label>
                                <input type="file" name="pdf_file" accept="application/pdf" class="file-input file-input-bordered file-input-primary w-full" />
                            </div>
                            
                            <div class="divider">OR</div>
                            
                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text">PDF URL</span>
                                </label>
                                <input type="url" name="pdf_url" placeholder="https://example.com/document.pdf" class="input input-bordered w-full" />
                            </div>
                            
                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text">Question (optional)</span>
                                </label>
                                <input type="text" name="question" placeholder="What are the key findings in this document?" class="input input-bordered w-full" value="What are the key findings in this document?" />
                            </div>
                            
                            <div class="form-control mt-6">
                                <button type="submit" class="btn btn-primary">Analyze PDF</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="drawer-side">
            <label for="history-drawer" aria-label="close sidebar" class="drawer-overlay"></label>
            <div class="menu p-4 w-80 min-h-full bg-base-200 text-base-content">
                <!-- Sidebar content here -->
                <h2 class="text-xl font-bold mb-4 text-primary">Analysis History</h2>
                <div id="history-list" class="space-y-2">
                    <p id="no-history" class="text-center italic">No previous analyses found</p>
                </div>
                <div class="mt-4">
                    <button id="clear-history" class="btn btn-sm btn-error w-full">Clear History</button>
                </div>
            </div>
        </div>
    </div>
    
    <script src="{{ url_for('static', path='/js/main.js') }}"></script>
    <script src="{{ url_for('static', path='/js/history.js') }}"></script>
</body>
</html> 