<!DOCTYPE html>
<html lang="en" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF Analysis Results</title>
    <link href="https://cdn.jsdelivr.net/npm/daisyui@3.9.4/dist/full.css" rel="stylesheet" type="text/css" />
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js"></script>
    <link rel="stylesheet" href="{{ url_for('static', path='/css/styles.css') }}">
</head>
<body class="min-h-screen bg-base-300">
    <div class="drawer lg:drawer-open">
        <input id="history-drawer" type="checkbox" class="drawer-toggle" />
        
        <div class="drawer-content flex flex-col items-center justify-center p-4">
            <!-- Page content here -->
            <div class="container mx-auto px-4 py-8">
                <div class="flex lg:hidden justify-between items-center mb-4">
                    <label for="history-drawer" class="btn btn-primary drawer-button lg:hidden">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
                        </svg>
                        History
                    </label>
                </div>
                
                <div class="card bg-base-100 shadow-xl max-w-4xl mx-auto">
                    <div class="card-body">
                        <h1 class="text-3xl font-bold text-center text-primary mb-6">PDF in Plain English</h1>
                        
                        <div class="mb-4">
                            <div class="badge badge-primary mb-2">Source</div>
                            <p class="text-sm">{{ source }}</p>
                        </div>
                        
                        <div class="mb-4">
                            <div class="badge badge-secondary mb-2">Question</div>
                            <p class="text-sm">{{ question }}</p>
                        </div>
                        
                        <div class="divider"></div>
                        
                        <div class="mb-8">
                            <h2 class="text-xl font-bold mb-4 text-accent">Simple Summary</h2>
                            <div class="prose max-w-none">
                                {{ summary | safe }}
                            </div>
                        </div>
                        
                        <div class="mb-8">
                            <h2 class="text-xl font-bold mb-4 text-secondary">Conversation-Ready Insights</h2>
                            {% if key_info_points %}
                                <div class="space-y-4">
                                    {% for point in key_info_points %}
                                        <div class="card bg-base-200 shadow-sm hover:shadow-md transition-shadow duration-300">
                                            <div class="card-body p-4">
                                                <p class="font-medium text-accent text-lg">{{ point.info }}</p>
                                                {% if point.importance %}
                                                    <p class="text-sm opacity-90 mt-2">
                                                        <span class="font-bold">Why it's interesting:</span> {{ point.importance }}
                                                    </p>
                                                {% endif %}
                                            </div>
                                        </div>
                                    {% endfor %}
                                </div>
                            {% else %}
                                <p class="italic text-center">No conversation insights were identified.</p>
                            {% endif %}
                        </div>
                        
                        <div class="flex justify-center mt-6">
                            <a href="/" class="btn btn-primary">Analyze Another PDF</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="drawer-side">
            <label for="history-drawer" aria-label="close sidebar" class="drawer-overlay"></label>
            <div class="menu p-4 w-80 min-h-full bg-base-200 text-base-content">
                <!-- Sidebar content here -->
                <h2 class="text-xl font-bold mb-4 text-primary">Analysis History</h2>
                <div id="history-list" class="space-y-2">
                    <p id="no-history" class="text-center italic">No previous analyses found</p>
                </div>
                <div class="mt-4">
                    <button id="clear-history" class="btn btn-sm btn-error w-full">Clear History</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Hidden data for saving to history -->
    <div id="analysis-data" 
         data-id="{{ analysis_id }}" 
         data-source="{{ source }}" 
         data-timestamp="{{ timestamp }}"
         data-question="{{ question }}"
         style="display: none;">
    </div>
    
    <script src="{{ url_for('static', path='/js/main.js') }}"></script>
    <script src="{{ url_for('static', path='/js/history.js') }}"></script>
    <script>
        // Save current analysis to history
        document.addEventListener('DOMContentLoaded', function() {
            saveCurrentAnalysisToHistory();
        });
    </script>
</body>
</html> 