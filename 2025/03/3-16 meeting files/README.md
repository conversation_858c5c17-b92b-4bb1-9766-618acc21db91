# PDF to Conversation Starter

A web application that analyzes PDF documents and extracts conversation-worthy information points to help users engage in meaningful discussions about the content.

## Features

- Upload PDF files or provide PDF URLs for analysis
- Ask specific questions about the document content
- Get simplified summaries in everyday language
- Extract 5-7 key conversation starters from each document
- View why each information point is interesting in everyday contexts
- Save analysis history for future reference
- Dark mode UI with responsive design

## Technology Stack

- **Backend**: FastAPI, Python
- **Frontend**: HTML, JavaScript, Tailwind CSS, DaisyUI
- **AI**: Claude 3.7 Sonnet (Anthropic API)
- **PDF Processing**: Direct document analysis via Anthropic API

## Installation

1. Clone the repository
2. Install the required dependencies:
   ```
   pip install -r requirements.txt
   ```
3. Set up your environment variable:
   ```
   export ANTHROPIC_API_KEY=your_api_key_here
   ```

## Usage

1. Start the application:
   ```
   uvicorn main:app --host 127.0.0.1 --port 8000 --reload
   ```
2. Open your browser and navigate to `http://127.0.0.1:8000`
3. Upload a PDF file or provide a PDF URL
4. Optionally customize the question prompt
5. View the simplified summary and conversation starters
6. Use the extracted information points in your conversations

## How It Works

1. The application accepts PDF documents either via file upload or URL
2. The document is sent to the Anthropic API along with a custom prompt
3. Claude analyzes the document and generates:
   - A simplified summary in everyday language
   - 5-7 key information points formatted as conversation starters
   - Context for why each point is interesting in everyday conversations
4. Results are displayed in a user-friendly interface
5. Analysis history is saved in the browser's local storage for future reference

## Requirements

- Python 3.7+
- Anthropic API key
- Internet connection for API calls

## Error Handling

The application includes comprehensive error handling with:
- Descriptive error messages
- Color-coded terminal output for debugging
- Graceful fallbacks for API failures
- User-friendly error displays

## License

[Your License Information] 