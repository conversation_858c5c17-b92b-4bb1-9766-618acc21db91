<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Brick Shooter Game - PixiJS Version</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pixi.js/7.3.2/pixi.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/matter-js/0.18.0/matter.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            background-color: #1a1a2e;
            color: #e6e6e6;
            font-family: Arial, sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
        }
        
        #game-container {
            position: relative;
            width: 95vw;
            height: 600px;
            border: 2px solid #4a4a82;
            border-radius: 8px;
            overflow: hidden;
        }
        
        #ui-container {
            position: absolute;
            top: 10px;
            left: 10px;
            z-index: 10;
            background-color: rgba(26, 26, 46, 0.7);
            padding: 10px;
            border-radius: 5px;
        }
        
        button {
            background-color: #4a4a82;
            color: white;
            border: none;
            padding: 8px 16px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        button:hover {
            background-color: #6a6aa8;
        }
        
        #loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            display: none;
            z-index: 20;
        }
        
        .loading-circle {
            width: 50px;
            height: 50px;
            border: 5px solid rgba(74, 74, 130, 0.3);
            border-top: 5px solid #4a4a82;
            border-radius: 50%;
        }
        
        #game-message {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: rgba(26, 26, 46, 0.9);
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            display: none;
            z-index: 15;
        }
        
        #instructions {
            margin-top: 20px;
            max-width: 95vw;
            background-color: rgba(26, 26, 46, 0.7);
            padding: 15px;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div id="game-container">
        <div id="ui-container">
            <button id="switch-phase-btn">Start Shooting</button>
            <button id="reset-btn">Reset Game</button>
            <div id="phase-info">Building Phase</div>
        </div>
        <div id="loading">
            <div class="loading-circle"></div>
        </div>
        <div id="game-message"></div>
    </div>
    
    <div id="instructions">
        <h2>How to Play</h2>
        <h3>Building Phase:</h3>
        <ul>
            <li>Click on the game area to create a new brick</li>
            <li>Drag bricks to position them</li>
            <li>Build a tower that can withstand shots</li>
        </ul>
        <h3>Shooting Phase:</h3>
        <ul>
            <li>Click and drag on the cannon to aim</li>
            <li>Release to fire</li>
            <li>Try to knock down the tower</li>
        </ul>
    </div>

    <script>
        // CONSTANTS AND CONFIGURATION
        const CANVAS_WIDTH = window.innerWidth * 0.95;
        const CANVAS_HEIGHT = 600;
        const GROUND_HEIGHT = 50;
        const BRICK_SIZE = 40;
        const CANNON_X = 100;
        const CANNON_Y = CANVAS_HEIGHT - GROUND_HEIGHT - 30;
        const CANNON_WIDTH = 60;
        const CANNON_HEIGHT = 30;
        const CANNON_BALL_RADIUS = 15;
        const CANNON_BALL_DENSITY = 0.02;
        const BRICK_DENSITY = 0.001;
        const MAX_POWER = 2.4; // Doubled from 1.2 to 2.4 (2x power)
        const BRICK_COLORS = ['#f94144', '#f3722c', '#f8961e', '#f9c74f', '#90be6d', '#43aa8b', '#577590'];
        
        // Game state
        let gamePhase = 'building'; // 'building' or 'shooting'
        let engine, world, app;
        let ground, cannon, cannonBall;
        let bricks = [];
        let sprites = {};
        let isDragging = false;
        let selectedBody = null;
        let mouseConstraint = null;
        let cannonAngle = -Math.PI / 4;
        let cannonPower = 0.1;
        let dragStart = { x: 0, y: 0 };
        let isAiming = false;
        let shotsFired = 0;
        let gameOver = false;
        
        // Initialize the game
        function init() {
            try {
                console.log('%cInitializing game...', 'color: #43aa8b');
                
                // Setup Matter.js
                engine = Matter.Engine.create();
                world = engine.world;
                
                // Setup PixiJS
                app = new PIXI.Application({
                    width: CANVAS_WIDTH,
                    height: CANVAS_HEIGHT,
                    backgroundColor: 0x16213e,
                    resolution: window.devicePixelRatio || 1,
                    antialias: true
                });
                
                // Add the PixiJS view to the DOM
                document.getElementById('game-container').appendChild(app.view);
                
                // Create ground
                ground = Matter.Bodies.rectangle(
                    CANVAS_WIDTH / 2,
                    CANVAS_HEIGHT - GROUND_HEIGHT / 2,
                    CANVAS_WIDTH,
                    GROUND_HEIGHT,
                    { isStatic: true }
                );
                
                // Create left wall to place cannon against
                const leftWall = Matter.Bodies.rectangle(
                    10,
                    CANVAS_HEIGHT / 2,
                    20,
                    CANVAS_HEIGHT,
                    { isStatic: true }
                );
                
                // Create right wall to prevent bricks from going off-screen
                const rightWall = Matter.Bodies.rectangle(
                    CANVAS_WIDTH - 10,
                    CANVAS_HEIGHT / 2,
                    20,
                    CANVAS_HEIGHT,
                    { isStatic: true }
                );
                
                // Add bodies to world
                Matter.Composite.add(world, [ground, leftWall, rightWall]);
                
                // Create PixiJS sprites for the static elements
                createStaticSprites();
                
                // Setup event listeners
                setupEventListeners();
                
                // Start the engine
                Matter.Runner.run(engine);
                
                // Start game loop
                app.ticker.add(gameLoop);
                
                console.log('%cGame initialized successfully!', 'color: #43aa8b');
            } catch (error) {
                console.error('%cError initializing game:', 'color: #f94144', error);
                showMessage('Error initializing game. Please refresh the page.');
            }
        }
        
        // Create static sprites (ground, walls)
        function createStaticSprites() {
            try {
                // Create ground sprite
                const groundSprite = new PIXI.Graphics();
                groundSprite.beginFill(0x4a4a82);
                groundSprite.drawRect(
                    ground.bounds.min.x,
                    ground.bounds.min.y,
                    ground.bounds.max.x - ground.bounds.min.x,
                    ground.bounds.max.y - ground.bounds.min.y
                );
                groundSprite.endFill();
                app.stage.addChild(groundSprite);
                sprites.ground = groundSprite;
                
                // Create left wall sprite
                const leftWallSprite = new PIXI.Graphics();
                leftWallSprite.beginFill(0x4a4a82);
                leftWallSprite.drawRect(0, 0, 20, CANVAS_HEIGHT);
                leftWallSprite.endFill();
                leftWallSprite.position.set(0, 0);
                app.stage.addChild(leftWallSprite);
                
                // Create right wall sprite
                const rightWallSprite = new PIXI.Graphics();
                rightWallSprite.beginFill(0x4a4a82);
                rightWallSprite.drawRect(0, 0, 20, CANVAS_HEIGHT);
                rightWallSprite.endFill();
                rightWallSprite.position.set(CANVAS_WIDTH - 20, 0);
                app.stage.addChild(rightWallSprite);
                
                // Create container for bricks
                sprites.brickContainer = new PIXI.Container();
                app.stage.addChild(sprites.brickContainer);
                
                // Create container for cannon and cannonball
                sprites.cannonContainer = new PIXI.Container();
                app.stage.addChild(sprites.cannonContainer);
                
                // Create container for UI elements (drag lines, aim lines)
                sprites.uiContainer = new PIXI.Container();
                app.stage.addChild(sprites.uiContainer);
            } catch (error) {
                console.error('%cError creating static sprites:', 'color: #f94144', error);
            }
        }
        
        // Setup event listeners
        function setupEventListeners() {
            try {
                console.log('%cSetting up event listeners...', 'color: #43aa8b');
                
                // PixiJS event listeners
                app.view.addEventListener('mousedown', handleMouseDown);
                app.view.addEventListener('mousemove', handleMouseMove);
                app.view.addEventListener('mouseup', handleMouseUp);
                
                // Button event listeners
                document.getElementById('switch-phase-btn').addEventListener('click', switchPhase);
                document.getElementById('reset-btn').addEventListener('click', resetGame);
                
                // Window resize listener
                window.addEventListener('resize', handleResize);
                
                console.log('%cEvent listeners set up successfully!', 'color: #43aa8b');
            } catch (error) {
                console.error('%cError setting up event listeners:', 'color: #f94144', error);
            }
        }
        
        // Handle mouse down event
        function handleMouseDown(event) {
            try {
                const rect = app.view.getBoundingClientRect();
                const mouseX = event.clientX - rect.left;
                const mouseY = event.clientY - rect.top;
                
                if (gamePhase === 'building') {
                    // Check if clicked on existing brick
                    const bodies = Matter.Composite.allBodies(world);
                    for (let i = 0; i < bodies.length; i++) {
                        const body = bodies[i];
                        if (body !== ground && !body.isStatic && Matter.Vertices.contains(body.vertices, { x: mouseX, y: mouseY })) {
                            selectedBody = body;
                            isDragging = true;
                            
                            // Create a constraint to drag the body
                            mouseConstraint = Matter.Constraint.create({
                                pointA: { x: mouseX, y: mouseY },
                                bodyB: selectedBody,
                                stiffness: 0.2
                            });
                            Matter.Composite.add(world, mouseConstraint);
                            return;
                        }
                    }
                    
                    // If not clicked on existing brick, create a new one
                    createBrick(mouseX, mouseY);
                } else if (gamePhase === 'shooting') {
                    // Check if clicked on cannon
                    const cannonPos = getCannonPosition();
                    const distance = Math.sqrt(
                        Math.pow(mouseX - cannonPos.x, 2) + 
                        Math.pow(mouseY - cannonPos.y, 2)
                    );
                    
                    if (distance < CANNON_WIDTH) {
                        isAiming = true;
                        dragStart = { x: mouseX, y: mouseY };
                    }
                }
            } catch (error) {
                console.error('%cError handling mouse down:', 'color: #f94144', error);
            }
        }
        
        // Handle mouse move event
        function handleMouseMove(event) {
            try {
                const rect = app.view.getBoundingClientRect();
                const mouseX = event.clientX - rect.left;
                const mouseY = event.clientY - rect.top;
                
                if (gamePhase === 'building' && isDragging && mouseConstraint) {
                    // Update the constraint point
                    mouseConstraint.pointA = { x: mouseX, y: mouseY };
                } else if (gamePhase === 'shooting' && isAiming) {
                    // Calculate angle and power based on drag
                    const dx = mouseX - dragStart.x;
                    const dy = mouseY - dragStart.y;
                    
                    // Update cannon angle
                    cannonAngle = Math.atan2(dy, dx);
                    
                    // Limit angle to reasonable range
                    cannonAngle = Math.max(Math.min(cannonAngle, 0), -Math.PI / 2);
                    
                    // Calculate power based on drag distance
                    const dragDistance = Math.sqrt(dx * dx + dy * dy);
                    cannonPower = Math.min(dragDistance / 200, MAX_POWER);
                }
            } catch (error) {
                console.error('%cError handling mouse move:', 'color: #f94144', error);
            }
        }
        
        // Handle mouse up event
        function handleMouseUp(event) {
            try {
                if (gamePhase === 'building' && isDragging) {
                    isDragging = false;
                    selectedBody = null;
                    
                    // Remove the constraint
                    if (mouseConstraint) {
                        Matter.Composite.remove(world, mouseConstraint);
                        mouseConstraint = null;
                    }
                } else if (gamePhase === 'shooting' && isAiming) {
                    fireCannon();
                    isAiming = false;
                }
            } catch (error) {
                console.error('%cError handling mouse up:', 'color: #f94144', error);
            }
        }
        
        // Create a new brick at the specified position
        function createBrick(x, y) {
            try {
                // Check if position overlaps with existing bricks
                const bodies = Matter.Composite.allBodies(world);
                for (let i = 0; i < bodies.length; i++) {
                    const body = bodies[i];
                    if (body !== ground && !body.isStatic) {
                        const bounds = body.bounds;
                        if (
                            x + BRICK_SIZE / 2 > bounds.min.x &&
                            x - BRICK_SIZE / 2 < bounds.max.x &&
                            y + BRICK_SIZE / 2 > bounds.min.y &&
                            y - BRICK_SIZE / 2 < bounds.max.y
                        ) {
                            console.log('%cCannot place brick: overlaps with existing brick', 'color: #f94144');
                            return;
                        }
                    }
                }
                
                // Create a new brick
                const colorIndex = bricks.length % BRICK_COLORS.length;
                const brick = Matter.Bodies.rectangle(
                    x,
                    y,
                    BRICK_SIZE,
                    BRICK_SIZE,
                    {
                        density: BRICK_DENSITY,
                        restitution: 0.1,
                        friction: 0.5
                    }
                );
                
                // Create a sprite for the brick
                const brickSprite = new PIXI.Graphics();
                brickSprite.lineStyle(1, 0xFFFFFF);
                brickSprite.beginFill(PIXI.utils.string2hex(BRICK_COLORS[colorIndex]));
                brickSprite.drawRect(-BRICK_SIZE/2, -BRICK_SIZE/2, BRICK_SIZE, BRICK_SIZE);
                brickSprite.endFill();
                brickSprite.position.set(x, y);
                sprites.brickContainer.addChild(brickSprite);
                
                // Add the brick to the world and our array
                Matter.Composite.add(world, brick);
                bricks.push({
                    body: brick,
                    sprite: brickSprite,
                    color: BRICK_COLORS[colorIndex]
                });
                
                console.log('%cBrick created at position:', 'color: #43aa8b', { x, y });
                
                // Create a constraint to drag the newly created brick
                selectedBody = brick;
                isDragging = true;
                mouseConstraint = Matter.Constraint.create({
                    pointA: { x, y },
                    bodyB: selectedBody,
                    stiffness: 0.2
                });
                Matter.Composite.add(world, mouseConstraint);
            } catch (error) {
                console.error('%cError creating brick:', 'color: #f94144', error);
            }
        }
        
        // Switch between building and shooting phases
        function switchPhase() {
            try {
                if (gamePhase === 'building') {
                    if (bricks.length === 0) {
                        showMessage('Please build a tower first!');
                        return;
                    }
                    
                    gamePhase = 'shooting';
                    document.getElementById('phase-info').textContent = 'Shooting Phase';
                    document.getElementById('switch-phase-btn').textContent = 'Back to Building';
                    
                    // Create cannon
                    createCannon();
                    
                    console.log('%cSwitched to shooting phase', 'color: #43aa8b');
                } else {
                    gamePhase = 'building';
                    document.getElementById('phase-info').textContent = 'Building Phase';
                    document.getElementById('switch-phase-btn').textContent = 'Start Shooting';
                    
                    // Remove cannon and cannon ball if they exist
                    if (sprites.cannon) {
                        sprites.cannonContainer.removeChild(sprites.cannon);
                        sprites.cannon = null;
                    }
                    
                    if (cannonBall) {
                        Matter.Composite.remove(world, cannonBall);
                        if (sprites.cannonBall) {
                            sprites.cannonContainer.removeChild(sprites.cannonBall);
                            sprites.cannonBall = null;
                        }
                        cannonBall = null;
                    }
                    
                    shotsFired = 0;
                    console.log('%cSwitched to building phase', 'color: #43aa8b');
                }
                
                // Clear UI elements
                sprites.uiContainer.removeChildren();
            } catch (error) {
                console.error('%cError switching phase:', 'color: #f94144', error);
            }
        }
        
        // Create the cannon
        function createCannon() {
            try {
                console.log('%cCreating cannon...', 'color: #43aa8b');
                
                // Create Matter.js cannon data
                cannon = {
                    x: CANNON_X,
                    y: CANNON_Y,
                    width: CANNON_WIDTH,
                    height: CANNON_HEIGHT
                };
                
                // Create PixiJS sprites for cannon
                const cannonSprite = new PIXI.Container();
                
                // Cannon barrel
                const barrel = new PIXI.Graphics();
                barrel.beginFill(0x4a4a82);
                barrel.drawRect(0, -CANNON_HEIGHT/2, CANNON_WIDTH, CANNON_HEIGHT);
                barrel.endFill();
                
                // Cannon base
                const base = new PIXI.Graphics();
                base.beginFill(0x6a6aa8);
                base.drawCircle(0, 0, CANNON_HEIGHT);
                base.endFill();
                
                cannonSprite.addChild(base);
                cannonSprite.addChild(barrel);
                cannonSprite.position.set(CANNON_X, CANNON_Y);
                
                sprites.cannon = cannonSprite;
                sprites.cannonContainer.addChild(cannonSprite);
                
                // Create aiming line container
                sprites.aimLine = new PIXI.Graphics();
                sprites.uiContainer.addChild(sprites.aimLine);
            } catch (error) {
                console.error('%cError creating cannon:', 'color: #f94144', error);
            }
        }
        
        // Get the position of the cannon barrel end
        function getCannonPosition() {
            return {
                x: cannon.x + Math.cos(cannonAngle) * cannon.width,
                y: cannon.y + Math.sin(cannonAngle) * cannon.width
            };
        }
        
        // Fire the cannon
        function fireCannon() {
            try {
                if (gameOver) return;
                
                console.log('%cFiring cannon with power:', 'color: #43aa8b', cannonPower);
                
                // Create a cannon ball
                const cannonPos = getCannonPosition();
                cannonBall = Matter.Bodies.circle(
                    cannonPos.x,
                    cannonPos.y,
                    CANNON_BALL_RADIUS,
                    {
                        density: CANNON_BALL_DENSITY,
                        restitution: 0.3,
                        friction: 0.05,
                        frictionAir: 0.001
                    }
                );
                
                // Create PixiJS sprite for cannon ball
                const cannonBallSprite = new PIXI.Graphics();
                cannonBallSprite.lineStyle(1, 0xFFFFFF);
                cannonBallSprite.beginFill(0xFFFFFF);
                cannonBallSprite.drawCircle(0, 0, CANNON_BALL_RADIUS);
                cannonBallSprite.endFill();
                cannonBallSprite.position.set(cannonPos.x, cannonPos.y);
                
                sprites.cannonBall = cannonBallSprite;
                sprites.cannonContainer.addChild(cannonBallSprite);
                
                // Apply force to the cannon ball
                const force = {
                    x: Math.cos(cannonAngle) * cannonPower,
                    y: Math.sin(cannonAngle) * cannonPower
                };
                
                Matter.Composite.add(world, cannonBall);
                Matter.Body.applyForce(cannonBall, cannonPos, force);
                
                shotsFired++;
                
                // Clear aiming line
                sprites.aimLine.clear();
                
                // Check if game is over after a delay
                setTimeout(checkGameState, 3000);
            } catch (error) {
                console.error('%cError firing cannon:', 'color: #f94144', error);
            }
        }
        
        // Check if the game is over
        function checkGameState() {
            try {
                if (gamePhase !== 'shooting' || gameOver) return;
                
                // Count how many bricks are still standing (not fallen)
                let standingBricks = 0;
                for (let i = 0; i < bricks.length; i++) {
                    const brick = bricks[i].body;
                    // A brick is considered standing if it's not too tilted
                    const angle = Math.abs(brick.angle % (Math.PI * 2));
                    const isTilted = angle > Math.PI / 4 && angle < Math.PI * 7 / 4;
                    
                    if (!isTilted) {
                        standingBricks++;
                    }
                }
                
                // If less than half of the bricks are standing, player wins
                if (standingBricks < bricks.length / 2) {
                    gameOver = true;
                    showMessage(`Victory! You knocked down the tower in ${shotsFired} shots!`);
                    console.log('%cGame over - Player won!', 'color: #43aa8b');
                } else if (shotsFired >= 5) {
                    // Limit shots to make the game challenging
                    gameOver = true;
                    showMessage(`Game Over! You used all ${shotsFired} shots but the tower still stands!`);
                    console.log('%cGame over - Player lost!', 'color: #f94144');
                }
            } catch (error) {
                console.error('%cError checking game state:', 'color: #f94144', error);
            }
        }
        
        // Reset the game
        function resetGame() {
            try {
                console.log('%cResetting game...', 'color: #43aa8b');
                
                // Remove all bodies except ground and walls
                const bodies = Matter.Composite.allBodies(world);
                for (let i = 0; i < bodies.length; i++) {
                    const body = bodies[i];
                    if (body !== ground && !body.isStatic) {
                        Matter.Composite.remove(world, body);
                    }
                }
                
                // Clear all containers
                sprites.brickContainer.removeChildren();
                sprites.cannonContainer.removeChildren();
                sprites.uiContainer.removeChildren();
                
                // Reset game state
                bricks = [];
                isDragging = false;
                selectedBody = null;
                mouseConstraint = null;
                cannonAngle = -Math.PI / 4;
                cannonPower = 0.1;
                isAiming = false;
                shotsFired = 0;
                gameOver = false;
                sprites.cannon = null;
                sprites.cannonBall = null;
                cannon = null;
                cannonBall = null;
                
                // Reset to building phase
                gamePhase = 'building';
                document.getElementById('phase-info').textContent = 'Building Phase';
                document.getElementById('switch-phase-btn').textContent = 'Start Shooting';
                
                // Hide any messages
                hideMessage();
                
                console.log('%cGame reset successfully!', 'color: #43aa8b');
            } catch (error) {
                console.error('%cError resetting game:', 'color: #f94144', error);
            }
        }
        
        // Show a message to the player
        function showMessage(text) {
            try {
                const messageElement = document.getElementById('game-message');
                messageElement.textContent = text;
                messageElement.style.display = 'block';
                
                // Animate the message
                anime({
                    targets: '#game-message',
                    opacity: [0, 1],
                    scale: [0.8, 1],
                    duration: 500,
                    easing: 'easeOutElastic(1, .5)'
                });
            } catch (error) {
                console.error('%cError showing message:', 'color: #f94144', error);
            }
        }
        
        // Hide the message
        function hideMessage() {
            try {
                const messageElement = document.getElementById('game-message');
                
                // Animate the message out
                anime({
                    targets: '#game-message',
                    opacity: 0,
                    scale: 0.8,
                    duration: 300,
                    easing: 'easeInOutQuad',
                    complete: function() {
                        messageElement.style.display = 'none';
                    }
                });
            } catch (error) {
                console.error('%cError hiding message:', 'color: #f94144', error);
            }
        }
        
        // Show loading animation
        function showLoading() {
            try {
                const loadingElement = document.getElementById('loading');
                loadingElement.style.display = 'block';
                
                // Animate the loading circle
                anime({
                    targets: '.loading-circle',
                    rotate: '360deg',
                    duration: 1000,
                    loop: true,
                    easing: 'linear'
                });
            } catch (error) {
                console.error('%cError showing loading animation:', 'color: #f94144', error);
            }
        }
        
        // Hide loading animation
        function hideLoading() {
            try {
                const loadingElement = document.getElementById('loading');
                loadingElement.style.display = 'none';
            } catch (error) {
                console.error('%cError hiding loading animation:', 'color: #f94144', error);
            }
        }
        
        // Draw a constraint line (for dragging)
        function drawConstraintLine() {
            try {
                // Clear previous constraint graphics
                sprites.uiContainer.removeChildAt(0);
                
                // Create new constraint graphics
                const constraintGraphics = new PIXI.Graphics();
                constraintGraphics.lineStyle(2, 0xFFFFFF, 0.5);
                constraintGraphics.moveTo(mouseConstraint.pointA.x, mouseConstraint.pointA.y);
                constraintGraphics.lineTo(
                    mouseConstraint.bodyB.position.x,
                    mouseConstraint.bodyB.position.y
                );
                
                sprites.uiContainer.addChildAt(constraintGraphics, 0);
            } catch (error) {
                console.error('%cError drawing constraint line:', 'color: #f94144', error);
            }
        }
        
        // Draw the aiming line
        function drawAimingLine() {
            try {
                // Clear previous aim line
                sprites.aimLine.clear();
                
                const cannonPos = getCannonPosition();
                const lineLength = cannonPower * 500;
                
                // Draw new aim line
                sprites.aimLine.lineStyle(2, 0xFFFFFF, 0.7);
                sprites.aimLine.moveTo(cannonPos.x, cannonPos.y);
                sprites.aimLine.lineTo(
                    cannonPos.x + Math.cos(cannonAngle) * lineLength,
                    cannonPos.y + Math.sin(cannonAngle) * lineLength
                );
                
                // Add dotted effect by drawing small dashes
                const segments = 20;
                const dashLength = lineLength / segments;
                
                sprites.aimLine.clear();
                sprites.aimLine.lineStyle(2, 0xFFFFFF, 0.7);
                
                for (let i = 0; i < segments; i++) {
                    if (i % 2 === 0) {
                        const startX = cannonPos.x + Math.cos(cannonAngle) * (i * dashLength);
                        const startY = cannonPos.y + Math.sin(cannonAngle) * (i * dashLength);
                        const endX = cannonPos.x + Math.cos(cannonAngle) * ((i + 1) * dashLength);
                        const endY = cannonPos.y + Math.sin(cannonAngle) * ((i + 1) * dashLength);
                        
                        sprites.aimLine.moveTo(startX, startY);
                        sprites.aimLine.lineTo(endX, endY);
                    }
                }
            } catch (error) {
                console.error('%cError drawing aiming line:', 'color: #f94144', error);
            }
        }
        
        // Handle window resize
        function handleResize() {
            try {
                console.log('%cResizing game area...', 'color: #43aa8b');
                const newWidth = window.innerWidth * 0.95;
                
                // Update PixiJS application size
                app.renderer.resize(newWidth, CANVAS_HEIGHT);
                
                // Update game container size
                document.getElementById('game-container').style.width = '95vw';
                
                // Update ground position
                if (ground) {
                    Matter.Body.setPosition(ground, {
                        x: newWidth / 2,
                        y: CANVAS_HEIGHT - GROUND_HEIGHT / 2
                    });
                    
                    // Update ground sprite
                    sprites.ground.clear();
                    sprites.ground.beginFill(0x4a4a82);
                    sprites.ground.drawRect(
                        0,
                        CANVAS_HEIGHT - GROUND_HEIGHT,
                        newWidth,
                        GROUND_HEIGHT
                    );
                    sprites.ground.endFill();
                }
                
                // Update right wall position
                const bodies = Matter.Composite.allBodies(world);
                for (let i = 0; i < bodies.length; i++) {
                    const body = bodies[i];
                    if (body !== ground && body.isStatic) {
                        // Check if it's the right wall by its x position
                        if (body.position.x > CANVAS_WIDTH / 2) {
                            Matter.Body.setPosition(body, {
                                x: newWidth - 10,
                                y: body.position.y
                            });
                        }
                    }
                }
                
                // Update variable
                CANVAS_WIDTH = newWidth;
                
                console.log('%cGame area resized successfully!', 'color: #43aa8b');
            } catch (error) {
                console.error('%cError resizing game area:', 'color: #f94144', error);
            }
        }
        
        // Main game loop
        function gameLoop(delta) {
            try {
                // Update brick positions to match their physics bodies
                for (let i = 0; i < bricks.length; i++) {
                    const brick = bricks[i];
                    if (brick.sprite && brick.body) {
                        brick.sprite.position.set(brick.body.position.x, brick.body.position.y);
                        brick.sprite.rotation = brick.body.angle;
                    }
                }
                
                // Update cannon rotation in shooting phase
                if (gamePhase === 'shooting' && sprites.cannon) {
                    sprites.cannon.rotation = cannonAngle;
                }
                
                // Update cannon ball position if it exists
                if (cannonBall && sprites.cannonBall) {
                    sprites.cannonBall.position.set(cannonBall.position.x, cannonBall.position.y);
                }
                
                // Draw constraint line if dragging
                if (isDragging && mouseConstraint) {
                    // Just update the constraint line - create it if it doesn't exist
                    if (sprites.uiContainer.children.length === 0) {
                        const constraintGraphics = new PIXI.Graphics();
                        sprites.uiContainer.addChild(constraintGraphics);
                    }
                    
                    const constraintGraphics = sprites.uiContainer.getChildAt(0);
                    constraintGraphics.clear();
                    constraintGraphics.lineStyle(2, 0xFFFFFF, 0.5);
                    constraintGraphics.moveTo(mouseConstraint.pointA.x, mouseConstraint.pointA.y);
                    constraintGraphics.lineTo(
                        mouseConstraint.bodyB.position.x,
                        mouseConstraint.bodyB.position.y
                    );
                }
                
                // Draw aiming line if aiming
                if (gamePhase === 'shooting' && isAiming) {
                    drawAimingLine();
                }
            } catch (error) {
                console.error('%cError in game loop:', 'color: #f94144', error);
            }
        }
        
        // Initialize the game when the page loads
        window.addEventListener('load', function() {
            try {
                console.log('%cGame loading...', 'color: #43aa8b');
                showLoading();
                
                // Initialize the game after a short delay to show loading animation
                setTimeout(function() {
                    init();
                    hideLoading();
                }, 500);
            } catch (error) {
                console.error('%cError loading game:', 'color: #f94144', error);
                hideLoading();
                showMessage('Error loading game. Please refresh the page.');
            }
        });
    </script>
</body>
</html> 