import os
import base64
import anthropic
import httpx
from fastapi import FastAP<PERSON>, Request, File, UploadFile, Form, HTTPException
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from termcolor import colored
import re
import asyncio
from typing import Optional
from datetime import datetime
import uuid

# Constants
API_KEY = os.getenv("ANTHROPIC_API_KEY")
MODEL_NAME = "claude-3-7-sonnet-20250219"
MAX_TOKENS = 1024

app = FastAPI()

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")

# Templates
templates = Jinja2Templates(directory="templates")

# Initialize Anthropic client
client = anthropic.Anthropic(api_key=API_KEY)

@app.get("/", response_class=HTMLResponse)
async def read_root(request: Request):
    print(colored("Rendering index page", "green"))
    return templates.TemplateResponse("index.html", {"request": request})

async def analyze_pdf_from_url(url: str, question: str):
    """Analyze PDF from a URL using Anthropic API"""
    print(colored(f"Analyzing PDF from URL: {url}", "blue"))
    
    try:
        message = client.messages.create(
            model=MODEL_NAME,
            max_tokens=MAX_TOKENS,
            messages=[
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "document",
                            "source": {
                                "type": "url",
                                "url": url
                            }
                        },
                        {
                            "type": "text",
                            "text": f"{question} Please provide a summary of the document in simple, everyday language that anyone could understand. Also include 5-7 key information points in <key_info>...</key_info> tags. Each key information point should be a general, broadly applicable fact or insight that could be mentioned in everyday conversations, even with people unfamiliar with the topic. Don't focus on technical details, but rather on interesting, relatable aspects that anyone would find engaging. Format each point as: <point>Conversational information point. | Why this is interesting in everyday contexts.</point>"
                        }
                    ]
                }
            ],
        )
        print(colored("Successfully received response from Anthropic API", "green"))
        # Ensure we're getting a string from the response
        if hasattr(message.content, 'text'):
            return message.content.text
        elif isinstance(message.content, list):
            # If it's a list of content blocks, join them
            return ' '.join(item.text if hasattr(item, 'text') else str(item) for item in message.content)
        else:
            return str(message.content)
    except Exception as e:
        print(colored(f"Error analyzing PDF from URL: {str(e)}", "red"))
        raise HTTPException(status_code=500, detail=f"Error analyzing PDF: {str(e)}")

async def analyze_pdf_from_upload(file_content: bytes, question: str):
    """Analyze uploaded PDF using Anthropic API"""
    print(colored("Analyzing uploaded PDF", "blue"))
    
    try:
        pdf_data = base64.standard_b64encode(file_content).decode("utf-8")
        
        message = client.messages.create(
            model=MODEL_NAME,
            max_tokens=MAX_TOKENS,
            messages=[
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "document",
                            "source": {
                                "type": "base64",
                                "media_type": "application/pdf",
                                "data": pdf_data
                            }
                        },
                        {
                            "type": "text",
                            "text": f"{question} Please provide a summary of the document in simple, everyday language that anyone could understand. Also include 5-7 key information points in <key_info>...</key_info> tags. Each key information point should be a general, broadly applicable fact or insight that could be mentioned in everyday conversations, even with people unfamiliar with the topic. Don't focus on technical details, but rather on interesting, relatable aspects that anyone would find engaging. Format each point as: <point>Conversational information point. | Why this is interesting in everyday contexts.</point>"
                        }
                    ]
                }
            ],
        )
        print(colored("Successfully received response from Anthropic API", "green"))
        # Ensure we're getting a string from the response
        if hasattr(message.content, 'text'):
            return message.content.text
        elif isinstance(message.content, list):
            # If it's a list of content blocks, join them
            return ' '.join(item.text if hasattr(item, 'text') else str(item) for item in message.content)
        else:
            return str(message.content)
    except Exception as e:
        print(colored(f"Error analyzing uploaded PDF: {str(e)}", "red"))
        raise HTTPException(status_code=500, detail=f"Error analyzing PDF: {str(e)}")

def extract_key_information(content: str):
    """Extract key information points from the response"""
    print(colored("Extracting key information points from response", "blue"))
    
    # Check if content is a list (which happens with newer Anthropic API versions)
    if isinstance(content, list):
        # Convert the list to a string by joining its elements
        print(colored("Converting list response to string", "yellow"))
        content = ' '.join(str(item) for item in content)
    
    # Extract the key information section
    key_info_pattern = r'<key_info>(.*?)</key_info>'
    key_info_match = re.search(key_info_pattern, content, re.DOTALL)
    
    key_info_points = []
    if key_info_match:
        key_info_content = key_info_match.group(1).strip()
        
        # Extract individual points
        point_pattern = r'<point>(.*?)</point>'
        points = re.findall(point_pattern, key_info_content, re.DOTALL)
        
        for point in points:
            parts = point.split('|', 1)
            if len(parts) == 2:
                info = parts[0].strip()
                importance = parts[1].strip()
                key_info_points.append({"info": info, "importance": importance})
            else:
                # If the point doesn't have the expected format, add it as is
                key_info_points.append({"info": point.strip(), "importance": ""})
        
        # Remove the key info section from the main content
        main_content = re.sub(key_info_pattern, '', content, flags=re.DOTALL).strip()
    else:
        # If no key info section is found, use the whole content as the summary
        main_content = content
        print(colored("No key information points found in the response", "yellow"))
    
    return main_content, key_info_points

@app.post("/analyze", response_class=HTMLResponse)
async def analyze_pdf(
    request: Request,
    pdf_file: Optional[UploadFile] = File(None),
    pdf_url: Optional[str] = Form(None),
    question: str = Form("What are the key findings in this document?")
):
    print(colored("Received analyze request", "green"))
    
    if not pdf_file and not pdf_url:
        raise HTTPException(status_code=400, detail="Either a PDF file or URL must be provided")
    
    try:
        if pdf_file:
            print(colored(f"Processing uploaded file: {pdf_file.filename}", "blue"))
            file_content = await pdf_file.read()
            response_content = await analyze_pdf_from_upload(file_content, question)
            source = pdf_file.filename
        else:
            print(colored(f"Processing URL: {pdf_url}", "blue"))
            response_content = await analyze_pdf_from_url(pdf_url, question)
            source = pdf_url
        
        print(colored(f"Response content type: {type(response_content)}", "yellow"))
        
        try:
            summary, key_info_points = extract_key_information(response_content)
        except Exception as e:
            print(colored(f"Error extracting key information: {str(e)}", "red"))
            # Fallback if extraction fails
            summary = response_content
            key_info_points = []
        
        # Create a timestamp for the analysis
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # Create a unique ID for this analysis
        analysis_id = str(uuid.uuid4())
        
        return templates.TemplateResponse(
            "result.html", 
            {
                "request": request,
                "summary": summary,
                "key_info_points": key_info_points,
                "source": source,
                "question": question,
                "timestamp": timestamp,
                "analysis_id": analysis_id
            }
        )
    except Exception as e:
        print(colored(f"Error in analyze_pdf: {str(e)}", "red"))
        # Return an error page instead of raising an exception
        return templates.TemplateResponse(
            "result.html", 
            {
                "request": request,
                "summary": f"Error processing PDF: {str(e)}",
                "key_info_points": [],
                "source": pdf_file.filename if pdf_file else pdf_url,
                "question": question,
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "analysis_id": str(uuid.uuid4())
            },
            status_code=500
        )

@app.post("/view-history", response_class=HTMLResponse)
async def view_history(request: Request, analysis_id: str = Form(...)):
    """View a previously analyzed PDF from history"""
    print(colored(f"Viewing history item: {analysis_id}", "green"))
    
    # We don't actually need to do anything server-side since the history is stored in localStorage
    # Just render the result template and the JavaScript will load the data from localStorage
    
    return templates.TemplateResponse(
        "result.html", 
        {
            "request": request,
            "summary": "",  # Will be populated by JavaScript
            "key_info_points": [],  # Will be populated by JavaScript
            "source": "",  # Will be populated by JavaScript
            "question": "",  # Will be populated by JavaScript
            "timestamp": "",  # Will be populated by JavaScript
            "analysis_id": analysis_id
        }
    )

if __name__ == "__main__":
    import uvicorn
    print(colored("Starting PDF Analyzer application", "green"))
    uvicorn.run("main:app", host="127.0.0.1", port=8000, reload=True) 