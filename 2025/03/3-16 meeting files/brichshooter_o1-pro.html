<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Brick Shooter Game with Levels and Sandbox</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/matter-js/0.18.0/matter.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            background-color: #1a1a2e;
            color: #e6e6e6;
            font-family: Arial, sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: flex-start;
            height: 100vh;
        }
        
        #game-container {
            position: relative;
            width: 95vw;
            height: 600px;
            border: 2px solid #4a4a82;
            border-radius: 8px;
            overflow: hidden;
            margin-top: 10px;
        }
        
        canvas {
            background-color: #16213e;
        }
        
        #ui-container {
            position: absolute;
            top: 10px;
            left: 10px;
            z-index: 10;
            background-color: rgba(26, 26, 46, 0.7);
            padding: 10px;
            border-radius: 5px;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
        }

        #level-container {
            margin-bottom: 10px;
        }
        
        button {
            background-color: #4a4a82;
            color: white;
            border: none;
            padding: 8px 16px;
            margin: 3px;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        button:hover {
            background-color: #6a6aa8;
        }
        
        #loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            display: none;
            z-index: 20;
        }
        
        .loading-circle {
            width: 50px;
            height: 50px;
            border: 5px solid rgba(74, 74, 130, 0.3);
            border-top: 5px solid #4a4a82;
            border-radius: 50%;
        }
        
        #game-message {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: rgba(26, 26, 46, 0.9);
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            display: none;
            z-index: 15;
        }
        
        #instructions {
            margin-top: 10px;
            max-width: 95vw;
            background-color: rgba(26, 26, 46, 0.7);
            padding: 15px;
            border-radius: 8px;
        }

        #instructions h2,
        #instructions h3 {
            margin: 0;
            padding: 0;
        }

        #instructions ul {
            margin: 5px 0 15px 20px;
        }
    </style>
</head>
<body>
    <div id="game-container">
        <canvas id="game-canvas"></canvas>
        <div id="ui-container">
            <!-- Level Selection / Mode Selection -->
            <div id="level-container">
                <button id="sandbox-btn">Sandbox</button>
                <button id="level1-btn">Level 1</button>
                <button id="level2-btn">Level 2</button>
                <button id="level3-btn">Level 3</button>
            </div>
            <!-- Phase and Control Buttons -->
            <button id="switch-phase-btn">Start Shooting</button>
            <button id="reset-btn">Reset Game</button>
            <div id="phase-info">Building Phase</div>
        </div>
        <div id="loading">
            <div class="loading-circle"></div>
        </div>
        <div id="game-message"></div>
    </div>
    
    <div id="instructions">
        <h2>How to Play</h2>
        <p>
            Select a mode above:
            <ul>
                <li><strong>Sandbox:</strong> Build your own tower and then shoot it down.</li>
                <li><strong>Levels 1-3:</strong> Pre-built towers of increasing difficulty. Try knocking them down within the shot limit!</li>
            </ul>
        </p>
        <h3>Building Phase (Sandbox only):</h3>
        <ul>
            <li>Click on the game area to create a new brick</li>
            <li>Drag bricks to position them</li>
            <li>Build a tower that can withstand shots</li>
        </ul>
        <h3>Shooting Phase (All modes):</h3>
        <ul>
            <li>Click and drag on the cannon to aim</li>
            <li>Release to fire</li>
            <li>Try to knock down the tower</li>
        </ul>
    </div>

    <script>
        // CONSTANTS AND CONFIGURATION
        let CANVAS_WIDTH = window.innerWidth * 0.95;
        const CANVAS_HEIGHT = 600;
        const GROUND_HEIGHT = 50;
        const BRICK_SIZE = 40;
        const CANNON_X = 100;
        const CANNON_Y = CANVAS_HEIGHT - GROUND_HEIGHT - 30;
        const CANNON_WIDTH = 60;
        const CANNON_HEIGHT = 30;
        const CANNON_BALL_RADIUS = 15;
        const CANNON_BALL_DENSITY = 0.02;
        const BRICK_DENSITY = 0.001;
        const MAX_POWER = 1.2;
        const BRICK_COLORS = ['#f94144', '#f3722c', '#f8961e', '#f9c74f', '#90be6d', '#43aa8b', '#577590'];

        // Game modes: "sandbox", "level1", "level2", "level3"
        let gameMode = 'sandbox';  
        
        // Game state
        let gamePhase = 'building'; // 'building' or 'shooting'
        let engine, world, canvas, context;
        let ground, cannon, cannonBall;
        let bricks = [];
        let isDragging = false;
        let selectedBody = null;
        let mouseConstraint = null;
        let cannonAngle = -Math.PI / 4;
        let cannonPower = 0.1;
        let dragStart = { x: 0, y: 0 };
        let isAiming = false;
        let shotsFired = 0;
        let gameOver = false;

        // For collision impact shake
        let shakeIntensity = 0;

        // Initialize the game
        function init() {
            try {
                console.log('%cInitializing game...', 'color: #43aa8b');
                
                // Setup Matter.js
                engine = Matter.Engine.create();
                world = engine.world;
                
                // Setup canvas
                canvas = document.getElementById('game-canvas');
                canvas.width = CANVAS_WIDTH;
                canvas.height = CANVAS_HEIGHT;
                context = canvas.getContext('2d');
                
                // Create ground
                ground = Matter.Bodies.rectangle(
                    CANVAS_WIDTH / 2,
                    CANVAS_HEIGHT - GROUND_HEIGHT / 2,
                    CANVAS_WIDTH,
                    GROUND_HEIGHT,
                    { isStatic: true }
                );
                
                // Create left wall to place cannon against
                const leftWall = Matter.Bodies.rectangle(
                    10,
                    CANVAS_HEIGHT / 2,
                    20,
                    CANVAS_HEIGHT,
                    { isStatic: true }
                );
                
                // Create right wall to prevent bricks from going off-screen
                const rightWall = Matter.Bodies.rectangle(
                    CANVAS_WIDTH - 10,
                    CANVAS_HEIGHT / 2,
                    20,
                    CANVAS_HEIGHT,
                    { isStatic: true }
                );
                
                // Add bodies to world
                Matter.Composite.add(world, [ground, leftWall, rightWall]);
                
                // Listen for collisions to animate impacts
                Matter.Events.on(engine, 'collisionStart', function(event) {
                    event.pairs.forEach(function(pair) {
                        // If the cannonBall is involved in the collision, trigger a screen shake
                        if (pair.bodyA === cannonBall || pair.bodyB === cannonBall) {
                            shakeIntensity = 10; // Start shake
                        }
                    });
                });
                
                // Setup event listeners
                setupEventListeners();
                
                // Start the engine
                Matter.Runner.run(engine);
                
                // Start game loop
                requestAnimationFrame(gameLoop);
                
                console.log('%cGame initialized successfully!', 'color: #43aa8b');
            } catch (error) {
                console.error('%cError initializing game:', 'color: #f94144', error);
                showMessage('Error initializing game. Please refresh the page.');
            }
        }
        
        // Setup event listeners
        function setupEventListeners() {
            try {
                console.log('%cSetting up event listeners...', 'color: #43aa8b');
                
                // Canvas event listeners
                canvas.addEventListener('mousedown', handleMouseDown);
                canvas.addEventListener('mousemove', handleMouseMove);
                canvas.addEventListener('mouseup', handleMouseUp);
                
                // Button event listeners
                document.getElementById('switch-phase-btn').addEventListener('click', switchPhase);
                document.getElementById('reset-btn').addEventListener('click', resetGame);

                // Mode buttons
                document.getElementById('sandbox-btn').addEventListener('click', () => setGameMode('sandbox'));
                document.getElementById('level1-btn').addEventListener('click', () => setGameMode('level1'));
                document.getElementById('level2-btn').addEventListener('click', () => setGameMode('level2'));
                document.getElementById('level3-btn').addEventListener('click', () => setGameMode('level3'));
                
                // Window resize listener
                window.addEventListener('resize', handleResize);
                
                console.log('%cEvent listeners set up successfully!', 'color: #43aa8b');
            } catch (error) {
                console.error('%cError setting up event listeners:', 'color: #f94144', error);
            }
        }

        // Change game mode (sandbox, level1, level2, level3)
        function setGameMode(mode) {
            gameMode = mode;
            resetGame(); // Clears everything
            hideMessage();
            
            if (gameMode === 'sandbox') {
                // In sandbox, we allow building phase. 
                gamePhase = 'building';
                document.getElementById('phase-info').textContent = 'Building Phase';
                document.getElementById('switch-phase-btn').textContent = 'Start Shooting';
                document.getElementById('switch-phase-btn').style.display = 'inline-block';
            } else {
                // For levels, we automatically set up the pre-built tower
                document.getElementById('switch-phase-btn').style.display = 'none';
                
                // Create the level
                createLevel(mode);
                
                // We skip building, so we go straight to shooting phase
                gamePhase = 'shooting';
                document.getElementById('phase-info').textContent = 'Shooting Phase';
                
                // Create the cannon
                createCannon();
            }
        }

        // Create the specified level's tower layout
        function createLevel(mode) {
            // Let's define some sample towers
            if (mode === 'level1') {
                // A simple small tower in the middle
                createBrick(CANVAS_WIDTH / 2, CANVAS_HEIGHT - GROUND_HEIGHT - 30);
                createBrick(CANVAS_WIDTH / 2 - BRICK_SIZE, CANVAS_HEIGHT - GROUND_HEIGHT - 30);
                createBrick(CANVAS_WIDTH / 2 + BRICK_SIZE, CANVAS_HEIGHT - GROUND_HEIGHT - 30);
                createBrick(CANVAS_WIDTH / 2, CANVAS_HEIGHT - GROUND_HEIGHT - 70);
            } else if (mode === 'level2') {
                // A taller tower
                for (let i = 0; i < 3; i++) {
                    createBrick(CANVAS_WIDTH / 2 - BRICK_SIZE, CANVAS_HEIGHT - GROUND_HEIGHT - 30 - i*BRICK_SIZE);
                    createBrick(CANVAS_WIDTH / 2, CANVAS_HEIGHT - GROUND_HEIGHT - 30 - i*BRICK_SIZE);
                    createBrick(CANVAS_WIDTH / 2 + BRICK_SIZE, CANVAS_HEIGHT - GROUND_HEIGHT - 30 - i*BRICK_SIZE);
                }
                createBrick(CANVAS_WIDTH / 2, CANVAS_HEIGHT - GROUND_HEIGHT - 30 - 3*BRICK_SIZE);
            } else if (mode === 'level3') {
                // A bigger multi-level tower
                for (let row = 0; row < 5; row++) {
                    for (let col = -2; col <= 2; col++) {
                        createBrick(
                            CANVAS_WIDTH / 2 + col * BRICK_SIZE,
                            CANVAS_HEIGHT - GROUND_HEIGHT - 30 - row * BRICK_SIZE
                        );
                    }
                }
            }
        }

        // Handle mouse down event
        function handleMouseDown(event) {
            try {
                const rect = canvas.getBoundingClientRect();
                const mouseX = event.clientX - rect.left;
                const mouseY = event.clientY - rect.top;
                
                if (gamePhase === 'building' && gameMode === 'sandbox') {
                    // Check if clicked on existing brick
                    const bodies = Matter.Composite.allBodies(world);
                    for (let i = 0; i < bodies.length; i++) {
                        const body = bodies[i];
                        if (
                            body !== ground && 
                            Matter.Vertices.contains(body.vertices, { x: mouseX, y: mouseY })
                        ) {
                            selectedBody = body;
                            isDragging = true;
                            
                            // Create a constraint to drag the body
                            mouseConstraint = Matter.Constraint.create({
                                pointA: { x: mouseX, y: mouseY },
                                bodyB: selectedBody,
                                stiffness: 0.2
                            });
                            Matter.Composite.add(world, mouseConstraint);
                            return;
                        }
                    }
                    
                    // If not clicked on existing brick, create a new one
                    createBrick(mouseX, mouseY);
                } else if (gamePhase === 'shooting') {
                    // Check if clicked on cannon
                    const cannonPos = getCannonPosition();
                    const distance = Math.sqrt(
                        Math.pow(mouseX - cannonPos.x, 2) + 
                        Math.pow(mouseY - cannonPos.y, 2)
                    );
                    
                    if (distance < CANNON_WIDTH) {
                        isAiming = true;
                        dragStart = { x: mouseX, y: mouseY };
                    }
                }
            } catch (error) {
                console.error('%cError handling mouse down:', 'color: #f94144', error);
            }
        }
        
        // Handle mouse move event
        function handleMouseMove(event) {
            try {
                const rect = canvas.getBoundingClientRect();
                const mouseX = event.clientX - rect.left;
                const mouseY = event.clientY - rect.top;
                
                if (gamePhase === 'building' && isDragging && mouseConstraint) {
                    // Update the constraint point
                    mouseConstraint.pointA = { x: mouseX, y: mouseY };
                } else if (gamePhase === 'shooting' && isAiming) {
                    // Calculate angle and power based on drag
                    const dx = mouseX - dragStart.x;
                    const dy = mouseY - dragStart.y;
                    
                    // Update cannon angle
                    cannonAngle = Math.atan2(dy, dx);
                    
                    // Limit angle to a reasonable range (between straight left and straight up)
                    cannonAngle = Math.max(Math.min(cannonAngle, 0), -Math.PI / 2);
                    
                    // Calculate power based on drag distance
                    const dragDistance = Math.sqrt(dx * dx + dy * dy);
                    cannonPower = Math.min(dragDistance / 200, MAX_POWER);
                }
            } catch (error) {
                console.error('%cError handling mouse move:', 'color: #f94144', error);
            }
        }
        
        // Handle mouse up event
        function handleMouseUp() {
            try {
                if (gamePhase === 'building' && isDragging) {
                    isDragging = false;
                    selectedBody = null;
                    
                    // Remove the constraint
                    if (mouseConstraint) {
                        Matter.Composite.remove(world, mouseConstraint);
                        mouseConstraint = null;
                    }
                } else if (gamePhase === 'shooting' && isAiming) {
                    fireCannon();
                    isAiming = false;
                }
            } catch (error) {
                console.error('%cError handling mouse up:', 'color: #f94144', error);
            }
        }
        
        // Create a new brick at the specified position
        function createBrick(x, y) {
            try {
                // Check if position overlaps with existing bricks
                const bodies = Matter.Composite.allBodies(world);
                for (let i = 0; i < bodies.length; i++) {
                    const body = bodies[i];
                    if (body !== ground) {
                        const bounds = body.bounds;
                        if (
                            x + BRICK_SIZE / 2 > bounds.min.x &&
                            x - BRICK_SIZE / 2 < bounds.max.x &&
                            y + BRICK_SIZE / 2 > bounds.min.y &&
                            y - BRICK_SIZE / 2 < bounds.max.y
                        ) {
                            console.log('%cCannot place brick: overlaps with existing brick', 'color: #f94144');
                            return;
                        }
                    }
                }
                
                // Create a new brick
                const colorIndex = bricks.length % BRICK_COLORS.length;
                const brick = Matter.Bodies.rectangle(
                    x,
                    y,
                    BRICK_SIZE,
                    BRICK_SIZE,
                    {
                        density: BRICK_DENSITY,
                        restitution: 0.1,
                        friction: 0.5,
                        render: {
                            fillStyle: BRICK_COLORS[colorIndex]
                        }
                    }
                );
                
                // Add the brick to the world and our array
                Matter.Composite.add(world, brick);
                bricks.push({
                    body: brick,
                    color: BRICK_COLORS[colorIndex]
                });
                
                // If we are in building phase and clicked to place the brick, automatically pick it up
                if (gamePhase === 'building' && gameMode === 'sandbox') {
                    selectedBody = brick;
                    isDragging = true;
                    mouseConstraint = Matter.Constraint.create({
                        pointA: { x, y },
                        bodyB: selectedBody,
                        stiffness: 0.2
                    });
                    Matter.Composite.add(world, mouseConstraint);
                }
            } catch (error) {
                console.error('%cError creating brick:', 'color: #f94144', error);
            }
        }
        
        // Switch between building and shooting phases (Sandbox only)
        function switchPhase() {
            try {
                if (gameMode !== 'sandbox') return; // Only valid in sandbox
                
                if (gamePhase === 'building') {
                    if (bricks.length === 0) {
                        showMessage('Please build a tower first!');
                        return;
                    }
                    
                    gamePhase = 'shooting';
                    document.getElementById('phase-info').textContent = 'Shooting Phase';
                    document.getElementById('switch-phase-btn').textContent = 'Back to Building';
                    
                    // Create cannon
                    createCannon();
                    
                    console.log('%cSwitched to shooting phase', 'color: #43aa8b');
                } else {
                    gamePhase = 'building';
                    document.getElementById('phase-info').textContent = 'Building Phase';
                    document.getElementById('switch-phase-btn').textContent = 'Start Shooting';
                    
                    // Remove cannon ball if it exists
                    if (cannonBall) {
                        Matter.Composite.remove(world, cannonBall);
                        cannonBall = null;
                    }
                    
                    shotsFired = 0;
                    console.log('%cSwitched to building phase', 'color: #43aa8b');
                }
            } catch (error) {
                console.error('%cError switching phase:', 'color: #f94144', error);
            }
        }
        
        // Create the cannon
        function createCannon() {
            try {
                console.log('%cCreating cannon...', 'color: #43aa8b');
                cannon = {
                    x: CANNON_X,
                    y: CANNON_Y,
                    width: CANNON_WIDTH,
                    height: CANNON_HEIGHT
                };
            } catch (error) {
                console.error('%cError creating cannon:', 'color: #f94144', error);
            }
        }
        
        // Get the position of the cannon barrel end
        function getCannonPosition() {
            return {
                x: cannon.x + Math.cos(cannonAngle) * cannon.width,
                y: cannon.y + Math.sin(cannonAngle) * cannon.width
            };
        }
        
        // Fire the cannon
        function fireCannon() {
            try {
                if (gameOver) return;
                
                console.log('%cFiring cannon with power:', 'color: #43aa8b', cannonPower);
                
                // Create a cannon ball
                const cannonPos = getCannonPosition();
                cannonBall = Matter.Bodies.circle(
                    cannonPos.x,
                    cannonPos.y,
                    CANNON_BALL_RADIUS,
                    {
                        density: CANNON_BALL_DENSITY,
                        restitution: 0.3,
                        friction: 0.05,
                        frictionAir: 0.001
                    }
                );
                
                // Apply force to the cannon ball
                const force = {
                    x: Math.cos(cannonAngle) * cannonPower,
                    y: Math.sin(cannonAngle) * cannonPower
                };
                
                Matter.Composite.add(world, cannonBall);
                Matter.Body.applyForce(cannonBall, cannonPos, force);
                
                shotsFired++;
                
                // Check if game is over after a delay
                setTimeout(checkGameState, 3000);
            } catch (error) {
                console.error('%cError firing cannon:', 'color: #f94144', error);
            }
        }
        
        // Check if the game is over
        function checkGameState() {
            try {
                if (gamePhase !== 'shooting' || gameOver) return;
                
                // Count how many bricks are still standing (not fallen too far)
                let standingBricks = 0;
                for (let i = 0; i < bricks.length; i++) {
                    const brick = bricks[i].body;
                    // A brick is considered "fallen" if its angle is too large
                    const angle = Math.abs(brick.angle % (Math.PI * 2));
                    const isTilted = angle > Math.PI / 4 && angle < Math.PI * 7 / 4;
                    
                    if (!isTilted) {
                        standingBricks++;
                    }
                }
                
                // If less than half of the bricks are standing, consider that a victory
                if (standingBricks < bricks.length / 2) {
                    gameOver = true;
                    showMessage(`Victory! You knocked down the tower in ${shotsFired} shots!`);
                    console.log('%cGame over - Player won!', 'color: #43aa8b');
                } else if (shotsFired >= 5 && gameMode !== 'sandbox') {
                    // For levels, limit the shots to 5. If it is sandbox, there's no limit.
                    gameOver = true;
                    showMessage(`Game Over! You used all ${shotsFired} shots but the tower still stands!`);
                    console.log('%cGame over - Player lost!', 'color: #f94144');
                }
            } catch (error) {
                console.error('%cError checking game state:', 'color: #f94144', error);
            }
        }
        
        // Reset the game
        function resetGame() {
            try {
                console.log('%cResetting game...', 'color: #43aa8b');
                
                // Remove all bodies except ground
                const bodies = Matter.Composite.allBodies(world);
                for (let i = 0; i < bodies.length; i++) {
                    const body = bodies[i];
                    if (body !== ground) {
                        Matter.Composite.remove(world, body);
                    }
                }
                
                // Reset game state
                bricks = [];
                isDragging = false;
                selectedBody = null;
                mouseConstraint = null;
                cannonAngle = -Math.PI / 4;
                cannonPower = 0.1;
                isAiming = false;
                shotsFired = 0;
                gameOver = false;
                shakeIntensity = 0;
                
                // For sandbox, default to building. For levels, we skip to shooting after creation.
                if (gameMode === 'sandbox') {
                    gamePhase = 'building';
                    document.getElementById('phase-info').textContent = 'Building Phase';
                    document.getElementById('switch-phase-btn').textContent = 'Start Shooting';
                    document.getElementById('switch-phase-btn').style.display = 'inline-block';
                } else {
                    gamePhase = 'shooting';
                    document.getElementById('phase-info').textContent = 'Shooting Phase';
                    document.getElementById('switch-phase-btn').style.display = 'none';
                }
                
                // Hide any messages
                hideMessage();
                
                console.log('%cGame reset successfully!', 'color: #43aa8b');
            } catch (error) {
                console.error('%cError resetting game:', 'color: #f94144', error);
            }
        }
        
        // Show a message to the player
        function showMessage(text) {
            try {
                const messageElement = document.getElementById('game-message');
                messageElement.textContent = text;
                messageElement.style.display = 'block';
                
                // Animate the message in
                anime({
                    targets: '#game-message',
                    opacity: [0, 1],
                    scale: [0.8, 1],
                    duration: 500,
                    easing: 'easeOutElastic(1, .5)'
                });
            } catch (error) {
                console.error('%cError showing message:', 'color: #f94144', error);
            }
        }
        
        // Hide the message
        function hideMessage() {
            try {
                const messageElement = document.getElementById('game-message');
                
                // Animate the message out
                anime({
                    targets: '#game-message',
                    opacity: 0,
                    scale: 0.8,
                    duration: 300,
                    easing: 'easeInOutQuad',
                    complete: function() {
                        messageElement.style.display = 'none';
                    }
                });
            } catch (error) {
                console.error('%cError hiding message:', 'color: #f94144', error);
            }
        }
        
        // Show loading animation
        function showLoading() {
            try {
                const loadingElement = document.getElementById('loading');
                loadingElement.style.display = 'block';
                
                // Animate the loading circle
                anime({
                    targets: '.loading-circle',
                    rotate: '360deg',
                    duration: 1000,
                    loop: true,
                    easing: 'linear'
                });
            } catch (error) {
                console.error('%cError showing loading animation:', 'color: #f94144', error);
            }
        }
        
        // Hide loading animation
        function hideLoading() {
            try {
                const loadingElement = document.getElementById('loading');
                loadingElement.style.display = 'none';
            } catch (error) {
                console.error('%cError hiding loading animation:', 'color: #f94144', error);
            }
        }
        
        // Main game loop
        function gameLoop() {
            try {
                // Clear the canvas
                context.clearRect(0, 0, canvas.width, canvas.height);

                // Apply screen shake for collisions
                context.save();
                if (shakeIntensity > 0) {
                    const shakeX = (Math.random() - 0.5) * shakeIntensity;
                    const shakeY = (Math.random() - 0.5) * shakeIntensity;
                    context.translate(shakeX, shakeY);
                    // Gradually reduce intensity
                    shakeIntensity *= 0.9;
                    if (shakeIntensity < 0.5) shakeIntensity = 0;
                }
                
                // Draw the ground
                context.fillStyle = '#4a4a82';
                context.fillRect(
                    ground.bounds.min.x,
                    ground.bounds.min.y,
                    ground.bounds.max.x - ground.bounds.min.x,
                    ground.bounds.max.y - ground.bounds.min.y
                );
                
                // Draw all bricks
                for (let i = 0; i < bricks.length; i++) {
                    const brick = bricks[i];
                    drawBody(brick.body, brick.color);
                }
                
                // Draw the cannon if in shooting phase or if in building but we might have it created
                if (cannon && (gamePhase === 'shooting' || gameMode !== 'sandbox')) {
                    drawCannon();
                }
                
                // Draw the cannon ball if it exists
                if (cannonBall) {
                    drawBody(cannonBall, '#ffffff');
                }
                
                // Draw the constraint if dragging (sandbox building)
                if (isDragging && mouseConstraint) {
                    drawConstraint(mouseConstraint);
                }
                
                // Draw aiming line if aiming
                if (gamePhase === 'shooting' && isAiming) {
                    drawAimingLine();
                }

                context.restore();

                // Continue the game loop
                requestAnimationFrame(gameLoop);
            } catch (error) {
                console.error('%cError in game loop:', 'color: #f94144', error);
            }
        }
        
        // Draw a physics body
        function drawBody(body, color) {
            try {
                context.fillStyle = color;
                context.beginPath();
                
                const vertices = body.vertices;
                context.moveTo(vertices[0].x, vertices[0].y);
                
                for (let i = 1; i < vertices.length; i++) {
                    context.lineTo(vertices[i].x, vertices[i].y);
                }
                
                context.closePath();
                context.fill();
                
                // Draw outline
                context.strokeStyle = '#ffffff';
                context.lineWidth = 1;
                context.stroke();
            } catch (error) {
                console.error('%cError drawing body:', 'color: #f94144', error);
            }
        }
        
        // Draw the cannon (with a small scale animation based on power)
        function drawCannon() {
            try {
                let aimFactor = cannonPower / MAX_POWER; 
                
                context.save();
                context.translate(cannon.x, cannon.y);
                context.rotate(cannonAngle);
                
                // Scale cannon slightly based on how far we've pulled back (aimFactor)
                context.scale(1 + 0.2 * aimFactor, 1 + 0.2 * aimFactor);
                
                // Draw cannon barrel
                context.fillStyle = '#4a4a82';
                context.fillRect(0, -cannon.height / 2, cannon.width, cannon.height);
                
                // Draw cannon base
                context.beginPath();
                context.arc(0, 0, cannon.height, 0, Math.PI * 2);
                context.fillStyle = '#6a6aa8';
                context.fill();
                
                context.restore();
            } catch (error) {
                console.error('%cError drawing cannon:', 'color: #f94144', error);
            }
        }
        
        // Draw a constraint (for dragging bricks in sandbox)
        function drawConstraint(constraint) {
            try {
                context.beginPath();
                context.moveTo(constraint.pointA.x, constraint.pointA.y);
                context.lineTo(
                    constraint.bodyB.position.x,
                    constraint.bodyB.position.y
                );
                context.strokeStyle = 'rgba(255, 255, 255, 0.5)';
                context.lineWidth = 2;
                context.stroke();
            } catch (error) {
                console.error('%cError drawing constraint:', 'color: #f94144', error);
            }
        }
        
        // Draw the aiming line
        function drawAimingLine() {
            try {
                const cannonPos = getCannonPosition();
                const lineLength = cannonPower * 500;
                
                context.beginPath();
                context.moveTo(cannonPos.x, cannonPos.y);
                context.lineTo(
                    cannonPos.x + Math.cos(cannonAngle) * lineLength,
                    cannonPos.y + Math.sin(cannonAngle) * lineLength
                );
                context.strokeStyle = 'rgba(255, 255, 255, 0.7)';
                context.lineWidth = 2;
                context.setLineDash([5, 5]);
                context.stroke();
                context.setLineDash([]);
            } catch (error) {
                console.error('%cError drawing aiming line:', 'color: #f94144', error);
            }
        }
        
        // Handle window resize
        function handleResize() {
            try {
                console.log('%cResizing game area...', 'color: #43aa8b');
                const newWidth = window.innerWidth * 0.95;
                
                // Update canvas size
                canvas.width = newWidth;
                CANVAS_WIDTH = newWidth;
                
                // Update game container size
                document.getElementById('game-container').style.width = '95vw';
                
                // Update ground position
                if (ground) {
                    Matter.Body.setPosition(ground, {
                        x: newWidth / 2,
                        y: CANVAS_HEIGHT - GROUND_HEIGHT / 2
                    });
                }
                
                // Update right wall position
                const bodies = Matter.Composite.allBodies(world);
                for (let i = 0; i < bodies.length; i++) {
                    const body = bodies[i];
                    if (body !== ground && body.isStatic) {
                        // Check if it's the right wall by its x position
                        if (body.position.x > CANVAS_WIDTH / 2) {
                            Matter.Body.setPosition(body, {
                                x: newWidth - 10,
                                y: body.position.y
                            });
                        }
                    }
                }
                
                console.log('%cGame area resized successfully!', 'color: #43aa8b');
            } catch (error) {
                console.error('%cError resizing game area:', 'color: #f94144', error);
            }
        }
        
        // Initialize the game when the page loads
        window.addEventListener('load', function() {
            try {
                console.log('%cGame loading...', 'color: #43aa8b');
                showLoading();
                
                // Initialize the game after a short delay to show loading animation
                setTimeout(function() {
                    init();
                    hideLoading();
                }, 500);
            } catch (error) {
                console.error('%cError loading game:', 'color: #f94144', error);
                hideLoading();
                showMessage('Error loading game. Please refresh the page.');
            }
        });
    </script>
</body>
</html>
