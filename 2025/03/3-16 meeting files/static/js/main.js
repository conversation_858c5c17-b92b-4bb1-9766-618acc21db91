document.addEventListener('DOMContentLoaded', function() {
    // Form submission animation
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            // Create loading overlay
            const overlay = document.createElement('div');
            overlay.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            
            const loadingCard = document.createElement('div');
            loadingCard.className = 'card bg-base-100 shadow-xl p-8 max-w-md';
            
            const loadingTitle = document.createElement('h2');
            loadingTitle.className = 'text-xl font-bold text-center mb-6';
            loadingTitle.textContent = 'Analyzing PDF...';
            
            const loadingAnimation = document.createElement('div');
            loadingAnimation.className = 'loading-animation';
            
            // Create dots for animation
            for (let i = 0; i < 5; i++) {
                const dot = document.createElement('div');
                dot.className = 'loading-dot';
                loadingAnimation.appendChild(dot);
            }
            
            loadingCard.appendChild(loadingTitle);
            loadingCard.appendChild(loadingAnimation);
            overlay.appendChild(loadingCard);
            document.body.appendChild(overlay);
            
            // Animate the dots
            anime({
                targets: '.loading-dot',
                opacity: [0, 1, 0],
                scale: [0.8, 1.2, 0.8],
                delay: anime.stagger(200),
                loop: true,
                easing: 'easeInOutQuad'
            });
        });
    }
    
    // Animate elements on page load
    anime({
        targets: '.card',
        opacity: [0, 1],
        translateY: [20, 0],
        easing: 'easeOutQuad',
        duration: 800
    });
    
    // Animate headings
    anime({
        targets: 'h1, h2',
        opacity: [0, 1],
        translateY: [10, 0],
        delay: anime.stagger(100),
        easing: 'easeOutQuad',
        duration: 600
    });
    
    // Debug local storage
    console.log("Local storage available:", typeof localStorage !== 'undefined');
    console.log("History exists:", debugLocalStorage());
    
    // Check if we're on a result page
    if (document.getElementById('analysis-data')) {
        console.log("Analysis data element found, will save to history");
    }
});

function debugLocalStorage() {
    try {
        const history = JSON.parse(localStorage.getItem('pdfAnalysisHistory') || '[]');
        console.log("Current history in localStorage:", history);
        return history.length > 0;
    } catch (e) {
        console.error("Error accessing localStorage:", e);
        return false;
    }
} 