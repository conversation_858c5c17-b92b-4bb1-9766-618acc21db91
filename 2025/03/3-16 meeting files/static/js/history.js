// History management functions
function saveCurrentAnalysisToHistory() {
    const analysisData = document.getElementById('analysis-data');
    if (!analysisData) return;
    
    const id = analysisData.dataset.id;
    const source = analysisData.dataset.source;
    const timestamp = analysisData.dataset.timestamp;
    const question = analysisData.dataset.question;
    
    // Get key information points with proper selector
    const keyInfoPoints = [];
    document.querySelectorAll('.card.bg-base-200').forEach(card => {
        const infoElement = card.querySelector('.text-accent');
        if (!infoElement) return;
        
        const info = infoElement.textContent;
        
        // Find the importance text correctly - it's the text after "Why it's interesting:"
        const importanceContainer = card.querySelector('.text-sm.opacity-90');
        let importance = "";
        
        if (importanceContainer) {
            // Get the text content and remove the "Why it's interesting:" part
            const fullText = importanceContainer.textContent;
            const importanceMatch = fullText.match(/Why it's interesting:\s*(.*)/);
            importance = importanceMatch ? importanceMatch[1].trim() : "";
        }
        
        // Log for debugging
        console.log("Saving point:", { info, importance });
        
        keyInfoPoints.push({ info, importance });
    });
    
    // Get summary
    const summaryElement = document.querySelector('.prose');
    const summary = summaryElement ? summaryElement.innerHTML : "";
    
    // Create history item
    const historyItem = {
        id,
        source,
        timestamp,
        question,
        summary,
        keyInfoPoints
    };
    
    // Log what we're saving
    console.log("Saving to history:", historyItem);
    
    // Get existing history
    let history = JSON.parse(localStorage.getItem('pdfAnalysisHistory') || '[]');
    
    // Add new item to history (at the beginning)
    history.unshift(historyItem);
    
    // Limit history to 20 items
    if (history.length > 20) {
        history = history.slice(0, 20);
    }
    
    // Save updated history
    localStorage.setItem('pdfAnalysisHistory', JSON.stringify(history));
    
    // Update history display
    displayHistory();
}

function displayHistory() {
    const historyList = document.getElementById('history-list');
    const noHistory = document.getElementById('no-history');
    
    // Get history from localStorage
    let history = [];
    try {
        history = JSON.parse(localStorage.getItem('pdfAnalysisHistory') || '[]');
        console.log("Retrieved history:", history);
    } catch (e) {
        console.error("Error parsing history from localStorage:", e);
    }
    
    // Clear current list
    while (historyList.firstChild) {
        historyList.removeChild(historyList.firstChild);
    }
    
    // Show or hide "no history" message
    if (history.length === 0) {
        noHistory.style.display = 'block';
        historyList.appendChild(noHistory);
        return;
    } else {
        if (noHistory) {
            noHistory.style.display = 'none';
        }
    }
    
    // Add history items
    history.forEach(item => {
        const historyItem = document.createElement('div');
        historyItem.className = 'card bg-base-100 shadow-sm mb-2 hover:bg-base-200 cursor-pointer';
        historyItem.dataset.id = item.id;
        
        const cardBody = document.createElement('div');
        cardBody.className = 'card-body p-3';
        
        const title = document.createElement('h3');
        title.className = 'card-title text-sm';
        title.textContent = truncateText(item.source, 30);
        
        const timestamp = document.createElement('p');
        timestamp.className = 'text-xs opacity-70';
        timestamp.textContent = item.timestamp;
        
        // Add a preview of insights if available
        if (item.keyInfoPoints && item.keyInfoPoints.length > 0) {
            const insightsCount = document.createElement('div');
            insightsCount.className = 'badge badge-accent badge-sm mt-1';
            insightsCount.textContent = `${item.keyInfoPoints.length} insights`;
            cardBody.appendChild(insightsCount);
        }
        
        cardBody.appendChild(title);
        cardBody.appendChild(timestamp);
        historyItem.appendChild(cardBody);
        
        // Add click event to load this analysis
        historyItem.addEventListener('click', () => {
            displaySavedAnalysis(item);
        });
        
        historyList.appendChild(historyItem);
    });
}

function displaySavedAnalysis(item) {
    console.log("Displaying saved analysis:", item);
    
    // If we're on the index page, navigate to a special route to display the saved analysis
    if (window.location.pathname === '/') {
        // Create a form to post the analysis ID
        const form = document.createElement('form');
        form.method = 'post';
        form.action = '/view-history';
        form.style.display = 'none';
        
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'analysis_id';
        input.value = item.id;
        
        form.appendChild(input);
        document.body.appendChild(form);
        form.submit();
        return;
    }
    
    // If we're already on a result page, update the content
    document.querySelector('h1').textContent = 'PDF in Plain English (History)';
    
    // Update source and question
    const sourceEl = document.querySelector('.badge.badge-primary + p');
    if (sourceEl) sourceEl.textContent = item.source;
    
    const questionEl = document.querySelector('.badge.badge-secondary + p');
    if (questionEl) questionEl.textContent = item.question;
    
    // Update summary
    const summaryEl = document.querySelector('.prose');
    if (summaryEl) summaryEl.innerHTML = item.summary;
    
    // Update key info points
    const keyInfoContainer = document.querySelector('.space-y-4');
    if (keyInfoContainer) {
        // Clear current points
        while (keyInfoContainer.firstChild) {
            keyInfoContainer.removeChild(keyInfoContainer.firstChild);
        }
        
        // Add saved points
        if (item.keyInfoPoints && item.keyInfoPoints.length > 0) {
            item.keyInfoPoints.forEach(point => {
                const pointCard = document.createElement('div');
                pointCard.className = 'card bg-base-200 shadow-sm hover:shadow-md transition-shadow duration-300';
                
                const cardBody = document.createElement('div');
                cardBody.className = 'card-body p-4';
                
                const info = document.createElement('p');
                info.className = 'font-medium text-accent text-lg';
                info.textContent = point.info;
                
                cardBody.appendChild(info);
                
                if (point.importance) {
                    const importanceContainer = document.createElement('p');
                    importanceContainer.className = 'text-sm opacity-90 mt-2';
                    
                    const importanceLabel = document.createElement('span');
                    importanceLabel.className = 'font-bold';
                    importanceLabel.textContent = 'Why it\'s interesting: ';
                    
                    importanceContainer.appendChild(importanceLabel);
                    importanceContainer.appendChild(document.createTextNode(point.importance));
                    
                    cardBody.appendChild(importanceContainer);
                }
                
                pointCard.appendChild(cardBody);
                keyInfoContainer.appendChild(pointCard);
            });
        } else {
            const noPoints = document.createElement('p');
            noPoints.className = 'italic text-center';
            noPoints.textContent = 'No conversation insights were found in this saved analysis.';
            keyInfoContainer.appendChild(noPoints);
        }
    }
    
    // Close the drawer on mobile
    const drawer = document.getElementById('history-drawer');
    if (drawer) drawer.checked = false;
}

function truncateText(text, maxLength) {
    if (!text) return "Untitled";
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
}

// Clear history function
function clearHistory() {
    if (confirm('Are you sure you want to clear your analysis history?')) {
        localStorage.removeItem('pdfAnalysisHistory');
        displayHistory();
    }
}

// Initialize history display and event listeners
document.addEventListener('DOMContentLoaded', function() {
    displayHistory();
    
    const clearButton = document.getElementById('clear-history');
    if (clearButton) {
        clearButton.addEventListener('click', clearHistory);
    }
}); 