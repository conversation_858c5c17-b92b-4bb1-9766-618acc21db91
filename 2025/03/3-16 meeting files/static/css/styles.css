.loading-animation {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100px;
}

.loading-dot {
    width: 12px;
    height: 12px;
    margin: 0 5px;
    border-radius: 50%;
    background-color: hsl(var(--p));
    opacity: 0;
}

.prose {
    color: inherit;
    font-size: 1.05rem;
    line-height: 1.7;
}

.prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 {
    color: hsl(var(--p));
}

.prose a {
    color: hsl(var(--s));
}

.prose strong {
    color: hsl(var(--a));
}

.prose blockquote {
    border-left-color: hsl(var(--p));
    font-style: italic;
    color: hsl(var(--s));
}

.prose code {
    background-color: hsl(var(--b2));
    padding: 2px 4px;
    border-radius: 4px;
}

.prose pre {
    background-color: hsl(var(--b1));
}

.key-info-point {
    border-left: 4px solid hsl(var(--p));
    padding-left: 1rem;
    margin-bottom: 1rem;
}

.key-info-info {
    font-weight: 500;
    color: hsl(var(--s));
}

.key-info-importance {
    font-size: 0.9rem;
    margin-top: 0.5rem;
    opacity: 0.9;
}

/* New styles for conversational cards */
.card-body p {
    font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.card:hover {
    transform: translateY(-2px);
    transition: transform 0.3s ease;
}

/* Add styles for the history panel */
.drawer-side {
    border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.history-item {
    transition: all 0.2s ease;
}

.history-item:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

.history-item.active {
    background-color: rgba(var(--p), 0.1);
    border-left: 3px solid hsl(var(--p));
} 