<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Brick Shooter Game</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/matter-js/0.18.0/matter.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            background-color: #1a1a2e;
            color: #e6e6e6;
            font-family: Arial, sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
        }
        
        #game-container {
            position: relative;
            width: 95vw;
            height: 600px;
            border: 2px solid #4a4a82;
            border-radius: 8px;
            overflow: hidden;
        }
        
        canvas {
            background-color: #16213e;
        }
        
        #ui-container {
            position: absolute;
            top: 10px;
            left: 10px;
            z-index: 10;
            background-color: rgba(26, 26, 46, 0.7);
            padding: 10px;
            border-radius: 5px;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
        }
        
        #level-select-container {
            display: flex;
            gap: 5px;
            margin-bottom: 10px;
        }
        
        #level-select-container button.level-btn {
            background-color: #6a6aa8;
        }

        #level-select-container button.level-btn.selected {
            background-color: #f9c74f;
            color: #1a1a2e;
        }
        
        button {
            background-color: #4a4a82;
            color: white;
            border: none;
            padding: 8px 16px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        button:hover {
            background-color: #6a6aa8;
        }
        
        #loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            display: none;
            z-index: 20;
        }
        
        .loading-circle {
            width: 50px;
            height: 50px;
            border: 5px solid rgba(74, 74, 130, 0.3);
            border-top: 5px solid #4a4a82;
            border-radius: 50%;
        }
        
        #game-message {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: rgba(26, 26, 46, 0.9);
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            display: none;
            z-index: 15;
        }
        
        #instructions {
            margin-top: 20px;
            max-width: 95vw;
            background-color: rgba(26, 26, 46, 0.7);
            padding: 15px;
            border-radius: 8px;
        }
        
        .impact-effect {
            position: absolute;
            width: 20px;
            height: 20px;
            background-color: #f9c74f;
            border-radius: 50%;
            opacity: 0.7;
            pointer-events: none; /* Make sure it doesn't interfere with mouse events */
        }
    </style>
</head>
<body>
    <div id="game-container">
        <canvas id="game-canvas"></canvas>
        <div id="ui-container">
            <div id="level-select-container">
                <button class="level-btn selected" data-level="sandbox">Sandbox</button>
                <button class="level-btn" data-level="1">Level 1</button>
                <button class="level-btn" data-level="2">Level 2</button>
                <button class="level-btn" data-level="3">Level 3</button>
            </div>
            <button id="switch-phase-btn">Start Shooting</button>
            <button id="reset-btn">Reset Game</button>
            <div id="phase-info">Building Phase</div>
        </div>
        <div id="loading">
            <div class="loading-circle"></div>
        </div>
        <div id="game-message"></div>
    </div>
    
    <div id="instructions">
        <h2>How to Play</h2>
        <h3>Building Phase:</h3>
        <ul>
            <li>Click on the game area to create a new brick</li>
            <li>Drag bricks to position them</li>
            <li>Build a tower that can withstand shots</li>
        </ul>
        <h3>Shooting Phase:</h3>
        <ul>
            <li>Click and drag on the cannon to aim</li>
            <li>Release to fire</li>
            <li>Try to knock down the tower</li>
        </ul>
    </div>

    <script>
        // CONSTANTS AND CONFIGURATION
        const CANVAS_WIDTH = window.innerWidth * 0.95;
        const CANVAS_HEIGHT = 600;
        const GROUND_HEIGHT = 50;
        const BRICK_SIZE = 40;
        const CANNON_X = 100;
        const CANNON_Y = CANVAS_HEIGHT - GROUND_HEIGHT - 30;
        const CANNON_WIDTH = 60;
        const CANNON_HEIGHT = 30;
        const CANNON_BALL_RADIUS = 15;
        const CANNON_BALL_DENSITY = 0.02;
        const BRICK_DENSITY = 0.001;
        const MAX_POWER = 1.2;
        const BRICK_COLORS = ['#f94144', '#f3722c', '#f8961e', '#f9c74f', '#90be6d', '#43aa8b', '#577590'];

        // LEVELS CONFIGURATION
        const LEVELS = {
            'sandbox': [], // Sandbox mode, no predefined bricks
            '1': [ // Level 1: Simple pyramid
                { x: CANVAS_WIDTH / 2, y: CANVAS_HEIGHT - GROUND_HEIGHT - BRICK_SIZE / 2, cols: 3, rows: 1 },
                { x: CANVAS_WIDTH / 2, y: CANVAS_HEIGHT - GROUND_HEIGHT - BRICK_SIZE * 1.5, cols: 2, rows: 1 },
                { x: CANVAS_WIDTH / 2, y: CANVAS_HEIGHT - GROUND_HEIGHT - BRICK_SIZE * 2.5, cols: 1, rows: 1 }
            ],
            '2': [ // Level 2: Higher tower
                { x: CANVAS_WIDTH / 2, y: CANVAS_HEIGHT - GROUND_HEIGHT - BRICK_SIZE / 2, cols: 5, rows: 1 },
                { x: CANVAS_WIDTH / 2, y: CANVAS_HEIGHT - GROUND_HEIGHT - BRICK_SIZE * 1.5, cols: 4, rows: 1 },
                { x: CANVAS_WIDTH / 2, y: CANVAS_HEIGHT - GROUND_HEIGHT - BRICK_SIZE * 2.5, cols: 3, rows: 1 },
                { x: CANVAS_WIDTH / 2, y: CANVAS_HEIGHT - GROUND_HEIGHT - BRICK_SIZE * 3.5, cols: 2, rows: 1 },
                { x: CANVAS_WIDTH / 2, y: CANVAS_HEIGHT - GROUND_HEIGHT - BRICK_SIZE * 4.5, cols: 1, rows: 1 }
            ],
            '3': [ // Level 3: Offset tower
                { x: CANVAS_WIDTH / 2 - BRICK_SIZE / 2, y: CANVAS_HEIGHT - GROUND_HEIGHT - BRICK_SIZE / 2, cols: 3, rows: 1 },
                { x: CANVAS_WIDTH / 2 + BRICK_SIZE / 2, y: CANVAS_HEIGHT - GROUND_HEIGHT - BRICK_SIZE * 1.5, cols: 3, rows: 1 },
                { x: CANVAS_WIDTH / 2 - BRICK_SIZE / 2, y: CANVAS_HEIGHT - GROUND_HEIGHT - BRICK_SIZE * 2.5, cols: 3, rows: 1 }
            ]
        };

        let CURRENT_LEVEL = 'sandbox'; // Default level is sandbox
        
        // Game state
        let gamePhase = 'building'; // 'building' or 'shooting'
        let engine, render, world, canvas, context;
        let ground, cannon, cannonBall;
        let bricks = [];
        let isDragging = false;
        let selectedBody = null;
        let mouseConstraint = null;
        let cannonAngle = -Math.PI / 4;
        let cannonPower = 0.1;
        let dragStart = { x: 0, y: 0 };
        let isAiming = false;
        let shotsFired = 0;
        let gameOver = false;
        
        // Initialize the game
        function init() {
            try {
                console.log('%cInitializing game...', 'color: #43aa8b');
                
                // Setup Matter.js
                engine = Matter.Engine.create();
                world = engine.world;
                
                // Setup canvas
                canvas = document.getElementById('game-canvas');
                canvas.width = CANVAS_WIDTH;
                canvas.height = CANVAS_HEIGHT;
                context = canvas.getContext('2d');
                
                // Create ground
                ground = Matter.Bodies.rectangle(
                    CANVAS_WIDTH / 2,
                    CANVAS_HEIGHT - GROUND_HEIGHT / 2,
                    CANVAS_WIDTH,
                    GROUND_HEIGHT,
                    { isStatic: true }
                );
                
                // Create left wall to place cannon against
                const leftWall = Matter.Bodies.rectangle(
                    10,
                    CANVAS_HEIGHT / 2,
                    20,
                    CANVAS_HEIGHT,
                    { isStatic: true }
                );
                
                // Create right wall to prevent bricks from going off-screen
                const rightWall = Matter.Bodies.rectangle(
                    CANVAS_WIDTH - 10,
                    CANVAS_HEIGHT / 2,
                    20,
                    CANVAS_HEIGHT,
                    { isStatic: true }
                );
                
                // Add bodies to world
                Matter.Composite.add(world, [ground, leftWall, rightWall]);
                
                // Setup event listeners
                setupEventListeners();
                
                // Start the engine
                Matter.Runner.run(engine);
                
                // Start game loop
                requestAnimationFrame(gameLoop);
                
                console.log('%cGame initialized successfully!', 'color: #43aa8b');
            } catch (error) {
                console.error('%cError initializing game:', 'color: #f94144', error);
                showMessage('Error initializing game. Please refresh the page.');
            }
        }
        
        // Setup event listeners
        function setupEventListeners() {
            try {
                console.log('%cSetting up event listeners...', 'color: #43aa8b');
                
                // Canvas event listeners
                canvas.addEventListener('mousedown', handleMouseDown);
                canvas.addEventListener('mousemove', handleMouseMove);
                canvas.addEventListener('mouseup', handleMouseUp);
                
                // Button event listeners
                document.getElementById('switch-phase-btn').addEventListener('click', switchPhase);
                document.getElementById('reset-btn').addEventListener('click', resetGame);
                
                // Level select buttons
                const levelButtons = document.querySelectorAll('#level-select-container button.level-btn');
                levelButtons.forEach(button => {
                    button.addEventListener('click', function() {
                        const level = this.dataset.level;
                        CURRENT_LEVEL = level;
                        resetGame(); // Reset and load the selected level
                        levelButtons.forEach(btn => btn.classList.remove('selected')); // Deselect other buttons
                        this.classList.add('selected'); // Select current button
                    });
                });
                
                // Window resize listener
                window.addEventListener('resize', handleResize);
                
                console.log('%cEvent listeners set up successfully!', 'color: #43aa8b');
            } catch (error) {
                console.error('%cError setting up event listeners:', 'color: #f94144', error);
            }
        }
        
        // Handle mouse down event
        function handleMouseDown(event) {
            try {
                const rect = canvas.getBoundingClientRect();
                const mouseX = event.clientX - rect.left;
                const mouseY = event.clientY - rect.top;
                
                if (gamePhase === 'building') {
                    // Check if clicked on existing brick
                    const bodies = Matter.Composite.allBodies(world);
                    for (let i = 0; i < bodies.length; i++) {
                        const body = bodies[i];
                        if (body !== ground && Matter.Vertices.contains(body.vertices, { x: mouseX, y: mouseY })) {
                            selectedBody = body;
                            isDragging = true;
                            
                            // Create a constraint to drag the body
                            mouseConstraint = Matter.Constraint.create({
                                pointA: { x: mouseX, y: mouseY },
                                bodyB: selectedBody,
                                stiffness: 0.2
                            });
                            Matter.Composite.add(world, mouseConstraint);
                            return;
                        }
                    }
                    
                    // If not clicked on existing brick, create a new one
                    createBrick(mouseX, mouseY);
                } else if (gamePhase === 'shooting') {
                    // Check if clicked on cannon
                    const cannonPos = getCannonPosition();
                    const distance = Math.sqrt(
                        Math.pow(mouseX - cannonPos.x, 2) + 
                        Math.pow(mouseY - cannonPos.y, 2)
                    );
                    
                    if (distance < CANNON_WIDTH) {
                        isAiming = true;
                        dragStart = { x: mouseX, y: mouseY };
                    }
                }
            } catch (error) {
                console.error('%cError handling mouse down:', 'color: #f94144', error);
            }
        }
        
        // Handle mouse move event
        function handleMouseMove(event) {
            try {
                const rect = canvas.getBoundingClientRect();
                const mouseX = event.clientX - rect.left;
                const mouseY = event.clientY - rect.top;
                
                if (gamePhase === 'building' && isDragging && mouseConstraint) {
                    // Update the constraint point
                    mouseConstraint.pointA = { x: mouseX, y: mouseY };
                } else if (gamePhase === 'shooting' && isAiming) {
                    // Calculate angle and power based on drag
                    const dx = mouseX - dragStart.x;
                    const dy = mouseY - dragStart.y;
                    
                    // Update cannon angle
                    cannonAngle = Math.atan2(dy, dx);
                    
                    // Limit angle to reasonable range
                    cannonAngle = Math.max(Math.min(cannonAngle, 0), -Math.PI / 2);
                    
                    // Calculate power based on drag distance
                    const dragDistance = Math.sqrt(dx * dx + dy * dy);
                    cannonPower = Math.min(dragDistance / 200, MAX_POWER);
                }
            } catch (error) {
                console.error('%cError handling mouse move:', 'color: #f94144', error);
            }
        }
        
        // Handle mouse up event
        function handleMouseUp(event) {
            try {
                if (gamePhase === 'building' && isDragging) {
                    isDragging = false;
                    selectedBody = null;
                    
                    // Remove the constraint
                    if (mouseConstraint) {
                        Matter.Composite.remove(world, mouseConstraint);
                        mouseConstraint = null;
                    }
                } else if (gamePhase === 'shooting' && isAiming) {
                    fireCannon();
                    isAiming = false;
                }
            } catch (error) {
                console.error('%cError handling mouse up:', 'color: #f94144', error);
            }
        }
        
        // Create a new brick at the specified position, with option to start dragging immediately
        function createBrick(x, y, startDragging = true) {
            try {
                // Check if position overlaps with existing bricks
                const bodies = Matter.Composite.allBodies(world);
                for (let i = 0; i < bodies.length; i++) {
                    const body = bodies[i];
                    if (body !== ground) {
                        const bounds = body.bounds;
                        if (
                            x + BRICK_SIZE / 2 > bounds.min.x &&
                            x - BRICK_SIZE / 2 < bounds.max.x &&
                            y + BRICK_SIZE / 2 > bounds.min.y &&
                            y - BRICK_SIZE / 2 < bounds.max.y
                        ) {
                            console.log('%cCannot place brick: overlaps with existing brick', 'color: #f94144');
                            return;
                        }
                    }
                }
                
                // Create a new brick
                const colorIndex = bricks.length % BRICK_COLORS.length;
                const brick = Matter.Bodies.rectangle(
                    x,
                    y,
                    BRICK_SIZE,
                    BRICK_SIZE,
                    {
                        density: BRICK_DENSITY,
                        restitution: 0.1,
                        friction: 0.5,
                        render: {
                            fillStyle: BRICK_COLORS[colorIndex]
                        }
                    }
                );
                
                // Add the brick to the world and our array
                Matter.Composite.add(world, brick);
                bricks.push({
                    body: brick,
                    color: BRICK_COLORS[colorIndex]
                });
                
                console.log('%cBrick created at position:', 'color: #43aa8b', { x, y });
                
                if (startDragging) {
                    // Create a constraint to drag the newly created brick
                    selectedBody = brick;
                    isDragging = true;
                    mouseConstraint = Matter.Constraint.create({
                        pointA: { x, y },
                        bodyB: selectedBody,
                        stiffness: 0.2
                    });
                    Matter.Composite.add(world, mouseConstraint);
                }
            } catch (error) {
                console.error('%cError creating brick:', 'color: #f94144', error);
            }
        }
        
        // Switch between building and shooting phases
        function switchPhase() {
            try {
                if (gamePhase === 'building') {
                    if (bricks.length === 0) {
                        showMessage('Please build a tower first!');
                        return;
                    }
                    
                    gamePhase = 'shooting';
                    document.getElementById('phase-info').textContent = 'Shooting Phase';
                    document.getElementById('switch-phase-btn').textContent = 'Back to Building';
                    
                    // Create cannon
                    createCannon();
                    
                    console.log('%cSwitched to shooting phase', 'color: #43aa8b');
                } else {
                    gamePhase = 'building';
                    document.getElementById('phase-info').textContent = 'Building Phase';
                    document.getElementById('switch-phase-btn').textContent = 'Start Shooting';
                    
                    // Remove cannon ball if it exists
                    if (cannonBall) {
                        Matter.Composite.remove(world, cannonBall);
                        cannonBall = null;
                    }
                    
                    shotsFired = 0;
                    console.log('%cSwitched to building phase', 'color: #43aa8b');
                }
            } catch (error) {
                console.error('%cError switching phase:', 'color: #f94144', error);
            }
        }
        
        // Create the cannon
        function createCannon() {
            try {
                console.log('%cCreating cannon...', 'color: #43aa8b');
                cannon = {
                    x: CANNON_X,
                    y: CANNON_Y,
                    width: CANNON_WIDTH,
                    height: CANNON_HEIGHT
                };
            } catch (error) {
                console.error('%cError creating cannon:', 'color: #f94144', error);
            }
        }
        
        // Get the position of the cannon barrel end
        function getCannonPosition() {
            return {
                x: cannon.x + Math.cos(cannonAngle) * cannon.width,
                y: cannon.y + Math.sin(cannonAngle) * cannon.width
            };
        }
        
        // Fire the cannon
        function fireCannon() {
            try {
                if (gameOver) return;

                console.log('%cFiring cannon with power:', 'color: #43aa8b', cannonPower);

                // Create a cannon ball
                const cannonPos = getCannonPosition();
                cannonBall = Matter.Bodies.circle(
                    cannonPos.x,
                    cannonPos.y,
                    CANNON_BALL_RADIUS,
                    {
                        density: CANNON_BALL_DENSITY,
                        restitution: 0.3,
                        friction: 0.05,
                        frictionAir: 0.001,
                        render: {
                            fillStyle: '#ffffff' // Set fillStyle here so we can access it later
                        }
                    }
                );

                // Event listener for collision start to create impact effect
                Matter.Events.on(engine, 'collisionStart', function(event) {
                    event.pairs.forEach(pair => {
                        let bodyA = pair.bodyA;
                        let bodyB = pair.bodyB;

                        if (bodyA === cannonBall || bodyB === cannonBall) {
                            let brickBody = (bodyA !== cannonBall && bricks.some(b => b.body === bodyA)) ? bodyA :
                                            (bodyB !== cannonBall && bricks.some(b => b.body === bodyB)) ? bodyB : null;

                            if (brickBody) {
                                createImpactEffect(pair.collision.supports[0].x, pair.collision.supports[0].y, cannonBall.render.fillStyle);
                            }
                        }
                    });
                });


                // Apply force to the cannon ball
                const force = {
                    x: Math.cos(cannonAngle) * cannonPower,
                    y: Math.sin(cannonAngle) * cannonPower
                };

                Matter.Composite.add(world, cannonBall);
                Matter.Body.applyForce(cannonBall, cannonPos, force);

                shotsFired++;

                // Check if game is over after a delay
                setTimeout(checkGameState, 3000);
            } catch (error) {
                console.error('%cError firing cannon:', 'color: #f94144', error);
            }
        }

        // Create impact effect
        function createImpactEffect(x, y, color) {
            try {
                const effect = document.createElement('div');
                effect.className = 'impact-effect';
                effect.style.left = `${x - 10}px`; // Center effect on impact point
                effect.style.top = `${y - 10}px`;
                effect.style.backgroundColor = color; // Use brick color or cannonball color
                document.getElementById('game-container').appendChild(effect);

                anime({
                    targets: effect,
                    scale: [1, 2],
                    opacity: [0.7, 0],
                    duration: 600,
                    easing: 'easeOutQuad',
                    complete: function(anim) {
                        effect.remove(); // Remove effect after animation
                    }
                });
            } catch (error) {
                console.error('%cError creating impact effect:', 'color: #f94144', error);
            }
        }
        
        // Check if the game is over
        function checkGameState() {
            try {
                if (gamePhase !== 'shooting' || gameOver) return;
                
                // Count how many bricks are still standing (not fallen)
                let standingBricks = 0;
                for (let i = 0; i < bricks.length; i++) {
                    const brick = bricks[i].body;
                    // A brick is considered standing if it's not too tilted
                    const angle = Math.abs(brick.angle % (Math.PI * 2));
                    const isTilted = angle > Math.PI / 4 && angle < Math.PI * 7 / 4;
                    
                    if (!isTilted) {
                        standingBricks++;
                    }
                }
                
                // If less than half of the bricks are standing, player wins
                if (standingBricks < bricks.length / 2) {
                    gameOver = true;
                    showMessage(`Victory! You knocked down the tower in ${shotsFired} shots!`);
                    console.log('%cGame over - Player won!', 'color: #43aa8b');
                } else if (shotsFired >= 5) {
                    // Limit shots to make the game challenging
                    gameOver = true;
                    showMessage(`Game Over! You used all ${shotsFired} shots but the tower still stands!`);
                    console.log('%cGame over - Player lost!', 'color: #f94144');
                }
            } catch (error) {
                console.error('%cError checking game state:', 'color: #f94144', error);
            }
        }
        
        // Reset the game
        function resetGame() {
            try {
                console.log('%cResetting game...', 'color: #43aa8b');
                
                // Remove all bodies except ground
                const bodies = Matter.Composite.allBodies(world);
                for (let i = 0; i < bodies.length; i++) {
                    const body = bodies[i];
                    if (body !== ground) {
                        Matter.Composite.remove(world, body);
                    }
                }
                
                // Reset game state
                bricks = [];
                isDragging = false;
                selectedBody = null;
                mouseConstraint = null;
                cannonAngle = -Math.PI / 4;
                cannonPower = 0.1;
                isAiming = false;
                shotsFired = 0;
                gameOver = false;
                
                // Reset to building phase
                gamePhase = 'building';
                document.getElementById('phase-info').textContent = 'Building Phase';
                document.getElementById('switch-phase-btn').textContent = 'Start Shooting';
                
                // Hide any messages
                hideMessage();
                
                // Load level configuration
                if (CURRENT_LEVEL !== 'sandbox') {
                    loadLevel(CURRENT_LEVEL);
                }
                
                console.log('%cGame reset successfully!', 'color: #43aa8b');
            } catch (error) {
                console.error('%cError resetting game:', 'color: #f94144', error);
            }
        }
        
        // Load a level
        function loadLevel(levelKey) {
            try {
                console.log(`%cLoading level ${levelKey}...`, 'color: #43aa8b');
                const levelConfig = LEVELS[levelKey];

                if (!levelConfig) {
                    console.error(`%cLevel configuration for ${levelKey} not found!`, 'color: #f94144');
                    return;
                }

                bricks = []; // Clear existing bricks array

                levelConfig.forEach(config => {
                    const startX = config.x - (config.cols - 1) * BRICK_SIZE / 2; // Calculate starting x to center the group
                    for (let r = 0; r < config.rows; r++) {
                        for (let c = 0; c < config.cols; c++) {
                            createBrick(startX + c * BRICK_SIZE, config.y + r * BRICK_SIZE, false); // false to prevent dragging immediately
                        }
                    }
                });
                console.log(`%cLevel ${levelKey} loaded successfully with ${bricks.length} bricks.`, 'color: #43aa8b');
            }
            catch (error) {
                console.error(`%cError loading level ${levelKey}:`, 'color: #f94144', error);
            }
        }
        
        // Show a message to the player
        function showMessage(text) {
            try {
                const messageElement = document.getElementById('game-message');
                messageElement.textContent = text;
                messageElement.style.display = 'block';
                
                // Animate the message
                anime({
                    targets: '#game-message',
                    opacity: [0, 1],
                    scale: [0.8, 1],
                    duration: 500,
                    easing: 'easeOutElastic(1, .5)'
                });
            } catch (error) {
                console.error('%cError showing message:', 'color: #f94144', error);
            }
        }
        
        // Hide the message
        function hideMessage() {
            try {
                const messageElement = document.getElementById('game-message');
                
                // Animate the message out
                anime({
                    targets: '#game-message',
                    opacity: 0,
                    scale: 0.8,
                    duration: 300,
                    easing: 'easeInOutQuad',
                    complete: function() {
                        messageElement.style.display = 'none';
                    }
                });
            } catch (error) {
                console.error('%cError hiding message:', 'color: #f94144', error);
            }
        }
        
        // Show loading animation
        function showLoading() {
            try {
                const loadingElement = document.getElementById('loading');
                loadingElement.style.display = 'block';
                
                // Animate the loading circle
                anime({
                    targets: '.loading-circle',
                    rotate: '360deg',
                    duration: 1000,
                    loop: true,
                    easing: 'linear'
                });
            } catch (error) {
                console.error('%cError showing loading animation:', 'color: #f94144', error);
            }
        }
        
        // Hide loading animation
        function hideLoading() {
            try {
                const loadingElement = document.getElementById('loading');
                loadingElement.style.display = 'none';
            } catch (error) {
                console.error('%cError hiding loading animation:', 'color: #f94144', error);
            }
        }
        
        // Main game loop
        function gameLoop() {
            try {
                // Clear the canvas
                context.clearRect(0, 0, canvas.width, canvas.height);
                
                // Draw the ground
                context.fillStyle = '#4a4a82';
                context.fillRect(
                    ground.bounds.min.x,
                    ground.bounds.min.y,
                    ground.bounds.max.x - ground.bounds.min.x,
                    ground.bounds.max.y - ground.bounds.min.y
                );
                
                // Draw all bricks
                for (let i = 0; i < bricks.length; i++) {
                    const brick = bricks[i];
                    drawBody(brick.body, brick.color);
                }
                
                // Draw the cannon if in shooting phase
                if (gamePhase === 'shooting') {
                    drawCannon();
                }
                
                // Draw the cannon ball if it exists
                if (cannonBall) {
                    drawBody(cannonBall, '#ffffff');
                }
                
                // Draw the constraint if dragging
                if (isDragging && mouseConstraint) {
                    drawConstraint(mouseConstraint);
                }
                
                // Draw aiming line if aiming
                if (gamePhase === 'shooting' && isAiming) {
                    drawAimingLine();
                }
                
                // Continue the game loop
                requestAnimationFrame(gameLoop);
            } catch (error) {
                console.error('%cError in game loop:', 'color: #f94144', error);
            }
        }
        
        // Draw a physics body
        function drawBody(body, color) {
            try {
                context.fillStyle = color;
                context.beginPath();
                
                const vertices = body.vertices;
                context.moveTo(vertices[0].x, vertices[0].y);
                
                for (let i = 1; i < vertices.length; i++) {
                    context.lineTo(vertices[i].x, vertices[i].y);
                }
                
                context.closePath();
                context.fill();
                
                // Draw outline
                context.strokeStyle = '#ffffff';
                context.lineWidth = 1;
                context.stroke();
            } catch (error) {
                console.error('%cError drawing body:', 'color: #f94144', error);
            }
        }
        
        // Draw the cannon
        function drawCannon() {
            try {
                context.save();
                context.translate(cannon.x, cannon.y);
                context.rotate(cannonAngle);
                
                // Power animation effect
                const powerScale = 1 + cannonPower * 0.5; // Scale cannon based on power
                context.scale(powerScale, powerScale);
                
                // Draw cannon barrel
                context.fillStyle = '#4a4a82';
                context.fillRect(0, -cannon.height / 2, cannon.width, cannon.height);
                
                // Draw cannon base
                context.beginPath();
                context.arc(0, 0, cannon.height, 0, Math.PI * 2);
                context.fillStyle = '#6a6aa8';
                context.fill();
                
                context.restore();
            } catch (error) {
                console.error('%cError drawing cannon:', 'color: #f94144', error);
            }
        }
        
        // Draw a constraint
        function drawConstraint(constraint) {
            try {
                context.beginPath();
                context.moveTo(constraint.pointA.x, constraint.pointA.y);
                context.lineTo(
                    constraint.bodyB.position.x,
                    constraint.bodyB.position.y
                );
                context.strokeStyle = 'rgba(255, 255, 255, 0.5)';
                context.lineWidth = 2;
                context.stroke();
            } catch (error) {
                console.error('%cError drawing constraint:', 'color: #f94144', error);
            }
        }
        
        // Draw the aiming line
        function drawAimingLine() {
            try {
                const cannonPos = getCannonPosition();
                const lineLength = cannonPower * 500;
                
                context.beginPath();
                context.moveTo(cannonPos.x, cannonPos.y);
                context.lineTo(
                    cannonPos.x + Math.cos(cannonAngle) * lineLength,
                    cannonPos.y + Math.sin(cannonAngle) * lineLength
                );
                context.strokeStyle = 'rgba(255, 255, 255, 0.7)';
                context.lineWidth = 2;
                context.setLineDash([5, 5]);
                context.stroke();
                context.setLineDash([]);
            } catch (error) {
                console.error('%cError drawing aiming line:', 'color: #f94144', error);
            }
        }
        
        // Handle window resize
        function handleResize() {
            try {
                console.log('%cResizing game area...', 'color: #43aa8b');
                const newWidth = window.innerWidth * 0.95;
                
                // Update canvas size
                canvas.width = newWidth;
                CANVAS_WIDTH = newWidth;
                
                // Update game container size
                document.getElementById('game-container').style.width = '95vw';
                
                // Update ground position
                if (ground) {
                    Matter.Body.setPosition(ground, {
                        x: newWidth / 2,
                        y: CANVAS_HEIGHT - GROUND_HEIGHT / 2
                    });
                }
                
                // Update right wall position
                const bodies = Matter.Composite.allBodies(world);
                for (let i = 0; i < bodies.length; i++) {
                    const body = bodies[i];
                    if (body !== ground && body.isStatic) {
                        // Check if it's the right wall by its x position
                        if (body.position.x > CANVAS_WIDTH / 2) {
                            Matter.Body.setPosition(body, {
                                x: newWidth - 10,
                                y: body.position.y
                            });
                        }
                    }
                }
                
                console.log('%cGame area resized successfully!', 'color: #43aa8b');
            } catch (error) {
                console.error('%cError resizing game area:', 'color: #f94144', error);
            }
        }
        
        // Initialize the game when the page loads
        window.addEventListener('load', function() {
            try {
                console.log('%cGame loading...', 'color: #43aa8b');
                showLoading();
                
                // Initialize the game after a short delay to show loading animation
                setTimeout(function() {
                    init();
                    hideLoading();
                }, 500);
            } catch (error) {
                console.error('%cError loading game:', 'color: #f94144', error);
                hideLoading();
                showMessage('Error loading game. Please refresh the page.');
            }
        });
    </script>
</body>
</html> 