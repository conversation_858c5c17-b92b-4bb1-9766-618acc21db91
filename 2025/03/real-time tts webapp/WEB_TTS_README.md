# Real-Time Text-to-Speech Web Application

A modern web application that converts text to speech in real-time using OpenAI's TTS API, featuring both streaming playback and MP3 downloads.

## Features

- **Real-time Audio Streaming**: Convert text to speech and hear it instantly
- **MP3 Download Option**: Save generated speech as MP3 files
- **Multiple Voice Options**: Choose from various OpenAI voices:
  - Alloy
  - Echo 
  - Fable
  - Onyx
  - Nova
  - Shimmer
- **Visual Feedback**: Animated sound wave during audio generation/playback
- **Dark Mode UI**: Clean, modern interface using DaisyUI and Tailwind CSS
- **Responsive Design**: Works on desktop and mobile devices

## Technical Stack

- **Backend**: FastAPI (Python)
- **Frontend**: HTML5, JavaScript
- **Audio Processing**: Web Audio API
- **Styling**: 
  - Tailwind CSS
  - DaisyUI
  - Anime.js for animations
- **TTS Engine**: OpenAI API

## Requirements

```bash
openai
fastapi
uvicorn
python-multipart
termcolor
```

## Setup & Installation

1. Set your OpenAI API key:
```bash
export OPENAI_API_KEY='your-api-key-here'
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Run the application:
```bash
python main.py
```

4. Access the web interface: 