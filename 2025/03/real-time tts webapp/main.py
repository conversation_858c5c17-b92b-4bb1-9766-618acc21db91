import os
import io
import uvicorn
from fastapi import FastAP<PERSON>, Request, Form, Response
from fastapi.responses import HTMLResponse, StreamingResponse, FileResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from openai import OpenAI
from termcolor import colored
import asyncio

# Constants
MODEL = "gpt-4o-mini-tts"
VOICE = "alloy"
OUTPUT_DIR = "outputs"
SAMPLE_RATE = 24000

# Ensure output directory exists
os.makedirs(OUTPUT_DIR, exist_ok=True)

# Initialize FastAPI app
app = FastAPI(title="TTS Streaming and Download")

# Initialize OpenAI client
try:
    client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
    print(colored("OpenAI client initialized successfully", "green"))
except Exception as e:
    print(colored(f"Error initializing OpenAI client: {str(e)}", "red"))

# Setup static file serving
app.mount("/static", StaticFiles(directory="."), name="static")

@app.get("/", response_class=HTMLResponse)
async def get_index():
    """Serve the HTML interface"""
    with open("index.html", encoding="utf-8") as f:
        html_content = f.read()
    return HTMLResponse(content=html_content)

@app.post("/generate-mp3")
async def generate_mp3(text: str = Form(...)):
    """Generate MP3 file and return it for download"""
    try:
        print(colored(f"Generating MP3 for text: {text[:50]}...", "cyan"))
        
        # Generate unique filename
        filename = f"{OUTPUT_DIR}/tts_{hash(text) & 0xffffffff}.mp3"
        
        # Generate MP3 file
        with client.audio.speech.with_streaming_response.create(
            model=MODEL,
            voice=VOICE,
            input=text,
            response_format="mp3"
        ) as response:
            with open(filename, "wb") as f:
                for chunk in response.iter_bytes():
                    f.write(chunk)
        
        print(colored(f"MP3 generated successfully: {filename}", "green"))
        
        # Return file for download
        return FileResponse(
            path=filename, 
            filename="tts_output.mp3", 
            media_type="audio/mpeg"
        )
    
    except Exception as e:
        print(colored(f"Error generating MP3: {str(e)}", "red"))
        return {"error": str(e)}

@app.post("/stream-audio")
async def stream_audio(text: str = Form(...)):
    """Stream PCM audio for real-time playback"""
    try:
        print(colored(f"Streaming audio for text: {text[:50]}...", "cyan"))
        
        async def generate():
            with client.audio.speech.with_streaming_response.create(
                model=MODEL,
                voice=VOICE,
                input=text,
                response_format="pcm"
            ) as response:
                for chunk in response.iter_bytes():
                    yield chunk
                    await asyncio.sleep(0.01)  # Small delay to prevent overwhelming the client
        
        return StreamingResponse(
            generate(),
            media_type="audio/pcm",
            headers={
                "Content-Disposition": "inline",
                "X-Sample-Rate": str(SAMPLE_RATE)
            }
        )
    
    except Exception as e:
        print(colored(f"Error streaming audio: {str(e)}", "red"))
        return {"error": str(e)}

if __name__ == "__main__":
    print(colored("Starting FastAPI TTS application on http://127.0.0.1:8000", "yellow"))
    uvicorn.run("main:app", host="127.0.0.1", port=8000, reload=True) 