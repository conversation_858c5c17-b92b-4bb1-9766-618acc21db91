// Global constants and variables
const SCENE_BACKGROUND_COLOR = 0x111827;
const BRICK_COLOR = 0x6419e6;
const FLOOR_COLOR = 0x1f2937;
const LIGHT_COLOR = 0xffffff;
const BRICK_SIZE = 1;
const BRICK_MASS = 1;
const TOWER_HEIGHT = 10;
const TOWER_WIDTH = 5;
const FLOOR_SIZE = 50;
const FORCE_MULTIPLIER = 15;

// Three.js variables
let scene, camera, renderer, controls;
let physicsWorld;
let meshes = [];
let bodies = [];
let tmpVector = new THREE.Vector3();
let raycaster = new THREE.Raycaster();
let mouse = new THREE.Vector2();
let clock = new THREE.Clock();
let loadingElement, statusElement;

// Initialize the application
function init() {
    console.log('%cInitializing 3D scene and physics...', 'color:cyan');
    loadingElement = document.getElementById('loading');
    statusElement = document.getElementById('status');
    updateStatus('Initializing...');

    try {
        initThree();
        initCannon();
        createEnvironment();
        createTower();
        setupEventListeners();
        animate();
        
        // Hide loading screen
        setTimeout(() => {
            anime({
                targets: loadingElement,
                opacity: 0,
                duration: 1000,
                easing: 'easeOutQuad',
                complete: function() {
                    loadingElement.style.display = 'none';
                }
            });
            updateStatus('Ready - Click on the tower to apply force');
        }, 1000);
    } catch (error) {
        console.error('%cInitialization error:', 'color:red', error);
        updateStatus('Error: ' + error.message, 'error');
    }
}

// Initialize Three.js
function initThree() {
    console.log('%cSetting up Three.js...', 'color:cyan');
    // Create scene
    scene = new THREE.Scene();
    scene.background = new THREE.Color(SCENE_BACKGROUND_COLOR);

    // Create camera
    camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    camera.position.set(0, 15, 20);
    camera.lookAt(0, 0, 0);

    // Create renderer
    renderer = new THREE.WebGLRenderer({ antialias: true });
    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.shadowMap.enabled = true;
    renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    document.getElementById('container').appendChild(renderer.domElement);

    // Create Orbit Controls - Important: use the right syntax to avoid the error
    try {
        controls = new THREE.OrbitControls(camera, renderer.domElement);
        controls.enableDamping = true;
        controls.dampingFactor = 0.05;
        controls.screenSpacePanning = false;
        controls.minDistance = 10;
        controls.maxDistance = 50;
        controls.maxPolarAngle = Math.PI / 2;
        console.log('%cOrbit Controls initialized successfully', 'color:green');
    } catch (error) {
        console.error('%cError initializing Orbit Controls:', 'color:red', error);
        updateStatus('Error with controls: ' + error.message, 'error');
    }

    // Lighting
    setupLighting();

    // Handle window resize
    window.addEventListener('resize', onWindowResize, false);
}

// Initialize Cannon.js physics
function initCannon() {
    console.log('%cSetting up physics engine...', 'color:cyan');
    physicsWorld = new CANNON.World({
        gravity: new CANNON.Vec3(0, -9.82, 0)
    });
    physicsWorld.broadphase = new CANNON.SAPBroadphase(physicsWorld);
    physicsWorld.allowSleep = true;
    physicsWorld.defaultContactMaterial.friction = 0.3;
    physicsWorld.defaultContactMaterial.restitution = 0.2;
}

// Set up the lighting for the scene
function setupLighting() {
    // Ambient light
    const ambientLight = new THREE.AmbientLight(LIGHT_COLOR, 0.4);
    scene.add(ambientLight);

    // Directional light (for shadows)
    const directionalLight = new THREE.DirectionalLight(LIGHT_COLOR, 0.8);
    directionalLight.position.set(10, 20, 10);
    directionalLight.castShadow = true;
    directionalLight.shadow.mapSize.width = 2048;
    directionalLight.shadow.mapSize.height = 2048;
    directionalLight.shadow.camera.near = 0.5;
    directionalLight.shadow.camera.far = 50;
    directionalLight.shadow.camera.left = -25;
    directionalLight.shadow.camera.right = 25;
    directionalLight.shadow.camera.top = 25;
    directionalLight.shadow.camera.bottom = -25;
    scene.add(directionalLight);

    // Add a point light to enhance the visuals
    const pointLight = new THREE.PointLight(0x6419e6, 1, 50);
    pointLight.position.set(0, 15, 0);
    scene.add(pointLight);
}

// Create the environment (floor)
function createEnvironment() {
    console.log('%cCreating environment...', 'color:cyan');
    // Create floor
    const floorGeometry = new THREE.PlaneGeometry(FLOOR_SIZE, FLOOR_SIZE);
    const floorMaterial = new THREE.MeshStandardMaterial({ 
        color: FLOOR_COLOR,
        roughness: 0.7,
        metalness: 0.2
    });
    const floor = new THREE.Mesh(floorGeometry, floorMaterial);
    floor.rotation.x = -Math.PI / 2;
    floor.receiveShadow = true;
    scene.add(floor);

    // Create physics body for floor
    const floorBody = new CANNON.Body({
        type: CANNON.Body.STATIC,
        shape: new CANNON.Plane()
    });
    floorBody.quaternion.setFromEuler(-Math.PI / 2, 0, 0);
    physicsWorld.addBody(floorBody);
}

// Create the tower of bricks
function createTower() {
    console.log('%cBuilding tower...', 'color:cyan');
    const brickGeometry = new THREE.BoxGeometry(BRICK_SIZE, BRICK_SIZE, BRICK_SIZE);
    const brickMaterial = new THREE.MeshStandardMaterial({
        color: BRICK_COLOR,
        roughness: 0.5,
        metalness: 0.5
    });

    // Physics material for the bricks
    const brickPhysMaterial = new CANNON.Material();

    // Create a tower in a U shape
    const startX = -TOWER_WIDTH / 2;
    const startZ = -TOWER_WIDTH / 2;

    for (let y = 0; y < TOWER_HEIGHT; y++) {
        // Alternate direction of bricks for stability
        const isEvenLayer = y % 2 === 0;
        
        if (isEvenLayer) {
            // Place bricks along Z-axis for bottom wall
            for (let z = 0; z < TOWER_WIDTH; z++) {
                createBrick(startX, y * BRICK_SIZE, startZ + z * BRICK_SIZE, brickGeometry, brickMaterial, brickPhysMaterial);
            }
            
            // Place bricks along X-axis for left wall
            for (let x = 0; x < TOWER_WIDTH; x++) {
                // Skip the corner brick that's already placed
                if (x > 0) {
                    createBrick(startX + x * BRICK_SIZE, y * BRICK_SIZE, startZ, brickGeometry, brickMaterial, brickPhysMaterial);
                }
            }
            
            // Place bricks along X-axis for right wall
            for (let x = 0; x < TOWER_WIDTH; x++) {
                // Skip the corner brick that's already placed
                if (x > 0) {
                    createBrick(startX + x * BRICK_SIZE, y * BRICK_SIZE, startZ + (TOWER_WIDTH - 1) * BRICK_SIZE, brickGeometry, brickMaterial, brickPhysMaterial);
                }
            }
        } else {
            // Bottom wall
            for (let x = 0; x < TOWER_WIDTH; x++) {
                createBrick(startX + x * BRICK_SIZE, y * BRICK_SIZE, startZ, brickGeometry, brickMaterial, brickPhysMaterial);
            }
            
            // Left wall
            for (let z = 1; z < TOWER_WIDTH; z++) {
                createBrick(startX, y * BRICK_SIZE, startZ + z * BRICK_SIZE, brickGeometry, brickMaterial, brickPhysMaterial);
            }
            
            // Right wall
            for (let z = 1; z < TOWER_WIDTH; z++) {
                createBrick(startX + (TOWER_WIDTH - 1) * BRICK_SIZE, y * BRICK_SIZE, startZ + z * BRICK_SIZE, brickGeometry, brickMaterial, brickPhysMaterial);
            }
        }
    }
}

// Create a single brick with physics
function createBrick(x, y, z, geometry, material, physicsMaterial) {
    // Create Three.js mesh
    const mesh = new THREE.Mesh(geometry, material);
    mesh.position.set(x, y, z);
    mesh.castShadow = true;
    mesh.receiveShadow = true;
    scene.add(mesh);
    meshes.push(mesh);

    // Create Cannon.js body
    const shape = new CANNON.Box(new CANNON.Vec3(BRICK_SIZE/2, BRICK_SIZE/2, BRICK_SIZE/2));
    const body = new CANNON.Body({
        mass: BRICK_MASS,
        position: new CANNON.Vec3(x, y, z),
        shape: shape,
        material: physicsMaterial,
        allowSleep: true,
        sleepSpeedLimit: 0.1,
        sleepTimeLimit: 1
    });
    physicsWorld.addBody(body);
    bodies.push(body);

    // Link the mesh to the body for updating later
    mesh.userData.physicsBody = body;
}

// Mouse click handler to apply force to the clicked brick
function onMouseClick(event) {
    console.log('%cMouse click detected', 'color:cyan');
    
    // Calculate mouse position in normalized device coordinates
    mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
    mouse.y = - (event.clientY / window.innerHeight) * 2 + 1;
    
    // Update the raycaster
    raycaster.setFromCamera(mouse, camera);
    
    // Check for intersections with the meshes
    const intersects = raycaster.intersectObjects(meshes);
    
    if (intersects.length > 0) {
        const clickedMesh = intersects[0].object;
        const physicsBody = clickedMesh.userData.physicsBody;
        
        if (physicsBody) {
            console.log('%cApplying force to brick', 'color:green');
            updateStatus('Force applied!');
            
            // Calculate force direction from camera to intersection point
            const forceDirection = new THREE.Vector3()
                .subVectors(intersects[0].point, camera.position)
                .normalize();
            
            // Convert Three.js vector to Cannon.js vector
            const force = new CANNON.Vec3(
                forceDirection.x * FORCE_MULTIPLIER,
                forceDirection.y * FORCE_MULTIPLIER,
                forceDirection.z * FORCE_MULTIPLIER
            );
            
            // Apply the force at the point of intersection
            const worldPoint = new CANNON.Vec3(
                intersects[0].point.x,
                intersects[0].point.y,
                intersects[0].point.z
            );
            
            physicsBody.applyForce(force, worldPoint);
            
            // Visual effect for impact
            createImpactEffect(intersects[0].point);
        }
    }
}

// Create a visual effect at the impact point
function createImpactEffect(position) {
    const effect = document.createElement('div');
    effect.className = 'impact-effect';
    effect.style.left = event.clientX + 'px';
    effect.style.top = event.clientY + 'px';
    document.body.appendChild(effect);
    
    anime({
        targets: effect,
        opacity: [0, 0.7, 0],
        scale: [0.5, 1.5],
        duration: 500,
        easing: 'easeOutQuad',
        complete: function() {
            document.body.removeChild(effect);
        }
    });
}

// Update status message
function updateStatus(message, type = 'info') {
    if (statusElement) {
        statusElement.textContent = 'Status: ' + message;
        statusElement.className = 'mt-2 text-sm';
        
        if (type === 'error') {
            statusElement.classList.add('text-red-500');
        } else {
            statusElement.classList.add('text-green-500');
        }
    }
}

// Handle window resize
function onWindowResize() {
    camera.aspect = window.innerWidth / window.innerHeight;
    camera.updateProjectionMatrix();
    renderer.setSize(window.innerWidth, window.innerHeight);
}

// Setup event listeners
function setupEventListeners() {
    console.log('%cSetting up event listeners...', 'color:cyan');
    window.addEventListener('click', onMouseClick, false);
}

// Animation loop
function animate() {
    requestAnimationFrame(animate);
    
    const deltaTime = Math.min(clock.getDelta(), 0.1);
    
    // Update physics
    physicsWorld.step(1/60, deltaTime);
    
    // Update mesh positions based on physics bodies
    for (let i = 0; i < meshes.length; i++) {
        const mesh = meshes[i];
        const body = bodies[i];
        mesh.position.copy(body.position);
        mesh.quaternion.copy(body.quaternion);
    }
    
    // Update controls
    if (controls) {
        controls.update();
    }
    
    // Render scene
    renderer.render(scene, camera);
}

// Start the application when the page is loaded
window.addEventListener('load', init); 