<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Three.js Basics</title>
    <style>
        body { 
            margin: 0; 
            overflow: hidden; 
            background-color: #1a1a1a;
            color: #fff;
            font-family: Arial, sans-serif;
        }
        canvas { 
            display: block; 
        }
        .info {
            position: absolute;
            top: 20px;
            left: 20px;
            background-color: rgba(0, 0, 0, 0.7);
            padding: 15px;
            border-radius: 5px;
            max-width: 300px;
        }
        h1 {
            margin-top: 0;
            color: #4ecca3;
        }
        .controls {
            position: absolute;
            top: 20px;
            right: 20px;
            background-color: rgba(0, 0, 0, 0.7);
            padding: 15px;
            border-radius: 5px;
            width: 250px;
        }
        .control-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            color: #4ecca3;
        }
        input[type="range"], 
        select {
            width: 100%;
            background: #333;
            color: white;
            border: none;
            padding: 5px;
            border-radius: 3px;
        }
        button {
            background: #4ecca3;
            color: #1a1a1a;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 5px;
            font-weight: bold;
        }
        button:hover {
            background: #3aa787;
        }
        .status {
            font-size: 12px;
            margin-top: 15px;
            color: #aaa;
        }
    </style>
</head>
<body>
    <div class="info">
        <h1>Three.js Basics</h1>
        <p>This is an interactive 3D scene created with Three.js.</p>
        <p><strong>Controls:</strong></p>
        <ul>
            <li>Left-click + drag: Rotate view</li>
            <li>Right-click + drag: Pan view</li>
            <li>Scroll: Zoom in/out</li>
            <li>Click on cube: Toggle wireframe</li>
        </ul>
    </div>

    <div class="controls">
        <h2 style="color: #4ecca3; margin-top: 0;">Cube Controls</h2>
        
        <div class="control-group">
            <label for="color">Color:</label>
            <select id="color">
                <option value="0x4ecca3">Teal</option>
                <option value="0xff5252">Red</option>
                <option value="0x42a5f5">Blue</option>
                <option value="0xffca28">Yellow</option>
                <option value="0x9c27b0">Purple</option>
            </select>
        </div>
        
        <div class="control-group">
            <label for="speed">Rotation Speed:</label>
            <input type="range" id="speed" min="0" max="0.1" step="0.001" value="0.01">
        </div>
        
        <div class="control-group">
            <label for="size">Size:</label>
            <input type="range" id="size" min="0.5" max="3" step="0.1" value="1">
        </div>
        
        <div class="control-group">
            <button id="geometry">Change Shape</button>
            <button id="reset">Reset Scene</button>
        </div>
        
        <div class="status">Try clicking directly on the cube!</div>
    </div>

    <!-- Import Three.js from CDN -->
    <script src="https://cdn.jsdelivr.net/npm/three@0.160.0/build/three.min.js"></script>
    <!-- Import OrbitControls correctly -->
    <script src="https://cdn.jsdelivr.net/npm/three@0.160.0/examples/jsm/controls/OrbitControls.js"></script>
    
    <script>
        // -------- GLOBAL VARIABLES --------
        const COLORS = {
            TEAL: 0x4ecca3,
            BACKGROUND: 0x2a2a2a,
            AMBIENT_LIGHT: 0x404040,
            DIRECTIONAL_LIGHT: 0xffffff
        };
        
        const INITIAL_ROTATION_SPEED = 0.01;
        const INITIAL_SIZE = 1;
        let rotationSpeed = INITIAL_ROTATION_SPEED;
        let autoRotate = true;
        
        // Import the necessary color utility for console output
        console.log("%cThree.js Demo Starting...", "color: #4ecca3; font-weight: bold; font-size: 16px;");
        
        // -------- SETUP: Create the basic Three.js components --------
        
        // Create a scene - this is the container for all objects, cameras, and lights
        const scene = new THREE.Scene();
        console.log("%cScene created", "color: #4ecca3");
        
        // Create a camera - this determines what we see
        // Parameters: field of view (degrees), aspect ratio, near clipping plane, far clipping plane
        const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        camera.position.z = 5; // Move camera back so we can see the scene
        console.log("%cCamera created and positioned", "color: #4ecca3");
        
        // Create a renderer - this draws what the camera sees onto a canvas
        const renderer = new THREE.WebGLRenderer({ antialias: true });
        renderer.setSize(window.innerWidth, window.innerHeight);
        renderer.setClearColor(COLORS.BACKGROUND); // Set background color
        document.body.appendChild(renderer.domElement); // Add the canvas to the page
        console.log("%cRenderer created and added to page", "color: #4ecca3");
        
        // Add OrbitControls to allow camera movement with mouse/touch
        // Fix: Use OrbitControls from the correct module path
        try {
            // For newer Three.js versions using ES modules
            const OrbitControls = THREE.OrbitControls;
            const controls = new OrbitControls(camera, renderer.domElement);
            controls.enableDamping = true; // Add smooth dampening effect
            controls.dampingFactor = 0.05;
            console.log("%cOrbit controls added", "color: #4ecca3");
        } catch (error) {
            console.error("%cError initializing OrbitControls: " + error.message, "color: red");
            console.log("%cFalling back to basic controls", "color: orange");
            // We'll continue without orbit controls if they fail to initialize
        }
        
        // -------- OBJECT: Create a simple cube --------
        
        // Create a geometry (the shape/vertices)
        let geometry = new THREE.BoxGeometry(INITIAL_SIZE, INITIAL_SIZE, INITIAL_SIZE);
        
        // Create a material (the appearance/color)
        const material = new THREE.MeshPhongMaterial({ 
            color: COLORS.TEAL,
            shininess: 100,
            flatShading: true
        });
        
        // Create a mesh by combining geometry and material
        const cube = new THREE.Mesh(geometry, material);
        
        // Add the cube to the scene
        scene.add(cube);
        console.log("%cCube created and added to scene", "color: #4ecca3");
        
        // -------- LIGHTING: Add some lights so we can see --------
        
        // Add ambient light (lights all objects equally)
        const ambientLight = new THREE.AmbientLight(COLORS.AMBIENT_LIGHT);
        scene.add(ambientLight);
        
        // Add directional light (comes from a specific direction)
        const directionalLight = new THREE.DirectionalLight(COLORS.DIRECTIONAL_LIGHT, 1);
        directionalLight.position.set(1, 1, 1);
        scene.add(directionalLight);
        console.log("%cLights added to scene", "color: #4ecca3");
        
        // -------- USER INTERACTION: Setup event listeners and controls --------
        
        // Color selector
        document.getElementById('color').addEventListener('change', function(event) {
            material.color.setHex(parseInt(event.target.value));
            console.log(`%cChanged color to ${event.target.value}`, "color: #4ecca3");
        });
        
        // Speed control
        document.getElementById('speed').addEventListener('input', function(event) {
            rotationSpeed = parseFloat(event.target.value);
            console.log(`%cChanged rotation speed to ${rotationSpeed}`, "color: #4ecca3");
        });
        
        // Size control
        document.getElementById('size').addEventListener('input', function(event) {
            const newSize = parseFloat(event.target.value);
            cube.scale.set(newSize, newSize, newSize);
            console.log(`%cChanged size to ${newSize}`, "color: #4ecca3");
        });
        
        // Geometry switcher
        let currentGeometry = 'box';
        document.getElementById('geometry').addEventListener('click', function() {
            scene.remove(cube);
            
            if (currentGeometry === 'box') {
                geometry = new THREE.SphereGeometry(INITIAL_SIZE * 0.7, 32, 32);
                currentGeometry = 'sphere';
                this.textContent = 'Switch to Cube';
            } else {
                geometry = new THREE.BoxGeometry(INITIAL_SIZE, INITIAL_SIZE, INITIAL_SIZE);
                currentGeometry = 'box';
                this.textContent = 'Switch to Sphere';
            }
            
            const newCube = new THREE.Mesh(geometry, material);
            scene.add(newCube);
            cube.geometry.dispose(); // Clean up old geometry
            cube.geometry = newCube.geometry;
            
            console.log(`%cSwitched geometry to ${currentGeometry}`, "color: #4ecca3");
        });
        
        // Reset button
        document.getElementById('reset').addEventListener('click', function() {
            camera.position.z = 5;
            camera.position.x = 0;
            camera.position.y = 0;
            
            if (typeof controls !== 'undefined') {
                controls.reset();
            }
            
            material.color.setHex(COLORS.TEAL);
            document.getElementById('color').value = '0x4ecca3';
            
            rotationSpeed = INITIAL_ROTATION_SPEED;
            document.getElementById('speed').value = INITIAL_ROTATION_SPEED;
            
            cube.scale.set(1, 1, 1);
            document.getElementById('size').value = INITIAL_SIZE;
            
            autoRotate = true;
            
            if (currentGeometry !== 'box') {
                document.getElementById('geometry').click();
            }
            
            console.log("%cReset scene to initial state", "color: #4ecca3");
        });
        
        // Object interaction - click detection
        const raycaster = new THREE.Raycaster();
        const mouse = new THREE.Vector2();
        
        function onMouseClick(event) {
            // Calculate mouse position in normalized device coordinates
            mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
            mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;
            
            // Update the picking ray with the camera and mouse position
            raycaster.setFromCamera(mouse, camera);
            
            // Calculate objects intersecting the picking ray
            const intersects = raycaster.intersectObjects(scene.children);
            
            if (intersects.length > 0 && intersects[0].object === cube) {
                material.wireframe = !material.wireframe;
                console.log(`%cToggled wireframe to ${material.wireframe}`, "color: #4ecca3");
            }
        }
        
        window.addEventListener('click', onMouseClick, false);
        
        // -------- ANIMATION: Create an animation loop --------
        
        // This function runs ~60 times per second
        function animate() {
            requestAnimationFrame(animate); // Request to run again next frame
            
            // Update orbit controls if they exist
            if (typeof controls !== 'undefined') {
                controls.update();
            }
            
            // Auto-rotate the cube if enabled
            if (autoRotate) {
                cube.rotation.x += rotationSpeed;
                cube.rotation.y += rotationSpeed;
            }
            
            // Render the scene from the camera's perspective
            renderer.render(scene, camera);
        }
        
        // Start the animation loop
        animate();
        console.log("%cAnimation started", "color: #4ecca3");
        
        // -------- RESPONSIVE: Make it work on window resize --------
        
        window.addEventListener('resize', function() {
            // Update camera
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            
            // Update renderer
            renderer.setSize(window.innerWidth, window.innerHeight);
            console.log("%cResized to match window", "color: #4ecca3");
        });
    </script>
</body>
</html> 