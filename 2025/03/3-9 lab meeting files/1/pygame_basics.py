import pygame
import sys
import random
import os
from termcolor import colored

# CONSTANTS
WIDTH = 800
HEIGHT = 600
FPS = 60
PLAYER_SIZE = 50
ENEMY_SIZE = 30
PLAYER_SPEED = 5
ENEMY_SPEED = 3
ENEMY_COUNT = 5
BACKGROUND_COLOR = (20, 20, 40)
PLAYER_COLOR = (0, 255, 0)
ENEMY_COLOR = (255, 0, 0)
SCORE_COLOR = (255, 255, 255)

def initialize_pygame():
    """Initialize pygame modules"""
    try:
        pygame.init()
        print(colored("Pygame initialized successfully!", "green"))
        return True
    except Exception as e:
        print(colored(f"Error initializing Pygame: {e}", "red"))
        return False

def create_window():
    """Create the game window"""
    try:
        screen = pygame.display.set_mode((WIDTH, HEIGHT))
        pygame.display.set_caption("Pygame Basics")
        print(colored("Game window created successfully!", "green"))
        return screen
    except Exception as e:
        print(colored(f"Error creating game window: {e}", "red"))
        return None

def create_clock():
    """Create a clock to control the game's frame rate"""
    try:
        clock = pygame.time.Clock()
        print(colored("Game clock created successfully!", "green"))
        return clock
    except Exception as e:
        print(colored(f"Error creating game clock: {e}", "red"))
        return None

class Player:
    def __init__(self):
        self.x = WIDTH // 2
        self.y = HEIGHT // 2
        self.rect = pygame.Rect(self.x, self.y, PLAYER_SIZE, PLAYER_SIZE)
        print(colored("Player created successfully!", "cyan"))
    
    def update(self, keys):
        """Update player position based on keyboard input"""
        if keys[pygame.K_LEFT] and self.rect.left > 0:
            self.rect.x -= PLAYER_SPEED
        if keys[pygame.K_RIGHT] and self.rect.right < WIDTH:
            self.rect.x += PLAYER_SPEED
        if keys[pygame.K_UP] and self.rect.top > 0:
            self.rect.y -= PLAYER_SPEED
        if keys[pygame.K_DOWN] and self.rect.bottom < HEIGHT:
            self.rect.y += PLAYER_SPEED
    
    def draw(self, screen):
        """Draw the player on the screen"""
        pygame.draw.rect(screen, PLAYER_COLOR, self.rect)

class Enemy:
    def __init__(self):
        self.x = random.randint(0, WIDTH - ENEMY_SIZE)
        self.y = random.randint(0, HEIGHT - ENEMY_SIZE)
        self.rect = pygame.Rect(self.x, self.y, ENEMY_SIZE, ENEMY_SIZE)
        self.speed_x = random.choice([-1, 1]) * ENEMY_SPEED
        self.speed_y = random.choice([-1, 1]) * ENEMY_SPEED
    
    def update(self):
        """Update enemy position and handle bouncing off walls"""
        self.rect.x += self.speed_x
        self.rect.y += self.speed_y
        
        # Bounce off walls
        if self.rect.left <= 0 or self.rect.right >= WIDTH:
            self.speed_x *= -1
        if self.rect.top <= 0 or self.rect.bottom >= HEIGHT:
            self.speed_y *= -1
    
    def draw(self, screen):
        """Draw the enemy on the screen"""
        pygame.draw.rect(screen, ENEMY_COLOR, self.rect)

def main():
    """Main game function"""
    print(colored("Starting Pygame Basics Demo...", "yellow"))
    
    # Initialize pygame
    if not initialize_pygame():
        return
    
    # Create game window
    screen = create_window()
    if not screen:
        return
    
    # Create game clock
    clock = create_clock()
    if not clock:
        return
    
    # Create font for score display
    try:
        font = pygame.font.SysFont(None, 36)
        print(colored("Font created successfully!", "green"))
    except Exception as e:
        print(colored(f"Error creating font: {e}", "red"))
        return
    
    # Create player
    player = Player()
    
    # Create enemies
    enemies = []
    for _ in range(ENEMY_COUNT):
        enemies.append(Enemy())
    print(colored(f"Created {ENEMY_COUNT} enemies!", "cyan"))
    
    # Game variables
    score = 0
    game_over = False
    
    # Game loop
    print(colored("Starting game loop...", "yellow"))
    running = True
    while running:
        # Process events
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running = False
            if event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    running = False
                if game_over and event.key == pygame.K_r:
                    # Reset game
                    player = Player()
                    enemies = []
                    for _ in range(ENEMY_COUNT):
                        enemies.append(Enemy())
                    score = 0
                    game_over = False
        
        if not game_over:
            # Update game state
            keys = pygame.key.get_pressed()
            player.update(keys)
            
            for enemy in enemies:
                enemy.update()
                
                # Check for collisions
                if player.rect.colliderect(enemy.rect):
                    print(colored("Game Over! Player collided with an enemy.", "red"))
                    game_over = True
            
            # Increase score over time
            score += 1
        
        # Draw everything
        screen.fill(BACKGROUND_COLOR)
        
        # Draw player and enemies
        player.draw(screen)
        for enemy in enemies:
            enemy.draw(screen)
        
        # Draw score
        score_text = font.render(f"Score: {score}", True, SCORE_COLOR)
        screen.blit(score_text, (10, 10))
        
        # Draw game over message
        if game_over:
            game_over_text = font.render("Game Over! Press R to restart", True, (255, 0, 0))
            screen.blit(game_over_text, (WIDTH//2 - 180, HEIGHT//2))
        
        # Update display
        pygame.display.flip()
        
        # Control frame rate
        clock.tick(FPS)
    
    # Clean up
    print(colored("Quitting game...", "yellow"))
    pygame.quit()
    sys.exit()

if __name__ == "__main__":
    main() 