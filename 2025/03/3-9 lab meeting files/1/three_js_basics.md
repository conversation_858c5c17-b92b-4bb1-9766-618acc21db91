# Three.js Basics: A Beginner's Guide

## What is Three.js?

Three.js is a JavaScript library that makes it easier to create 3D graphics in the browser using WebGL. Without Three.js, working with WebGL directly would be extremely complex and verbose. Three.js simplifies this process by providing a high-level API that handles much of the complexity.

## Why Three.js?

- **Browser-based 3D**: Create 3D experiences that run directly in web browsers
- **No plugins required**: Works natively in modern browsers
- **Cross-platform**: Works on desktops, mobile devices, and tablets
- **Active community**: Large ecosystem with examples, extensions, and resources
- **Performance**: Optimized for real-time rendering

## Core Concepts

### 1. Scene

Think of the scene as a stage or container where all your 3D objects exist. Everything you want to display needs to be added to the scene.

```javascript
const scene = new THREE.Scene();
```

### 2. Camera

The camera defines what part of the scene is visible. It's like a movie camera that determines the viewpoint.

Common types:
- **PerspectiveCamera**: Mimics how human eyes see (objects farther away appear smaller)
- **OrthographicCamera**: No perspective distortion (used for 2D-like views)

```javascript
// Parameters: FOV (degrees), aspect ratio, near clipping plane, far clipping plane
const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
camera.position.z = 5; // Move the camera back so we can see objects
```

### 3. Renderer

The renderer is responsible for drawing the scene as viewed through the camera. It creates the actual pixels on your screen.

```javascript
const renderer = new THREE.WebGLRenderer();
renderer.setSize(window.innerWidth, window.innerHeight);
document.body.appendChild(renderer.domElement); // Adds the <canvas> to the page
```

### 4. Geometries and Materials

3D objects consist of two main parts:

- **Geometry**: The shape/structure (vertices and faces)
- **Material**: The appearance (color, texture, shininess)

```javascript
// Create a cube
const geometry = new THREE.BoxGeometry(1, 1, 1);
const material = new THREE.MeshBasicMaterial({ color: 0x00ff00 });
const cube = new THREE.Mesh(geometry, material);
scene.add(cube);
```

### 5. Lights

For materials that respond to light (like MeshPhongMaterial), you need light sources:

```javascript
// Ambient light (lights everything equally)
const ambientLight = new THREE.AmbientLight(0x404040);
scene.add(ambientLight);

// Directional light (like sunlight)
const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
directionalLight.position.set(1, 1, 1);
scene.add(directionalLight);
```

### 6. Animation Loop

To create motion, you use an animation loop that runs many times per second:

```javascript
function animate() {
    requestAnimationFrame(animate); // Request to run again next frame
    
    // Update your objects here
    cube.rotation.x += 0.01;
    cube.rotation.y += 0.01;
    
    // Render the scene
    renderer.render(scene, camera);
}
animate();
```

## Explaining the Example (one.html)

Our example demonstrates these core concepts by creating a rotating cube:

1. **Setup**: We create a scene, camera, and renderer
2. **Object Creation**: We create a cube with BoxGeometry and MeshPhongMaterial
3. **Lighting**: We add ambient and directional lights to illuminate the cube
4. **Animation**: We rotate the cube slightly on each frame
5. **Responsiveness**: We update the camera and renderer when the window is resized

## Common Three.js Objects and Features

- **Meshes**: 3D objects created from geometry and material
- **Textures**: Images mapped onto materials
- **Groups**: Collections of objects that can be manipulated together
- **Controls**: Utilities for interacting with the scene (orbit controls, fly controls)
- **Loaders**: Utilities for loading models, textures, and other assets
- **Helpers**: Visual aids for debugging (axes, grids, camera helpers)

## Next Steps After Understanding the Basics

1. **Add interactivity**: Handle mouse/touch events to interact with 3D objects
2. **Use more complex geometries**: Try spheres, cylinders, or custom geometries
3. **Load 3D models**: Import models created in Blender, Maya, etc.
4. **Add physics**: Integrate with libraries like Cannon.js for realistic physics
5. **Explore post-processing**: Add effects like bloom, depth of field, etc.

## Resources for Learning More

- [Three.js Documentation](https://threejs.org/docs/): Official documentation
- [Three.js Examples](https://threejs.org/examples/): Official examples
- [Three.js Fundamentals](https://threejsfundamentals.org/): Comprehensive tutorials
- [Discover Three.js](https://discoverthreejs.com/): In-depth tutorials and book
- [Three.js Journey](https://threejs-journey.xyz/): Paid course but very comprehensive

## Common Challenges and Solutions

### Performance Issues

- Reduce polygon count in models
- Use instancing for repeated objects
- Implement level-of-detail (LOD)
- Use proper lighting techniques (baking shadows where possible)

### Mobile Compatibility

- Test on actual devices
- Simplify scenes for mobile
- Be mindful of touch controls vs. mouse

### Asset Loading

- Use compressed textures
- Show loading screens
- Consider progressive loading

Remember that 3D programming has a learning curve, but the results are uniquely rewarding. Start with simple examples, understand the core concepts, and gradually build more complex scenes! 