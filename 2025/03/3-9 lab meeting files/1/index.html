<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Three.js Physics Tower</title>
    <link href="https://cdn.jsdelivr.net/npm/daisyui@3.5.0/dist/full.css" rel="stylesheet" type="text/css" />
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/animejs@3.2.1/lib/anime.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.157.0/build/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.157.0/examples/js/controls/OrbitControls.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/cannon-es@0.20.0/dist/cannon-es.min.js"></script>
    <link rel="stylesheet" href="style.css">
</head>
<body class="bg-gray-900 text-white">
    <div id="loading" class="fixed inset-0 flex items-center justify-center z-50 bg-gray-900 bg-opacity-90">
        <div class="text-center">
            <div class="loading-spinner mb-4"></div>
            <h2 class="text-xl font-bold text-primary">Loading Physics Simulation...</h2>
        </div>
    </div>
    
    <div id="container" class="w-full h-screen"></div>
    
    <div class="fixed bottom-4 left-4 p-4 bg-gray-800 rounded-lg shadow-lg opacity-80">
        <h2 class="text-lg font-bold text-primary mb-2">Physics Tower</h2>
        <p>Click and drag to orbit. Click on the tower to apply force.</p>
        <div id="status" class="mt-2 text-sm">Status: Ready</div>
    </div>
    
    <script src="main.js"></script>
</body>
</html> 