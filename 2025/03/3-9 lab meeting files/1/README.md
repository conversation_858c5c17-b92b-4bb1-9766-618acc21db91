# Three.js Physics Tower

A 3D simulation of a tower made of bricks with realistic physics. The user can click on the tower to apply forces and watch it collapse.

## Features

- Interactive 3D scene with a U-shaped tower of bricks
- Physics simulation for realistic motion and collisions
- Orbit controls for camera movement
- Click interaction to apply forces to the tower
- Visual effects for impacts
- Dark mode UI

## How to Use

1. Open `index.html` in a web browser
2. Click and drag to orbit around the scene
3. Click on any brick in the tower to apply a force
4. Watch as the tower responds to physics and collapses

## Technologies Used

- Three.js for 3D rendering
- Cannon.js for physics simulation
- Anime.js for animations
- Tailwind CSS & DaisyUI for styling

## Notes

This application runs entirely in the browser and doesn't require a server to function. Just open the HTML file directly. 