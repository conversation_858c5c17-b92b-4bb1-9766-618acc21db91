# Pygame Basics Tutorial

This guide will walk you through the fundamentals of game development using Pygame, a popular Python library for creating 2D games. The accompanying `pygame_basics.py` file demonstrates these concepts with a simple game.

## Table of Contents
1. [Introduction to Pygame](#introduction-to-pygame)
2. [Setting Up Pygame](#setting-up-pygame)
3. [Game Loop Structure](#game-loop-structure)
4. [Drawing on the Screen](#drawing-on-the-screen)
5. [Handling User Input](#handling-user-input)
6. [Game Objects and Classes](#game-objects-and-classes)
7. [Collision Detection](#collision-detection)
8. [Game State Management](#game-state-management)
9. [Next Steps](#next-steps)

## Introduction to Pygame

Pygame is a set of Python modules designed for writing video games. It includes computer graphics and sound libraries designed to be used with the Python programming language. Pygame is built on top of the Simple DirectMedia Layer (SDL) library, providing a simple way to create games and multimedia programs.

Key features of Pygame:
- 2D graphics rendering
- Sound playback
- Keyboard, mouse, and joystick input
- Collision detection
- Sprite management
- Timer functionality

## Setting Up Pygame

### Installation

Before using Pygame, you need to install it:

```bash
pip install pygame termcolor
```

### Initializing Pygame

Every Pygame program starts with initializing the Pygame modules:

```python
import pygame
pygame.init()
```

In our example, we've wrapped this in a function with error handling:

```python
def initialize_pygame():
    try:
        pygame.init()
        print(colored("Pygame initialized successfully!", "green"))
        return True
    except Exception as e:
        print(colored(f"Error initializing Pygame: {e}", "red"))
        return False
```

### Creating a Game Window

After initializing Pygame, you need to create a window where your game will be displayed:

```python
screen = pygame.display.set_mode((WIDTH, HEIGHT))
pygame.display.set_caption("Pygame Basics")
```

The `set_mode()` function creates a display surface with the specified dimensions. The `set_caption()` function sets the title of the window.

## Game Loop Structure

Every game has a main loop that continues until the player quits. This loop typically follows this pattern:

1. Process events (user input)
2. Update game state
3. Render graphics
4. Control frame rate

Here's the basic structure from our example:

```python
running = True
while running:
    # 1. Process events
    for event in pygame.event.get():
        if event.type == pygame.QUIT:
            running = False
    
    # 2. Update game state
    # (update player position, enemy positions, check collisions, etc.)
    
    # 3. Render graphics
    screen.fill(BACKGROUND_COLOR)
    # (draw player, enemies, score, etc.)
    pygame.display.flip()
    
    # 4. Control frame rate
    clock.tick(FPS)
```

### Controlling Frame Rate

The `pygame.time.Clock` object helps control the game's frame rate:

```python
clock = pygame.time.Clock()
# In the game loop:
clock.tick(FPS)
```

This ensures your game runs at a consistent speed across different computers.

## Drawing on the Screen

Pygame provides several functions for drawing shapes and displaying images on the screen.

### Drawing Shapes

In our example, we use rectangles to represent the player and enemies:

```python
pygame.draw.rect(screen, PLAYER_COLOR, self.rect)
```

Pygame can draw various shapes:
- `pygame.draw.rect()` - rectangles
- `pygame.draw.circle()` - circles
- `pygame.draw.line()` - lines
- `pygame.draw.polygon()` - polygons

### Displaying Text

To display text, you need to create a font object, render the text to a surface, and then blit (copy) that surface to the screen:

```python
font = pygame.font.SysFont(None, 36)
score_text = font.render(f"Score: {score}", True, SCORE_COLOR)
screen.blit(score_text, (10, 10))
```

### Updating the Display

After drawing everything, you need to update the display to show the changes:

```python
pygame.display.flip()
```

## Handling User Input

Pygame provides several ways to handle user input.

### Event Handling

You can check for specific events in the event queue:

```python
for event in pygame.event.get():
    if event.type == pygame.QUIT:
        running = False
    if event.type == pygame.KEYDOWN:
        if event.key == pygame.K_ESCAPE:
            running = False
```

### Keyboard State

For continuous input (like movement), you can check the current state of all keys:

```python
keys = pygame.key.get_pressed()
if keys[pygame.K_LEFT]:
    # Move left
```

In our example, we use this approach for player movement:

```python
def update(self, keys):
    if keys[pygame.K_LEFT] and self.rect.left > 0:
        self.rect.x -= PLAYER_SPEED
    # ... other directions
```

## Game Objects and Classes

Organizing game elements into classes makes your code more maintainable and easier to understand.

In our example, we have `Player` and `Enemy` classes:

```python
class Player:
    def __init__(self):
        self.rect = pygame.Rect(WIDTH//2, HEIGHT//2, PLAYER_SIZE, PLAYER_SIZE)
    
    def update(self, keys):
        # Update player position based on input
    
    def draw(self, screen):
        # Draw player on the screen
```

Each class encapsulates:
- The object's properties (position, size, etc.)
- Methods to update the object's state
- Methods to draw the object

## Collision Detection

Pygame provides several ways to detect collisions between game objects.

### Rectangle Collision

The simplest method is using the `colliderect()` method of `pygame.Rect`:

```python
if player.rect.colliderect(enemy.rect):
    # Collision detected!
    game_over = True
```

This checks if two rectangles overlap.

### Other Collision Methods

Pygame also provides:
- `collidepoint()` - check if a point is inside a rectangle
- `collidelist()` - check if a rectangle collides with any rectangle in a list
- `collidelistall()` - get all rectangles in a list that collide with a rectangle

## Game State Management

Managing different game states (playing, game over, paused, etc.) is important for creating a complete game experience.

In our example, we use a simple `game_over` flag:

```python
if not game_over:
    # Update game state
else:
    # Display game over screen and handle restart
```

For more complex games, you might use a state machine or scene manager.

## Next Steps

Now that you understand the basics of Pygame, here are some ways to expand your knowledge:

1. **Add Sound Effects and Music**:
   ```python
   pygame.mixer.init()
   sound_effect = pygame.mixer.Sound('explosion.wav')
   sound_effect.play()
   ```

2. **Use Sprite Groups**:
   Pygame's `sprite.Group` class helps manage collections of sprites.

3. **Load and Display Images**:
   ```python
   player_image = pygame.image.load('player.png').convert_alpha()
   screen.blit(player_image, player_rect)
   ```

4. **Create Animations**:
   Store animation frames in a list and cycle through them.

5. **Implement a Camera**:
   For games larger than the screen, implement a camera that follows the player.

6. **Add Particle Effects**:
   Create visual effects like explosions or trails.

7. **Implement a Menu System**:
   Create start screens, options menus, and pause screens.

## Resources

- [Pygame Documentation](https://www.pygame.org/docs/)
- [Pygame Wiki](https://www.pygame.org/wiki/GettingStarted)
- [Pygame Tutorials](https://www.pygame.org/wiki/tutorials)

Happy game development! 