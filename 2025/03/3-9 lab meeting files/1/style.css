:root {
    --primary: #6419e6;
    --secondary: #d926a9;
    --accent: #1fb2a6;
}

body {
    margin: 0;
    overflow: hidden;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #1f2937;
    color: white;
}

#container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: 4px solid rgba(100, 25, 230, 0.3);
    border-top-color: #6419e6;
    animation: spin 1s infinite linear;
    margin: 0 auto;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.text-primary {
    color: var(--primary);
}

.info-panel {
    position: fixed;
    bottom: 20px;
    left: 20px;
    padding: 15px;
    background-color: rgba(31, 41, 55, 0.8);
    border-radius: 10px;
    z-index: 100;
}

canvas {
    display: block;
}

.impact-effect {
    position: absolute;
    pointer-events: none;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(100, 25, 230, 0.7) 0%, rgba(100, 25, 230, 0) 70%);
    transform: translate(-50%, -50%);
    z-index: 1000;
    opacity: 0;
} 