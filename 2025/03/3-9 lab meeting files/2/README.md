# AI Assistant - ChatGPT Clone

A modern, feature-rich ChatGPT clone built using FastAPI and OpenAI's GPT-4o API.

## Features

- Real-time chat interface with streaming responses
- Dark mode UI with sleek, modern design
- WebSocket support for streaming responses
- Category-based prompts for different types of queries
- Support for multiple OpenAI models (GPT-4o, GPT-4o-mini)
- Markdown formatting in messages
- Responsive design for all devices
- Sleek animations using Anime.js

## Screenshots

The application features a dark-themed UI with a subtle tech background, glassmorphism effects, and a modern chat interface for interacting with the AI assistant.

## Important Note About Background Image

This application uses a background image that needs to be added manually:

1. Add a tech-focused background image file named `tech-background.jpg` to the `static/images/` directory
2. The image should be subtle enough to not interfere with readability

## Setup

### Prerequisites

- Python 3.8+
- OpenAI API key

### Installation

1. Clone this repository:
```bash
git clone https://github.com/yourusername/ai-assistant.git
cd ai-assistant
```

2. Install the required dependencies:
```bash
pip install -r requirements.txt
```

3. Set your OpenAI API key as an environment variable:
```bash
# On Windows
set OPENAI_API_KEY=your_api_key_here

# On macOS/Linux
export OPENAI_API_KEY=your_api_key_here
```

### Running the Application

1. Start the FastAPI server:
```bash
python main.py
```

2. Open your browser and navigate to:
```
http://127.0.0.1:8000
```

## Project Structure

```
ai-assistant/
├── main.py                # FastAPI application
├── requirements.txt       # Project dependencies
├── static/                # Static files
│   ├── css/
│   │   └── styles.css     # CSS styles
│   ├── js/
│   │   └── app.js         # JavaScript for the frontend
│   └── images/
│       └── tech-background.jpg  # Background image (add this manually)
└── templates/             # HTML templates
    └── index.html         # Main page template
```

## Usage

1. Select a category from the category selector or type your query directly
2. The AI will respond based on your input
3. You can change the AI model using the dropdown in the header
4. Clear the chat history using the "New Chat" button

## Customization

- Modify the `styles.css` file to change the appearance
- Update the categories in `index.html` to add or remove topics
- Adjust the OpenAI model parameters in `main.py` to change the response behavior

## License

MIT

## Acknowledgements

- OpenAI for the GPT-4o API
- FastAPI for the backend framework
- DaisyUI and Tailwind CSS for the UI components
- Anime.js for animations 