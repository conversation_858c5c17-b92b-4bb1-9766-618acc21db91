document.addEventListener('DOMContentLoaded', () => {
    // DOM Elements
    const chatForm = document.getElementById('chat-form');
    const userInput = document.getElementById('user-input');
    const chatMessages = document.getElementById('chat-messages');
    const typingIndicator = document.getElementById('typing-indicator');
    const clearChatButton = document.getElementById('clear-chat');
    const showCategoriesButton = document.getElementById('show-categories');
    const categorySelector = document.getElementById('category-selector');
    const modelDropdown = document.querySelector('.dropdown');
    const currentModelSpan = document.getElementById('current-model');
    const categoryCards = document.querySelectorAll('.category-card');

    // State
    let currentModel = 'gpt-4o';
    let chatHistory = [];
    let socket = null;
    let isStreaming = false;
    let currentAssistantMessage = null;

    // Initialize
    initializeChat();
    setupEventListeners();
    setupWebSocket();
    
    // Add welcome message
    addAssistantMessage("Hello! I'm your AI assistant powered by GPT-4o. I can help with a wide range of topics including creative writing, coding, knowledge questions, and more. How can I assist you today?");

    // Functions
    function initializeChat() {
        // Auto-resize textarea
        userInput.addEventListener('input', () => {
            userInput.style.height = 'auto';
            userInput.style.height = (userInput.scrollHeight) + 'px';
        });

        // Initialize anime.js animations for background
        anime({
            targets: '.background-overlay',
            opacity: [0, 0.4],
            duration: 1500,
            easing: 'easeOutQuad'
        });
    }

    function setupEventListeners() {
        // Form submission
        chatForm.addEventListener('submit', handleSubmit);

        // Clear chat
        clearChatButton.addEventListener('click', clearChat);

        // Show/hide categories
        showCategoriesButton.addEventListener('click', () => {
            categorySelector.classList.toggle('hidden');
            
            if (!categorySelector.classList.contains('hidden')) {
                // Animate category cards when shown
                anime({
                    targets: '.category-card',
                    scale: [0.9, 1],
                    opacity: [0, 1],
                    delay: anime.stagger(100),
                    easing: 'easeOutElastic(1, .5)'
                });
            }
        });

        // Model selection
        modelDropdown.querySelectorAll('a').forEach(item => {
            item.addEventListener('click', (e) => {
                currentModel = e.target.dataset.model;
                currentModelSpan.textContent = currentModel;
            });
        });

        // Category selection
        categoryCards.forEach(card => {
            card.addEventListener('click', () => {
                const category = card.dataset.category;
                let prompt = "";
                
                // Set different prompts based on category
                switch(category) {
                    case 'creativity':
                        prompt = "I need some creative ideas for a project I'm working on.";
                        break;
                    case 'coding':
                        prompt = "Can you help me understand how to implement a basic algorithm?";
                        break;
                    case 'knowledge':
                        prompt = "Explain a complex topic in simple terms.";
                        break;
                    case 'writing':
                        prompt = "Help me draft a professional email.";
                        break;
                    case 'analysis':
                        prompt = "Can you analyze this concept and provide insights?";
                        break;
                    case 'assistant':
                        prompt = "I need help with planning my day efficiently.";
                        break;
                    default:
                        prompt = "Let's discuss something interesting!";
                }
                
                userInput.value = prompt;
                categorySelector.classList.add('hidden');
                chatForm.dispatchEvent(new Event('submit'));
            });
        });

        // Auto-resize textarea on input
        userInput.addEventListener('input', () => {
            userInput.style.height = 'auto';
            userInput.style.height = userInput.scrollHeight + 'px';
        });
    }

    function setupWebSocket() {
        // Close any existing connection
        if (socket) {
            socket.close();
        }

        // Create new WebSocket connection
        socket = new WebSocket(`ws://${window.location.host}/ws/chat`);
        
        socket.onopen = () => {
            console.log('WebSocket connection established');
        };
        
        socket.onmessage = (event) => {
            const data = JSON.parse(event.data);
            
            if (data.error) {
                showError(data.error);
                hideTypingIndicator();
                return;
            }
            
            if (isStreaming) {
                if (data.end) {
                    isStreaming = false;
                    hideTypingIndicator();
                    scrollToBottom();
                    return;
                }
                
                appendToAssistantMessage(data.content);
            }
        };
        
        socket.onclose = () => {
            console.log('WebSocket connection closed');
            // Try to reconnect after a delay
            setTimeout(setupWebSocket, 3000);
        };
        
        socket.onerror = (error) => {
            console.error('WebSocket error:', error);
            showError('Connection error. Please try again later.');
        };
    }

    async function handleSubmit(e) {
        e.preventDefault();
        
        const message = userInput.value.trim();
        if (!message) return;
        
        // Add user message to UI
        addUserMessage(message);
        
        // Clear input
        userInput.value = '';
        userInput.style.height = 'auto';
        
        // Update chat history
        chatHistory.push({ role: 'user', content: message });
        
        // Show typing indicator
        showTypingIndicator();
        
        try {
            if (socket && socket.readyState === WebSocket.OPEN) {
                // Use WebSocket for streaming
                isStreaming = true;
                currentAssistantMessage = createAssistantMessageElement('');
                chatMessages.appendChild(currentAssistantMessage);
                
                socket.send(JSON.stringify({
                    messages: chatHistory,
                    model: currentModel
                }));
            } else {
                // Fallback to REST API
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        messages: chatHistory.map(msg => ({ role: msg.role, content: msg.content })),
                        model: currentModel
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                addAssistantMessage(data.response);
                chatHistory.push({ role: 'assistant', content: data.response });
                hideTypingIndicator();
            }
        } catch (error) {
            console.error('Error:', error);
            showError('Failed to get response. Please try again.');
            hideTypingIndicator();
        }
    }

    function addUserMessage(message) {
        const messageElement = document.createElement('div');
        messageElement.className = 'message user-message';
        messageElement.textContent = message;
        
        chatMessages.appendChild(messageElement);
        scrollToBottom();
    }

    function addAssistantMessage(message) {
        const messageElement = createAssistantMessageElement(message);
        chatMessages.appendChild(messageElement);
        chatHistory.push({ role: 'assistant', content: message });
        scrollToBottom();
        
        // Animate the message appearance
        anime({
            targets: messageElement,
            opacity: [0, 1],
            translateY: [10, 0],
            duration: 300,
            easing: 'easeOutQuad'
        });
    }

    function createAssistantMessageElement(message) {
        const messageElement = document.createElement('div');
        messageElement.className = 'message assistant-message';
        
        // Process markdown-like formatting
        const formattedMessage = formatMessage(message);
        messageElement.innerHTML = formattedMessage;
        
        return messageElement;
    }

    function appendToAssistantMessage(content) {
        if (!currentAssistantMessage) return;
        
        const currentContent = currentAssistantMessage.innerHTML;
        const formattedContent = formatMessage(currentContent + content);
        currentAssistantMessage.innerHTML = formattedContent;
        
        // Update chat history
        if (chatHistory.length > 0 && chatHistory[chatHistory.length - 1].role === 'assistant') {
            chatHistory[chatHistory.length - 1].content += content;
        } else {
            chatHistory.push({ role: 'assistant', content });
        }
        
        scrollToBottom();
    }

    function formatMessage(message) {
        // Basic markdown-like formatting
        let formatted = message
            // Code blocks
            .replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>')
            // Inline code
            .replace(/`([^`]+)`/g, '<code>$1</code>')
            // Bold
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            // Italic
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            // Line breaks
            .replace(/\n/g, '<br>');
        
        return formatted;
    }

    function showTypingIndicator() {
        typingIndicator.classList.remove('hidden');
        scrollToBottom();
    }

    function hideTypingIndicator() {
        typingIndicator.classList.add('hidden');
    }

    function showError(message) {
        const errorElement = document.createElement('div');
        errorElement.className = 'message assistant-message error';
        errorElement.textContent = `Error: ${message}`;
        chatMessages.appendChild(errorElement);
        scrollToBottom();
    }

    function clearChat() {
        // Clear UI
        chatMessages.innerHTML = '';
        
        // Reset chat history
        chatHistory = [];
        
        // Add welcome message
        addAssistantMessage("Hello! I'm your AI assistant powered by GPT-4o. I can help with a wide range of topics including creative writing, coding, knowledge questions, and more. How can I assist you today?");
    }

    function scrollToBottom() {
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }
}); 