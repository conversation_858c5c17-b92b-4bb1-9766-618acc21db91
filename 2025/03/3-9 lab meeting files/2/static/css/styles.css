/* Base styles */
:root {
    --primary-color: #6366f1;
    --secondary-color: #8b5cf6;
    --accent-color: #4f46e5;
    --background-dark: #111827;
    --card-bg: rgba(30, 41, 59, 0.8);
    --text-primary: #f3f4f6;
    --text-secondary: #d1d5db;
}

body {
    font-family: 'Inter', sans-serif;
    position: relative;
    background-color: var(--background-dark);
    overflow-x: hidden;
}

/* Background overlay */
.background-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('/static/images/tech-background.jpg');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    opacity: 0.4;
    z-index: 0;
}

/* Category selector */
.category-selector {
    background-color: rgba(17, 24, 39, 0.7);
    border-radius: 1rem;
    padding: 2rem;
    backdrop-filter: blur(8px);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(99, 102, 241, 0.2);
    z-index: 10;
}

/* Category cards */
.category-card {
    background-color: var(--card-bg);
    border-radius: 0.75rem;
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3);
    border-color: var(--primary-color);
    background-color: rgba(49, 58, 85, 0.8);
}

.icon-container {
    background-color: rgba(99, 102, 241, 0.2);
    border-radius: 50%;
    width: 64px;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 0.75rem;
    transition: all 0.3s ease;
}

.category-card:hover .icon-container {
    background-color: rgba(99, 102, 241, 0.4);
    transform: scale(1.05);
}

/* Chat messages */
.message {
    padding: 1rem;
    border-radius: 0.5rem;
    max-width: 90%;
    animation: fadeIn 0.3s ease-out;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.user-message {
    background-color: var(--primary-color);
    color: white;
    margin-left: auto;
    border-top-right-radius: 0;
}

.assistant-message {
    background-color: var(--card-bg);
    color: var(--text-primary);
    margin-right: auto;
    border-top-left-radius: 0;
}

/* Typing indicator */
.typing-dot {
    width: 8px;
    height: 8px;
    background-color: var(--primary-color);
    border-radius: 50%;
    animation: typingAnimation 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) {
    animation-delay: 0s;
}

.typing-dot:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typingAnimation {
    0%, 60%, 100% {
        transform: translateY(0);
    }
    30% {
        transform: translateY(-5px);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Chat container */
.chat-container {
    position: relative;
    z-index: 10;
}

#chat-messages {
    padding-right: 8px;
    min-height: 300px;
}

/* Textarea auto-resize */
#user-input {
    resize: none;
    overflow: hidden;
    min-height: 44px;
    transition: height 0.2s ease;
}

/* Code blocks in messages */
pre {
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 0.375rem;
    padding: 1rem;
    overflow-x: auto;
    margin: 0.5rem 0;
}

code {
    font-family: 'Fira Code', monospace;
    font-size: 0.875rem;
}

/* Scrollbar styling */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
}

::-webkit-scrollbar-thumb {
    background: rgba(99, 102, 241, 0.5);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(99, 102, 241, 0.8);
}

/* Glassmorphism effect */
.chat-input-container {
    background-color: rgba(30, 41, 59, 0.7);
    backdrop-filter: blur(8px);
    border-radius: 0.75rem;
    padding: 0.75rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* Button hover effects */
.btn-primary, .btn-secondary {
    transition: all 0.3s ease;
}

.btn-primary:hover, .btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Error message */
.error {
    background-color: rgba(220, 38, 38, 0.2);
    border-left: 4px solid #dc2626;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .category-selector .grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .category-selector .grid {
        grid-template-columns: 1fr;
    }
} 