<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Assistant</title>
    <link href="https://cdn.jsdelivr.net/npm/daisyui@3.5.0/dist/full.css" rel="stylesheet" type="text/css" />
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/animejs@3.2.1/lib/anime.min.js"></script>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap">
    <link rel="stylesheet" href="/static/css/styles.css">
</head>
<body class="bg-gray-900 text-white min-h-screen flex flex-col">
    <div class="background-overlay"></div>
    
    <header class="bg-gray-800 bg-opacity-50 backdrop-blur-md py-4 px-6 shadow-lg z-10 relative">
        <div class="container mx-auto flex justify-between items-center">
            <div class="flex items-center space-x-2">
                <div class="text-3xl font-bold text-indigo-400">AI Assistant</div>
            </div>
            <div class="flex items-center space-x-4">
                <button id="show-categories" class="btn btn-sm btn-outline btn-secondary">Categories</button>
                <button id="clear-chat" class="btn btn-sm btn-outline btn-primary">New Chat</button>
                <div class="dropdown dropdown-end">
                    <label tabindex="0" class="btn btn-sm btn-outline btn-secondary">
                        <span>Model: </span>
                        <span id="current-model">gpt-4o</span>
                    </label>
                    <ul tabindex="0" class="dropdown-content z-[1] menu p-2 shadow bg-gray-800 rounded-box w-52">
                        <li><a data-model="gpt-4o">GPT-4o</a></li>
                        <li><a data-model="gpt-4o-mini">GPT-4o Mini</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </header>

    <main class="flex-grow container mx-auto px-4 py-6 flex flex-col relative z-10">
        <div id="category-selector" class="category-selector mb-8 hidden">
            <h2 class="text-2xl font-bold text-center mb-6">CHOOSE A CATEGORY</h2>
            <div class="grid grid-cols-3 gap-6 max-w-3xl mx-auto">
                <div class="category-card" data-category="creativity">
                    <div class="icon-container">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                    </div>
                    <div class="text-center mt-2">Creativity</div>
                </div>
                <div class="category-card" data-category="coding">
                    <div class="icon-container">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                        </svg>
                    </div>
                    <div class="text-center mt-2">Coding</div>
                </div>
                <div class="category-card" data-category="knowledge">
                    <div class="icon-container">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                        </svg>
                    </div>
                    <div class="text-center mt-2">Knowledge</div>
                </div>
                <div class="category-card" data-category="writing">
                    <div class="icon-container">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                    </div>
                    <div class="text-center mt-2">Writing</div>
                </div>
                <div class="category-card" data-category="analysis">
                    <div class="icon-container">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                        </svg>
                    </div>
                    <div class="text-center mt-2">Analysis</div>
                </div>
                <div class="category-card" data-category="assistant">
                    <div class="icon-container">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                        </svg>
                    </div>
                    <div class="text-center mt-2">Assistant</div>
                </div>
            </div>
        </div>

        <div class="chat-container flex-grow flex flex-col">
            <div id="chat-messages" class="flex-grow overflow-y-auto mb-4 space-y-4 pb-4"></div>
            
            <div id="typing-indicator" class="hidden">
                <div class="flex items-center space-x-2 text-indigo-400 mb-4">
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                </div>
            </div>
            
            <div class="chat-input-container">
                <form id="chat-form" class="flex items-center space-x-2">
                    <textarea id="user-input" class="flex-grow textarea textarea-bordered bg-gray-800 bg-opacity-60 focus:border-indigo-500" placeholder="Ask me anything..." rows="1"></textarea>
                    <button type="submit" class="btn btn-primary">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z" />
                        </svg>
                    </button>
                </form>
            </div>
        </div>
    </main>

    <script src="/static/js/app.js"></script>
</body>
</html> 