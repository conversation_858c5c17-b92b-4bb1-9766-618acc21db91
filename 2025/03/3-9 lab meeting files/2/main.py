import os
import json
from fastapi import FastAP<PERSON>, HTTPException, Request, WebSocket, WebSocketDisconnect
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
import openai
from termcolor import colored
import asyncio
from openai import AsyncOpenAI

# Constants
API_KEY = os.getenv("OPENAI_API_KEY")
MODEL = "gpt-4o"
MAX_TOKENS = 1000
TEMPERATURE = 0.7

# Initialize FastAPI app
app = FastAPI(title="AI Assistant")

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")

# Setup templates
templates = Jinja2Templates(directory="templates")

# Initialize OpenAI client
client = AsyncOpenAI(api_key=API_KEY)

# Data models
class ChatMessage(BaseModel):
    role: str
    content: str

class ChatRequest(BaseModel):
    messages: List[ChatMessage]
    model: Optional[str] = MODEL
    temperature: Optional[float] = TEMPERATURE
    max_tokens: Optional[int] = MAX_TOKENS

class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)
        print(colored(f"WebSocket client connected. Total connections: {len(self.active_connections)}", "green"))

    def disconnect(self, websocket: WebSocket):
        self.active_connections.remove(websocket)
        print(colored(f"WebSocket client disconnected. Remaining connections: {len(self.active_connections)}", "yellow"))

    async def send_message(self, message: str, websocket: WebSocket):
        await websocket.send_text(message)

manager = ConnectionManager()

@app.get("/", response_class=HTMLResponse)
async def get_home(request: Request):
    print(colored("Serving home page", "blue"))
    return templates.TemplateResponse("index.html", {"request": request})

@app.post("/api/chat")
async def chat(chat_request: ChatRequest):
    try:
        print(colored(f"Processing chat request with {len(chat_request.messages)} messages", "blue"))
        
        # Convert Pydantic models to dictionaries
        messages = [{"role": msg.role, "content": msg.content} for msg in chat_request.messages]
        
        # Call OpenAI API
        response = await client.chat.completions.create(
            model=chat_request.model,
            messages=messages,
            temperature=chat_request.temperature,
            max_tokens=chat_request.max_tokens
        )
        
        print(colored("Successfully received response from OpenAI", "green"))
        return {
            "response": response.choices[0].message.content,
            "usage": {
                "prompt_tokens": response.usage.prompt_tokens,
                "completion_tokens": response.usage.completion_tokens,
                "total_tokens": response.usage.total_tokens
            }
        }
    except Exception as e:
        print(colored(f"Error in chat endpoint: {str(e)}", "red"))
        raise HTTPException(status_code=500, detail=f"OpenAI API error: {str(e)}")

@app.websocket("/ws/chat")
async def websocket_endpoint(websocket: WebSocket):
    await manager.connect(websocket)
    try:
        while True:
            data = await websocket.receive_text()
            print(colored(f"Received WebSocket message", "blue"))
            
            try:
                request_data = json.loads(data)
                messages = request_data.get("messages", [])
                
                # Call OpenAI API
                response = await client.chat.completions.create(
                    model=request_data.get("model", MODEL),
                    messages=messages,
                    temperature=request_data.get("temperature", TEMPERATURE),
                    max_tokens=request_data.get("max_tokens", MAX_TOKENS),
                    stream=True
                )
                
                # Stream the response
                async for chunk in response:
                    if chunk.choices and chunk.choices[0].delta.content:
                        content = chunk.choices[0].delta.content
                        await manager.send_message(json.dumps({"content": content}), websocket)
                
                # Send end of stream marker
                await manager.send_message(json.dumps({"content": "", "end": True}), websocket)
                
            except Exception as e:
                print(colored(f"Error processing WebSocket message: {str(e)}", "red"))
                await manager.send_message(json.dumps({"error": str(e)}), websocket)
    
    except WebSocketDisconnect:
        manager.disconnect(websocket)

if __name__ == "__main__":
    import uvicorn
    print(colored("Starting AI Assistant server...", "cyan"))
    uvicorn.run("main:app", host="127.0.0.1", port=8000, reload=True) 