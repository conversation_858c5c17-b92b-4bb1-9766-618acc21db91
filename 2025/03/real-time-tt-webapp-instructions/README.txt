Real-Time TTS Streaming Web Application
=====================================

A web application that provides real-time text-to-speech streaming using OpenAI's TTS API. Features include live audio playback, MP3 downloads, and voice customization through instructions.

Features
--------
- Real-time audio streaming
- MP3 download option
- Multiple voice options (<PERSON>oy, Ash, Ballad, Coral, Echo, Fable, Onyx, Nova, Sage, <PERSON>mmer, Verse)
- Custom voice instructions
- Preset instruction templates (<PERSON><PERSON> Motivator, Mystic Narrator, Pirate Captain, etc.)
- Live audio visualization
- Dark mode UI

Requirements
-----------
- Python 3.8+
- OpenAI API key
- Modern web browser

Setup
-----
1. Install requirements:
   pip install -r requirements.txt

2. Set your OpenAI API key as an environment variable:
   export OPENAI_API_KEY='your-api-key'

3. Run the application:
   python main.py

4. Open your browser and visit:
   http://127.0.0.1:8000

Usage
-----
1. Enter text in the input area
2. Select a voice from the dropdown
3. (Optional) Add voice instructions or select a preset
4. Click "Play Audio" to stream or "Download MP3" to save

Voice Instructions
----------------
You can customize how the AI reads your text using instructions:
- Voice: Overall energy and quality
- Punctuation: Pause and emphasis handling
- Delivery: Pace and intonation
- Phrasing: Word grouping
- Tone: Emotional quality

Preset Templates
--------------
- Hyper Motivator: Energetic, enthusiastic trainer
- Mystic Narrator: Mystical storyteller
- Pirate Captain: Swashbuckling sea captain
- Superhero Announcer: Epic, dramatic announcer
- Mad Scientist: Eccentric, excitable genius

Technical Details
---------------
- FastAPI backend
- Real-time PCM audio streaming
- Web Audio API for playback
- DaisyUI + Tailwind CSS for styling
- Anime.js for animations

Note: The application requires an active internet connection and valid OpenAI API key to function. 