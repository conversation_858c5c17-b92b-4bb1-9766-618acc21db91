<!DOCTYPE html>
<html lang="en" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Real-Time TTS Streaming</title>
    <link href="https://cdn.jsdelivr.net/npm/daisyui@3.5.0/dist/full.css" rel="stylesheet" type="text/css" />
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/animejs@3.2.1/lib/anime.min.js"></script>
    <style>
        .loader-container {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 20px 0;
        }
        .sound-wave {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 50px;
        }
        .sound-wave .bar {
            background: #64b5f6;
            width: 5px;
            margin: 0 3px;
            border-radius: 3px;
            animation: none;
        }
    </style>
</head>
<body class="min-h-screen bg-base-300">
    <div class="container mx-auto p-4">
        <div class="card bg-base-100 shadow-xl">
            <div class="card-body">
                <h1 class="card-title text-2xl text-primary mb-6">Real-Time TTS Streaming</h1>
                
                <div class="form-control">
                    <label class="label">
                        <span class="label-text">Enter text to convert to speech</span>
                    </label>
                    <textarea id="text-input" class="textarea textarea-bordered h-32" placeholder="Enter your text here..."></textarea>
                </div>
                
                <div class="form-control mt-4">
                    <label class="label">
                        <span class="label-text">Voice</span>
                    </label>
                    <select id="voice-select" class="select select-bordered">
                        <option value="alloy">Alloy</option>
                        <option value="ash">Ash</option>
                        <option value="ballad">Ballad</option>
                        <option value="coral">Coral</option>
                        <option value="echo">Echo</option>
                        <option value="fable">Fable</option>
                        <option value="onyx">Onyx</option>
                        <option value="nova">Nova</option>
                        <option value="sage">Sage</option>
                        <option value="shimmer">Shimmer</option>
                        <option value="verse">Verse</option>
                    </select>
                </div>
                
                <div class="form-control mt-4">
                    <label class="label">
                        <span class="label-text">Instructions</span>
                        <span class="label-text-alt">
                            <button class="btn btn-circle btn-xs btn-info" onclick="showInstructionsInfo()">?</button>
                        </span>
                    </label>
                    <textarea id="instructions-input" class="textarea textarea-bordered h-24" placeholder="Provide instructions for voice style, tone, delivery, etc..."></textarea>
                    
                    <div class="mt-2">
                        <div class="label">
                            <span class="label-text">Preset Instructions</span>
                        </div>
                        <div class="flex flex-wrap gap-2">
                            <button class="btn btn-sm btn-secondary" onclick="setInstructions('energetic')">Hyper Motivator</button>
                            <button class="btn btn-sm btn-secondary" onclick="setInstructions('storyteller')">Mystic Narrator</button>
                            <button class="btn btn-sm btn-secondary" onclick="setInstructions('pirate')">Pirate Captain</button>
                            <button class="btn btn-sm btn-secondary" onclick="setInstructions('superhero')">Superhero Announcer</button>
                            <button class="btn btn-sm btn-secondary" onclick="setInstructions('scientist')">Mad Scientist</button>
                        </div>
                    </div>
                </div>
                
                <div class="flex flex-wrap gap-4 mt-6">
                    <button id="play-btn" class="btn btn-primary">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        Play Audio
                    </button>
                    
                    <button id="download-btn" class="btn btn-accent">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                        </svg>
                        Download MP3
                    </button>
                </div>
                
                <!-- Sound Wave Animation -->
                <div id="sound-wave-container" class="loader-container hidden mt-6">
                    <div class="sound-wave">
                        <div class="bar"></div>
                        <div class="bar"></div>
                        <div class="bar"></div>
                        <div class="bar"></div>
                        <div class="bar"></div>
                        <div class="bar"></div>
                        <div class="bar"></div>
                        <div class="bar"></div>
                    </div>
                </div>
                
                <!-- Audio element for playback -->
                <audio id="audio-player" class="w-full mt-4 hidden"></audio>
                
                <!-- Status message -->
                <div id="status-message" class="mt-4 text-info hidden"></div>
            </div>
        </div>
    </div>
    
    <!-- Instructions Info Modal -->
    <div id="instructions-modal" class="modal">
        <div class="modal-box">
            <h3 class="font-bold text-lg">How to Write Effective Instructions</h3>
            <div class="py-4">
                <p class="mb-2">Instructions help shape how the AI reads your text. Effective instructions include:</p>
                <ul class="list-disc list-inside mb-4">
                    <li><strong>Voice:</strong> Describe the overall energy and quality</li>
                    <li><strong>Punctuation:</strong> How pauses and emphasis should be handled</li>
                    <li><strong>Delivery:</strong> Specify pace and intonation</li>
                    <li><strong>Phrasing:</strong> Describe how words should be grouped</li>
                    <li><strong>Tone:</strong> The emotional quality of the speech</li>
                </ul>
                <p class="text-sm italic">Example: "Voice: High-energy, upbeat, and encouraging. Delivery: Fast-paced with strategic pauses. Tone: Positive and empowering."</p>
            </div>
            <div class="modal-action">
                <button class="btn" onclick="closeInstructionsModal()">Close</button>
            </div>
        </div>
    </div>
    
    <script>
        // DOM elements
        const textInput = document.getElementById('text-input');
        const instructionsInput = document.getElementById('instructions-input');
        const voiceSelect = document.getElementById('voice-select');
        const playBtn = document.getElementById('play-btn');
        const downloadBtn = document.getElementById('download-btn');
        const audioPlayer = document.getElementById('audio-player');
        const soundWaveContainer = document.getElementById('sound-wave-container');
        const statusMessage = document.getElementById('status-message');
        const instructionsModal = document.getElementById('instructions-modal');
        const bars = document.querySelectorAll('.sound-wave .bar');
        
        // Instructions presets
        const instructionPresets = {
            energetic: "Voice: Super-hyped, bouncy, and unstoppable, like a caffeinated cheerleader.\n\nPunctuation: Short, explosive phrases with excited exclamation points!\n\nDelivery: Rapid-fire words with dramatic emphasis on motivational keywords.\n\nTone: Ridiculously positive and infectious, like every sentence ends with a high-five.",
            
            storyteller: "Voice: Mystical and enchanting, like a wise elder sharing ancient secrets.\n\nPunctuation: Rhythmic phrasing with dramatic pauses for suspense...\n\nDelivery: Gentle whispers that build to powerful revelations, with magical wonder in every word.\n\nTone: Captivating and otherworldly, transporting listeners to fantastical realms.",
            
            pirate: "Voice: Gruff, sea-weathered, and boisterous with a strong rolling accent.\n\nPunctuation: Abrupt stops and loud exclamations, punctuated with 'Arrr!' and 'Yarrr!'\n\nDelivery: Swaggering and uneven, like talking while balancing on a ship deck.\n\nTone: Bold, adventurous, and slightly menacing, with hearty laughter between phrases.",
            
            superhero: "Voice: Bold, resonant, and larger-than-life with epic confidence.\n\nPunctuation: Powerful pauses before key phrases for maximum dramatic impact!\n\nDelivery: Starting low and building to triumphant crescendos, with special emphasis on action words.\n\nTone: Inspiring and heroic, making even ordinary sentences sound like they'll save the world.",
            
            scientist: "Voice: Eccentric, excitable, and slightly unhinged with random pitch changes.\n\nPunctuation: Frantic run-on sentences interrupted by sudden EUREKA moments!\n\nDelivery: Alternating between manic fast-talking and slow, deliberate explanations of 'brilliant' ideas.\n\nTone: Wildly enthusiastic about obscure details, with maniacal laughter when describing exciting concepts."
        };
        
        // Show instructions modal
        function showInstructionsInfo() {
            instructionsModal.classList.add('modal-open');
        }
        
        // Close instructions modal
        function closeInstructionsModal() {
            instructionsModal.classList.remove('modal-open');
        }
        
        // Set preset instructions
        function setInstructions(presetKey) {
            instructionsInput.value = instructionPresets[presetKey];
        }
        
        // Audio context and elements for PCM streaming
        let audioContext;
        let audioBuffer = [];
        let isPlaying = false;
        let audioSource = null;
        
        // Declare these variables at the top level to access them when stopping
        let currentScriptProcessor = null;
        let currentGainNode = null;
        
        // Initialize audio context on user interaction
        function initAudioContext() {
            if (!audioContext) {
                audioContext = new (window.AudioContext || window.webkitAudioContext)({sampleRate: 24000});
            }
        }
        
        // Animate sound bars
        function animateSoundBars(animate) {
            if (animate) {
                soundWaveContainer.classList.remove('hidden');
                anime({
                    targets: '.sound-wave .bar',
                    height: function() {
                        return anime.random(10, 50);
                    },
                    duration: 400,
                    easing: 'easeInOutSine',
                    loop: true,
                    direction: 'alternate',
                    delay: anime.stagger(100)
                });
            } else {
                soundWaveContainer.classList.add('hidden');
                anime.remove('.sound-wave .bar');
                bars.forEach(bar => {
                    bar.style.height = '10px';
                });
            }
        }
        
        // Play audio in real-time
        playBtn.addEventListener('click', async () => {
            const text = textInput.value.trim();
            const voice = voiceSelect.value;
            const instructions = instructionsInput.value.trim();
            
            if (!text) {
                showStatus('Please enter some text', 'error');
                return;
            }
            
            console.log("Selected voice:", voice); // Add debugging
            
            try {
                initAudioContext();
                
                if (isPlaying) {
                    // Stop current playback immediately
                    if (currentScriptProcessor) {
                        currentScriptProcessor.disconnect();
                        currentScriptProcessor = null;
                    }
                    if (currentGainNode) {
                        currentGainNode.disconnect();
                        currentGainNode = null;
                    }
                    isPlaying = false;
                    animateSoundBars(false);
                    playBtn.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>Play Audio';
                    return;
                }
                
                showStatus('Generating audio...', 'info');
                animateSoundBars(true);
                playBtn.textContent = 'Stop';
                isPlaying = true;
                
                // Create form data
                const formData = new FormData();
                formData.append('text', text);
                formData.append('voice', voice);
                formData.append('instructions', instructions);
                
                // Stream PCM audio
                const response = await fetch('/stream-audio', {
                    method: 'POST',
                    body: formData
                });
                
                if (!response.ok) {
                    throw new Error('Failed to generate audio');
                }
                
                const reader = response.body.getReader();
                const sampleRate = parseInt(response.headers.get('X-Sample-Rate') || '24000');
                
                // Process audio chunks
                const processChunks = async () => {
                    let accumulatedBuffer = new Uint8Array(0);
                    let allAudioReceived = false;
                    
                    // Create a ScriptProcessor for consistent playback
                    const scriptProcessor = audioContext.createScriptProcessor(4096, 0, 1);
                    const gainNode = audioContext.createGain();
                    
                    // Store references to the audio nodes so we can disconnect them when stopping
                    currentScriptProcessor = scriptProcessor;
                    currentGainNode = gainNode;
                    
                    // Connect audio nodes
                    scriptProcessor.connect(gainNode);
                    gainNode.connect(audioContext.destination);
                    
                    // Fade in
                    gainNode.gain.setValueAtTime(0, audioContext.currentTime);
                    gainNode.gain.linearRampToValueAtTime(1, audioContext.currentTime + 0.1);
                    
                    // Audio buffer for processing
                    let remainingDataArray = new Float32Array(0);
                    
                    // Fill the audio buffer
                    scriptProcessor.onaudioprocess = (e) => {
                        const outputBuffer = e.outputBuffer.getChannelData(0);
                        
                        // If we have remaining data, use it first
                        if (remainingDataArray.length > 0) {
                            const toCopy = Math.min(remainingDataArray.length, outputBuffer.length);
                            outputBuffer.set(remainingDataArray.subarray(0, toCopy));
                            
                            // If we have more data than needed for this buffer
                            if (remainingDataArray.length > outputBuffer.length) {
                                remainingDataArray = remainingDataArray.subarray(outputBuffer.length);
                            } else {
                                // We've used all the remaining data
                                remainingDataArray = new Float32Array(0);
                                
                                // Fill the rest with zeros if we don't have enough
                                if (toCopy < outputBuffer.length) {
                                    outputBuffer.fill(0, toCopy);
                                }
                            }
                            
                            // Check if we should end playback - only if we've received all audio
                            // AND we have no more data to play
                            if (allAudioReceived && remainingDataArray.length === 0) {
                                // Add a small delay before cleanup to ensure all audio is played
                                setTimeout(() => {
                                    if (isPlaying) {
                                        // Only disconnect if still playing (avoid double cleanup)
                                        gainNode.gain.setValueAtTime(gainNode.gain.value, audioContext.currentTime);
                                        gainNode.gain.linearRampToValueAtTime(0, audioContext.currentTime + 0.2);
                                        
                                        setTimeout(() => {
                                            // Final cleanup
                                            if (isPlaying) {
                                                scriptProcessor.disconnect();
                                                gainNode.disconnect();
                                                
                                                showStatus('Playback complete', 'success');
                                                isPlaying = false;
                                                animateSoundBars(false);
                                                playBtn.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>Play Audio';
                                            }
                                        }, 300); // Give time for fade-out
                                    }
                                }, 100); // Small buffer to ensure all audio processing is complete
                            }
                        } else {
                            // No data available, output silence
                            outputBuffer.fill(0);
                        }
                    };
                    
                    while (isPlaying) {
                        const { done, value } = await reader.read();
                        
                        if (done) {
                            // Mark that we've received all audio data
                            allAudioReceived = true;
                            console.log("All audio received - waiting for playback to complete");
                            
                            // Don't disconnect yet - let the onaudioprocess handle the cleanup
                            // when it determines all audio has been played
                            
                            // Exit the reading loop but keep scriptProcessor running
                            break;
                        }
                        
                        if (value) {
                            // Add new data to our accumulated buffer
                            const newBuffer = new Uint8Array(accumulatedBuffer.length + value.length);
                            newBuffer.set(accumulatedBuffer);
                            newBuffer.set(value, accumulatedBuffer.length);
                            accumulatedBuffer = newBuffer;
                            
                            // Process only complete 16-bit samples (ensuring even byte length)
                            const completeBytes = Math.floor(accumulatedBuffer.length / 2) * 2;
                            
                            if (completeBytes > 0) {
                                // Extract only complete samples
                                const dataToProcess = accumulatedBuffer.slice(0, completeBytes);
                                accumulatedBuffer = accumulatedBuffer.slice(completeBytes);
                                
                                // Create float data array
                                const floatData = new Float32Array(dataToProcess.length / 2);
                                
                                // Process two bytes at a time to create 16-bit values
                                for (let i = 0; i < dataToProcess.length; i += 2) {
                                    // Combine two bytes into one 16-bit integer (little-endian)
                                    const int16Value = (dataToProcess[i+1] << 8) | dataToProcess[i];
                                    // Convert to float in [-1, 1] range - handle signed int16
                                    const signedValue = int16Value >= 0x8000 ? int16Value - 0x10000 : int16Value;
                                    floatData[i/2] = signedValue / 32767.0;
                                }
                                
                                // Append to our audio stream
                                const newRemainingArray = new Float32Array(remainingDataArray.length + floatData.length);
                                newRemainingArray.set(remainingDataArray);
                                newRemainingArray.set(floatData, remainingDataArray.length);
                                remainingDataArray = newRemainingArray;
                            }
                        }
                    }
                };
                
                processChunks().catch(error => {
                    console.error('Error processing audio chunks:', error);
                    showStatus('Error during playback: ' + error.message, 'error');
                    isPlaying = false;
                    animateSoundBars(false);
                    playBtn.textContent = 'Play Audio';
                });
                
            } catch (error) {
                console.error('Error playing audio:', error);
                showStatus('Error: ' + error.message, 'error');
                isPlaying = false;
                animateSoundBars(false);
                playBtn.textContent = 'Play Audio';
            }
        });
        
        // Download MP3
        downloadBtn.addEventListener('click', async () => {
            const text = textInput.value.trim();
            const voice = voiceSelect.value;
            const instructions = instructionsInput.value.trim();
            
            if (!text) {
                showStatus('Please enter some text', 'error');
                return;
            }
            
            try {
                showStatus('Generating MP3 file...', 'info');
                animateSoundBars(true);
                downloadBtn.disabled = true;
                
                // Create form data
                const formData = new FormData();
                formData.append('text', text);
                formData.append('voice', voice);
                formData.append('instructions', instructions);
                
                // Request MP3 generation
                const response = await fetch('/generate-mp3', {
                    method: 'POST',
                    body: formData
                });
                
                if (!response.ok) {
                    throw new Error('Failed to generate MP3');
                }
                
                // Create download link
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.style.display = 'none';
                a.href = url;
                a.download = 'tts_output.mp3';
                document.body.appendChild(a);
                a.click();
                
                // Clean up
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);
                
                showStatus('MP3 generated and downloaded successfully', 'success');
                
            } catch (error) {
                console.error('Error downloading MP3:', error);
                showStatus('Error: ' + error.message, 'error');
            } finally {
                animateSoundBars(false);
                downloadBtn.disabled = false;
            }
        });
        
        // Show status message
        function showStatus(message, type) {
            statusMessage.textContent = message;
            statusMessage.classList.remove('hidden', 'text-error', 'text-success', 'text-info');
            
            switch (type) {
                case 'error':
                    statusMessage.classList.add('text-error');
                    break;
                case 'success':
                    statusMessage.classList.add('text-success');
                    break;
                default:
                    statusMessage.classList.add('text-info');
            }
        }
    </script>
</body>
</html> 