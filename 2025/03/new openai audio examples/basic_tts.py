import os
from openai import OpenAI
from termcolor import colored

# Constants
MODEL = "gpt-4o-mini-tts"
VOICE = "alloy"
TEXT = "Hello world! This is a streaming test."
OUTPUT_FILE = "output.mp3"

try:
    # Initialize OpenAI client
    client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
    
    print(colored(f"Creating speech with model: {MODEL}, voice: {VOICE}", "cyan"))
    
    # Using proper streaming method
    with client.audio.speech.with_streaming_response.create(
        model=MODEL,
        voice=VOICE,
        input=TEXT,
        response_format="mp3"  # Default format - options: mp3, opus, aac, flac, wav, pcm
    ) as response:
        print(colored("Streaming response to file...", "yellow"))
        
        # Stream to file with utf-8 encoding where applicable
        with open(OUTPUT_FILE, "wb") as file:
            for chunk in response.iter_bytes():
                file.write(chunk)
                
    print(colored(f"Speech successfully saved to {OUTPUT_FILE}", "green"))

except Exception as e:
    print(colored(f"Error generating speech: {str(e)}", "red"))