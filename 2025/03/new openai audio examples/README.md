# OpenAI Text-to-Speech (TTS) Examples

This repository contains two example scripts demonstrating different approaches to using OpenAI's Text-to-Speech API.

## Scripts Overview

### 1. basic_tts.py
A simple script that generates speech from text and saves it to an MP3 file.

**Features:**
- Saves TTS output to an MP3 file
- Supports multiple output formats (mp3, opus, aac, flac, wav, pcm)
- Uses streaming response to handle large audio files efficiently
- Includes error handling and colored terminal output

**Usage:**
```bash
python basic_tts.py
```

### 2. stream_tts.py
An advanced script that streams TTS audio directly to your speakers in real-time and saves to an MP3 file.

**Features:**
- Real-time audio playback as the API generates speech
- Uses PCM format for minimal latency during playback
- Also saves the same audio to an MP3 file for later use
- Implements proper audio buffering for smooth playback
- Visual feedback during playback with green indicators
- Colored terminal status messages

**Usage:**
```bash
python stream_tts.py
```

## Requirements

Install the required packages:
```bash
pip install -r requirements.txt
```

Required packages:
- openai
- pyaudio
- termcolor
- numpy

## Environment Setup

Set your OpenAI API key as an environment variable:
```bash
export OPENAI_API_KEY='your-api-key-here'
```

## Audio Format Support

The scripts support various audio formats:
- MP3 (default): General use cases
- Opus: Low-latency streaming
- AAC: Digital audio compression
- FLAC: Lossless audio compression
- WAV: Uncompressed audio
- PCM: Raw 24kHz 16-bit audio samples

## Technical Details

### PCM Format Specifications
- Sample Rate: 24kHz
- Channels: Mono
- Bit Depth: 16-bit signed
- Endianness: Little-endian
- Buffer Size: 200ms (4800 samples)

## Error Handling

Both scripts include comprehensive error handling with:
- Colored error messages
- Detailed error descriptions
- Proper resource cleanup
- Safe file/stream handling

## Contributing

Feel free to submit issues and enhancement requests!

## License

[Your chosen license here] 