import os
import sys
import pyaudio
import numpy as np
from openai import OpenAI
from termcolor import colored

# Constants
MODEL = "gpt-4o-mini-tts"
VOICE = "alloy"
TEXT = """The main changes include:
Added MP3 file saving in the stream_tts.py script
Updated the README description for stream_tts.py to mention it now also saves to an MP3 file
Used a second API call with MP3 format for saving to file to ensure good quality"""
SAMPLE_RATE = 24000  # PCM from OpenAI is 24kHz
CHANNELS = 1  # Mono audio
SAMPLE_WIDTH = 2  # 16-bit audio
CHUNK_SIZE = 4800  # Buffer 200ms of audio (24000 * 0.2)
OUTPUT_FILE = "output.mp3"  # Output file path

try:
    # Initialize OpenAI client
    client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
    
    print(colored(f"Creating speech with model: {MODEL}, voice: {VOICE}", "cyan"))
    
    # Initialize PyAudio
    p = pyaudio.PyAudio()
    
    # Open audio stream with correct format
    # OpenAI PCM is 16-bit signed, little-endian
    stream = p.open(
        format=pyaudio.paInt16,  # Explicitly use 16-bit signed int format
        channels=CHANNELS,
        rate=SAMPLE_RATE,
        output=True,
        frames_per_buffer=CHUNK_SIZE
    )
    
    print(colored("Streaming PCM audio directly to speakers...", "yellow"))
    
    # Receive PCM data and handle it properly
    buffer = bytearray()
    all_audio_data = bytearray()  # Store all audio data for saving to file
    
    with client.audio.speech.with_streaming_response.create(
        model=MODEL,
        voice=VOICE,
        input=TEXT,
        response_format="pcm"  # Using PCM format for direct playback
    ) as response:
        for chunk in response.iter_bytes():
            # Store all audio data
            all_audio_data.extend(chunk)
            
            # Add to playback buffer
            buffer.extend(chunk)
            
            # Process complete frames when we have enough data
            while len(buffer) >= CHUNK_SIZE * SAMPLE_WIDTH:
                # Extract a complete chunk from the buffer
                data = buffer[:CHUNK_SIZE * SAMPLE_WIDTH]
                buffer = buffer[CHUNK_SIZE * SAMPLE_WIDTH:]
                
                # Convert bytearray to bytes before playing the audio chunk
                stream.write(bytes(data))
                print(colored("▶", "green"), end="", flush=True)
        
        # Play any remaining audio in the buffer
        if buffer:
            stream.write(bytes(buffer))
            print(colored("▶", "green"), end="", flush=True)
            
    print("\n" + colored("Speech playback completed", "green"))
    
    # Clean up audio playback resources
    stream.stop_stream()
    stream.close()
    p.terminate()
    
    # Save the same audio to MP3 file for later use
    print(colored(f"Saving audio to {OUTPUT_FILE}...", "yellow"))
    
    with client.audio.speech.with_streaming_response.create(
        model=MODEL,
        voice=VOICE,
        input=TEXT,
        response_format="mp3"  # Using MP3 format for file saving
    ) as response:
        with open(OUTPUT_FILE, "wb") as file:
            for chunk in response.iter_bytes():
                file.write(chunk)
    
    print(colored(f"Audio successfully saved to {OUTPUT_FILE}", "green"))

except Exception as e:
    print(colored(f"Error generating or playing speech: {str(e)}", "red")) 