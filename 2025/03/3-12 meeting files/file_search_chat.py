import os
import sys
from openai import OpenAI
from termcolor import colored

# Constants
VECTOR_STORE_ID = "vs_67d12c40e56c81918b95e56f56c95f85"
MODEL = "gpt-4o-mini"
MAX_RESULTS = 5

def clear_screen():
    """Clear the terminal screen based on OS."""
    os.system('cls' if os.name == 'nt' else 'clear')

def print_header():
    """Print the application header."""
    print(colored("=" * 80, "blue"))
    print(colored("File Search Chat Application", "cyan", attrs=["bold"]))
    print(colored("This application uses OpenAI's file search tool to answer your questions", "cyan"))
    print(colored("Type 'exit' to quit the application", "yellow"))
    print(colored("=" * 80, "blue"))
    print()

def process_response(response):
    """Process and display the response from the API."""
    print(colored("\nProcessing response...", "cyan"))
    
    # Track if we found any search results to display
    found_search_results = False
    
    # Process each output item
    for item in response.output:
        # Handle file search call information
        if item.type == "file_search_call":
            print(colored(f"Search status: {item.status}", "yellow"))
            
            # Display search queries
            if hasattr(item, 'queries') and item.queries:
                print(colored(f"Search queries: {', '.join(item.queries)}", "yellow"))
            
            # Display search results if available
            if hasattr(item, 'search_results') and item.search_results:
                found_search_results = True
                print(colored("\n📚 Search Results:", "magenta", attrs=["bold"]))
                for i, result in enumerate(item.search_results, 1):
                    print(colored(f"Result {i}: {result.file.filename}", "blue"))
                    print(colored(f"Excerpt: {result.text[:150]}...", "white"))
                    print(colored("-" * 40, "blue"))
        
        # Handle the assistant's message
        elif item.type == "message":
            print(colored("\n🤖 Assistant:", "green", attrs=["bold"]))
            for content in item.content:
                if content.type == "output_text":
                    print(colored(content.text, "white"))
                    
                    # Display citations if available
                    if hasattr(content, 'annotations') and content.annotations:
                        print(colored("\n📑 Citations:", "magenta", attrs=["bold"]))
                        for annotation in content.annotations:
                            if annotation.type == "file_citation":
                                print(colored(f"- {annotation.filename}", "blue"))
    
    # If no search results were found but we know a search was performed
    if not found_search_results:
        print(colored("\nNo detailed search results available in the response.", "yellow"))
    
    print()
    print(colored("-" * 80, "blue"))

def main():
    """Main function to run the chat application."""
    try:
        # Initialize OpenAI client
        client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
        print(colored("OpenAI client initialized successfully", "green"))
        
        # Check if API key is set
        if not os.getenv("OPENAI_API_KEY"):
            print(colored("Error: OPENAI_API_KEY environment variable is not set", "red"))
            print(colored("Please set your OpenAI API key as an environment variable", "yellow"))
            sys.exit(1)
        
        clear_screen()
        print_header()
        
        # Store conversation history using previous_response_id
        previous_response_id = None
        
        # Main chat loop
        while True:
            # Get user input
            user_input = input(colored("You: ", "green"))
            
            # Check if user wants to exit
            if user_input.lower() in ['exit', 'quit', 'bye']:
                print(colored("\nThank you for using File Search Chat. Goodbye!", "cyan"))
                break
            
            # Skip empty inputs
            if not user_input.strip():
                continue
            
            print(colored("\nSearching knowledge base and generating response...", "yellow"))
            
            try:
                # Create API request parameters
                request_params = {
                    "model": MODEL,
                    "input": user_input,
                    "tools": [{
                        "type": "file_search",
                        "vector_store_ids": [VECTOR_STORE_ID],
                        "max_num_results": MAX_RESULTS
                    }],
                    "include": ["output[*].file_search_call.search_results"]
                }
                
                # Add previous_response_id if available for conversation continuity
                if previous_response_id:
                    request_params["previous_response_id"] = previous_response_id
                
                # Send request to OpenAI
                response = client.responses.create(**request_params)
                
                # Store the response ID for the next iteration
                previous_response_id = response.id
                
                # Process and display the response
                process_response(response)
                
            except Exception as e:
                print(colored(f"Error: {str(e)}", "red"))
                print(colored("Please try again with a different query.", "yellow"))
    
    except Exception as e:
        print(colored(f"Application error: {str(e)}", "red"))
        sys.exit(1)

if __name__ == "__main__":
    main() 