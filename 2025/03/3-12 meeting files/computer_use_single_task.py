#!/usr/bin/env python3
# OpenAI Computer-Using Agent Demo
# This script demonstrates the capabilities of the new Computer Use API

import os
import base64
import time
import pyautogui
import keyboard
from termcolor import colored
from openai import OpenAI

# WARNING!!! THIS SCRIPT WILL TAKE CONTROL OF YOUR COMPUTER!!! KEEP A CLOSE EYE ON IT!!!

# MAJOR VARIABLES
API_KEY = os.getenv("OPENAI_API_KEY")
MODEL = "computer-use-preview"
DISPLAY_WIDTH = 1920
DISPLAY_HEIGHT = 1080
ENVIRONMENT = "windows"
USER_TASK = "open edge and search 'nvidia cards' on google. Do not stop until you have performed the search"

def print_step(step_name):
    """Print a step in the process with formatting"""
    print(colored(f"\n[STEP] {step_name}", "cyan", attrs=["bold"]))

def print_response(title, content):
    """Print a response with formatting"""
    print(colored(f"\n{title}:", "green"))
    print(colored(f"{content}", "white"))

def print_error(error_msg, exception=None):
    """Print an error message with formatting"""
    print(colored(f"\n[ERROR] {error_msg}", "red", attrs=["bold"]))
    if exception:
        print(colored(f"Exception: {str(exception)}", "red"))

def initialize_client():
    """Initialize the OpenAI client"""
    try:
        print_step("Initializing OpenAI client")
        client = OpenAI(api_key=API_KEY)
        print(colored("✓ Client initialized successfully", "green"))
        return client
    except Exception as e:
        print_error("Failed to initialize OpenAI client", e)
        exit(1)

def take_screenshot():
    """Take a screenshot of the current screen"""
    try:
        print_step("Taking screenshot")
        screenshot = pyautogui.screenshot()
        screenshot_path = "temp_screenshot.png"
        screenshot.save(screenshot_path)
        
        # Convert to base64
        with open(screenshot_path, "rb") as image_file:
            screenshot_bytes = image_file.read()
            screenshot_base64 = base64.b64encode(screenshot_bytes).decode("utf-8")
        
        print(colored("✓ Screenshot captured successfully", "green"))
        return screenshot_base64
    except Exception as e:
        print_error("Failed to take screenshot", e)
        return None

def handle_model_action(action):
    """Execute the action suggested by the model"""
    try:
        action_type = action.type
        print_step(f"Executing action: {action_type}")
        
        match action_type:
            case "click":
                x, y = action.x, action.y
                button = action.button
                print(colored(f"Clicking at ({x}, {y}) with {button} button", "yellow"))
                button_map = {"left": "left", "right": "right", "middle": "middle"}
                pyautogui.click(x, y, button=button_map.get(button, "left"))
                
            case "double_click":
                x, y = action.x, action.y
                button = action.button
                print(colored(f"Double-clicking at ({x}, {y}) with {button} button", "yellow"))
                button_map = {"left": "left", "right": "right", "middle": "middle"}
                pyautogui.doubleClick(x, y, button=button_map.get(button, "left"))
                
            case "scroll":
                x, y = action.x, action.y
                scroll_x, scroll_y = action.scroll_x, action.scroll_y
                print(colored(f"Scrolling at ({x}, {y}) with offsets ({scroll_x}, {scroll_y})", "yellow"))
                pyautogui.moveTo(x, y)
                pyautogui.scroll(int(scroll_y))
                
            case "keypress":
                keys = action.keys
                print(colored(f"Pressing keys: {keys}", "yellow"))
                for key in keys:
                    pyautogui.press(key)
                    
            case "type":
                text = action.text
                print(colored(f"Typing text: {text}", "yellow"))
                pyautogui.typewrite(text)
                
            case "wait":
                duration = getattr(action, "duration", 2)
                print(colored(f"Waiting for {duration} seconds", "yellow"))
                time.sleep(duration)
                
            case "screenshot":
                print(colored("Taking screenshot", "yellow"))
                # Screenshot is taken at each turn anyway
                
            case _:
                print(colored(f"Unrecognized action: {action_type}", "red"))
                
        # Add a small delay after each action to let the UI update
        time.sleep(0.5)
        print(colored(f"✓ Action executed successfully", "green"))
        return True
    except Exception as e:
        print_error(f"Failed to execute action: {action_type}", e)
        return False

def check_for_exit():
    """Check if user wants to exit the script"""
    if keyboard.is_pressed('ctrl+c'):
        print_step("User requested to exit (Ctrl+C detected)")
        return True
    return False

def computer_use_loop(client, response):
    """Run the loop that executes computer actions until no 'computer_call' is found"""
    try:
        while True:
            # Check if user wants to exit
            if check_for_exit():
                return None
                
            # Find computer calls in the response
            computer_calls = [item for item in response.output if item.type == "computer_call"]
            if not computer_calls:
                print_step("No more computer actions to execute")
                # Print the final output from the model
                for item in response.output:
                    if hasattr(item, 'content') and item.content:
                        for content in item.content:
                            if hasattr(content, 'text') and content.text:
                                print_response("Final Output", content.text)
                break
            
            # We expect at most one computer call per response
            computer_call = computer_calls[0]
            last_call_id = computer_call.call_id
            action = computer_call.action
            
            # Check for pending safety checks
            pending_safety_checks = getattr(computer_call, "pending_safety_checks", [])
            acknowledged_safety_checks = []
            
            if pending_safety_checks:
                print_step("Safety checks detected")
                for check in pending_safety_checks:
                    check_id = check.id
                    check_code = check.code
                    check_message = check.message
                    
                    print(colored(f"Safety check: {check_code}", "red"))
                    print(colored(f"Message: {check_message}", "red"))
                    
                    # Ask user to acknowledge the safety check
                    print(colored("Do you want to proceed? (y/n): ", "yellow"), end="")
                    user_input = input().lower()
                    
                    if user_input == 'y':
                        acknowledged_safety_checks.append({
                            "id": check_id,
                            "code": check_code,
                            "message": check_message
                        })
                    else:
                        print_step("User declined to proceed with the action")
                        return None
            
            # Execute the action
            success = handle_model_action(action)
            if not success:
                print_error("Failed to execute action, stopping the loop")
                return None
                
            # Take a screenshot after the action
            screenshot_base64 = take_screenshot()
            if not screenshot_base64:
                print_error("Failed to take screenshot, stopping the loop")
                return None
                
            # Prepare the next request with computer_call_output
            # Following the documentation exactly
            input_data = [{
                "type": "computer_call_output",
                "call_id": last_call_id,
                "output": {
                    "type": "computer_screenshot",
                    "image_url": f"data:image/png;base64,{screenshot_base64}"
                }
            }]
            
            # Add acknowledged safety checks if any
            if acknowledged_safety_checks:
                input_data[0]["acknowledged_safety_checks"] = acknowledged_safety_checks
                
            # Send the screenshot back as a computer_call_output
            print_step("Sending screenshot to OpenAI")
            response = client.responses.create(
                model=MODEL,
                previous_response_id=response.id,
                tools=[{
                    "type": "computer_use_preview",
                    "display_width": DISPLAY_WIDTH,
                    "display_height": DISPLAY_HEIGHT,
                    "environment": ENVIRONMENT
                }],
                input=input_data,
                truncation="auto"
            )
            print(colored("✓ Received response from OpenAI", "green"))
            
        return response
    except KeyboardInterrupt:
        print_step("User interrupted the process")
        return None
    except Exception as e:
        print_error("Error in computer use loop", e)
        return None

def main():
    """Main function to run the demo"""
    print(colored("\n=== OPENAI COMPUTER-USING AGENT DEMO ===", "magenta", attrs=["bold"]))
    
    # Check if API key is set
    if not API_KEY:
        print_error("OPENAI_API_KEY environment variable is not set")
        exit(1)
    
    # Initialize client
    client = initialize_client()
    
    # Take initial screenshot
    screenshot_base64 = take_screenshot()
    if not screenshot_base64:
        return
    
    # Start the computer use session
    try:
        print_step(f"Starting computer use session with task: {USER_TASK}")
        
        # Following the documentation exactly
        response = client.responses.create(
            model=MODEL,
            tools=[{
                "type": "computer_use_preview",
                "display_width": DISPLAY_WIDTH,
                "display_height": DISPLAY_HEIGHT,
                "environment": ENVIRONMENT
            }],
            input=[
                {
                    "role": "user",
                    "type": "message",
                    "content": USER_TASK
                }
            ],
            truncation="auto"
        )
        
        print(colored("✓ Received initial response from OpenAI", "green"))
        
        # Run the computer use loop
        final_response = computer_use_loop(client, response)
        
        if final_response:
            print(colored("\n=== TASK COMPLETED SUCCESSFULLY ===", "magenta", attrs=["bold"]))
        else:
            print(colored("\n=== TASK INTERRUPTED ===", "magenta", attrs=["bold"]))
            
    except KeyboardInterrupt:
        print_step("User interrupted the process")
    except Exception as e:
        print_error("Error in main function", e)
    
if __name__ == "__main__":
    main() 