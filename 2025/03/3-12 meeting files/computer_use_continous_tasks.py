import os
import time
import base64
import json
from termcolor import colored
from openai import OpenAI
from openai import AsyncOpenAI
import pyautogui
import sys

# Major variables
API_KEY = os.getenv("OPENAI_API_KEY")
DISPLAY_WIDTH = 1920
DISPLAY_HEIGHT = 1080
ENVIRONMENT = "windows"
MODEL = "computer-use-preview"

def print_step(message):
    """Print a step in the process with color."""
    print(colored(f"[STEP] {message}", "cyan"))

def print_info(message):
    """Print information with color."""
    print(colored(f"[INFO] {message}", "green"))

def print_error(message, error=None):
    """Print error with color."""
    print(colored(f"[ERROR] {message}", "red"))
    if error:
        print(colored(f"        {str(error)}", "red"))

def print_chat(message):
    """Print chat messages with color."""
    print(colored(f"[AGENT] {message}", "magenta"))

def get_screenshot():
    """Take a screenshot and return base64 encoded string."""
    try:
        print_step("Taking screenshot")
        screenshot = pyautogui.screenshot()
        screenshot_path = "temp_screenshot.png"
        screenshot.save(screenshot_path)
        
        with open(screenshot_path, "rb") as image_file:
            encoded_string = base64.b64encode(image_file.read()).decode("utf-8")
        
        # Clean up the temporary file
        os.remove(screenshot_path)
        return encoded_string
    except Exception as e:
        print_error("Failed to take screenshot", e)
        return None

def handle_model_action(action):
    """Execute the action suggested by the model."""
    try:
        action_type = action.type
        
        print_step(f"Executing action: {action_type}")
        
        if action_type == "click":
            x, y = action.x, action.y
            button = action.button
            print_info(f"Clicking at ({x}, {y}) with {button} button")
            
            button_map = {
                "left": "left",
                "right": "right",
                "middle": "middle"
            }
            pyautogui.click(x, y, button=button_map.get(button, "left"))
            
        elif action_type == "double_click":
            x, y = action.x, action.y
            print_info(f"Double-clicking at ({x}, {y})")
            pyautogui.doubleClick(x, y)
            
        elif action_type == "scroll":
            x, y = action.x, action.y
            scroll_x, scroll_y = action.scroll_x, action.scroll_y
            print_info(f"Scrolling at ({x}, {y}) with offsets ({scroll_x}, {scroll_y})")
            pyautogui.moveTo(x, y)
            pyautogui.scroll(int(scroll_y))
            
        elif action_type == "keypress":
            keys = action.keys
            print_info(f"Pressing keys: {keys}")
            for key in keys:
                pyautogui.press(key)
                
        elif action_type == "type":
            text = action.text
            print_info(f"Typing text: {text}")
            pyautogui.write(text)
            
        elif action_type == "wait":
            duration = getattr(action, "duration", 2)
            print_info(f"Waiting for {duration} seconds")
            time.sleep(duration)
            
        elif action_type == "screenshot":
            print_info("Taking screenshot (no action needed)")
            # Screenshot is taken at each turn automatically
            
        else:
            print_info(f"Unrecognized action: {action_type}")
            
        # Add a small delay after each action to let the UI update
        time.sleep(1)
        
    except Exception as e:
        print_error(f"Error handling action {action_type}", e)

def computer_use_loop(client, response):
    """Run the loop that executes computer actions until no 'computer_call' is found."""
    try:
        while True:
            # Extract computer calls from the response
            computer_calls = [item for item in response.output if item.type == "computer_call"]
            
            if not computer_calls:
                print_step("No more computer calls. Task completed or assistance needed.")
                for item in response.output:
                    if hasattr(item, "type") and item.type == "text":
                        print_chat(item.text)
                break
            
            # We expect at most one computer call per response
            computer_call = computer_calls[0]
            call_id = computer_call.call_id
            action = computer_call.action
            pending_safety_checks = getattr(computer_call, "pending_safety_checks", [])
            
            # Handle safety checks if any
            acknowledged_safety_checks = []
            if pending_safety_checks:
                print_step("Safety checks detected")
                for check in pending_safety_checks:
                    check_id = check.id
                    check_code = check.code
                    check_message = check.message
                    
                    print_info(f"Safety check: {check_code}")
                    print_info(f"Message: {check_message}")
                    
                    # In a real application, you might want to ask for user confirmation here
                    # For this demo, we'll automatically acknowledge all safety checks
                    acknowledged_safety_checks.append({
                        "id": check_id,
                        "code": check_code,
                        "message": check_message
                    })
            
            # Execute the action
            handle_model_action(action)
            
            # Take a screenshot after the action
            screenshot_base64 = get_screenshot()
            
            if not screenshot_base64:
                print_error("Failed to get screenshot. Exiting loop.")
                break
            
            # Send the screenshot back as a computer_call_output
            print_step("Sending screenshot back to the model")
            response = client.responses.create(
                model=MODEL,
                previous_response_id=response.id,
                tools=[
                    {
                        "type": "computer_use_preview",
                        "display_width": DISPLAY_WIDTH,
                        "display_height": DISPLAY_HEIGHT,
                        "environment": ENVIRONMENT
                    }
                ],
                input=[
                    {
                        "type": "computer_call_output",
                        "call_id": call_id,
                        "acknowledged_safety_checks": acknowledged_safety_checks,
                        "output": {
                            "type": "input_image",
                            "image_url": f"data:image/png;base64,{screenshot_base64}"
                        }
                    }
                ],
                truncation="auto"
            )
            
        return response
    except Exception as e:
        print_error("Error in computer use loop", e)
        return None

def execute_task(client, task):
    """Execute a single task with the computer-using agent."""
    try:
        print_step(f"Executing task: {task}")
        
        print_step("Taking initial screenshot")
        initial_screenshot = get_screenshot()
        
        if not initial_screenshot:
            print_error("Failed to get initial screenshot. Exiting.")
            return None
        
        print_step("Sending initial request to OpenAI")
        response = client.responses.create(
            model=MODEL,
            tools=[
                {
                    "type": "computer_use_preview",
                    "display_width": DISPLAY_WIDTH,
                    "display_height": DISPLAY_HEIGHT,
                    "environment": ENVIRONMENT
                }
            ],
            input=[
                {
                    "role": "user",
                    "content": task
                }
            ],
            truncation="auto"
        )
        
        # According to the docs, we need to wait for the first computer_call
        # before sending a screenshot
        computer_calls = [item for item in response.output if item.type == "computer_call"]
        
        if not computer_calls:
            print_error("No computer call in the initial response.")
            return None
            
        computer_call = computer_calls[0]
        call_id = computer_call.call_id
        
        # Send the screenshot as a computer_call_output
        print_step("Sending initial screenshot to the model")
        response = client.responses.create(
            model=MODEL,
            previous_response_id=response.id,
            tools=[
                {
                    "type": "computer_use_preview",
                    "display_width": DISPLAY_WIDTH,
                    "display_height": DISPLAY_HEIGHT,
                    "environment": ENVIRONMENT
                }
            ],
            input=[
                {
                    "type": "computer_call_output",
                    "call_id": call_id,
                    "output": {
                        "type": "input_image",
                        "image_url": f"data:image/png;base64,{initial_screenshot}"
                    }
                }
            ],
            truncation="auto"
        )
        
        print_step("Starting computer use loop")
        final_response = computer_use_loop(client, response)
        
        if final_response:
            print_step("Task completed")
            for item in final_response.output:
                if hasattr(item, "type") and item.type == "text":
                    print_chat(item.text)
        
        return final_response
    except Exception as e:
        print_error("Error executing task", e)
        return None

def interactive_chat():
    """Run an interactive chat with the computer-using agent."""
    try:
        print_step("Initializing OpenAI client")
        client = OpenAI(api_key=API_KEY)
        
        print_info("Welcome to the Computer-Using Agent Chat!")
        print_info("Type your tasks and the agent will execute them.")
        print_info("Type 'exit' or 'quit' to end the session.")
        
        conversation_id = None
        
        while True:
            # Get user input
            print()
            user_input = input(colored("[YOU] ", "yellow"))
            
            # Check if user wants to exit
            if user_input.lower() in ["exit", "quit", "bye", "goodbye"]:
                print_info("Ending session. Goodbye!")
                break
            
            # Skip empty inputs
            if not user_input.strip():
                continue
            
            # Execute the task
            response = execute_task(client, user_input)
            
            if not response:
                print_error("Failed to execute task. Please try again.")
                
            print_info("Ready for your next task!")
            
    except KeyboardInterrupt:
        print_step("\nProcess interrupted by user")
    except Exception as e:
        print_error("Unexpected error in interactive chat", e)

if __name__ == "__main__":
    try:
        print_step("Starting Computer-Using Agent Chat")
        interactive_chat()
    except KeyboardInterrupt:
        print_step("Process interrupted by user")
    except Exception as e:
        print_error("Unexpected error", e) 