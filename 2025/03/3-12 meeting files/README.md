# File Search Chat Application

This application demonstrates OpenAI's file search tool functionality, allowing you to chat with an AI assistant that can search through files in a vector store to provide more accurate and contextual responses.

## Features

- Interactive chat interface in the terminal
- Uses OpenAI's file search tool to search through documents
- Displays search results and citations
- Maintains conversation context between messages

## Prerequisites

- Python 3.7+
- OpenAI API key

## Installation

1. Clone this repository or download the files
2. Install the required packages:

```bash
pip install -r requirements.txt
```

3. Set your OpenAI API key as an environment variable:

```bash
# On Windows
set OPENAI_API_KEY=your_api_key_here

# On macOS/Linux
export OPENAI_API_KEY=your_api_key_here
```

## Usage

### File Search Demo

To run a simple demonstration of the file search functionality:

```bash
python file_search_demo.py
```

This will execute a predefined query and show how the file search tool works.

### Interactive Chat Application

To start the interactive chat application:

```bash
python file_search_chat.py
```

- Type your questions or messages and press Enter
- The application will use the file search tool to find relevant information
- Type 'exit' to quit the application

## How It Works

1. The application sends your query to OpenAI's API along with the file search tool configuration
2. The model decides when to use the file search tool to look up information
3. The API returns both the search results and the model's response
4. The application displays this information in a user-friendly format
5. Conversation context is maintained between messages using the `previous_response_id` parameter

## Notes

- The vector store ID is pre-configured in the application
- The application uses the gpt-4o-mini model by default
- Search results are limited to 5 per query to optimize performance 