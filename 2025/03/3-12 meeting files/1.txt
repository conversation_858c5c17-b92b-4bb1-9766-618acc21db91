Analytical engine

Article
Talk
Read
Edit
View history

Tools
Appearance hide
Text

Small

Standard

Large
Width

Standard

Wide
Color (beta)

Automatic

Light

Dark
From Wikipedia, the free encyclopedia

Portion of the calculating machine with a printing mechanism of the analytical engine, built by <PERSON>, as displayed at the Science Museum (London)[1]
History of computing

Hardware
Hardware 1960s to present
Software
SoftwareSoftware configuration managementUnixFree software and open-source software
Computer science
Artificial intelligenceCompiler constructionEarly computer scienceOperating systemsProgramming languagesProminent pioneersSoftware engineering
Modern concepts
General-purpose CPUsGraphical user interfaceInternetLaptopsPersonal computersVideo gamesWorld Wide WebCloudQuantum
By country
BulgariaEastern BlocPolandRomaniaSouth AmericaSoviet UnionYugoslavia
Timeline of computing
before 19501950–19791980–19891990–19992000–20092010–20192020–presentmore timelines ...
Glossary of computer science
 Category
vte
The analytical engine was a proposed digital mechanical general-purpose computer designed by English mathematician and computer pioneer <PERSON>.[2][3] It was first described in 1837 as the successor to <PERSON><PERSON><PERSON>'s Difference Engine, which was a design for a simpler mechanical calculator.[4]

The analytical engine incorporated an arithmetic logic unit, control flow in the form of conditional branching and loops, and integrated memory, making it the first design for a general-purpose computer that could be described in modern terms as Turing-Complete.[5][6] In other words, the structure of the analytical engine was essentially the same as that which has dominated computer design in the electronic era.[3] The analytical engine is one of the most successful achievements of <PERSON> <PERSON><PERSON>ge.

Ba<PERSON>ge was never able to complete construction of any of his machines due to conflicts with his chief engineer and inadequate funding.[7][8] It was not until 1941 that <PERSON> Zuse built the first general-purpose computer, Z3, more than a century after Babbage had proposed the pioneering analytical engine in 1837.[3]

Design

Two types of punched cards used to program the machine. Foreground: 'operational cards', for inputting instructions; background: 'variable cards', for inputting data
Babbage's first attempt at a mechanical computing device, the Difference Engine, was a special-purpose machine designed to tabulate logarithms and trigonometric functions by evaluating finite differences to create approximating polynomials. Construction of this machine was never completed; Babbage had conflicts with his chief engineer, Joseph Clement, and ultimately the British government withdrew its funding for the project.[9][10][11]

During this project, Babbage realised that a much more general design, the analytical engine, was possible.[9] The work on the design of the analytical engine started around 1833.[12][4]

The input, consisting of programs ("formulae") and data,[13][9] was to be provided to the machine via punched cards, a method being used at the time to direct mechanical looms such as the Jacquard loom.[14] For output, the machine would have a printer, a curve plotter, and a bell.[9] The machine would also be able to punch numbers onto cards to be read in later. It employed ordinary base-10 fixed-point arithmetic.[9]

There was to be a store (that is, a memory) capable of holding 1,000 numbers of 40 decimal digits[15] each (ca. 16.6 kB). An arithmetic unit (the "mill") would be able to perform all four arithmetic operations, plus comparisons and optionally square roots.[16] Initially (1838) it was conceived as a difference engine curved back upon itself, in a generally circular layout, with the long store exiting off to one side.[17] Later drawings (1858) depict a regularised grid layout.[18][19] Like the central processing unit (CPU) in a modern computer, the mill would rely upon its own internal procedures, roughly equivalent to microcode in modern CPUs, to be stored in the form of pegs inserted into rotating drums called "barrels", to carry out some of the more complex instructions the user's program might specify.[7]

The programming language to be employed by users was akin to modern day assembly languages. Loops and conditional branching were possible, and so the language as conceived would have been Turing-complete as later defined by Alan Turing. Three different types of punch cards were used: one for arithmetical operations, one for numerical constants, and one for load and store operations, transferring numbers from the store to the arithmetical unit or back. There were three separate readers for the three types of cards. Babbage developed some two dozen programs for the analytical engine between 1837 and 1840, and one program later.[14][20] These programs treat polynomials, iterative formulas, Gaussian elimination, and Bernoulli numbers.[14][21]

In 1842, the Italian mathematician Luigi Federico Menabrea published a description of the engine in French,[22] based on lectures Babbage gave when he visited Turin in 1840.[23] In 1843, the description was translated into English and extensively annotated by Ada Lovelace, who had become interested in the engine eight years earlier.[13] In recognition of her additions to Menabrea's paper, which included a way to calculate Bernoulli numbers using the machine (widely considered to be the first complete computer program), she has been described by many as the first computer programmer, although others have challenged this view.

Construction
Late in his life, Babbage sought ways to build a simplified version of the machine, and assembled a small part of it before his death in 1871.[1][7][24]

In 1878, a committee of the British Association for the Advancement of Science described the analytical engine as "a marvel of mechanical ingenuity", but recommended against constructing it. The committee acknowledged the usefulness and value of the machine, but could not estimate the cost of building it, and were unsure whether the machine would function correctly after being built.[25][26]


Henry Babbage's analytical engine mill, built in 1910,[27] in the Science Museum (London)
Intermittently from 1880 to 1910,[28] Babbage's son Henry Prevost Babbage was constructing a part of the mill and the printing apparatus. In 1910, it was able to calculate a (faulty) list of multiples of pi.[29] This constituted only a small part of the whole engine; it was not programmable and had no storage. (Popular images of this section have sometimes been mislabelled, implying that it was the entire mill or even the entire engine.) Henry Babbage's "analytical engine mill" is on display at the Science Museum in London.[27] Henry also proposed building a demonstration version of the full engine, with a smaller storage capacity: "perhaps for a first machine ten (columns) would do, with fifteen wheels in each".[30] Such a version could manipulate 20 numbers of 25 digits each, and what it could be told to do with those numbers could still be impressive. "It is only a question of cards and time", wrote Henry Babbage in 1888, "... and there is no reason why (twenty thousand) cards should not be used if necessary, in an analytical engine for the purposes of the mathematician".[30]

In 1991, the London Science Museum built a complete and working specimen of Babbage's Difference Engine No. 2, a design that incorporated refinements Babbage discovered during the development of the analytical engine.[5] This machine was built using materials and engineering tolerances that would have been available to Babbage, quelling the suggestion that Babbage's designs could not have been produced using the manufacturing technology of his time.[31]

In October 2010, John Graham-Cumming started a "Plan 28" campaign to raise funds by "public subscription" to enable serious historical and academic study of Babbage's plans, with a view to then build and test a fully working virtual design which will then in turn enable construction of the physical analytical engine.[32][33][34] As of May 2016, actual construction had not been attempted, since no consistent understanding could yet be obtained from Babbage's original design drawings. In particular it was unclear whether it could handle the indexed variables which were required for Lovelace's Bernoulli program.[35] By 2017, the "Plan 28" effort reported that a searchable database of all catalogued material was available, and an initial review of Babbage's voluminous Scribbling Books had been completed.[36]

Many of Babbage's original drawings have been digitised and are publicly available online.[37]

Instruction set

Plan diagram of the Analytical Engine from 1840
Babbage is not known to have written down an explicit set of instructions for the engine in the manner of a modern processor manual. Instead he showed his programs as lists of states during their execution, showing what operator was run at each step with little indication of how the control flow would be guided.

Allan G. Bromley has assumed that the card deck could be read in forwards and backwards directions as a function of conditional branching after testing for conditions, which would make the engine Turing-complete:

...the cards could be ordered to move forward and reverse (and hence to loop)...[14]

The introduction for the first time, in 1845, of user operations for a variety of service functions including, most importantly, an effective system for user control of looping in user programs. There is no indication how the direction of turning of the operation and variable cards is specified. In the absence of other evidence I have had to adopt the minimal default assumption that both the operation and variable cards can only be turned backward as is necessary to implement the loops used in Babbage's sample programs. There would be no mechanical or microprogramming difficulty in placing the direction of motion under the control of the user.[38]

In their emulator of the engine, Fourmilab say:

The Engine's Card Reader is not constrained to simply process the cards in a chain one after another from start to finish. It can, in addition, directed by the very cards it reads and advised by whether the Mill's run-up lever is activated, either advance the card chain forward, skipping the intervening cards, or backward, causing previously-read cards to be processed once again.

This emulator does provide a written symbolic instruction set, though this has been constructed by its authors rather than based on Babbage's original works. For example, a factorial program would be written as:

N0 6
N1 1
N2 1
×
L1
L0
S1
–
L0
L2
S0
L2
L0
CB?11
where the CB is the conditional branch instruction or "combination card" used to make the control flow jump, in this case backward by 11 cards.

Influence
Predicted influence
Babbage understood that the existence of an automatic computer would kindle interest in the field now known as algorithmic efficiency, writing in his Passages from the Life of a Philosopher, "As soon as an analytical engine exists, it will necessarily guide the future course of the science. Whenever any result is sought by its aid, the question will then arise—By what course of calculation can these results be arrived at by the machine in the shortest time?"[39]

Computer science
From 1872, Henry continued diligently with his father's work and then intermittently in retirement in 1875.[40]

Percy Ludgate wrote about the engine in 1914[41] and published his own design for an analytical engine in 1909.[42][43] It was drawn up in detail, but never built, and the drawings have never been found. Ludgate's engine would be much smaller (about 8 cubic feet (230 L), which corresponds to cube of side length 2 feet (61 cm)) than Babbage's, and hypothetically would be capable of multiplying two 20-decimal-digit numbers in about six seconds.[44]

In his work Essays on Automatics (1914) Leonardo Torres Quevedo, inspired by Babbage, designed a theoretical electromechanical calculating machine which was to be controlled by a read-only program. The paper also contains the idea of floating-point arithmetic.[45][46][47] In 1920, to celebrate the 100th anniversary of the invention of the arithmometer, Torres presented in Paris the Electromechanical Arithmometer, which consisted of an arithmetic unit connected to a (possibly remote) typewriter, on which commands could be typed and the results printed automatically.[48][49]

Vannevar Bush's paper Instrumental Analysis (1936) included several references to Babbage's work. In the same year he started the Rapid Arithmetical Machine project to investigate the problems of constructing an electronic digital computer.[50]

Despite this groundwork, Babbage's work fell into historical obscurity, and the analytical engine was unknown to builders of electromechanical and electronic computing machines in the 1930s and 1940s when they began their work, resulting in the need to re-invent many of the architectural innovations Babbage had proposed. Howard Aiken, who built the quickly-obsoleted electromechanical calculator, the Harvard Mark I, between 1937 and 1945, praised Babbage's work likely as a way of enhancing his own stature, but knew nothing of the analytical engine's architecture during the construction of the Mark I, and considered his visit to the constructed portion of the analytical engine "the greatest disappointment of my life".[51] The Mark I showed no influence from the analytical engine and lacked the analytical engine's most prescient architectural feature, conditional branching.[51] J. Presper Eckert and John W. Mauchly similarly were not aware of the details of Babbage's analytical engine work prior to the completion of their design for the first electronic general-purpose computer, the ENIAC.[52][53]

Comparison to other early computers
If the analytical engine had been built, it would have been digital, programmable and Turing-complete. It would, however, have been very slow. Luigi Federico Menabrea reported in Sketch of the Analytical Engine: "Mr. Babbage believes he can, by his engine, form the product of two numbers, each containing twenty figures, in three minutes".[54] By comparison the Harvard Mark I could perform the same task in just six seconds (though it is debatable that computer is Turing complete; the ENIAC, which is, would also have been faster). A modern CPU could do the same thing in under a billionth of a second.

Further information: History of computing hardware § Early digital computer characteristics
Name	First operational	Numeral system	Computing mechanism	Programming	Turing complete	Memory
Difference engine	Not built until the 1990s (design 1820s)	Decimal	Mechanical	Not programmable; initial numerical constants of polynomial differences set physically	No	Physical state of wheels in axes
Analytical Engine	Not built (design 1830s)[55]	Decimal	Mechanical	Program-controlled by punched cards	Yes (design; not built, yet)	Physical state of wheels in axes
Ludgate's Analytical Engine	Not built (design 1909)	Decimal	Mechanical	Program-controlled by punched cards	Yes (not built)	Physical state of rods
Torres' Analytical Machine	1920	Decimal	Electro-mechanical	Not programmable; input and output settings specified by patch cables	No	Mechanical relays
Zuse Z1 (Germany)	1939	Binary floating point	Mechanical	Not programmable; cipher input settings specified by patch cables	No	Physical state of rods
Bombe (Poland, UK, US)	1939 (Polish), March 1940 (British), May 1943 (US)	Character computations	Electro-mechanical	Not programmable; cipher input settings specified by patch cables	No	Physical state of rotors
Zuse Z2 (Germany)	1940	Binary fixed point	Electro-mechanical (mechanical memory)	Program-controlled by punched 35 mm film stock (no conditional branch)	No	Physical state of rods
Zuse Z3 (Germany)	May 1941	Binary floating point	Electro-mechanical	Program-controlled by punched 35 mm film stock (but no conditional branch)	In theory (1998)	Mechanical relays
Atanasoff–Berry Computer (US)	1942	Binary	Electronic	Not programmable; linear system coefficients input using punched cards	No	Regenerative capacitor memory
Colossus Mark 1 (UK)	December 1943	Binary	Electronic	Program-controlled by patch cables and switches	No	Thermionic valves (vacuum tubes) and thyratrons
Harvard Mark I – IBM ASCC (US)	May 1944	Decimal	Electro-mechanical	Program-controlled by 24-channel punched paper tape (but no conditional branch)	Debatable	Mechanical relays[56]
Colossus Mark 2 (UK)	1 June 1944	Binary	Electronic	Program-controlled by patch cables and switches	Conjectured[57]
Zuse Z4 (Germany)	March 1945 (or 1948)[58]	Binary floating point	Electro-mechanical	Program-controlled by punched 35 mm film stock	In 1950	Mechanical relays
ENIAC (US)	December 1945	Decimal	Electronic	Program-controlled by patch cables and switches	Yes	Vacuum tube triode flip-flops
Manchester Baby (UK)	June 1948	Binary	Electronic	Binary program entered into memory by keyboard[59] (first electronic stored-program digital computer)	Yes	Williams cathode ray tube
EDSAC (UK)	May 1949	Binary	Electronic	Five-bit opcode and variable-length operand (first stored-program computer offering computing services to a wide community).	Yes	Mercury delay lines
In popular culture
The cyberpunk novelists William Gibson and Bruce Sterling co-authored a steampunk novel of alternative history titled The Difference Engine in which Babbage's difference and analytical engines became available to Victorian society. The novel explores the consequences and implications of the early introduction of computational technology.
Moriarty by Modem, a short story by Jack Nimersheim, describes an alternative history where Babbage's analytical engine was indeed completed and had been deemed highly classified by the British government. The characters of Sherlock Holmes and Moriarty had in reality been a set of prototype programs written for the analytical engine. This short story follows Holmes as his program is implemented on modern computers and he is forced to compete against his nemesis yet again in the modern counterparts of Babbage's analytical engine.[60]
A similar setting to The Difference Engine is used by Sydney Padua in the webcomic The Thrilling Adventures of Lovelace and Babbage.[61][62] It features an alternative history where Ada Lovelace and Babbage have built the analytical engine and use it to fight crime at Queen Victoria's request.[63] The comic is based on thorough research on the biographies of and correspondence between Babbage and Lovelace, which is then twisted for humorous effect.
The Orion's Arm online project features the Machina Babbagenseii, fully sentient Babbage-inspired mechanical computers. Each is the size of a large asteroid, only capable of surviving in microgravity conditions, and processes data at 0.5% the speed of a human brain.[64]
Charles Babbage and Ada Lovelace appear in an episode of Doctor Who, "Spyfall Part 2", where the engine is displayed and referenced.