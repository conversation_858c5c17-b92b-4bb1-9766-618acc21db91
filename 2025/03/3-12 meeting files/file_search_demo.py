import os
from openai import OpenAI
from termcolor import colored

# Constants
VECTOR_STORE_ID = "vs_67d12c40e56c81918b95e56f56c95f85"
MODEL = "gpt-4o-mini"

try:
    # Initialize OpenAI client
    client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
    print(colored("OpenAI client initialized successfully", "green"))

    # Example query
    query = "who played a big role in Presidential Rogers Commission"
    print(colored(f"Sending query: {query}", "cyan"))

    # Create a response using the file search tool
    response = client.responses.create(
        model=MODEL,
        input=query,
        tools=[{
            "type": "file_search",
            "vector_store_ids": [VECTOR_STORE_ID],
            "max_num_results": 5  # Limiting to 5 results
        }],
        include=["output[*].file_search_call.search_results"]  # Include search results in response
    )

    # Print the response
    print(colored("\nResponse:", "green"))
    
    # Print file search call information
    for item in response.output:
        if item.type == "file_search_call":
            print(colored(f"File search call ID: {item.id}", "yellow"))
            print(colored(f"Status: {item.status}", "yellow"))
            print(colored(f"Queries: {item.queries}", "yellow"))
            
            # Print search results if available
            if hasattr(item, 'search_results') and item.search_results:
                print(colored("\nSearch Results:", "magenta"))
                for result in item.search_results:
                    print(colored(f"File: {result.file.filename}", "blue"))
                    print(colored(f"Text: {result.text[:100]}...", "white"))
                    print("---")
        
        # Print the message content
        elif item.type == "message":
            print(colored("\nAssistant Response:", "green"))
            for content in item.content:
                if content.type == "output_text":
                    print(colored(content.text, "white"))
                    
                    # Print citations if available
                    if hasattr(content, 'annotations') and content.annotations:
                        print(colored("\nCitations:", "magenta"))
                        for annotation in content.annotations:
                            if annotation.type == "file_citation":
                                print(colored(f"File: {annotation.filename}", "blue"))

except Exception as e:
    print(colored(f"Error: {str(e)}", "red")) 