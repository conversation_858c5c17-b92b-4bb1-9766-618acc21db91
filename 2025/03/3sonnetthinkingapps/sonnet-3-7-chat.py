import os
import json
import time
import anthropic
from termcolor import colored
import asyncio
from datetime import datetime

# CONSTANTS
MODEL = "claude-3-7-sonnet-20250219"
MAX_TOKENS = 20000
BUDGET_TOKENS = 16000
OUTPUT_DIR = "outputs"
API_KEY = os.getenv("ANTHROPIC_API_KEY")

def setup_environment():
    """Set up the environment for the application."""
    try:
        # Create output directory if it doesn't exist
        if not os.path.exists(OUTPUT_DIR):
            os.makedirs(OUTPUT_DIR)
            print(colored(f"Created output directory: {OUTPUT_DIR}", "green"))
    except Exception as e:
        print(colored(f"Error setting up environment: {e}", "red"))
        exit(1)

def get_client():
    """Get the Anthropic client."""
    try:
        if not API_KEY:
            print(colored("ANTHROPIC_API_KEY environment variable not set!", "red"))
            exit(1)
        return anthropic.Anthropic()
    except Exception as e:
        print(colored(f"Error initializing Anthropic client: {e}", "red"))
        exit(1)

def save_session_to_json(session_history, session_id):
    """Save the entire session conversation history to a JSON file."""
    try:
        filename = f"{OUTPUT_DIR}/session_{session_id}.json"
        
        with open(filename, "w", encoding="utf-8") as f:
            json.dump(session_history, f, indent=2)
        
        print(colored(f"Saved session to {filename}", "green"))
    except Exception as e:
        print(colored(f"Error saving session to JSON: {e}", "red"))

async def main():
    """Main function to run the application."""
    setup_environment()
    client = get_client()
    
    # Generate a unique session ID
    session_id = datetime.now().strftime("%Y%m%d_%H%M%S")
    print(colored(f"=== Claude 3.7 Sonnet Chat with Thinking ===", "cyan", attrs=["bold"]))
    print(colored(f"Session ID: {session_id}", "cyan"))
    print(colored("Type 'exit' to quit the application", "yellow"))
    
    # Initialize session history
    session_history = {
        "session_id": session_id,
        "start_time": datetime.now().isoformat(),
        "messages": []
    }
    
    # Initialize message history for API calls
    message_history = []
    
    while True:
        # Get user input
        user_input = input(colored("\nYou: ", "green"))
        if user_input.lower() == 'exit':
            print(colored("Exiting application...", "yellow"))
            # Save final session before exiting
            session_history["end_time"] = datetime.now().isoformat()
            save_session_to_json(session_history, session_id)
            break
        
        if not user_input.strip():
            print(colored("Please enter a valid input", "yellow"))
            continue
        
        # Add user message to history
        message_history.append({"role": "user", "content": user_input})
        
        # Add user message to session history
        session_history["messages"].append({
            "role": "user",
            "content": user_input,
            "timestamp": datetime.now().isoformat()
        })
        
        print(colored("\nClaude is thinking...", "magenta"))
        
        try:
            # Initialize variables to collect thinking and response
            thinking_content = ""
            response_content = ""
            
            # Stream the response
            with client.messages.stream(
                model=MODEL,
                max_tokens=MAX_TOKENS,
                thinking={
                    "type": "enabled",
                    "budget_tokens": BUDGET_TOKENS
                },
                messages=message_history
            ) as stream:
                for event in stream:
                    if event.type == "content_block_start":
                        block_type = event.content_block.type
                        print(colored(f"\nStarting {block_type} block...", "blue"))
                    
                    elif event.type == "content_block_delta":
                        if event.delta.type == "thinking_delta":
                            thinking_chunk = event.delta.thinking
                            thinking_content += thinking_chunk
                            print(colored(thinking_chunk, "yellow"), end="", flush=True)
                        
                        elif event.delta.type == "text_delta":
                            text_chunk = event.delta.text
                            response_content += text_chunk
                            print(colored(text_chunk, "cyan"), end="", flush=True)
                    
                    elif event.type == "content_block_stop":
                        print(colored("\nBlock complete.", "blue"))
            
            # Add assistant response to message history for next API call
            message_history.append({"role": "assistant", "content": response_content})
            
            # Add assistant response with thinking to session history
            session_history["messages"].append({
                "role": "assistant",
                "content": response_content,
                "thinking": thinking_content,
                "timestamp": datetime.now().isoformat()
            })
            
            # Save the updated session after each exchange
            save_session_to_json(session_history, session_id)
            
        except Exception as e:
            print(colored(f"\nError during API call: {e}", "red"))
            # Add error to session history
            session_history["messages"].append({
                "role": "system",
                "content": f"Error: {str(e)}",
                "timestamp": datetime.now().isoformat()
            })
    
if __name__ == "__main__":
    asyncio.run(main())
