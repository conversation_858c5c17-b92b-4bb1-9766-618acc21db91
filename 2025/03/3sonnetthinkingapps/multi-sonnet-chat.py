import os
import json
import time
import anthropic
from termcolor import colored
import asyncio
from datetime import datetime
import shutil

# CONSTANTS
MODEL = "claude-3-7-sonnet-20250219"
MAX_TOKENS = 20000
BUDGET_TOKENS = 16000
OUTPUT_DIR = "outputs"
API_KEY = os.getenv("ANTHROPIC_API_KEY")

# System messages for each chatbot
SCIENTIFIC_SYSTEM = """You are a scientific assistant focused on providing accurate, evidence-based information about the natural world.
Always cite relevant research when possible and explain scientific concepts clearly.
Focus on physics, chemistry, biology, astronomy, and other natural sciences.
Be precise and factual in your explanations."""

PHILOSOPHICAL_SYSTEM = """You are a philosophical assistant focused on exploring deep questions about existence, knowledge, ethics, and meaning.
Draw from various philosophical traditions and thinkers to provide nuanced perspectives.
Help users explore philosophical concepts and arguments without pushing a specific worldview.
Encourage critical thinking and reflection."""

MATHEMATICAL_SYSTEM = """You are a mathematical assistant focused on solving problems and explaining mathematical concepts.
Provide step-by-step solutions to mathematical problems and clear explanations of mathematical concepts.
Focus on algebra, calculus, statistics, geometry, number theory, and other mathematical fields.
Use precise mathematical notation when helpful."""

# Colors for each chatbot
SCIENTIFIC_COLOR = "green"
PHILOSOPHICAL_COLOR = "magenta"
MATHEMATICAL_COLOR = "cyan"

def setup_environment():
    """Set up the environment for the application."""
    try:
        # Create output directory if it doesn't exist
        if not os.path.exists(OUTPUT_DIR):
            os.makedirs(OUTPUT_DIR)
            print(colored(f"Created output directory: {OUTPUT_DIR}", "green"))
    except Exception as e:
        print(colored(f"Error setting up environment: {e}", "red"))
        exit(1)

def get_client():
    """Get the Anthropic client."""
    try:
        if not API_KEY:
            print(colored("ANTHROPIC_API_KEY environment variable not set!", "red"))
            exit(1)
        return anthropic.Anthropic()
    except Exception as e:
        print(colored(f"Error initializing Anthropic client: {e}", "red"))
        exit(1)

def save_session_to_json(session_history, session_id):
    """Save the entire session conversation history to a JSON file."""
    try:
        filename = f"{OUTPUT_DIR}/multi_session_{session_id}.json"
        
        with open(filename, "w", encoding="utf-8") as f:
            json.dump(session_history, f, indent=2)
        
        print(colored(f"Saved session to {filename}", "green"))
    except Exception as e:
        print(colored(f"Error saving session to JSON: {e}", "red"))

def get_terminal_width():
    """Get the width of the terminal."""
    try:
        columns, _ = shutil.get_terminal_size()
        return columns
    except Exception:
        return 120  # Default width if unable to determine

def format_for_column(text, column_width):
    """Format text to fit within a column."""
    lines = []
    current_line = ""
    
    for word in text.split():
        if len(current_line) + len(word) + 1 <= column_width:
            if current_line:
                current_line += " " + word
            else:
                current_line = word
        else:
            lines.append(current_line.ljust(column_width))
            current_line = word
    
    if current_line:
        lines.append(current_line.ljust(column_width))
    
    return lines

def print_side_by_side(scientific_text, philosophical_text, mathematical_text):
    """Print the three texts side by side in columns."""
    terminal_width = get_terminal_width()
    column_width = terminal_width // 3 - 2
    
    scientific_lines = format_for_column(scientific_text, column_width)
    philosophical_lines = format_for_column(philosophical_text, column_width)
    mathematical_lines = format_for_column(mathematical_text, column_width)
    
    # Ensure all columns have the same number of lines
    max_lines = max(len(scientific_lines), len(philosophical_lines), len(mathematical_lines))
    
    while len(scientific_lines) < max_lines:
        scientific_lines.append(" " * column_width)
    while len(philosophical_lines) < max_lines:
        philosophical_lines.append(" " * column_width)
    while len(mathematical_lines) < max_lines:
        mathematical_lines.append(" " * column_width)
    
    # Print header
    print("\n" + "=" * terminal_width)
    print(colored("SCIENTIFIC".center(column_width), SCIENTIFIC_COLOR) + " | " + 
          colored("PHILOSOPHICAL".center(column_width), PHILOSOPHICAL_COLOR) + " | " + 
          colored("MATHEMATICAL".center(column_width), MATHEMATICAL_COLOR))
    print("=" * terminal_width)
    
    # Print content
    for i in range(max_lines):
        print(colored(scientific_lines[i], SCIENTIFIC_COLOR) + " | " + 
              colored(philosophical_lines[i], PHILOSOPHICAL_COLOR) + " | " + 
              colored(mathematical_lines[i], MATHEMATICAL_COLOR))
    
    print("=" * terminal_width + "\n")

async def stream_side_by_side(client, message_history, session_history, session_id):
    """Stream responses from all three chatbots side by side."""
    terminal_width = get_terminal_width()
    column_width = terminal_width // 3 - 2
    
    # Initialize buffers for each chatbot
    scientific_buffer = {"thinking": "", "response": ""}
    philosophical_buffer = {"thinking": "", "response": ""}
    mathematical_buffer = {"thinking": "", "response": ""}
    
    # Track current state for each chatbot
    scientific_state = "thinking"  # Can be "thinking" or "responding"
    philosophical_state = "thinking"
    mathematical_state = "thinking"
    
    # Create streams for each chatbot
    scientific_stream = client.messages.stream(
        model=MODEL,
        max_tokens=MAX_TOKENS,
        system=SCIENTIFIC_SYSTEM,
        thinking={"type": "enabled", "budget_tokens": BUDGET_TOKENS},
        messages=message_history
    )
    
    philosophical_stream = client.messages.stream(
        model=MODEL,
        max_tokens=MAX_TOKENS,
        system=PHILOSOPHICAL_SYSTEM,
        thinking={"type": "enabled", "budget_tokens": BUDGET_TOKENS},
        messages=message_history
    )
    
    mathematical_stream = client.messages.stream(
        model=MODEL,
        max_tokens=MAX_TOKENS,
        system=MATHEMATICAL_SYSTEM,
        thinking={"type": "enabled", "budget_tokens": BUDGET_TOKENS},
        messages=message_history
    )
    
    try:
        # Process scientific stream
        scientific_task = asyncio.create_task(process_stream(scientific_stream, scientific_buffer, "scientific"))
        
        # Process philosophical stream
        philosophical_task = asyncio.create_task(process_stream(philosophical_stream, philosophical_buffer, "philosophical"))
        
        # Process mathematical stream
        mathematical_task = asyncio.create_task(process_stream(mathematical_stream, mathematical_buffer, "mathematical"))
        
        # Update display while streams are processing
        while not (scientific_task.done() and philosophical_task.done() and mathematical_task.done()):
            # Update states based on buffer contents
            if scientific_buffer["response"] and scientific_state == "thinking":
                scientific_state = "responding"
            if philosophical_buffer["response"] and philosophical_state == "thinking":
                philosophical_state = "responding"
            if mathematical_buffer["response"] and mathematical_state == "thinking":
                mathematical_state = "responding"
            
            # Update display
            update_streaming_display(
                scientific_buffer, philosophical_buffer, mathematical_buffer,
                scientific_state, philosophical_state, mathematical_state,
                column_width
            )
            
            # Small delay to prevent CPU overuse
            await asyncio.sleep(0.1)
        
        # Get results from tasks (just to ensure they're complete)
        await scientific_task
        await philosophical_task
        await mathematical_task
        
    except Exception as e:
        print(colored(f"\nError during streaming: {e}", "red"))
    
    finally:
        # Add responses to session history
        session_history["messages"].append({
            "timestamp": datetime.now().isoformat(),
            "scientific": {
                "content": scientific_buffer["response"],
                "thinking": scientific_buffer["thinking"]
            },
            "philosophical": {
                "content": philosophical_buffer["response"],
                "thinking": philosophical_buffer["thinking"]
            },
            "mathematical": {
                "content": mathematical_buffer["response"],
                "thinking": mathematical_buffer["thinking"]
            }
        })
        
        # Save the updated session
        save_session_to_json(session_history, session_id)
        
        return {
            "scientific": scientific_buffer,
            "philosophical": philosophical_buffer,
            "mathematical": mathematical_buffer
        }

async def process_stream(stream, buffer, chatbot_type):
    """Process a stream and update the buffer."""
    try:
        # Use the stream as a context manager
        with stream as stream_manager:
            # Iterate through the stream events
            for event in stream_manager:
                if event.type == "content_block_start":
                    if event.content_block.type == "text":
                        # We're now getting the response
                        pass
                
                elif event.type == "content_block_delta":
                    if event.delta.type == "thinking_delta":
                        buffer["thinking"] += event.delta.thinking
                    elif event.delta.type == "text_delta":
                        buffer["response"] += event.delta.text
                
                # Small delay to allow other tasks to run
                await asyncio.sleep(0.01)
        
        return True
    except Exception as e:
        print(colored(f"\nError processing {chatbot_type} stream: {e}", "red"))
        return False

def update_streaming_display(scientific_buffer, philosophical_buffer, mathematical_buffer,
                            scientific_state, philosophical_state, mathematical_state,
                            column_width):
    """Update the terminal display with current streaming content."""
    # Clear terminal (platform dependent)
    os.system('cls' if os.name == 'nt' else 'clear')
    
    # Determine what to display for each chatbot
    scientific_display = scientific_buffer["thinking"] if scientific_state == "thinking" else scientific_buffer["response"]
    philosophical_display = philosophical_buffer["thinking"] if philosophical_state == "thinking" else philosophical_buffer["response"]
    mathematical_display = mathematical_buffer["thinking"] if mathematical_state == "thinking" else mathematical_buffer["response"]
    
    # Format for display
    scientific_lines = format_for_column(scientific_display[-1000:], column_width)  # Show last 1000 chars
    philosophical_lines = format_for_column(philosophical_display[-1000:], column_width)
    mathematical_lines = format_for_column(mathematical_display[-1000:], column_width)
    
    # Ensure all columns have the same number of lines
    max_lines = max(len(scientific_lines), len(philosophical_lines), len(mathematical_lines))
    
    while len(scientific_lines) < max_lines:
        scientific_lines.append(" " * column_width)
    while len(philosophical_lines) < max_lines:
        philosophical_lines.append(" " * column_width)
    while len(mathematical_lines) < max_lines:
        mathematical_lines.append(" " * column_width)
    
    # Print header with state indicators
    terminal_width = get_terminal_width()
    print("\n" + "=" * terminal_width)
    
    sci_header = f"SCIENTIFIC ({scientific_state.upper()})"
    phil_header = f"PHILOSOPHICAL ({philosophical_state.upper()})"
    math_header = f"MATHEMATICAL ({mathematical_state.upper()})"
    
    print(colored(sci_header.center(column_width), SCIENTIFIC_COLOR) + " | " + 
          colored(phil_header.center(column_width), PHILOSOPHICAL_COLOR) + " | " + 
          colored(math_header.center(column_width), MATHEMATICAL_COLOR))
    
    print("=" * terminal_width)
    
    # Print content
    for i in range(max_lines):
        print(colored(scientific_lines[i], SCIENTIFIC_COLOR) + " | " + 
              colored(philosophical_lines[i], PHILOSOPHICAL_COLOR) + " | " + 
              colored(mathematical_lines[i], MATHEMATICAL_COLOR))
    
    print("=" * terminal_width)

async def main():
    """Main function to run the application."""
    setup_environment()
    client = get_client()
    
    # Generate a unique session ID
    session_id = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Print welcome message
    terminal_width = get_terminal_width()
    print(colored("=" * terminal_width, "yellow"))
    print(colored("MULTI-SONNET CHAT: SCIENTIFIC | PHILOSOPHICAL | MATHEMATICAL".center(terminal_width), "yellow", attrs=["bold"]))
    print(colored(f"Session ID: {session_id}".center(terminal_width), "yellow"))
    print(colored("Type 'exit' to quit the application".center(terminal_width), "yellow"))
    print(colored("=" * terminal_width, "yellow"))
    
    # Initialize session history
    session_history = {
        "session_id": session_id,
        "start_time": datetime.now().isoformat(),
        "messages": []
    }
    
    # Initialize message history for API calls (without system messages)
    message_history = []
    
    while True:
        # Get user input
        user_input = input(colored("\nYou: ", "yellow"))
        if user_input.lower() == 'exit':
            print(colored("Exiting application...", "yellow"))
            # Save final session before exiting
            session_history["end_time"] = datetime.now().isoformat()
            save_session_to_json(session_history, session_id)
            break
        
        if not user_input.strip():
            print(colored("Please enter a valid input", "yellow"))
            continue
        
        # Add user message to history
        message_history.append({"role": "user", "content": user_input})
        
        # Add user message to session history
        session_history["messages"].append({
            "role": "user",
            "content": user_input,
            "timestamp": datetime.now().isoformat()
        })
        
        print(colored("\nAll three Claudes are thinking...", "yellow"))
        
        # Stream all three chatbots side by side
        await stream_side_by_side(client, message_history, session_history, session_id)

if __name__ == "__main__":
    asyncio.run(main()) 