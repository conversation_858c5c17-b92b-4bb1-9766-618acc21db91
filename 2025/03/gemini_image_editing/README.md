# Echo Hive Image Generator

A FastAPI application that generates images from text prompts using the Gemini API. The application displays the generated image in the main area and stores older images as thumbnails on the right side.

## Features

- Text-to-image generation using Google's Gemini API
- Beautiful dark mode UI with DaisyUI and Tailwind CSS
- Interactive image handling:
  - Upload your own images as starting points
  - Select any image as "active" for further editing/generation
  - Paste or drag-and-drop images directly
- Enhanced image selection with visual feedback
- Integrated circular generate button with animations
- Clean loading animations during image generation
- Persistent session storage (images stay between page refreshes)
- Image history with clickable thumbnails
- Responsive design for desktop and mobile

## Setup

### Prerequisites

- Python 3.8 or higher
- Google Gemini API key

### Installation

1. Clone this repository:
```bash
git clone <repository-url>
cd <repository-directory>
```

2. Install the required dependencies:
```bash
pip install -r requirements.txt
```

3. Set up your Gemini API key as an environment variable:
```bash
# On Windows
set GEMINI_API_KEY=your_api_key_here

# On macOS/Linux
export GEMINI_API_KEY=your_api_key_here
```

### Running the Application

1. Start the FastAPI server:
```bash
python main.py
```

2. Open your browser and navigate to:
```
http://127.0.0.1:8000
```

## Usage

1. Enter a text prompt in the textarea
2. Click the purple circular "Generate Image" button in the lower right of the text area
3. Wait for the image to be generated and displayed
4. View your image generation history in the thumbnails on the right
5. Click on any thumbnail to display it in the main image area

### Advanced Usage

- **Image Editing**: Upload or paste an image, then modify it with text prompts
- **Session Management**: Use the "New Session" button to start fresh
- **Active Image Selection**: Click any image in your current session to make it active for editing
- **Keyboard Shortcuts**: Press Enter to generate (Shift+Enter for new line)

## Project Structure

- `main.py`: FastAPI application with routes and Gemini API integration
- `templates/index.html`: HTML template with DaisyUI/Tailwind CSS styling
- `static/images/`: Directory for storing generated images
- `static/history.json`: JSON file for storing image generation history

## License

MIT 