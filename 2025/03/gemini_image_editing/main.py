import os
import base64
import uuid
from datetime import datetime
from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException, UploadFile, File, Form
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from pydantic import BaseModel
from google import genai
from google.genai import types
from termcolor import colored
import asyncio
import json
from io import BytesIO
from PIL import Image
import shutil

# Constants
API_KEY = os.getenv("GEMINI_API_KEY")
IMAGE_DIR = "static/images"
UPLOADS_DIR = "static/uploads"
HISTORY_FILE = "static/history.json"
MODEL_ID = "gemini-2.0-flash-exp"

# Initialize FastAPI app
app = FastAPI(title="Echo Hive Image Generator")

# Ensure directories exist
os.makedirs(IMAGE_DIR, exist_ok=True)
os.makedirs(UPLOADS_DIR, exist_ok=True)

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")

# Setup templates
templates = Jinja2Templates(directory="templates")

# Initialize Gemini API
try:
    client = genai.Client(api_key=API_KEY)
    print(colored("✅ Gemini API configured successfully", "green"))
except Exception as e:
    print(colored(f"❌ Error configuring Gemini API: {e}", "red"))

# Models
class TextPrompt(BaseModel):
    text: str

# Helper functions
def save_image(image_data, prompt, is_upload=False):
    """Save the generated image and update history"""
    try:
        # Create a unique filename
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        image_id = str(uuid.uuid4())[:8]
        filename = f"{timestamp}_{image_id}.jpg"
        filepath = os.path.join(IMAGE_DIR, filename)
        
        if is_upload:
            # For uploaded files, just move or copy the file
            shutil.copy2(image_data, filepath)
            print(colored(f"✅ Uploaded image saved: {filename}", "green"))
        else:
            # For generated images, process with PIL
            try:
                image = Image.open(BytesIO(image_data))
                image.save(filepath, format="JPEG")
                print(colored(f"✅ Image processed with PIL and saved", "green"))
            except Exception as e:
                print(colored(f"❌ Error processing image with PIL: {e}", "red"))
                # Fallback: try direct save if PIL processing fails
                with open(filepath, "wb") as f:
                    f.write(image_data)
                print(colored("⚠️ Used fallback method to save image", "yellow"))
        
        # Update history
        history_entry = {
            "id": image_id,
            "timestamp": timestamp,
            "prompt": prompt,
            "filename": filename
        }
        
        history = []
        if os.path.exists(HISTORY_FILE):
            try:
                with open(HISTORY_FILE, "r", encoding="utf-8") as f:
                    history = json.load(f)
            except json.JSONDecodeError:
                print(colored("❌ Error reading history file, creating new history", "yellow"))
        
        history.append(history_entry)
        
        with open(HISTORY_FILE, "w", encoding="utf-8") as f:
            json.dump(history, f)
        
        print(colored(f"✅ Image saved: {filename}", "green"))
        return filename
    except Exception as e:
        print(colored(f"❌ Error saving image: {e}", "red"))
        raise

def get_image_history():
    """Get the history of generated images"""
    try:
        if os.path.exists(HISTORY_FILE):
            with open(HISTORY_FILE, "r", encoding="utf-8") as f:
                return json.load(f)
        return []
    except Exception as e:
        print(colored(f"❌ Error loading image history: {e}", "red"))
        return []

async def save_upload_file(upload_file: UploadFile) -> str:
    """Save an uploaded file and return the path"""
    try:
        file_location = os.path.join(UPLOADS_DIR, f"{uuid.uuid4()}{os.path.splitext(upload_file.filename)[1]}")
        with open(file_location, "wb") as file_object:
            shutil.copyfileobj(upload_file.file, file_object)
        print(colored(f"✅ File uploaded: {file_location}", "green"))
        return file_location
    except Exception as e:
        print(colored(f"❌ Error saving uploaded file: {e}", "red"))
        raise HTTPException(status_code=500, detail=str(e))

# Routes
@app.get("/", response_class=HTMLResponse)
async def read_root(request: Request):
    """Render the main page"""
    history = get_image_history()
    return templates.TemplateResponse(
        "index.html", 
        {"request": request, "history": history}
    )

@app.post("/generate")
async def generate_image(prompt: TextPrompt):
    """Generate an image from text using Gemini API"""
    try:
        print(colored(f"🔄 Generating image for prompt: {prompt.text}", "cyan"))
        
        try:
            # Generate the image using the same style as image_gen_basic.py
            response = client.models.generate_content(
                model=f"models/{MODEL_ID}",
                contents=prompt.text,
                config=types.GenerateContentConfig(response_modalities=['Text', 'Image'])
            )
            
            print(colored("✅ Received response from Gemini API", "green"))
            
            # Debug response structure
            print(colored(f"Response type: {type(response)}", "yellow"))
            print(colored(f"Response has candidates: {hasattr(response, 'candidates')}", "yellow"))
            
            if hasattr(response, 'candidates') and len(response.candidates) > 0:
                candidate = response.candidates[0]
                print(colored(f"Candidate has content: {hasattr(candidate, 'content')}", "yellow"))
                
                if hasattr(candidate, 'content') and hasattr(candidate.content, 'parts'):
                    print(colored(f"Number of parts: {len(candidate.content.parts)}", "yellow"))
                    
                    for i, part in enumerate(candidate.content.parts):
                        print(colored(f"Part {i} type: {type(part)}", "yellow"))
                        print(colored(f"Part {i} has text: {hasattr(part, 'text')}", "yellow"))
                        print(colored(f"Part {i} has inline_data: {hasattr(part, 'inline_data')}", "yellow"))
                        
                        if hasattr(part, 'text') and part.text is not None:
                            print(colored(f"📝 Text response: {part.text[:100]}...", "blue"))
                        elif hasattr(part, 'inline_data') and part.inline_data is not None:
                            print(colored(f"🖼️ Found image data, mime type: {part.inline_data.mime_type}", "green"))
                            # Save the image and get the filename
                            filename = save_image(part.inline_data.data, prompt.text)
                            return {"success": True, "filename": filename}
            
            # If we get here, no image was found in the response
            print(colored("❌ No image found in the response", "red"))
            raise HTTPException(status_code=500, detail="No image found in the response")
        except Exception as e:
            print(colored(f"❌ Error with Gemini API: {e}", "red"))
            raise HTTPException(status_code=500, detail=str(e))
            
    except Exception as e:
        print(colored(f"❌ Error generating image: {e}", "red"))
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/generate-with-image")
async def generate_with_image(
    prompt: str = Form(...),
    image: UploadFile = File(...)
):
    """Generate an image from text and an uploaded image using Gemini API"""
    try:
        print(colored(f"🔄 Generating image for prompt: {prompt} with uploaded image", "cyan"))
        
        # Save the uploaded image
        upload_path = await save_upload_file(image)
        
        try:
            # Open the image with PIL
            pil_image = Image.open(upload_path)
            
            # Generate the image using the uploaded image and prompt
            response = client.models.generate_content(
                model=f"models/{MODEL_ID}",
                contents=[prompt, types.Part(inline_data=types.Blob(
                    mime_type=f"image/{pil_image.format.lower() if pil_image.format else 'jpeg'}",
                    data=open(upload_path, "rb").read()
                ))],
                config=types.GenerateContentConfig(response_modalities=['Text', 'Image'])
            )
            
            print(colored("✅ Received response from Gemini API", "green"))
            
            if hasattr(response, 'candidates') and len(response.candidates) > 0:
                candidate = response.candidates[0]
                
                if hasattr(candidate, 'content') and hasattr(candidate.content, 'parts'):
                    for part in candidate.content.parts:
                        if hasattr(part, 'text') and part.text is not None:
                            print(colored(f"📝 Text response: {part.text[:100]}...", "blue"))
                        elif hasattr(part, 'inline_data') and part.inline_data is not None:
                            print(colored(f"🖼️ Found image data, mime type: {part.inline_data.mime_type}", "green"))
                            # Save the image and get the filename
                            filename = save_image(part.inline_data.data, prompt)
                            return {"success": True, "filename": filename}
            
            # If we get here, no image was found in the response
            print(colored("❌ No image found in the response", "red"))
            raise HTTPException(status_code=500, detail="No image found in the response")
        except Exception as e:
            print(colored(f"❌ Error with Gemini API: {e}", "red"))
            raise HTTPException(status_code=500, detail=str(e))
            
    except Exception as e:
        print(colored(f"❌ Error generating image: {e}", "red"))
        raise HTTPException(status_code=500, detail=str(e))

# Run the app
if __name__ == "__main__":
    import uvicorn
    print(colored("🚀 Starting Echo Hive Image Generator...", "magenta"))
    uvicorn.run("main:app", host="127.0.0.1", port=8000, reload=True) 