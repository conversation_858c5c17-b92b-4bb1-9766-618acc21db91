<!DOCTYPE html>
<html lang="en" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Echo Hive Image Generator</title>
    <link href="https://cdn.jsdelivr.net/npm/daisyui@3.9.4/dist/full.css" rel="stylesheet" type="text/css" />
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js"></script>
    <style>
        .image-container {
            min-height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .thumbnail {
            transition: transform 0.2s;
            cursor: pointer;
        }
        
        .thumbnail:hover {
            transform: scale(1.05);
            box-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
        }
        
        .loading-animation {
            width: 80px;
            height: 80px;
            display: none;
            margin: 2rem auto;
        }
        
        .loading-animation div {
            position: absolute;
            width: 64px;
            height: 64px;
            border: 8px solid #7e22ce;
            border-radius: 50%;
            animation: loading-animation 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
            border-color: #7e22ce transparent transparent transparent;
        }
        
        .loading-animation div:nth-child(1) {
            animation-delay: -0.45s;
        }
        
        .loading-animation div:nth-child(2) {
            animation-delay: -0.3s;
        }
        
        .loading-animation div:nth-child(3) {
            animation-delay: -0.15s;
        }
        
        @keyframes loading-animation {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }
        
        .loading-text {
            text-align: center;
            color: #a855f7;
            font-size: 1.1rem;
            margin-top: 0.5rem;
            animation: pulse 1.5s infinite;
        }
        
        @keyframes pulse {
            0%, 100% {
                opacity: 0.7;
            }
            50% {
                opacity: 1;
            }
        }
        
        .fade-in {
            opacity: 0;
            animation: fadeIn 0.5s forwards;
        }
        
        @keyframes fadeIn {
            to {
                opacity: 1;
            }
        }
        
        .prompt-container {
            position: relative;
            border-radius: 0.5rem;
            overflow: hidden;
            transition: all 0.3s ease;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            width: 100%;
        }
        
        .prompt-container:focus-within {
            box-shadow: 0 0 0 2px rgba(126, 34, 206, 0.3);
        }
        
        .prompt-textarea {
            resize: none;
            transition: all 0.3s ease;
            min-height: 140px !important;
            font-size: 1rem;
            line-height: 1.5;
            padding: 1rem !important;
            padding-bottom: 3.5rem !important;
            background-color: rgba(30, 30, 46, 0.6) !important;
            border-color: rgba(126, 34, 206, 0.2) !important;
            width: 100% !important;
            box-sizing: border-box !important;
        }
        
        .prompt-textarea:focus {
            background-color: rgba(30, 30, 46, 0.8) !important;
            border-color: rgba(126, 34, 206, 0.4) !important;
            box-shadow: none !important;
        }
        
        .form-control {
            width: 100%;
        }
        
        .image-preview-area {
            margin-top: 1rem;
            display: flex;
            flex-wrap: wrap;
            gap: 0.75rem;
            min-height: 0;
            transition: min-height 0.3s ease;
            width: 100%;
        }
        
        .image-preview-area.has-images {
            min-height: 100px;
            padding: 0.5rem 0;
        }
        
        .image-preview-area-header {
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }
        
        .image-preview-area-header span {
            font-size: 0.9rem;
            color: #a855f7;
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }
        
        .image-preview-area-header span svg {
            width: 1rem;
            height: 1rem;
        }
        
        .image-preview-item {
            position: relative;
            width: 100px;
            height: 100px;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            transition: transform 0.2s;
        }
        
        .image-preview-item:hover {
            transform: scale(1.05);
        }
        
        .image-preview-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .image-preview-item .remove-btn {
            position: absolute;
            top: 4px;
            right: 4px;
            background: rgba(0, 0, 0, 0.6);
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            opacity: 0;
            transition: opacity 0.2s;
        }
        
        .image-preview-item:hover .remove-btn {
            opacity: 1;
        }
        
        .image-preview-item .active-indicator {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: #7e22ce;
            transform: scaleX(0);
            transition: transform 0.2s;
        }
        
        .image-preview-item.active .active-indicator {
            transform: scaleX(1);
        }
        
        .image-preview-item.active::before {
            content: "Active";
            position: absolute;
            top: 4px;
            left: 4px;
            background: rgba(126, 34, 206, 0.8);
            color: white;
            font-size: 0.6rem;
            padding: 0.1rem 0.3rem;
            border-radius: 4px;
            pointer-events: none;
        }
        
        .tools-container {
            position: absolute;
            right: 12px;
            bottom: 12px;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .upload-icon {
            width: 40px;
            height: 40px;
            background: rgba(126, 34, 206, 0.15);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s;
            color: #a855f7;
            backdrop-filter: blur(4px);
            border: 1px solid rgba(126, 34, 206, 0.3);
        }
        
        .upload-icon:hover {
            background: rgba(126, 34, 206, 0.25);
            transform: scale(1.05);
            box-shadow: 0 0 15px rgba(126, 34, 206, 0.3);
        }
        
        .upload-icon svg {
            width: 20px;
            height: 20px;
        }
        
        .generate-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, #7e22ce, #a855f7);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s;
            color: white;
            border: none;
            box-shadow: 0 4px 10px rgba(126, 34, 206, 0.3);
        }
        
        .generate-icon:hover {
            transform: scale(1.05) translateY(-2px);
            box-shadow: 0 6px 15px rgba(126, 34, 206, 0.4);
        }
        
        .generate-icon:active {
            transform: scale(0.95);
        }
        
        .generate-icon svg {
            width: 24px;
            height: 24px;
        }
        
        .session-controls {
            display: flex;
            justify-content: flex-end;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
        }
        
        .session-btn {
            display: flex;
            align-items: center;
            gap: 0.25rem;
            padding: 0.375rem 0.75rem;
            border-radius: 0.375rem;
            font-size: 0.875rem;
            transition: all 0.2s;
            color: #a855f7;
            background: rgba(126, 34, 206, 0.05);
            border: 1px solid rgba(126, 34, 206, 0.1);
        }
        
        .session-btn:hover {
            background: rgba(126, 34, 206, 0.15);
            border-color: rgba(126, 34, 206, 0.2);
        }
        
        .session-btn svg {
            width: 16px;
            height: 16px;
        }
        
        .paste-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            gap: 1rem;
            color: white;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s;
            border-radius: 0.5rem;
            backdrop-filter: blur(4px);
        }
        
        .paste-overlay.active {
            opacity: 1;
            pointer-events: auto;
        }
        
        .paste-overlay svg {
            width: 48px;
            height: 48px;
            color: #a855f7;
        }
        
        .hidden-file-input {
            display: none;
        }
        
        .tooltip {
            position: relative;
        }
        
        .tooltip:before {
            content: attr(data-tip);
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            white-space: nowrap;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.2s;
            z-index: 10;
        }
        
        .tooltip:hover:before {
            opacity: 1;
        }
        
        .card-body {
            padding: 1.5rem;
        }
        
        @media (min-width: 768px) {
            .card-body {
                padding: 2rem;
            }
            
            .prompt-textarea {
                min-height: 160px !important;
            }
        }
    </style>
</head>
<body class="min-h-screen bg-base-300">
    <div class="container mx-auto p-4">
        <header class="text-center mb-8">
            <h1 class="text-4xl font-bold text-primary mb-2">Echo Hive Image Generator</h1>
            <p class="text-lg text-secondary">Enter text to generate beautiful images with Gemini AI</p>
        </header>
        
        <div class="flex flex-col lg:flex-row gap-6">
            <!-- Main content area -->
            <div class="flex-grow lg:w-3/4">
                <div class="card bg-base-200 shadow-xl">
                    <div class="card-body">
                        <!-- Session controls -->
                        <div class="session-controls">
                            <button id="newSessionBtn" class="session-btn tooltip" data-tip="Start a new session">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
                                </svg>
                                New Session
                            </button>
                        </div>
                        
                        <!-- Combined form -->
                        <form id="generateForm" class="mb-6 w-full" enctype="multipart/form-data">
                            <div class="form-control w-full">
                                <label class="label">
                                    <span class="label-text text-lg font-medium">Enter your text prompt</span>
                                </label>
                                <div class="prompt-container w-full">
                                    <textarea id="textPrompt" class="prompt-textarea textarea textarea-bordered w-full" placeholder="Describe the image you want to generate... (Paste or drop an image here)"></textarea>
                                    <div class="tools-container">
                                        <div class="upload-icon tooltip" data-tip="Upload image" id="uploadTrigger">
                                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5m-13.5-9L12 3m0 0l4.5 4.5M12 3v13.5" />
                                            </svg>
                                        </div>
                                        <button type="submit" class="generate-icon tooltip" data-tip="Generate Image">
                                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 001.423 1.423l1.183.394-1.183.394a2.25 2.25 0 00-1.423 1.423z" />
                                            </svg>
                                        </button>
                                    </div>
                                    <input type="file" id="imageFile" class="hidden-file-input" accept="image/*">
                                    <div class="paste-overlay" id="pasteOverlay">
                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5m-13.5-9L12 3m0 0l4.5 4.5M12 3v13.5" />
                                        </svg>
                                        <p>Drop image here</p>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Image preview area with instructions -->
                            <div id="imagePreviewAreaContainer" class="w-full">
                                <div class="image-preview-area-header mt-4" id="imagePreviewAreaHeader" style="display: none;">
                                    <span>
                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M7.5 8.25h9m-9 3H12m-9.75 1.51c0 1.6 1.123 2.994 2.707 3.227 1.129.166 2.27.293 3.423.379.35.026.67.21.865.501L12 21l2.755-4.133a1.14 1.14 0 01.865-.501 48.172 48.172 0 003.423-.379c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0012 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018z" />
                                        </svg>
                                        Click an image to make it active for editing
                                    </span>
                                </div>
                                <div id="imagePreviewArea" class="image-preview-area w-full"></div>
                            </div>
                        </form>
                        
                        <!-- Loading animation now positioned above the image container -->
                        <div id="loadingAnimation" class="loading-animation">
                            <div></div>
                            <div></div>
                            <div></div>
                            <div></div>
                        </div>
                        
                        <div class="divider">Generated Image</div>
                        
                        <div id="imageContainer" class="image-container bg-base-100 rounded-lg p-4 flex items-center justify-center">
                            <div class="text-center text-base-content opacity-50">
                                <p>Your generated image will appear here</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Sidebar with thumbnails -->
            <div class="lg:w-1/4">
                <div class="card bg-base-200 shadow-xl">
                    <div class="card-body">
                        <h2 class="card-title text-accent">History</h2>
                        <div class="divider mt-0"></div>
                        
                        <div id="thumbnailContainer" class="grid grid-cols-2 gap-2">
                            {% if history and history|length > 0 %}
                                {% for item in history|reverse %}
                                    <div class="thumbnail-wrapper">
                                        <img 
                                            src="/static/images/{{ item.filename }}" 
                                            alt="{{ item.prompt }}" 
                                            class="thumbnail rounded-md w-full h-24 object-cover"
                                            data-filename="{{ item.filename }}"
                                            data-prompt="{{ item.prompt }}"
                                            title="{{ item.prompt }}"
                                        >
                                    </div>
                                {% endfor %}
                            {% else %}
                                <div class="col-span-2 text-center text-base-content opacity-50 py-4" id="noHistoryMessage">
                                    <p>No images generated yet</p>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // DOM Elements
            const generateForm = document.getElementById('generateForm');
            const textPrompt = document.getElementById('textPrompt');
            const imageFile = document.getElementById('imageFile');
            const imageContainer = document.getElementById('imageContainer');
            const loadingAnimation = document.getElementById('loadingAnimation');
            const thumbnailContainer = document.getElementById('thumbnailContainer');
            const noHistoryMessage = document.getElementById('noHistoryMessage');
            const uploadTrigger = document.getElementById('uploadTrigger');
            const pasteOverlay = document.getElementById('pasteOverlay');
            const imagePreviewArea = document.getElementById('imagePreviewArea');
            const imagePreviewAreaHeader = document.getElementById('imagePreviewAreaHeader');
            const newSessionBtn = document.getElementById('newSessionBtn');
            
            // Session storage
            let sessionImages = [];
            let activeImageIndex = -1;
            
            // Initialize from localStorage if available
            initializeFromStorage();
            
            // Upload trigger click
            uploadTrigger.addEventListener('click', function() {
                imageFile.click();
            });
            
            // File input change
            imageFile.addEventListener('change', function() {
                if (this.files && this.files[0]) {
                    handleImageFile(this.files[0]);
                }
            });
            
            // New session button
            newSessionBtn.addEventListener('click', function() {
                if (confirm('Start a new session? This will clear all current images.')) {
                    sessionImages = [];
                    activeImageIndex = -1;
                    updateImagePreviewArea();
                    localStorage.removeItem('echohive_session');
                }
            });
            
            // Paste functionality
            textPrompt.addEventListener('paste', function(e) {
                const items = (e.clipboardData || e.originalEvent.clipboardData).items;
                for (let i = 0; i < items.length; i++) {
                    if (items[i].type.indexOf('image') !== -1) {
                        const file = items[i].getAsFile();
                        handleImageFile(file);
                        break;
                    }
                }
            });
            
            // Drag and drop for textarea
            textPrompt.addEventListener('dragenter', function(e) {
                e.preventDefault();
                pasteOverlay.classList.add('active');
            });
            
            pasteOverlay.addEventListener('dragleave', function(e) {
                e.preventDefault();
                pasteOverlay.classList.remove('active');
            });
            
            pasteOverlay.addEventListener('dragover', function(e) {
                e.preventDefault();
            });
            
            pasteOverlay.addEventListener('drop', function(e) {
                e.preventDefault();
                pasteOverlay.classList.remove('active');
                
                const dt = e.dataTransfer;
                const files = dt.files;
                
                if (files && files[0]) {
                    handleImageFile(files[0]);
                }
            });
            
            // Submit with Enter key for text prompt
            textPrompt.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    generateForm.dispatchEvent(new Event('submit'));
                }
            });
            
            // Add click event listeners to existing thumbnails
            document.querySelectorAll('.thumbnail').forEach(thumbnail => {
                thumbnail.addEventListener('click', function() {
                    displayImage(this.getAttribute('data-filename'), this.getAttribute('data-prompt'));
                });
            });
            
            // Form submission
            generateForm.addEventListener('submit', async function(e) {
                e.preventDefault();
                
                const prompt = textPrompt.value.trim();
                if (!prompt) {
                    alert('Please enter a text prompt');
                    return;
                }
                
                // Show loading animation
                imageContainer.innerHTML = '';
                loadingAnimation.style.display = 'block';
                
                try {
                    let response;
                    
                    // Check if we have an active image
                    if (activeImageIndex >= 0) {
                        // Use the active image from session
                        const activeImage = sessionImages[activeImageIndex];
                        const blob = await fetch(activeImage.dataUrl).then(r => r.blob());
                        const file = new File([blob], "session_image.jpg", { type: "image/jpeg" });
                        
                        const formData = new FormData();
                        formData.append('prompt', prompt);
                        formData.append('image', file);
                        
                        response = await fetch('/generate-with-image', {
                            method: 'POST',
                            body: formData
                        });
                    } else {
                        // Text-only submission
                        response = await fetch('/generate', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({ text: prompt })
                        });
                    }
                    
                    const data = await response.json();
                    
                    if (data.success) {
                        // Hide loading animation
                        loadingAnimation.style.display = 'none';
                        
                        // Display the generated image
                        displayImage(data.filename, prompt);
                        
                        // Add the new image to the thumbnail container
                        addThumbnail(data.filename, prompt);
                        
                        // Add to session images
                        const imageUrl = `/static/images/${data.filename}`;
                        addImageToSession(imageUrl, prompt);
                        
                        // Clear the text prompt
                        textPrompt.value = '';
                    } else {
                        throw new Error(data.detail || 'Failed to generate image');
                    }
                } catch (error) {
                    console.error('Error:', error);
                    loadingAnimation.style.display = 'none';
                    imageContainer.innerHTML = `
                        <div class="text-center text-error">
                            <p>Error: ${error.message}</p>
                        </div>
                    `;
                }
            });
            
            // Handle image file (from upload, paste, or drop)
            function handleImageFile(file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const dataUrl = e.target.result;
                    addImageToSession(dataUrl, 'Uploaded image');
                };
                reader.readAsDataURL(file);
            }
            
            // Add image to session
            function addImageToSession(dataUrl, prompt) {
                sessionImages.push({
                    dataUrl: dataUrl,
                    prompt: prompt,
                    timestamp: new Date().toISOString()
                });
                
                activeImageIndex = sessionImages.length - 1;
                updateImagePreviewArea();
                saveToLocalStorage();
            }
            
            // Update image preview area
            function updateImagePreviewArea() {
                imagePreviewArea.innerHTML = '';
                
                if (sessionImages.length > 0) {
                    imagePreviewArea.classList.add('has-images');
                    imagePreviewAreaHeader.style.display = 'flex';
                    
                    sessionImages.forEach((image, index) => {
                        const previewItem = document.createElement('div');
                        previewItem.className = `image-preview-item ${index === activeImageIndex ? 'active' : ''}`;
                        previewItem.innerHTML = `
                            <img src="${image.dataUrl}" alt="${image.prompt}">
                            <div class="remove-btn" data-index="${index}">×</div>
                            <div class="active-indicator"></div>
                        `;
                        
                        // Click to make active
                        previewItem.addEventListener('click', function(e) {
                            if (!e.target.classList.contains('remove-btn')) {
                                activeImageIndex = index;
                                updateImagePreviewArea();
                                
                                // Show a brief message to confirm the image is active
                                const activeMsg = document.createElement('div');
                                activeMsg.className = 'fixed top-4 right-4 bg-purple-600 text-white px-4 py-2 rounded-lg shadow-lg z-50 fade-in';
                                activeMsg.textContent = 'Image selected for editing';
                                document.body.appendChild(activeMsg);
                                
                                setTimeout(() => {
                                    activeMsg.style.opacity = '0';
                                    setTimeout(() => {
                                        document.body.removeChild(activeMsg);
                                    }, 500);
                                }, 2000);
                            }
                        });
                        
                        imagePreviewArea.appendChild(previewItem);
                    });
                    
                    // Add remove button event listeners
                    document.querySelectorAll('.remove-btn').forEach(btn => {
                        btn.addEventListener('click', function(e) {
                            e.stopPropagation();
                            const index = parseInt(this.getAttribute('data-index'));
                            sessionImages.splice(index, 1);
                            
                            if (activeImageIndex === index) {
                                activeImageIndex = sessionImages.length > 0 ? 0 : -1;
                            } else if (activeImageIndex > index) {
                                activeImageIndex--;
                            }
                            
                            updateImagePreviewArea();
                            saveToLocalStorage();
                        });
                    });
                } else {
                    imagePreviewArea.classList.remove('has-images');
                    imagePreviewAreaHeader.style.display = 'none';
                }
            }
            
            // Initialize from localStorage
            function initializeFromStorage() {
                const savedSession = localStorage.getItem('echohive_session');
                if (savedSession) {
                    try {
                        const sessionData = JSON.parse(savedSession);
                        sessionImages = sessionData.images || [];
                        activeImageIndex = sessionData.activeIndex || -1;
                        updateImagePreviewArea();
                    } catch (e) {
                        console.error('Error loading session from localStorage:', e);
                    }
                }
            }
            
            // Save to localStorage
            function saveToLocalStorage() {
                try {
                    localStorage.setItem('echohive_session', JSON.stringify({
                        images: sessionImages,
                        activeIndex: activeImageIndex
                    }));
                } catch (e) {
                    console.error('Error saving session to localStorage:', e);
                }
            }
            
            function displayImage(filename, prompt) {
                imageContainer.innerHTML = `
                    <div class="text-center w-full">
                        <img src="/static/images/${filename}" alt="${prompt}" class="max-w-full max-h-[500px] mx-auto rounded-lg shadow-lg fade-in">
                        <p class="mt-4 text-sm opacity-70">${prompt}</p>
                    </div>
                `;
                
                // Animate the image appearance
                anime({
                    targets: imageContainer.querySelector('img'),
                    scale: [0.9, 1],
                    opacity: [0, 1],
                    easing: 'easeOutElastic(1, .8)',
                    duration: 800
                });
            }
            
            function addThumbnail(filename, prompt) {
                // Remove "No images" message if it exists
                if (noHistoryMessage) {
                    noHistoryMessage.remove();
                }
                
                const thumbnailWrapper = document.createElement('div');
                thumbnailWrapper.className = 'thumbnail-wrapper';
                
                const thumbnail = document.createElement('img');
                thumbnail.src = `/static/images/${filename}`;
                thumbnail.alt = prompt;
                thumbnail.className = 'thumbnail rounded-md w-full h-24 object-cover';
                thumbnail.dataset.filename = filename;
                thumbnail.dataset.prompt = prompt;
                thumbnail.title = prompt;
                
                thumbnail.addEventListener('click', function() {
                    displayImage(filename, prompt);
                });
                
                thumbnailWrapper.appendChild(thumbnail);
                
                // Add the new thumbnail at the beginning
                if (thumbnailContainer.firstChild) {
                    thumbnailContainer.insertBefore(thumbnailWrapper, thumbnailContainer.firstChild);
                } else {
                    thumbnailContainer.innerHTML = ''; // Clear any remaining content
                    thumbnailContainer.appendChild(thumbnailWrapper);
                }
                
                // Animate the new thumbnail
                anime({
                    targets: thumbnail,
                    translateY: [20, 0],
                    opacity: [0, 1],
                    easing: 'easeOutExpo',
                    duration: 500
                });
            }
        });
    </script>
</body>
</html> 