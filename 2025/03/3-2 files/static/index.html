<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multiplayer Grid Game</title>
    <link href="https://cdn.jsdelivr.net/npm/daisyui@3.5.0/dist/full.css" rel="stylesheet" type="text/css" />
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js"></script>
    <style>
        body {
            background-color: #1a1a2e;
            color: #e6e6e6;
        }
        
        .grid-cell {
            width: 50px;
            height: 50px;
            border: 1px solid #444;
            transition: all 0.3s ease;
        }
        
        .grid-cell:hover {
            transform: scale(1.05);
            box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
        }
        
        .player-indicator {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 10px;
        }
        
        .loading-animation {
            width: 100px;
            height: 100px;
            position: relative;
        }
        
        .loading-animation div {
            position: absolute;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #fff;
            animation: loading 1.5s infinite ease-in-out;
        }
        
        .loading-animation div:nth-child(1) {
            top: 0;
            left: 40px;
            animation-delay: 0s;
        }
        
        .loading-animation div:nth-child(2) {
            top: 20px;
            left: 70px;
            animation-delay: 0.2s;
        }
        
        .loading-animation div:nth-child(3) {
            top: 60px;
            left: 70px;
            animation-delay: 0.4s;
        }
        
        .loading-animation div:nth-child(4) {
            top: 80px;
            left: 40px;
            animation-delay: 0.6s;
        }
        
        .loading-animation div:nth-child(5) {
            top: 60px;
            left: 10px;
            animation-delay: 0.8s;
        }
        
        .loading-animation div:nth-child(6) {
            top: 20px;
            left: 10px;
            animation-delay: 1s;
        }
        
        @keyframes loading {
            0%, 100% {
                transform: scale(0.5);
                opacity: 0.3;
            }
            50% {
                transform: scale(1);
                opacity: 1;
            }
        }
        
        .grid-container {
            animation: fadeIn 0.5s ease-in-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .player-list {
            animation: slideIn 0.5s ease-in-out;
        }
        
        @keyframes slideIn {
            from { opacity: 0; transform: translateX(-20px); }
            to { opacity: 1; transform: translateX(0); }
        }
    </style>
</head>
<body class="min-h-screen flex flex-col items-center justify-center p-4">
    <div class="container mx-auto max-w-4xl">
        <h1 class="text-4xl font-bold text-center mb-8 text-primary">Multiplayer Grid Game</h1>
        
        <div id="join-screen" class="flex flex-col items-center justify-center">
            <button id="join-btn" class="btn btn-primary btn-lg">Join Game</button>
        </div>
        
        <div id="loading-screen" class="hidden flex flex-col items-center justify-center">
            <div class="loading-animation mb-4">
                <div></div>
                <div></div>
                <div></div>
                <div></div>
                <div></div>
                <div></div>
            </div>
            <p class="text-xl">Connecting to game server...</p>
        </div>
        
        <div id="game-screen" class="hidden">
            <div class="flex flex-col md:flex-row gap-8">
                <div class="flex-1">
                    <div class="card bg-base-300 shadow-xl">
                        <div class="card-body">
                            <h2 class="card-title mb-4">Game Grid</h2>
                            <div id="grid" class="grid-container grid grid-cols-8 gap-1 mx-auto"></div>
                        </div>
                    </div>
                </div>
                
                <div class="w-full md:w-64">
                    <div class="card bg-base-300 shadow-xl">
                        <div class="card-body">
                            <h2 class="card-title mb-4">Players</h2>
                            <div id="player-list" class="player-list space-y-2"></div>
                        </div>
                    </div>
                    
                    <div class="card bg-base-300 shadow-xl mt-4">
                        <div class="card-body">
                            <h2 class="card-title mb-2">Your Color</h2>
                            <div class="flex items-center">
                                <div id="your-color" class="w-8 h-8 rounded-full mr-2"></div>
                                <span>You</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        let socket;
        let clientId;
        let clientColor;
        let grid = [];
        let players = [];
        
        // DOM elements
        const joinScreen = document.getElementById('join-screen');
        const loadingScreen = document.getElementById('loading-screen');
        const gameScreen = document.getElementById('game-screen');
        const joinBtn = document.getElementById('join-btn');
        const gridElement = document.getElementById('grid');
        const playerListElement = document.getElementById('player-list');
        const yourColorElement = document.getElementById('your-color');
        
        // Initialize the grid
        function initializeGrid() {
            gridElement.innerHTML = '';
            
            for (let row = 0; row < 8; row++) {
                for (let col = 0; col < 8; col++) {
                    const cell = document.createElement('div');
                    cell.className = 'grid-cell';
                    cell.dataset.row = row;
                    cell.dataset.col = col;
                    
                    // Add click event
                    cell.addEventListener('click', () => {
                        claimCell(row, col);
                    });
                    
                    gridElement.appendChild(cell);
                }
            }
            
            // Animate grid cells appearing
            anime({
                targets: '.grid-cell',
                scale: [0, 1],
                opacity: [0, 1],
                delay: anime.stagger(20, {grid: [8, 8], from: 'center'})
            });
        }
        
        // Update the grid based on server data
        function updateGrid(gridData) {
            grid = gridData;
            
            for (let row = 0; row < 8; row++) {
                for (let col = 0; col < 8; col++) {
                    const cellData = grid[row][col];
                    const cellElement = document.querySelector(`.grid-cell[data-row="${row}"][data-col="${col}"]`);
                    
                    if (cellData) {
                        cellElement.style.backgroundColor = cellData.color;
                    } else {
                        cellElement.style.backgroundColor = '';
                    }
                }
            }
        }
        
        // Update a single cell
        function updateCell(row, col, color) {
            const cellElement = document.querySelector(`.grid-cell[data-row="${row}"][data-col="${col}"]`);
            if (cellElement) {
                // Animate the cell being claimed
                anime({
                    targets: cellElement,
                    backgroundColor: color,
                    scale: [1.2, 1],
                    duration: 300,
                    easing: 'easeOutElastic(1, .8)'
                });
            }
        }
        
        // Update player list
        function updatePlayerList() {
            playerListElement.innerHTML = '';
            
            console.log("Updating player list with", players.length, "players");
            
            if (players.length <= 1) {
                // Show a message when you're the only player
                playerListElement.innerHTML = '<div class="text-sm opacity-70">Waiting for other players to join...</div>';
                return;
            }
            
            players.forEach(player => {
                if (player.id !== clientId) {
                    const playerElement = document.createElement('div');
                    playerElement.className = 'flex items-center my-2';
                    playerElement.innerHTML = `
                        <div class="player-indicator" style="background-color: ${player.color}"></div>
                        <span>Player ${player.id.substring(0, 6)}</span>
                    `;
                    playerListElement.appendChild(playerElement);
                }
            });
        }
        
        // Claim a cell
        function claimCell(row, col) {
            if (socket && socket.readyState === WebSocket.OPEN) {
                socket.send(JSON.stringify({
                    type: 'claim',
                    row: row,
                    col: col
                }));
            }
        }
        
        // Connect to WebSocket server
        function connectToServer() {
            showLoadingScreen();
            
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws`;
            
            socket = new WebSocket(wsUrl);
            
            socket.onopen = () => {
                console.log('Connected to server');
            };
            
            socket.onmessage = (event) => {
                const message = JSON.parse(event.data);
                
                switch (message.type) {
                    case 'init':
                        clientId = message.clientId;
                        clientColor = message.color;
                        grid = message.grid;
                        players = message.players;
                        
                        // Show game screen
                        showGameScreen();
                        
                        // Initialize grid
                        initializeGrid();
                        updateGrid(grid);
                        updatePlayerList();
                        
                        // Set your color indicator
                        yourColorElement.style.backgroundColor = clientColor;
                        break;
                        
                    case 'update':
                        // Update grid data
                        if (!grid[message.row]) {
                            grid[message.row] = [];
                        }
                        grid[message.row][message.col] = {
                            clientId: message.clientId,
                            color: message.color
                        };
                        
                        // Update UI
                        updateCell(message.row, message.col, message.color);
                        break;
                        
                    case 'player_joined':
                        // Check if player already exists
                        const existingPlayerIndex = players.findIndex(p => p.id === message.player.id);
                        
                        if (existingPlayerIndex === -1) {
                            // Only add if player doesn't exist
                            players.push(message.player);
                            updatePlayerList();
                            
                            // Show toast notification
                            showToast(`New player joined!`);
                        }
                        break;
                        
                    case 'player_left':
                        players = players.filter(p => p.id !== message.clientId);
                        updatePlayerList();
                        
                        // Show toast notification
                        showToast(`A player left the game`);
                        break;
                }
            };
            
            socket.onclose = () => {
                console.log('Disconnected from server');
                showJoinScreen();
                showToast('Disconnected from server. Please rejoin.', 'error');
            };
            
            socket.onerror = (error) => {
                console.error('WebSocket error:', error);
                showJoinScreen();
                showToast('Connection error. Please try again.', 'error');
            };
        }
        
        // Show toast notification
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `toast toast-end ${type === 'error' ? 'toast-error' : 'toast-info'}`;
            toast.innerHTML = `
                <div class="alert">
                    <span>${message}</span>
                </div>
            `;
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.remove();
            }, 3000);
        }
        
        // Show/hide screens
        function showJoinScreen() {
            joinScreen.classList.remove('hidden');
            loadingScreen.classList.add('hidden');
            gameScreen.classList.add('hidden');
        }
        
        function showLoadingScreen() {
            joinScreen.classList.add('hidden');
            loadingScreen.classList.remove('hidden');
            gameScreen.classList.add('hidden');
        }
        
        function showGameScreen() {
            joinScreen.classList.add('hidden');
            loadingScreen.classList.add('hidden');
            gameScreen.classList.remove('hidden');
        }
        
        // Event listeners
        joinBtn.addEventListener('click', connectToServer);
        
        // Initial setup
        showJoinScreen();
    </script>
</body>
</html> 