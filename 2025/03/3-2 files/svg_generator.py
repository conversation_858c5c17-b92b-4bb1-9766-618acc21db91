import os
import sys
import json
import anthropic
from termcolor import colored
import tempfile
import webbrowser
import re
import base64
from pathlib import Path
import time
import pyautogui
from PIL import Image
import io

# MAJOR VARIABLES
MODEL = "claude-3-7-sonnet-20250219"
API_KEY = os.getenv("ANTHROPIC_API_KEY")
MAX_TOKENS = 4096
TEMPERATURE = 1
USER_PROMPT = "A colorful landscape with mountains and a sunset" # Default prompt, will be overridden
FOLLOW_UP_PROMPT = "Improve this image" # Default follow-up prompt
SCREENSHOT_DELAY = 3  # Seconds to wait before taking screenshot
OUTPUT_FOLDER = "generated_svgs"  # Folder to save SVGs
MAX_ITERATIONS = 5  # Number of improvement iterations to perform
USE_THINKING_MODE = True  # Toggle to use <PERSON>'s thinking mode
THINKING_BUDGET_TOKENS = 3000  # Budget for thinking tokens when thinking mode is enabled

def generate_svg(prompt, image_data=None):
    """Generate SVG using Claude API based on user prompt"""
    try:
        print(colored(f"🔄 Connecting to Claude API with model: {M<PERSON><PERSON>}", "cyan"))
        
        client = anthropic.Anthropic(api_key=API_KEY)
        
        # Create the system prompt to instruct Claude to return only SVG
        system_prompt = """You are an expert SVG creator. Generate SVG code based on the user's request.
        Return ONLY the SVG code without any explanation, markdown formatting, or code blocks.
        The SVG should be complete, valid, and ready to use in an HTML document.
        Start with <svg and end with </svg>."""
        
        messages = []
        
        if image_data:
            print(colored(f"🤖 Sending request to Claude with image and prompt: '{prompt}'", "yellow"))
            messages = [
                {
                    "role": "user", 
                    "content": [
                        {
                            "type": "image",
                            "source": {
                                "type": "base64",
                                "media_type": "image/png",
                                "data": image_data
                            }
                        },
                        {
                            "type": "text",
                            "text": f"{prompt}"
                        }
                    ]
                }
            ]
        else:
            print(colored(f"🤖 Sending request to Claude: '{prompt}'", "yellow"))
            messages = [
                {"role": "user", "content": f"Create an SVG for: {prompt}"}
            ]
        
        # Set up request parameters
        request_params = {
            "model": MODEL,
            "max_tokens": MAX_TOKENS,
            "temperature": TEMPERATURE,
            "system": system_prompt,
            "messages": messages
        }
        
        # Add thinking mode if enabled
        if USE_THINKING_MODE:
            print(colored("🧠 Using Claude's thinking mode", "blue"))
            request_params["thinking"] = {
                "type": "enabled",
                "budget_tokens": THINKING_BUDGET_TOKENS
            }
        
        response = client.messages.create(**request_params)
        
        print(colored("✅ Received response from Claude", "green"))
        
        # Extract text content from response, handling both thinking and regular responses
        content_text = ""
        for content_block in response.content:
            if content_block.type == "text":
                content_text = content_block.text
                break
        
        if not content_text:
            print(colored("❌ No text content found in response", "red"))
            return None
            
        svg_code = extract_svg(content_text)
        
        if not svg_code:
            print(colored("❌ No valid SVG found in the response", "red"))
            return None
            
        print(colored("🎨 Successfully extracted SVG code", "green"))
        return svg_code
        
    except Exception as e:
        print(colored(f"❌ Error generating SVG: {str(e)}", "red"))
        print(colored(f"Error details: {e}", "red"))
        return None

def extract_svg(content):
    """Extract SVG code from Claude's response"""
    try:
        # Try to find SVG using regex
        svg_pattern = re.compile(r'<svg[\s\S]*?<\/svg>')
        match = svg_pattern.search(content)
        
        if match:
            return match.group(0)
        
        # If no match with regex, check if the content is just SVG
        if content.strip().startswith('<svg') and content.strip().endswith('</svg>'):
            return content.strip()
            
        print(colored("⚠️ Could not extract SVG using standard methods, returning raw content", "yellow"))
        return content
        
    except Exception as e:
        print(colored(f"❌ Error extracting SVG: {str(e)}", "red"))
        return None

def take_screenshot():
    """Take a screenshot and return as base64 encoded string"""
    try:
        print(colored(f"⏳ Waiting {SCREENSHOT_DELAY} seconds for browser to load...", "cyan"))
        time.sleep(SCREENSHOT_DELAY)  # Wait for browser to load
        
        print(colored("📸 Taking screenshot...", "cyan"))
        screenshot = pyautogui.screenshot()
        
        # Convert to base64
        buffered = io.BytesIO()
        screenshot.save(buffered, format="PNG")
        img_str = base64.b64encode(buffered.getvalue()).decode()
        
        print(colored("✅ Screenshot captured successfully", "green"))
        return img_str
    except Exception as e:
        print(colored(f"❌ Error taking screenshot: {str(e)}", "red"))
        print(colored(f"Error details: {e}", "red"))
        return None

def save_svg(svg_code, iteration=0):
    """Save the SVG to a numbered file in the output folder"""
    try:
        # Create output folder if it doesn't exist
        if not os.path.exists(OUTPUT_FOLDER):
            os.makedirs(OUTPUT_FOLDER)
            print(colored(f"📁 Created output folder: {OUTPUT_FOLDER}", "blue"))
        
        # Get the next available number
        existing_files = [f for f in os.listdir(OUTPUT_FOLDER) if f.endswith('.svg')]
        next_num = len(existing_files) + 1
        
        # Create filename
        iteration_text = f"iteration_{iteration}" if iteration > 0 else "initial"
        sanitized_prompt = USER_PROMPT.lower().replace(" ", "_")[:30]  # Use first 30 chars of prompt
        filename = f"{next_num:03d}_{iteration_text}_{sanitized_prompt}.svg"
        filepath = os.path.join(OUTPUT_FOLDER, filename)
        
        # Save the file
        with open(filepath, 'w', encoding="utf-8") as f:
            f.write(svg_code)
        
        print(colored(f"💾 Saved SVG to {filepath}", "green"))
        return filepath
    except Exception as e:
        print(colored(f"❌ Error saving SVG: {str(e)}", "red"))
        print(colored(f"Error details: {e}", "red"))
        return None

def display_svg(svg_code, iteration=0):
    """Display the SVG in a web browser"""
    try:
        # Create a temporary HTML file with a unique name based on iteration
        suffix = f"_iteration_{iteration}.html"
        with tempfile.NamedTemporaryFile(delete=False, suffix=suffix, mode='w', encoding="utf-8") as f:
            html_content = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>SVG - Iteration {iteration}</title>
                <style>
                    body {{
                        background-color: #1a1a1a;
                        margin: 0;
                        padding: 0;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        min-height: 100vh;
                    }}
                    .svg-container {{
                        max-width: 100vw;
                        max-height: 100vh;
                    }}
                </style>
            </head>
            <body>
                <div class="svg-container">
                    {svg_code}
                </div>
            </body>
            </html>
            """
            f.write(html_content)
            temp_file_path = f.name
        
        # Open the HTML file in the default web browser
        print(colored(f"🌐 Opening SVG (Iteration {iteration}) in web browser", "cyan"))
        webbrowser.open('file://' + temp_file_path + f"?v={time.time()}")  # Add cache-busting parameter
        
    except Exception as e:
        print(colored(f"❌ Error displaying SVG: {str(e)}", "red"))

def main():
    """Main function to run the SVG generator"""
    global USER_PROMPT, FOLLOW_UP_PROMPT
    
    if len(sys.argv) > 1:
        # If command line argument is provided, use it as the prompt
        USER_PROMPT = " ".join(sys.argv[1:])
    
    print(colored(f"🎨 Starting SVG generation with {MAX_ITERATIONS} iterations...", "magenta"))
    
    if not API_KEY:
        print(colored("❌ ANTHROPIC_API_KEY environment variable not set", "red"))
        print(colored("Please set your Anthropic API key as an environment variable", "yellow"))
        sys.exit(1)
    
    # Generate initial SVG
    current_svg = generate_svg(USER_PROMPT)
    
    if not current_svg:
        print(colored("❌ Failed to generate initial SVG", "red"))
        return
    
    # Save and display initial SVG
    save_svg(current_svg, iteration=0)
    display_svg(current_svg, iteration=0)
    
    # Perform improvement iterations
    for i in range(1, MAX_ITERATIONS + 1):
        print(colored(f"\n🔄 Starting iteration {i} of {MAX_ITERATIONS}", "blue", attrs=["bold"]))
        
        # Take screenshot of current SVG
        screenshot_base64 = take_screenshot()
        
        if not screenshot_base64:
            print(colored("❌ Failed to take screenshot for improvement", "red"))
            break
        
        # Generate improved SVG
        print(colored(f"🔄 Sending screenshot to Claude with prompt: '{FOLLOW_UP_PROMPT}'", "magenta"))
        improved_svg = generate_svg(FOLLOW_UP_PROMPT, screenshot_base64)
        
        if not improved_svg:
            print(colored(f"❌ Failed to generate improved SVG for iteration {i}", "red"))
            break
        
        # Update current SVG
        current_svg = improved_svg
        
        # Save and display improved SVG
        save_svg(current_svg, iteration=i)
        display_svg(current_svg, iteration=i)
        
        # Give user time to view the current iteration before proceeding
        if i < MAX_ITERATIONS:
            print(colored(f"⏳ Waiting before starting next iteration...", "yellow"))
            time.sleep(2)  # Short pause between iterations
    
    print(colored(f"\n✅ Completed {MAX_ITERATIONS} iterations of SVG improvement", "green", attrs=["bold"]))

if __name__ == "__main__":
    main() 