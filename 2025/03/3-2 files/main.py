import json
import uuid
import os
from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from termcolor import colored
import asyncio
import random

# MAJOR VARIABLES
GRID_SIZE = 8
COLORS = [
    "#FF5733", "#33FF57", "#3357FF", "#F033FF", "#FF33A1", 
    "#33FFF5", "#FFF533", "#A133FF", "#FF8C33", "#33FFCB"
]
GRID = [[None for _ in range(GRID_SIZE)] for _ in range(GRID_SIZE)]
CONNECTIONS = {}

app = FastAPI()

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")

class ConnectionManager:
    def __init__(self):
        self.active_connections = {}

    async def connect(self, websocket: WebSocket, client_id: str):
        await websocket.accept()
        self.active_connections[client_id] = websocket
        print(colored(f"Client {client_id} connected. Total connections: {len(self.active_connections)}", "green"))

    def disconnect(self, client_id: str):
        if client_id in self.active_connections:
            del self.active_connections[client_id]
            print(colored(f"Client {client_id} disconnected. Total connections: {len(self.active_connections)}", "yellow"))

    async def broadcast(self, message: dict):
        disconnected_clients = []
        for client_id, connection in self.active_connections.items():
            try:
                await connection.send_text(json.dumps(message))
            except Exception as e:
                print(colored(f"Error broadcasting to client {client_id}: {str(e)}", "red"))
                disconnected_clients.append(client_id)
        
        # Clean up disconnected clients
        for client_id in disconnected_clients:
            self.disconnect(client_id)

manager = ConnectionManager()

@app.get("/")
async def get():
    try:
        print(colored("Serving index.html", "cyan"))
        return FileResponse("static/index.html")
    except Exception as e:
        print(colored(f"Error serving index.html: {str(e)}", "red"))
        return {"error": str(e)}

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    # Generate a unique client ID and assign a color
    client_id = str(uuid.uuid4())
    
    # Use the current length of CONNECTIONS
    color = COLORS[len(CONNECTIONS) % len(COLORS)]
    
    await manager.connect(websocket, client_id)
    
    # Store client info
    CONNECTIONS[client_id] = {
        "color": color,
        "id": client_id
    }
    
    # Send initial state to the new client
    try:
        all_players = list(CONNECTIONS.values())
        print(colored(f"Total players before sending to new client: {len(all_players)}", "cyan"))
        
        await websocket.send_text(json.dumps({
            "type": "init",
            "grid": GRID,
            "clientId": client_id,
            "color": color,
            "players": all_players
        }))
        
        # Broadcast new player joined to all OTHER clients
        for other_id, connection in manager.active_connections.items():
            if other_id != client_id:  # Don't send to the new client
                try:
                    await connection.send_text(json.dumps({
                        "type": "player_joined",
                        "player": CONNECTIONS[client_id]
                    }))
                except Exception as e:
                    print(colored(f"Error sending player_joined to {other_id}: {str(e)}", "red"))
        
        print(colored(f"Sent initial state to client {client_id}", "cyan"))
    except Exception as e:
        print(colored(f"Error during initialization for client {client_id}: {str(e)}", "red"))
        manager.disconnect(client_id)
        if client_id in CONNECTIONS:
            del CONNECTIONS[client_id]
        return
    
    try:
        while True:
            data = await websocket.receive_text()
            message = json.loads(data)
            
            if message["type"] == "claim":
                row, col = message["row"], message["col"]
                
                # Validate coordinates
                if 0 <= row < GRID_SIZE and 0 <= col < GRID_SIZE:
                    # Update grid
                    GRID[row][col] = {
                        "clientId": client_id,
                        "color": color
                    }
                    
                    print(colored(f"Client {client_id} claimed cell ({row}, {col})", "blue"))
                    
                    # Broadcast the update
                    await manager.broadcast({
                        "type": "update",
                        "row": row,
                        "col": col,
                        "clientId": client_id,
                        "color": color
                    })
    except WebSocketDisconnect:
        manager.disconnect(client_id)
        if client_id in CONNECTIONS:
            del CONNECTIONS[client_id]
        
        # Broadcast player left
        await manager.broadcast({
            "type": "player_left",
            "clientId": client_id
        })
    except Exception as e:
        print(colored(f"Error in websocket connection for client {client_id}: {str(e)}", "red"))
        manager.disconnect(client_id)
        if client_id in CONNECTIONS:
            del CONNECTIONS[client_id]

if __name__ == "__main__":
    import uvicorn
    import os
    
    port = int(os.getenv("PORT", 8000))
    print(colored(f"Starting multiplayer grid game server on port {port}...", "green"))
    uvicorn.run("main:app", host="0.0.0.0", port=port) 