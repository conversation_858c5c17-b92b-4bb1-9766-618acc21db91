<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D Tank Shooter</title>
    <style>
        body {
            margin: 0;
            overflow: hidden;
            background-color: #1a1a2e;
            color: #e2e2e2;
            font-family: Arial, sans-serif;
        }
        canvas {
            display: block;
        }
        #ui {
            position: absolute;
            top: 20px;
            left: 20px;
            z-index: 100;
        }
        #health-bar {
            width: 200px;
            height: 20px;
            background-color: #333;
            border-radius: 10px;
            margin-bottom: 10px;
            overflow: hidden;
        }
        #health {
            width: 100%;
            height: 100%;
            background-color: #4CAF50;
            transition: width 0.3s;
        }
        #score {
            font-size: 24px;
            color: #fb9062;
            text-shadow: 0 0 5px #000;
        }
        #ammo {
            font-size: 18px;
            color: #64dfdf;
            margin-top: 5px;
        }
        #game-over {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: rgba(0, 0, 0, 0.8);
            padding: 30px;
            border-radius: 10px;
            text-align: center;
            display: none;
            z-index: 200;
        }
        #game-over h2 {
            color: #ff6b6b;
            font-size: 36px;
            margin-bottom: 20px;
        }
        #game-over button {
            background-color: #5e60ce;
            color: white;
            border: none;
            padding: 10px 20px;
            font-size: 18px;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        #game-over button:hover {
            background-color: #6930c3;
        }
        #crosshair {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            transform: translate(-50%, -50%);
            pointer-events: none;
        }
        #crosshair::before, #crosshair::after {
            content: '';
            position: absolute;
            background-color: rgba(255, 255, 255, 0.7);
        }
        #crosshair::before {
            width: 2px;
            height: 20px;
            left: 9px;
            top: 0;
        }
        #crosshair::after {
            width: 20px;
            height: 2px;
            left: 0;
            top: 9px;
        }
        #loading {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #1a1a2e;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        .loader {
            width: 80px;
            height: 80px;
            border: 8px solid rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            border-top: 8px solid #6930c3;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }
        .loader-text {
            color: #e2e2e2;
            font-size: 20px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div id="loading">
        <div class="loader"></div>
        <div class="loader-text">Loading game assets...</div>
    </div>

    <div id="ui">
        <div id="health-bar"><div id="health"></div></div>
        <div id="score">Score: 0</div>
        <div id="ammo">Ammo: 10/10</div>
    </div>

    <div id="crosshair"></div>

    <div id="game-over">
        <h2>Game Over</h2>
        <p>Your final score: <span id="final-score">0</span></p>
        <button id="restart-button">Play Again</button>
    </div>

    <script type="module">
        import * as THREE from 'https://unpkg.com/three@0.152.2/build/three.module.js';
        import { PointerLockControls } from 'https://unpkg.com/three@0.152.2/examples/jsm/controls/PointerLockControls.js';
        
        // Game Constants
        const GAME_SPEED = 1;
        const MAX_ENEMIES = 10;
        const RELOAD_TIME = 2000; // in milliseconds
        const MAX_AMMO = 10;
        const BULLET_SPEED = 70;
        const TANK_SPEED = 20;
        const ENEMY_SPEED = 10;
        const TERRAIN_SIZE = 1000;
        const HEALTH_MAX = 100;

        // Game variables
        let scene, camera, renderer;
        let playerTank, playerTurret;
        let enemies = [];
        let bullets = [];
        let terrain;
        let controls;
        let moveForward = false;
        let moveBackward = false;
        let moveLeft = false;
        let moveRight = false;
        let canShoot = true;
        let ammo = MAX_AMMO;
        let health = HEALTH_MAX;
        let score = 0;
        let isGameOver = false;
        let clock = new THREE.Clock();
        let enemyModels = [];

        // Initialize and run the game
        init();
        
        function init() {
            // Create scene
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0x0a0a1e);
            scene.fog = new THREE.FogExp2(0x0a0a1e, 0.002);

            // Create camera
            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            camera.position.set(0, 5, 0);

            // Create renderer
            try {
                renderer = new THREE.WebGLRenderer({ antialias: true });
                renderer.setSize(window.innerWidth, window.innerHeight);
                renderer.shadowMap.enabled = true;
                document.body.appendChild(renderer.domElement);
            } catch (error) {
                console.error("Error initializing WebGL renderer:", error);
                alert("Failed to initialize WebGL. Make sure your browser supports WebGL.");
                return;
            }

            // Set up lighting
            setupLighting();

            // Create terrain
            createTerrain();

            // Create player tank
            createPlayerTank();

            // Set up controls
            setupControls();

            // Create enemy tanks
            for (let i = 0; i < 5; i++) {
                spawnEnemy();
            }

            // Set up event listeners
            setupEventListeners();

            // Start the game loop
            animate();

            // Hide loading screen after everything is loaded
            setTimeout(() => {
                document.getElementById('loading').style.display = 'none';
            }, 1500);
        }

        function setupLighting() {
            // Ambient light
            const ambientLight = new THREE.AmbientLight(0x404040, 0.5);
            scene.add(ambientLight);

            // Directional light (sun)
            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            directionalLight.position.set(100, 100, 50);
            directionalLight.castShadow = true;
            directionalLight.shadow.mapSize.width = 2048;
            directionalLight.shadow.mapSize.height = 2048;
            directionalLight.shadow.camera.near = 0.5;
            directionalLight.shadow.camera.far = 500;
            directionalLight.shadow.camera.left = -100;
            directionalLight.shadow.camera.right = 100;
            directionalLight.shadow.camera.top = 100;
            directionalLight.shadow.camera.bottom = -100;
            scene.add(directionalLight);
        }

        function createTerrain() {
            // Create ground
            const groundGeometry = new THREE.PlaneGeometry(TERRAIN_SIZE, TERRAIN_SIZE, 32, 32);
            const groundMaterial = new THREE.MeshStandardMaterial({ 
                color: 0x3d5e3a,
                roughness: 0.8,
                metalness: 0.2
            });
            const ground = new THREE.Mesh(groundGeometry, groundMaterial);
            ground.rotation.x = -Math.PI / 2;
            ground.receiveShadow = true;
            scene.add(ground);
            
            // Add terrain features
            addTerrainFeatures();
        }

        function addTerrainFeatures() {
            // Add rocks
            for (let i = 0; i < 30; i++) {
                const rockGeometry = new THREE.DodecahedronGeometry(Math.random() * 3 + 1, 0);
                const rockMaterial = new THREE.MeshStandardMaterial({ 
                    color: 0x777777,
                    roughness: 0.9,
                    metalness: 0.1
                });
                
                const rock = new THREE.Mesh(rockGeometry, rockMaterial);
                const x = Math.random() * TERRAIN_SIZE - TERRAIN_SIZE/2;
                const z = Math.random() * TERRAIN_SIZE - TERRAIN_SIZE/2;
                rock.position.set(x, rockGeometry.parameters.radius, z);
                rock.castShadow = true;
                rock.receiveShadow = true;
                scene.add(rock);
            }
            
            // Add trees
            for (let i = 0; i < 20; i++) {
                const treeHeight = Math.random() * 5 + 8;
                const trunkGeometry = new THREE.CylinderGeometry(0.5, 0.8, treeHeight, 8);
                const trunkMaterial = new THREE.MeshStandardMaterial({ color: 0x8B4513 });
                const trunk = new THREE.Mesh(trunkGeometry, trunkMaterial);
                
                const leavesGeometry = new THREE.ConeGeometry(3, 6, 8);
                const leavesMaterial = new THREE.MeshStandardMaterial({ color: 0x2d4c2b });
                const leaves = new THREE.Mesh(leavesGeometry, leavesMaterial);
                leaves.position.y = treeHeight/2 + 2;
                
                const tree = new THREE.Group();
                tree.add(trunk);
                tree.add(leaves);
                
                const x = Math.random() * (TERRAIN_SIZE - 50) - (TERRAIN_SIZE/2 - 25);
                const z = Math.random() * (TERRAIN_SIZE - 50) - (TERRAIN_SIZE/2 - 25);
                tree.position.set(x, treeHeight/2, z);
                
                tree.castShadow = true;
                tree.receiveShadow = true;
                scene.add(tree);
            }
        }

        function createPlayerTank() {
            // Tank body
            const bodyGeometry = new THREE.BoxGeometry(4, 1.5, 6);
            const bodyMaterial = new THREE.MeshStandardMaterial({ color: 0x2c6e49 });
            const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
            body.castShadow = true;
            body.receiveShadow = true;
            
            // Tank turret
            const turretGeometry = new THREE.CylinderGeometry(1.5, 1.5, 1, 16);
            const turretMaterial = new THREE.MeshStandardMaterial({ color: 0x1e5631 });
            const turret = new THREE.Mesh(turretGeometry, turretMaterial);
            turret.position.y = 1.25;
            turret.castShadow = true;
            turret.receiveShadow = true;
            
            // Tank cannon
            const cannonGeometry = new THREE.CylinderGeometry(0.3, 0.3, 3, 12);
            const cannonMaterial = new THREE.MeshStandardMaterial({ color: 0x111111 });
            const cannon = new THREE.Mesh(cannonGeometry, cannonMaterial);
            cannon.position.z = 1.5;
            cannon.rotation.x = Math.PI / 2;
            turret.add(cannon);
            
            // Create tracks
            const trackGeometry = new THREE.BoxGeometry(1, 0.8, 6);
            const trackMaterial = new THREE.MeshStandardMaterial({ color: 0x333333 });
            
            const leftTrack = new THREE.Mesh(trackGeometry, trackMaterial);
            leftTrack.position.set(-2, -0.5, 0);
            leftTrack.castShadow = true;
            leftTrack.receiveShadow = true;
            
            const rightTrack = new THREE.Mesh(trackGeometry, trackMaterial);
            rightTrack.position.set(2, -0.5, 0);
            rightTrack.castShadow = true;
            rightTrack.receiveShadow = true;
            
            // Create tank group
            playerTank = new THREE.Group();
            playerTank.add(body);
            playerTank.add(turret);
            playerTank.add(leftTrack);
            playerTank.add(rightTrack);
            
            playerTank.position.y = 1.5;
            
            // Store reference to turret for aiming
            playerTurret = turret;
            
            // Add the tank to the scene
            scene.add(playerTank);
            
            // Attach camera to tank
            playerTank.add(camera);
            camera.position.set(0, 4, -8);
            camera.lookAt(playerTank.position);
        }

        function setupControls() {
            try {
                controls = new PointerLockControls(camera, document.body);
                
                // Start game on click
                document.addEventListener('click', function() {
                    if (!isGameOver) {
                        controls.lock();
                    }
                });
                
                // Add locked change event
                controls.addEventListener('lock', function() {
                    document.getElementById('crosshair').style.display = 'block';
                });
                
                controls.addEventListener('unlock', function() {
                    document.getElementById('crosshair').style.display = 'none';
                });
            } catch (error) {
                console.error("Error setting up PointerLockControls:", error);
                // Implement a simple fallback if the control fails
                controls = {
                    isLocked: false,
                    lock: function() { this.isLocked = true; document.getElementById('crosshair').style.display = 'block'; },
                    unlock: function() { this.isLocked = false; document.getElementById('crosshair').style.display = 'none'; },
                    addEventListener: function(event, callback) { 
                        if (event === 'lock' || event === 'unlock') { setTimeout(callback, 0); }
                    }
                };
            }
        }

        function setupEventListeners() {
            // Keyboard controls
            document.addEventListener('keydown', onKeyDown);
            document.addEventListener('keyup', onKeyUp);
            
            // Shooting
            document.addEventListener('mousedown', onMouseDown);
            
            // Window resize
            window.addEventListener('resize', onWindowResize);
            
            // Restart button
            document.getElementById('restart-button').addEventListener('click', restartGame);
        }

        function onKeyDown(event) {
            if (isGameOver) return;
            
            switch (event.code) {
                case 'KeyW':
                case 'ArrowUp':
                    moveForward = true;
                    break;
                case 'KeyS':
                case 'ArrowDown':
                    moveBackward = true;
                    break;
                case 'KeyA':
                case 'ArrowLeft':
                    moveLeft = true;
                    break;
                case 'KeyD':
                case 'ArrowRight':
                    moveRight = true;
                    break;
                case 'Space':
                    shoot();
                    break;
                case 'KeyR':
                    reload();
                    break;
            }
        }

        function onKeyUp(event) {
            switch (event.code) {
                case 'KeyW':
                case 'ArrowUp':
                    moveForward = false;
                    break;
                case 'KeyS':
                case 'ArrowDown':
                    moveBackward = false;
                    break;
                case 'KeyA':
                case 'ArrowLeft':
                    moveLeft = false;
                    break;
                case 'KeyD':
                case 'ArrowRight':
                    moveRight = false;
                    break;
            }
        }

        function onMouseDown(event) {
            if (controls.isLocked && event.button === 0) {
                shoot();
            }
        }

        function onWindowResize() {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        }

        function spawnEnemy() {
            if (enemies.length >= MAX_ENEMIES) return;
            
            // Enemy tank body
            const bodyGeometry = new THREE.BoxGeometry(4, 1.5, 6);
            const bodyMaterial = new THREE.MeshStandardMaterial({ color: 0x990000 });
            const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
            body.castShadow = true;
            body.receiveShadow = true;
            
            // Enemy tank turret
            const turretGeometry = new THREE.CylinderGeometry(1.5, 1.5, 1, 16);
            const turretMaterial = new THREE.MeshStandardMaterial({ color: 0x770000 });
            const turret = new THREE.Mesh(turretGeometry, turretMaterial);
            turret.position.y = 1.25;
            turret.castShadow = true;
            turret.receiveShadow = true;
            
            // Enemy tank cannon
            const cannonGeometry = new THREE.CylinderGeometry(0.3, 0.3, 3, 12);
            const cannonMaterial = new THREE.MeshStandardMaterial({ color: 0x111111 });
            const cannon = new THREE.Mesh(cannonGeometry, cannonMaterial);
            cannon.position.z = 1.5;
            cannon.rotation.x = Math.PI / 2;
            turret.add(cannon);
            
            // Create tracks
            const trackGeometry = new THREE.BoxGeometry(1, 0.8, 6);
            const trackMaterial = new THREE.MeshStandardMaterial({ color: 0x333333 });
            
            const leftTrack = new THREE.Mesh(trackGeometry, trackMaterial);
            leftTrack.position.set(-2, -0.5, 0);
            leftTrack.castShadow = true;
            leftTrack.receiveShadow = true;
            
            const rightTrack = new THREE.Mesh(trackGeometry, trackMaterial);
            rightTrack.position.set(2, -0.5, 0);
            rightTrack.castShadow = true;
            rightTrack.receiveShadow = true;
            
            // Create enemy tank group
            const enemyTank = new THREE.Group();
            enemyTank.add(body);
            enemyTank.add(turret);
            enemyTank.add(leftTrack);
            enemyTank.add(rightTrack);
            
            // Position the enemy away from the player
            let x, z;
            do {
                x = Math.random() * TERRAIN_SIZE - TERRAIN_SIZE/2;
                z = Math.random() * TERRAIN_SIZE - TERRAIN_SIZE/2;
            } while (
                Math.sqrt(
                    Math.pow(x - playerTank.position.x, 2) + 
                    Math.pow(z - playerTank.position.z, 2)
                ) < 100
            );
            
            enemyTank.position.set(x, 1.5, z);
            
            // Store turret reference for aiming
            enemyTank.userData = {
                turret: turret,
                health: 100,
                shootCooldown: 0,
                type: 'enemy'
            };
            
            scene.add(enemyTank);
            enemies.push(enemyTank);
            
            return enemyTank;
        }

        function shoot() {
            if (!canShoot || ammo <= 0 || isGameOver) return;
            
            // Create bullet
            const bulletGeometry = new THREE.SphereGeometry(0.3, 8, 8);
            const bulletMaterial = new THREE.MeshBasicMaterial({ color: 0xffff00 });
            const bullet = new THREE.Mesh(bulletGeometry, bulletMaterial);
            
            // Position bullet at the tip of the tank's cannon
            playerTank.updateMatrixWorld();
            const cannonTip = new THREE.Vector3(0, 0, 3);
            cannonTip.applyMatrix4(playerTurret.matrixWorld);
            bullet.position.copy(cannonTip);
            
            // Set bullet direction
            const bulletDirection = new THREE.Vector3(0, 0, 1);
            bulletDirection.applyQuaternion(playerTurret.quaternion);
            bullet.userData = {
                direction: bulletDirection,
                type: 'playerBullet',
                age: 0
            };
            
            scene.add(bullet);
            bullets.push(bullet);
            
            // Reduce ammo
            ammo--;
            updateAmmoDisplay();
            
            // Add recoil cooldown
            canShoot = false;
            setTimeout(() => {
                canShoot = true;
            }, 250);
            
            // Auto-reload when empty
            if (ammo === 0) {
                reload();
            }
        }

        function enemyShoot(enemy) {
            if (isGameOver) return;
            
            // Create bullet
            const bulletGeometry = new THREE.SphereGeometry(0.3, 8, 8);
            const bulletMaterial = new THREE.MeshBasicMaterial({ color: 0xff0000 });
            const bullet = new THREE.Mesh(bulletGeometry, bulletMaterial);
            
            // Get the turret from enemy data
            const turret = enemy.userData.turret;
            
            // Position bullet at the tip of the tank's cannon
            enemy.updateMatrixWorld();
            const cannonTip = new THREE.Vector3(0, 0, 3);
            cannonTip.applyMatrix4(turret.matrixWorld);
            bullet.position.copy(cannonTip);
            
            // Set bullet direction
            const bulletDirection = new THREE.Vector3(0, 0, 1);
            bulletDirection.applyQuaternion(turret.quaternion);
            bullet.userData = {
                direction: bulletDirection,
                type: 'enemyBullet',
                age: 0
            };
            
            scene.add(bullet);
            bullets.push(bullet);
            
            // Reset cooldown
            enemy.userData.shootCooldown = 3;
        }

        function reload() {
            if (ammo === MAX_AMMO || isGameOver) return;
            
            canShoot = false;
            const ammoDisplay = document.getElementById('ammo');
            ammoDisplay.textContent = "Reloading...";
            
            setTimeout(() => {
                ammo = MAX_AMMO;
                canShoot = true;
                updateAmmoDisplay();
            }, RELOAD_TIME);
        }

        function updateAmmoDisplay() {
            const ammoDisplay = document.getElementById('ammo');
            ammoDisplay.textContent = `Ammo: ${ammo}/${MAX_AMMO}`;
        }

        function updateHealthDisplay() {
            const healthBar = document.getElementById('health');
            healthBar.style.width = `${health}%`;
            
            // Change color based on health
            if (health > 60) {
                healthBar.style.backgroundColor = '#4CAF50';
            } else if (health > 30) {
                healthBar.style.backgroundColor = '#FFC107';
            } else {
                healthBar.style.backgroundColor = '#F44336';
            }
        }

        function updateScoreDisplay() {
            const scoreDisplay = document.getElementById('score');
            scoreDisplay.textContent = `Score: ${score}`;
        }

        function takeDamage(amount) {
            health -= amount;
            updateHealthDisplay();
            
            if (health <= 0) {
                gameOver();
            }
        }

        function gameOver() {
            isGameOver = true;
            controls.unlock();
            
            const finalScoreDisplay = document.getElementById('final-score');
            finalScoreDisplay.textContent = score;
            
            const gameOverScreen = document.getElementById('game-over');
            gameOverScreen.style.display = 'block';
        }

        function restartGame() {
            // Reset game state
            health = HEALTH_MAX;
            score = 0;
            ammo = MAX_AMMO;
            isGameOver = false;
            
            // Update UI
            updateHealthDisplay();
            updateScoreDisplay();
            updateAmmoDisplay();
            
            // Hide game over screen
            const gameOverScreen = document.getElementById('game-over');
            gameOverScreen.style.display = 'none';
            
            // Reset player position
            playerTank.position.set(0, 1.5, 0);
            playerTank.rotation.y = 0;
            playerTurret.rotation.y = 0;
            
            // Remove all enemies
            for (let i = enemies.length - 1; i >= 0; i--) {
                scene.remove(enemies[i]);
            }
            enemies = [];
            
            // Remove all bullets
            for (let i = bullets.length - 1; i >= 0; i--) {
                scene.remove(bullets[i]);
            }
            bullets = [];
            
            // Spawn new enemies
            for (let i = 0; i < 5; i++) {
                spawnEnemy();
            }
        }

        function animate() {
            requestAnimationFrame(animate);
            
            if (!isGameOver) {
                const delta = clock.getDelta() * GAME_SPEED;
                
                // Move player tank
                updatePlayerTank(delta);
                
                // Update enemy tanks
                updateEnemies(delta);
                
                // Update bullets
                updateBullets(delta);
                
                // Spawn enemies if needed
                if (Math.random() < 0.003 && enemies.length < MAX_ENEMIES) {
                    spawnEnemy();
                }
            }
            
            // Render scene
            renderer.render(scene, camera);
        }

        function updatePlayerTank(delta) {
            const speed = TANK_SPEED * delta;
            const rotationSpeed = 2 * delta;
            
            // Rotate tank body based on movement keys
            if (moveLeft) {
                playerTank.rotation.y += rotationSpeed;
            }
            if (moveRight) {
                playerTank.rotation.y -= rotationSpeed;
            }
            
            // Move tank forward/backward
            if (moveForward) {
                playerTank.translateZ(speed);
            }
            if (moveBackward) {
                playerTank.translateZ(-speed);
            }
            
            // Keep tank within bounds
            const boundarySize = TERRAIN_SIZE / 2 - 10;
            playerTank.position.x = Math.max(-boundarySize, Math.min(boundarySize, playerTank.position.x));
            playerTank.position.z = Math.max(-boundarySize, Math.min(boundarySize, playerTank.position.z));
            
            // Rotate turret to follow mouse
            const euler = new THREE.Euler(0, 0, 0, 'YXZ');
            euler.setFromQuaternion(camera.quaternion);
            playerTurret.rotation.y = euler.y - playerTank.rotation.y;
        }

        function updateEnemies(delta) {
            for (let i = 0; i < enemies.length; i++) {
                const enemy = enemies[i];
                
                // Skip destroyed enemies
                if (!enemy.visible) continue;
                
                // Calculate direction to player
                const direction = new THREE.Vector3();
                direction.subVectors(playerTank.position, enemy.position).normalize();
                
                // Calculate angle to player
                const angle = Math.atan2(direction.x, direction.z);
                
                // Rotate enemy tank towards player
                enemy.rotation.y = angle;
                
                // Rotate turret directly at player (accounting for tank rotation)
                const turret = enemy.userData.turret;
                const turretAngle = Math.atan2(
                    playerTank.position.x - enemy.position.x,
                    playerTank.position.z - enemy.position.z
                );
                turret.rotation.y = turretAngle - enemy.rotation.y;
                
                // Move enemy towards player if not too close
                const distanceToPlayer = enemy.position.distanceTo(playerTank.position);
                if (distanceToPlayer > 30) {
                    const speed = ENEMY_SPEED * delta;
                    enemy.translateZ(speed);
                }
                
                // Shoot at player occasionally
                enemy.userData.shootCooldown -= delta;
                if (enemy.userData.shootCooldown <= 0 && distanceToPlayer < 70) {
                    enemyShoot(enemy);
                }
            }
        }

        function updateBullets(delta) {
            for (let i = bullets.length - 1; i >= 0; i--) {
                const bullet = bullets[i];
                
                // Move bullet
                const speed = BULLET_SPEED * delta;
                const direction = bullet.userData.direction;
                bullet.position.x += direction.x * speed;
                bullet.position.y += direction.y * speed;
                bullet.position.z += direction.z * speed;
                
                // Increment age
                bullet.userData.age += delta;
                
                // Check for collisions
                if (bullet.userData.type === 'playerBullet') {
                    checkEnemyCollisions(bullet, i);
                } else if (bullet.userData.type === 'enemyBullet') {
                    checkPlayerCollision(bullet, i);
                }
                
                // Remove old bullets
                if (bullet.userData.age > 3 || 
                    Math.abs(bullet.position.x) > TERRAIN_SIZE/2 ||
                    Math.abs(bullet.position.z) > TERRAIN_SIZE/2) {
                    scene.remove(bullet);
                    bullets.splice(i, 1);
                }
            }
        }

        function checkEnemyCollisions(bullet, bulletIndex) {
            for (let i = 0; i < enemies.length; i++) {
                const enemy = enemies[i];
                
                // Skip hidden enemies
                if (!enemy.visible) continue;
                
                // Check distance
                const distance = bullet.position.distanceTo(enemy.position);
                
                if (distance < 3) {
                    // Hit an enemy
                    enemy.userData.health -= 50;
                    
                    // Remove the bullet
                    scene.remove(bullet);
                    bullets.splice(bulletIndex, 1);
                    
                    if (enemy.userData.health <= 0) {
                        // Enemy destroyed
                        enemy.visible = false;
                        setTimeout(() => {
                            scene.remove(enemy);
                            enemies.splice(enemies.indexOf(enemy), 1);
                        }, 1000);
                        
                        // Update score
                        score += 100;
                        updateScoreDisplay();
                    }
                    
                    return;
                }
            }
        }

        function checkPlayerCollision(bullet, bulletIndex) {
            const distance = bullet.position.distanceTo(playerTank.position);
            
            if (distance < 3) {
                // Player hit
                takeDamage(20);
                
                // Remove the bullet
                scene.remove(bullet);
                bullets.splice(bulletIndex, 1);
            }
        }
    </script>
</body>
</html> 