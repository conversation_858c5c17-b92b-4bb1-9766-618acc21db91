import anthropic
import os
import re
import sys
import subprocess
import time
import threading
import signal
from termcolor import colored

# CONSTANTS
USER_PROMPT = "please build a fully featured, beautiful(dark mode) calendar and personal management app using tkinter"
INITIAL_GENERATION_PROMPT = "Please return the full code in between <code> and </code> tags"
ERROR_FIX_PROMPT = "Fix the following Python code that has an error. Return the full and complete fixed code between <code> and </code> tags. Always return the full app code. Error: {}"
IMPROVEMENT_PROMPT = "Improve the following Python code by adding new features, tidying the codebase, and enhancing visuals if applicable. Return the full and complete improved code between <code> and </code> tags."
MODEL = "claude-3-7-sonnet-20250219"
MAX_TOKENS = 64000
OUTPUT_FOLDER = "generated_code"  # Folder to save all generated files
OUTPUT_FILE = "generated_app.py"
STREAM_MODE = True  # Set to False to disable streaming
TIMEOUT_SECONDS = 5  # Timeout for running the app if no errors
MAX_TOTAL_ITERATIONS = 5  # Maximum number of total improvement iterations
MAX_ERROR_FIX_ITERATIONS = 2  # Maximum number of error fixing attempts per iteration
MAX_LINES = 1000  # Maximum number of lines of code (set to None to disable this limit)

def setup_client():
    """Set up and return the Anthropic client."""
    try:
        api_key = os.getenv("ANTHROPIC_API_KEY")
        if not api_key:
            print(colored("Error: ANTHROPIC_API_KEY not found in environment variables", "red"))
            sys.exit(1)
        return anthropic.Anthropic(api_key=api_key)
    except Exception as e:
        print(colored(f"Error setting up Anthropic client: {e}", "red"))
        sys.exit(1)

def ensure_output_folder_exists():
    """Ensure the output folder exists, create it if it doesn't."""
    try:
        if not os.path.exists(OUTPUT_FOLDER):
            os.makedirs(OUTPUT_FOLDER)
            print(colored(f"Created output folder: {OUTPUT_FOLDER}", "green"))
        return True
    except Exception as e:
        print(colored(f"Error creating output folder: {e}", "red"))
        return False

def get_file_path(filename):
    """Get the full path for a file in the output folder."""
    return os.path.join(OUTPUT_FOLDER, filename)

def call_claude_api(client, prompt, system_prompt, stream=True, task_description=""):
    """Generic function to call Claude API with streaming or non-streaming mode."""
    print(colored(f"Calling Claude API for {task_description} {'(streaming mode)' if stream else ''}...", "cyan"))
    
    full_response = ""
    
    try:
        if stream:
            with client.messages.stream(
                model=MODEL,
                max_tokens=MAX_TOKENS,
                system=system_prompt,
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": prompt
                            }
                        ]
                    }
                ]
            ) as stream:
                print(colored(f"\n--- Streaming {task_description} response ---\n", "yellow"))
                for text in stream.text_stream:
                    print(text, end="", flush=True)
                    full_response += text
                print(colored(f"\n\n--- End of streaming {task_description} response ---\n", "yellow"))
        else:
            message = client.messages.create(
                model=MODEL,
                max_tokens=MAX_TOKENS,
                system=system_prompt,
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": prompt
                            }
                        ]
                    }
                ]
            )
            full_response = message.content[0].text
            print(full_response)
        
        print(colored(f"{task_description.capitalize()} completed successfully!", "green"))
        return full_response
    except Exception as e:
        print(colored(f"Error in Claude API call for {task_description}: {e}", "red"))
        sys.exit(1)

def extract_code(response):
    """Extract code from between <code> and </code> tags."""
    print(colored("Extracting code from response...", "cyan"))
    try:
        # Look for content between <code> and </code> tags
        code_match = re.search(r'<code>(.*?)</code>', str(response), re.DOTALL)
        
        if code_match:
            code = code_match.group(1).strip()
            print(colored("Code extracted successfully!", "green"))
            return code
        else:
            print(colored("No code found between <code> and </code> tags", "yellow"))
            # If no tags found, try to extract code blocks
            code_blocks = re.findall(r'```python\s*(.*?)```', str(response), re.DOTALL)
            if code_blocks:
                print(colored("Found code in Python code blocks instead", "green"))
                return '\n\n'.join(code_blocks)
            else:
                print(colored("No code blocks found either. Returning full response.", "yellow"))
                return str(response)
    except Exception as e:
        print(colored(f"Error extracting code: {e}", "red"))
        return str(response)

def save_to_file(code, filename):
    """Save the extracted code to a file."""
    filepath = get_file_path(filename)
    print(colored(f"Saving code to {filepath}...", "cyan"))
    try:
        with open(filepath, 'w', encoding="utf-8") as f:
            f.write(code)
        print(colored(f"Code successfully saved to {filepath}", "green"))
        return True
    except Exception as e:
        print(colored(f"Error saving code to file: {e}", "red"))
        return False

def run_generated_code(filename):
    """Run the generated code and capture any errors."""
    filepath = get_file_path(filename)
    print(colored(f"Running the generated code from {filepath}...", "cyan"))
    
    # Create a process to run the generated code
    process = subprocess.Popen(
        [sys.executable, filepath],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True,
        encoding="utf-8"
    )
    
    # Flag to track if the process was terminated by timeout
    terminated_by_timeout = [False]
    
    # Set up a timer to kill the process after TIMEOUT_SECONDS if no errors
    def timeout_handler():
        terminated_by_timeout[0] = True
        kill_process(process)
    
    timer = threading.Timer(TIMEOUT_SECONDS, timeout_handler)
    timer.start()
    
    try:
        # Wait for the process to complete or be killed
        stdout, stderr = process.communicate()
        
        # Cancel the timer if the process completes before timeout
        timer.cancel()
        
        if terminated_by_timeout[0]:
            print(colored("Process ran without errors and was terminated after timeout.", "green"))
            return None
        elif stderr:
            print(colored("Error detected while running the code:", "red"))
            # Get the last 500 characters of the error message
            error_tail = stderr[-500:] if len(stderr) > 500 else stderr
            print(colored(error_tail, "red"))
            return error_tail
        else:
            print(colored("Code executed successfully with no errors!", "green"))
            return None
    except Exception as e:
        timer.cancel()
        print(colored(f"Error running the generated code: {e}", "red"))
        return str(e)

def kill_process(process):
    """Kill the process after timeout."""
    print(colored(f"No errors detected after {TIMEOUT_SECONDS} seconds. Terminating the subprocess...", "yellow"))
    try:
        # First try to terminate the process gracefully
        process.terminate()
        
        # Give it a moment to terminate
        time.sleep(0.5)
        
        # If it's still running, force kill it
        if process.poll() is None:
            if sys.platform == 'win32':
                # On Windows, use taskkill to force kill
                subprocess.run(['taskkill', '/F', '/T', '/PID', str(process.pid)], 
                              stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            else:
                # On Unix-like systems, use SIGKILL
                process.kill()
        
        print(colored("Subprocess terminated successfully.", "green"))
    except Exception as e:
        print(colored(f"Error terminating subprocess: {e}", "red"))

def generate_code(client, prompt, stream=True):
    """Generate code using Claude API."""
    # Add line count instruction if MAX_LINES is set
    enhanced_prompt = prompt
    if MAX_LINES is not None:
        enhanced_prompt = f"{prompt}\n\nIMPORTANT: The code should not exceed {MAX_LINES} lines."
        print(colored(f"Adding line count restriction: maximum {MAX_LINES} lines", "cyan"))
    
    return call_claude_api(
        client=client,
        prompt=enhanced_prompt,
        system_prompt=INITIAL_GENERATION_PROMPT,
        stream=stream,
        task_description="code generation"
    )

def fix_code_error(client, code, error_message, stream=True, original_prompt=None):
    """Use Claude to fix the code based on the error message."""
    prompt = f"""
The following Python code has an error:

```python
{code}
```

Error message:
{error_message}

Please fix the code and return the complete fixed version.
"""
    
    # Add the original user prompt as context if provided
    if original_prompt:
        prompt = f"""
Original request: {original_prompt}

{prompt}
"""
    
    # Add line count instruction if MAX_LINES is set
    if MAX_LINES is not None:
        prompt += f"\n\nIMPORTANT: The fixed code should not exceed {MAX_LINES} lines."
        print(colored(f"Adding line count restriction: maximum {MAX_LINES} lines", "cyan"))
    
    return call_claude_api(
        client=client,
        prompt=prompt,
        system_prompt=ERROR_FIX_PROMPT.format(error_message),
        stream=stream,
        task_description="error fixing"
    )

def improve_code(client, code, iteration, stream=True, original_prompt=None):
    """Use Claude to improve the code with new features and better visuals."""
    print(colored(f"Starting improvement iteration {iteration}...", "magenta"))
    
    prompt = f"""
Please improve the following Python code (iteration {iteration} of {MAX_TOTAL_ITERATIONS}):

```python
{code}
```

Focus on:
1. Adding new features that enhance functionality
2. Tidying the codebase for better readability and maintainability
3. Enhancing visuals and user experience if applicable
"""

    # Add the original user prompt as context if provided
    if original_prompt:
        prompt = f"""
Original request: {original_prompt}

{prompt}
"""

    # Add line count instruction if MAX_LINES is set
    if MAX_LINES is not None:
        prompt += f"\n4. Ensuring the code remains under {MAX_LINES} lines"
        print(colored(f"Adding line count restriction: maximum {MAX_LINES} lines", "cyan"))
    else:
        prompt += "\n4. Keeping the code concise and efficient"
    
    prompt += "\n\nReturn the complete improved code."
    
    return call_claude_api(
        client=client,
        prompt=prompt,
        system_prompt=IMPROVEMENT_PROMPT,
        stream=stream,
        task_description=f"code improvement (iteration {iteration})"
    )

def process_code_with_error_fixing(client, code, filename, error_fix_count=0, iteration_info=None, original_prompt=None):
    """Process code with error fixing if needed."""
    # Save the code to file
    save_to_file(code, filename)
    
    # Track this file in iteration_info if provided
    if iteration_info is not None:
        line_count = len(code.splitlines())
        iteration_info["files"].append({
            "filename": filename,
            "line_count": line_count,
            "error_fix_attempt": error_fix_count,
            "description": f"{'Initial code' if error_fix_count == 0 else f'Error fix attempt {error_fix_count}'}"
        })
    
    # Check if code exceeds MAX_LINES (if set)
    if MAX_LINES is not None:
        line_count = len(code.splitlines())
        if line_count > MAX_LINES:
            print(colored(f"Warning: Generated code has {line_count} lines, which exceeds the maximum of {MAX_LINES} lines", "yellow"))
    
    # Run the code and check for errors
    error = run_generated_code(filename)
    
    # If there's an error and we haven't exceeded the max error fix attempts
    if error and error_fix_count < MAX_ERROR_FIX_ITERATIONS:
        print(colored(f"Attempting to fix error (attempt {error_fix_count + 1}/{MAX_ERROR_FIX_ITERATIONS})...", "yellow"))
        
        # Read the code from file (in case it was modified)
        with open(get_file_path(filename), 'r', encoding="utf-8") as f:
            current_code = f.read()
        
        # Get fixed code from Claude
        fixed_response = fix_code_error(client, current_code, error, stream=STREAM_MODE, original_prompt=original_prompt)
        if fixed_response:
            fixed_code = extract_code(fixed_response)
            if fixed_code:
                # Extract the base part of the filename (remove any existing fix_ prefix)
                base_filename = filename
                if "_fix_" in filename:
                    # If it already has a fix prefix, extract the base part
                    parts = filename.split("_fix_", 1)
                    if len(parts) > 1:
                        base_filename = parts[1]
                
                # Create a new filename for the fixed version with proper sorting
                # Extract the iteration number from the filename if it exists
                iteration_prefix = ""
                if filename.startswith("00_"):
                    iteration_prefix = "00"
                elif filename[0:2].isdigit():
                    iteration_prefix = filename[0:2]
                
                # Create the fixed filename with proper sorting
                fixed_filename = f"{iteration_prefix}_fix_{error_fix_count + 1:02d}_{base_filename}"
                
                # Recursively process the fixed code (with incremented error fix count)
                return process_code_with_error_fixing(
                    client, 
                    fixed_code, 
                    fixed_filename, 
                    error_fix_count + 1,
                    iteration_info,
                    original_prompt
                )
            else:
                print(colored("Could not extract fixed code from Claude's response", "red"))
                return False, code, filename
        else:
            print(colored("Failed to get a fix from Claude", "red"))
            return False, code, filename
    elif error:
        # We've exceeded the maximum error fix attempts
        print(colored(f"Failed to fix errors after {MAX_ERROR_FIX_ITERATIONS} attempts", "red"))
        return False, code, filename
    else:
        # No errors!
        return True, code, filename

def display_iterations_summary(iterations):
    """Display a summary of all iterations."""
    print(colored("\n=== ITERATIONS SUMMARY ===", "magenta"))
    
    for i, iteration in enumerate(iterations):
        print(colored(f"\nIteration {i}: {iteration['description']}", "cyan"))
        print(colored("Files generated:", "cyan"))
        
        for file_info in iteration["files"]:
            status = "✓ Success" if file_info.get("success", False) else "✗ Failed"
            status_color = "green" if file_info.get("success", False) else "red"
            filepath = get_file_path(file_info['filename'])
            print(colored(f"  - {filepath} ({file_info['line_count']} lines): {file_info['description']} [{colored(status, status_color)}]", "white"))
    
    if iterations and iterations[-1]["final_file"]:
        final_filepath = get_file_path(iterations[-1]["final_file"])
        print(colored(f"\nFinal working file: {final_filepath}", "green"))
    else:
        print(colored("\nNo successful iterations completed.", "yellow"))

def main():
    """Main function to orchestrate the code generation process."""
    print(colored("Starting Auto Coder with Claude...", "magenta"))
    
    # Display configuration
    print(colored(f"Using prompt: {USER_PROMPT}", "cyan"))
    print(colored(f"Output folder: {OUTPUT_FOLDER}", "cyan"))
    print(colored(f"Output file: {OUTPUT_FILE}", "cyan"))
    print(colored(f"Streaming mode: {'enabled' if STREAM_MODE else 'disabled'}", "cyan"))
    print(colored(f"Max total iterations: {MAX_TOTAL_ITERATIONS}", "cyan"))
    print(colored(f"Max error fix attempts per iteration: {MAX_ERROR_FIX_ITERATIONS}", "cyan"))
    print(colored(f"Max lines of code: {MAX_LINES if MAX_LINES is not None else 'No limit'}", "cyan"))
    print(colored(f"Timeout for running app: {TIMEOUT_SECONDS} seconds", "cyan"))
    
    # Ensure output folder exists
    if not ensure_output_folder_exists():
        print(colored("Failed to create output folder. Exiting.", "red"))
        return
    
    client = setup_client()
    
    # Keep track of all iterations for the summary
    iterations = []
    
    # Initial code generation
    print(colored("\n=== INITIAL CODE GENERATION ===", "magenta"))
    initial_iteration = {
        "description": "Initial code generation",
        "files": [],
        "final_file": None,
        "success": False
    }
    
    response = generate_code(client, USER_PROMPT, stream=STREAM_MODE)
    code = extract_code(response)
    
    # Create a filename for the initial code - use 00 prefix for sorting
    initial_filename = f"00_initial_{OUTPUT_FILE}"
    
    # Process the initial code (with error fixing if needed)
    success, current_code, final_file = process_code_with_error_fixing(
        client, code, initial_filename, 0, initial_iteration, USER_PROMPT
    )
    
    # Update the initial iteration with success status and final file
    initial_iteration["success"] = success
    initial_iteration["final_file"] = final_file
    
    # Mark the successful file
    for file_info in initial_iteration["files"]:
        if file_info["filename"] == final_file:
            file_info["success"] = True
    
    # Add the initial iteration to our list
    iterations.append(initial_iteration)
    
    if not success:
        print(colored("Failed to generate working code. Exiting.", "red"))
        display_iterations_summary(iterations)
        return
    
    # Instead of copying to OUTPUT_FILE, just use the final file as our current working code
    with open(get_file_path(final_file), 'r', encoding="utf-8") as f:
        current_code = f.read()
    
    # Improvement iterations
    current_iteration = 1
    while current_iteration <= MAX_TOTAL_ITERATIONS:
        print(colored(f"\n=== IMPROVEMENT ITERATION {current_iteration}/{MAX_TOTAL_ITERATIONS} ===", "magenta"))
        
        # Create a new iteration info dictionary
        improvement_iteration = {
            "description": f"Improvement {current_iteration}",
            "files": [],
            "final_file": None,
            "success": False
        }
        
        # Remove user input prompt and automatically continue with improvements
        print(colored(f"Automatically continuing with improvement iteration {current_iteration}...", "cyan"))
        
        # Improve the code
        improved_response = improve_code(client, current_code, current_iteration, stream=STREAM_MODE, original_prompt=USER_PROMPT)
        improved_code = extract_code(improved_response)
        
        # Create a filename for this iteration - use padded numbers for sorting
        iteration_file = f"{current_iteration:02d}_improved_{OUTPUT_FILE}"
        
        # Process the improved code (with error fixing if needed)
        success, current_code, final_file = process_code_with_error_fixing(
            client, improved_code, iteration_file, 0, improvement_iteration, USER_PROMPT
        )
        
        # Update the improvement iteration with success status and final file
        improvement_iteration["success"] = success
        improvement_iteration["final_file"] = final_file
        
        # Mark the successful file
        for file_info in improvement_iteration["files"]:
            if file_info["filename"] == final_file:
                file_info["success"] = True
        
        # Add this iteration to our list
        iterations.append(improvement_iteration)
        
        if success:
            print(colored(f"Improvement iteration {current_iteration} completed successfully!", "green"))
            # Update our current code from the final file
            with open(get_file_path(final_file), 'r', encoding="utf-8") as f:
                current_code = f.read()
        else:
            print(colored(f"Failed to improve code in iteration {current_iteration}", "red"))
            # Don't increment the iteration counter if we failed
            continue
        
        current_iteration += 1
    
    # After all iterations, save the final version to OUTPUT_FILE
    # Use a special prefix to make it appear at the top
    final_output_file = f"zz_final_{OUTPUT_FILE}"
    save_to_file(current_code, final_output_file)
    
    print(colored("\n=== AUTO CODER PROCESS COMPLETED ===", "magenta"))
    print(colored(f"Final code saved to {get_file_path(final_output_file)}", "green"))
    
    # Display a summary of all iterations
    display_iterations_summary(iterations)

if __name__ == "__main__":
    main()
