import anthropic
import os
import re
import sys
from termcolor import colored

# CONSTANTS
USER_PROMPT = "please build a fully featured calendar and personal management app using tkinter"
INITIAL_GENERATION_PROMPT = "Please return the full code in between <code> and </code> tags"
MODEL = "claude-3-7-sonnet-20250219"
MAX_TOKENS = 64000
OUTPUT_FILE = "generated_app.py"
STREAM_MODE = True  # Set to False to disable streaming

def setup_client():
    """Set up and return the Anthropic client."""
    try:
        api_key = os.getenv("ANTHROPIC_API_KEY")
        if not api_key:
            print(colored("Error: ANTHROPIC_API_KEY not found in environment variables", "red"))
            sys.exit(1)
        return anthropic.Anthropic(api_key=api_key)
    except Exception as e:
        print(colored(f"Error setting up Anthropic client: {e}", "red"))
        sys.exit(1)

def generate_code_stream(client, prompt):
    """Generate code using <PERSON> with streaming."""
    print(colored("Generating code with <PERSON> (streaming mode)...", "cyan"))
    full_response = ""
    try:
        with client.messages.stream(
            model=MODEL,
            max_tokens=MAX_TOKENS,
            system=INITIAL_GENERATION_PROMPT,
            messages=[
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": prompt
                        }
                    ]
                }
            ]
        ) as stream:
            print(colored("\n--- Streaming response ---\n", "yellow"))
            for text in stream.text_stream:
                print(text, end="", flush=True)
                full_response += text
            print(colored("\n\n--- End of streaming response ---\n", "yellow"))
        
        print(colored("Code generation completed successfully!", "green"))
        return full_response
    except Exception as e:
        print(colored(f"Error generating code with streaming: {e}", "red"))
        sys.exit(1)

def generate_code(client, prompt, stream=True):
    """Generate code using Claude API."""
    if stream:
        return generate_code_stream(client, prompt)
    
    print(colored("Generating code with Claude...", "cyan"))
    try:
        message = client.messages.create(
            model=MODEL,
            max_tokens=MAX_TOKENS,
            system=INITIAL_GENERATION_PROMPT,
            messages=[
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": prompt
                        }
                    ]
                }
            ]
        )
        response_text = message.content[0].text
        print(response_text)
        print(colored("Code generation completed successfully!", "green"))
        return response_text
    except Exception as e:
        print(colored(f"Error generating code: {e}", "red"))
        sys.exit(1)

def extract_code(response):
    """Extract code from between <code> and </code> tags."""
    print(colored("Extracting code from response...", "cyan"))
    try:
        # Look for content between <code> and </code> tags
        code_match = re.search(r'<code>(.*?)</code>', str(response), re.DOTALL)
        
        if code_match:
            code = code_match.group(1).strip()
            print(colored("Code extracted successfully!", "green"))
            return code
        else:
            print(colored("No code found between <code> and </code> tags", "yellow"))
            # If no tags found, try to extract code blocks
            code_blocks = re.findall(r'```python\s*(.*?)```', str(response), re.DOTALL)
            if code_blocks:
                print(colored("Found code in Python code blocks instead", "green"))
                return '\n\n'.join(code_blocks)
            else:
                print(colored("No code blocks found either. Returning full response.", "yellow"))
                return str(response)
    except Exception as e:
        print(colored(f"Error extracting code: {e}", "red"))
        return str(response)

def save_to_file(code, filename):
    """Save the extracted code to a file."""
    print(colored(f"Saving code to {filename}...", "cyan"))
    try:
        with open(filename, 'w', encoding="utf-8") as f:
            f.write(code)
        print(colored(f"Code successfully saved to {filename}", "green"))
    except Exception as e:
        print(colored(f"Error saving code to file: {e}", "red"))

def main():
    """Main function to orchestrate the code generation process."""
    print(colored("Starting Auto Coder with Claude...", "magenta"))
    
    # Display configuration
    print(colored(f"Using prompt: {USER_PROMPT}", "cyan"))
    print(colored(f"Output will be saved to: {OUTPUT_FILE}", "cyan"))
    print(colored(f"Streaming mode: {'enabled' if STREAM_MODE else 'disabled'}", "cyan"))
    
    client = setup_client()
    response = generate_code(client, USER_PROMPT, stream=STREAM_MODE)
    code = extract_code(response)
    save_to_file(code, OUTPUT_FILE)
    
    print(colored(f"Process completed! Check {OUTPUT_FILE} for your generated application.", "magenta"))
    print(colored("To run the application: python " + OUTPUT_FILE, "cyan"))

if __name__ == "__main__":
    main()
