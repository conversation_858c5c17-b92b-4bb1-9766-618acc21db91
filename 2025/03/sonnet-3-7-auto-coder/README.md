# Auto Coder with <PERSON>

An automated code generation and improvement tool powered by <PERSON>thropic's <PERSON>. This tool can generate, fix, and iteratively improve Python code based on natural language prompts.

## Features

- 🤖 Automated code generation from text prompts
- 🔄 Iterative code improvements
- 🐛 Automatic error detection and fixing
- 📊 Detailed iteration summaries
- 🔄 Progress tracking with colored output
- ⏱️ Timeout handling for running code
- 📝 Line count limitations (optional)
- 📂 Organized file naming for easy tracking

## Prerequisites

- Python 3.7+
- Anthropic API key

## Installation

1. Install the required packages:

```bash
pip install -r requirements.txt
```

2. Set up your Anthropic API key as an environment variable:

```bash
# On Unix/macOS:
export ANTHROPIC_API_KEY='your-api-key'

# On Windows:
set ANTHROPIC_API_KEY=your-api-key
```

## Configuration

The following constants can be modified in `auto_coder_fixer_claude.py`:

```python
USER_PROMPT = "your prompt here"  # The prompt for code generation
MAX_TOKENS = 64000  # Maximum tokens for Claude API
OUTPUT_FOLDER = "generated_code"  # Folder for generated files
OUTPUT_FILE = "generated_app.py"  # Base name for output files
STREAM_MODE = True  # Enable/disable streaming mode
TIMEOUT_SECONDS = 5  # Timeout for running generated code
MAX_TOTAL_ITERATIONS = 3  # Maximum improvement iterations
MAX_ERROR_FIX_ITERATIONS = 2  # Maximum error fix attempts per iteration
MAX_LINES = 100  # Maximum lines of code (None for no limit)
```

## Usage

1. Set your desired prompt in the `USER_PROMPT` constant
2. Run the script:

```bash
python auto_coder_fixer_claude.py
```

The tool will:

1. Generate initial code based on your prompt
2. Test the code for errors
3. Fix any errors automatically
4. Perform multiple iterations of improvements
5. Save the final version to a file with the "zz_final_" prefix

## Process Flow

1. **Initial Generation**

   - Generates code from the user prompt
   - Saves to `00_initial_generated_app.py`
2. **Error Fixing**

   - Detects and fixes any runtime errors
   - Creates `00_fix_01_initial_generated_app.py` for each fix attempt
3. **Improvement Iterations**

   - Improves code functionality and readability
   - Creates `01_improved_generated_app.py`, `02_improved_generated_app.py`, etc.
   - Fixes any errors in improved versions with `01_fix_01_improved_generated_app.py`
4. **Final Output**

   - Saves the final working version to `zz_final_generated_app.py`
   - Provides a detailed summary of all iterations

## Output Structure

```
generated_code/
├── 00_initial_generated_app.py
├── 00_fix_01_initial_generated_app.py (if needed)
├── 01_improved_generated_app.py
├── 01_fix_01_improved_generated_app.py (if needed)
├── 02_improved_generated_app.py
└── zz_final_generated_app.py (final version)
```

The files are named with prefixes to ensure they appear in chronological order in file explorers.

## Error Handling

- Automatic detection of runtime errors
- Multiple fix attempts per error
- Detailed error messages and stack traces
- Graceful timeout handling for infinite loops

## Limitations

- Maximum number of improvement iterations: 3 (configurable)
- Maximum error fix attempts per iteration: 2 (configurable)
- Default maximum lines of code: 100 (configurable)
- Timeout for running code: 5 seconds (configurable)

## Acknowledgments

- Powered by [Anthropic&#39;s Claude AI](https://www.anthropic.com/)
- Uses [termcolor](https://pypi.org/project/termcolor/) for colored output
