import os
from termcolor import colored

def count_lines_in_file(file_path):
    try:
        with open(file_path, encoding="utf-8") as f:
            return len(f.readlines())
    except Exception as e:
        print(colored(f"Error reading file {file_path}: {e}", "red"))
        return 0

def get_total_lines():
    try:
        total_lines = 0
        file_counts = {}
        extensions = ('.py', '.js', '.css', '.html')
        
        print(colored("Starting to count lines in files...", "cyan"))
        
        # Walk through all directories
        for root, dirs, files in os.walk('.'):
            for file in files:
                if file.endswith(extensions):
                    file_path = os.path.join(root, file)
                    lines = count_lines_in_file(file_path)
                    total_lines += lines
                    file_counts[file_path] = lines
                    print(colored(f"Counted {lines} lines in {file_path}", "green"))
        
        # Print detailed breakdown
        print(colored("\nDetailed breakdown:", "yellow"))
        for file_path, count in file_counts.items():
            print(colored(f"{file_path}: {count} lines", "cyan"))
            
        print(colored(f"\nTotal lines of code: {total_lines}", "green", attrs=['bold']))
        
    except Exception as e:
        print(colored(f"Error counting lines: {e}", "red"))

if __name__ == "__main__":
    get_total_lines()
