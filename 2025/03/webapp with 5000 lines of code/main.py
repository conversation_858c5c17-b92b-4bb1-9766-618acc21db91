import os
import json
from typing import List, Dict, Any, Optional
from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException, WebSocket, WebSocketDisconnect, File, UploadFile
from fastapi.responses import HTMLResponse, StreamingResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from pydantic import BaseModel
from termcolor import colored
from openai import OpenAI, AsyncOpenAI
import asyncio
import base64
import re
import tempfile

# Constants
MODEL = "gpt-4o"
MAX_TOKENS = 4000
TEMPERATURE = 0.7
API_KEY_ENV = "OPENAI_API_KEY"

app = FastAPI(title="ChatGPT Clone")

# Mount static directory
app.mount("/static", StaticFiles(directory="static"), name="static")

# Templates
templates = Jinja2Templates(directory="templates")

# OpenAI client initialization
try:
    client = OpenAI(api_key=os.getenv(API_KEY_ENV))
    async_client = AsyncOpenAI(api_key=os.getenv(API_KEY_ENV))
    print(colored("OpenAI client initialized successfully", "green"))
except Exception as e:
    print(colored(f"Error initializing OpenAI client: {e}", "red"))
    raise

# Models
class ChatMessage(BaseModel):
    message: str
    systemMessage: Optional[str] = None
    history: Optional[List[Dict[str, str]]] = None

class ImageMessage(BaseModel):
    input: List[Dict[str, Any]]
    systemMessage: Optional[str] = None

class DocumentMessage(BaseModel):
    input: List[Dict[str, Any]]
    systemMessage: Optional[str] = None

@app.get("/", response_class=HTMLResponse)
async def get_chat_page(request: Request):
    print(colored("Serving chat page", "cyan"))
    return templates.TemplateResponse("index.html", {"request": request})

@app.post("/chat")
async def chat(message: ChatMessage):
    print(colored(f"Received message: {message.message}", "yellow"))
    
    try:
        # Prepare messages with system message if provided and history
        messages = []
        
        if message.systemMessage:
            messages.append({"role": "system", "content": message.systemMessage})
        
        if hasattr(message, "history") and message.history:
            for msg in message.history:
                role = msg.get("role")
                content = msg.get("content")
                if role and content:
                    messages.append({"role": role, "content": content})
        
        messages.append({"role": "user", "content": message.message})
        
        response = client.responses.create(
            model=MODEL,
            input=messages if messages else message.message
        )
        
        # Extract the text from the response
        if hasattr(response, 'output') and response.output:
            for item in response.output:
                if item.get("type") == "message" and item.get("role") == "assistant":
                    for content in item.get("content", []):
                        if content.get("type") == "output_text":
                            return {"response": content.get("text", "")}
        
        # If we couldn't extract the text using the above method, try the output_text attribute
        if hasattr(response, 'output_text'):
            return {"response": response.output_text}
            
        # If all else fails, return the raw response
        return {"response": str(response)}
    except Exception as e:
        print(colored(f"Error generating response: {e}", "red"))
        raise HTTPException(status_code=500, detail=f"Error generating response: {str(e)}")

@app.post("/chat-with-images")
async def chat_with_images(message: ImageMessage):
    print(colored(f"Received message with images", "yellow"))
    
    try:
        # Prepare the input for the OpenAI API
        input_data = message.input
        
        # If system message is provided, prepend it to the messages
        if message.systemMessage:
            print(colored(f"System message provided with image request", "cyan"))
            # Insert system message at the beginning of the array
            input_data.insert(0, {
                "role": "system",
                "content": message.systemMessage
            })
        
        # Check if streaming is requested
        stream_requested = True  # Enable streaming by default
        
        if stream_requested:
            # Create a streaming response
            async def generate():
                response = client.responses.create(
                    model=MODEL,
                    input=input_data,
                    stream=True
                )
                
                for event in response:
                    if event.type == "response.output_text.delta" and hasattr(event, "delta"):
                        # Send each token as a separate SSE event
                        yield f"data: {json.dumps({'type': 'token', 'token': event.delta})}\n\n"
                    elif event.type == "response.completed":
                        yield f"data: {json.dumps({'type': 'complete'})}\n\n"
                
                # End the stream
                yield f"data: {json.dumps({'type': 'end'})}\n\n"
            
            return StreamingResponse(
                generate(),
                media_type="text/event-stream"
            )
        else:
            # Non-streaming response (fallback)
            response = client.responses.create(
                model=MODEL,
                input=input_data
            )
            
            # Extract the text from the response
            if hasattr(response, 'output_text'):
                return {"response": response.output_text}
            
            # If output_text property isn't available, try to extract from output
            if hasattr(response, 'output') and response.output:
                for item in response.output:
                    if item.get("type") == "message" and item.get("role") == "assistant":
                        for content in item.get("content", []):
                            if content.get("type") == "output_text":
                                return {"response": content.get("text", "")}
                
            # If all else fails, return the raw response
            return {"response": str(response)}
    except Exception as e:
        print(colored(f"Error processing image message: {e}", "red"))
        raise HTTPException(status_code=500, detail=f"Error processing image message: {str(e)}")

# WebSocket for streaming responses
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    await websocket.accept()
    print(colored("WebSocket connection established", "green"))
    
    try:
        while True:
            data = await websocket.receive_text()
            print(colored(f"Received message via WebSocket", "yellow"))
            
            try:
                # Parse the JSON data
                json_data = json.loads(data)
                user_message = json_data.get("message", "")
                system_message = json_data.get("systemMessage")
                conversation_history = json_data.get("history", [])
                
                # Prepare the messages array
                messages = []
                
                # Add system message if provided
                if system_message:
                    messages.append({"role": "system", "content": system_message})
                
                # Add conversation history
                for msg in conversation_history:
                    role = msg.get("role")
                    content = msg.get("content")
                    if role and content:
                        # Convert 'user' and 'assistant' to proper roles
                        messages.append({"role": role, "content": content})
                
                # Add the current user message
                messages.append({"role": "user", "content": user_message})
                
                print(colored(f"Sending {len(messages)} messages to the API", "cyan"))
                
                # Process the message using the OpenAI API with streaming enabled
                response = client.responses.create(
                    model=MODEL,
                    input=messages if messages else user_message,
                    stream=True
                )
                
                # Process streaming response
                current_message = ""
                for event in response:
                    print(colored(f"Event type: {event.type}", "cyan"))
                    
                    # Handle text delta events (token streaming)
                    if event.type == "response.output_text.delta" and hasattr(event, "delta"):
                        token = event.delta
                        current_message += token
                        
                        # Send the delta token to the client
                        await websocket.send_text(json.dumps({
                            "type": "token",
                            "token": token,
                            "messageId": event.item_id
                        }))
                    
                    # Handle completion event
                    elif event.type == "response.completed":
                        await websocket.send_text(json.dumps({
                            "type": "complete",
                            "messageId": None
                        }))
                
            except Exception as e:
                print(colored(f"Error processing WebSocket message: {e}", "red"))
                await websocket.send_text(json.dumps({"error": str(e)}))
    except WebSocketDisconnect:
        print(colored("WebSocket disconnected", "yellow"))

@app.get("/chat-with-images-stream")
async def chat_with_images_stream(request: Request):
    print(colored(f"Received image stream request", "yellow"))
    
    # Get query parameters
    input_str = request.query_params.get('input')
    system_message = request.query_params.get('systemMessage')
    
    if not input_str:
        raise HTTPException(status_code=400, detail="Input parameter is required")
    
    try:
        # Parse the input JSON
        input_data = json.loads(input_str)
        
        # If system message is provided, prepend it
        if system_message:
            input_data.insert(0, {
                "role": "system",
                "content": system_message
            })
        
        async def generate():
            # Initialize the streaming response
            yield "data: {\"type\":\"start\"}\n\n"
            
            try:
                # Create streaming response
                response = client.responses.create(
                    model=MODEL,
                    input=input_data,
                    stream=True
                )
                
                for event in response:
                    if event.type == "response.output_text.delta" and hasattr(event, "delta"):
                        # Send each token as a separate SSE event
                        data = json.dumps({"type": "token", "token": event.delta})
                        yield f"data: {data}\n\n"
                    elif event.type == "response.completed":
                        data = json.dumps({"type": "complete"})
                        yield f"data: {data}\n\n"
            except Exception as e:
                print(colored(f"Error in stream: {e}", "red"))
                error_data = json.dumps({"type": "error", "message": str(e)})
                yield f"data: {error_data}\n\n"
            
            # End the stream
            yield "data: {\"type\":\"end\"}\n\n"
        
        return StreamingResponse(
            generate(),
            media_type="text/event-stream"
        )
    except Exception as e:
        print(colored(f"Error setting up image stream: {e}", "red"))
        raise HTTPException(status_code=500, detail=f"Error: {str(e)}")

@app.post("/chat-with-documents")
async def chat_with_documents(message: DocumentMessage):
    print(colored(f"Received message with documents", "yellow"))
    
    try:
        # Prepare the input for the OpenAI API
        input_data = message.input
        
        # If system message is provided, prepend it to the messages
        if message.systemMessage:
            print(colored(f"System message provided with document request", "cyan"))
            # Insert system message at the beginning of the array
            input_data.insert(0, {
                "role": "system",
                "content": message.systemMessage
            })
        
        # Create a streaming response
        async def generate():
            response = client.responses.create(
                model=MODEL,
                input=input_data,
                stream=True
            )
            
            for event in response:
                if event.type == "response.output_text.delta" and hasattr(event, "delta"):
                    # Send each token as a separate SSE event
                    yield f"data: {json.dumps({'type': 'token', 'token': event.delta})}\n\n"
                elif event.type == "response.completed":
                    yield f"data: {json.dumps({'type': 'complete'})}\n\n"
            
            # End the stream
            yield f"data: {json.dumps({'type': 'end'})}\n\n"
        
        return StreamingResponse(
            generate(),
            media_type="text/event-stream"
        )
    except Exception as e:
        print(colored(f"Error processing document message: {e}", "red"))
        raise HTTPException(status_code=500, detail=f"Error processing document message: {str(e)}")

@app.post("/transcribe-audio")
async def transcribe_audio(file: UploadFile = File(...)):
    """
    Endpoint for streaming audio transcription
    """
    print(colored(f"Received audio file for transcription: {file.filename}", "magenta"))
    
    # Create a temporary file to store the uploaded audio
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(file.filename)[1])
    try:
        # Write the file content
        content = await file.read()
        temp_file.write(content)
        temp_file.close()
        
        async def generate():
            try:
                # Open the file for reading
                with open(temp_file.name, "rb") as audio_file:
                    # Call OpenAI API with streaming enabled
                    stream = client.audio.transcriptions.create(
                        model="gpt-4o-mini-transcribe",
                        file=audio_file,
                        response_format="text",
                        stream=True
                    )
                    
                    current_text = ""
                    
                    # Process each streaming event
                    for event in stream:
                        if hasattr(event, 'type'):
                            print(colored(f"Transcription event: {event.type}", "cyan"))
                            
                            if event.type == "transcript.text.delta":
                                # Stream delta tokens
                                delta_text = getattr(event, 'text', '')
                                current_text += delta_text
                                yield f"data: {json.dumps({'type': 'transcript.text.delta', 'text': delta_text})}\n\n"
                                await asyncio.sleep(0.01)  # Small delay to avoid overwhelming the client
                                
                            elif event.type == "transcript.text.done":
                                # Send the complete transcription
                                full_text = getattr(event, 'text', current_text)
                                print(colored(f"Transcription complete: {full_text[:50]}...", "green"))
                                yield f"data: {json.dumps({'type': 'transcript.text.done', 'text': full_text})}\n\n"
                    
                    # End the stream
                    yield f"data: {json.dumps({'type': 'end'})}\n\n"
                    
            except Exception as e:
                print(colored(f"Error in transcription: {e}", "red"))
                yield f"data: {json.dumps({'type': 'error', 'message': str(e)})}\n\n"
            
            finally:
                # Clean up the temporary file
                if os.path.exists(temp_file.name):
                    os.unlink(temp_file.name)
                    print(colored(f"Temporary file deleted: {temp_file.name}", "yellow"))
                    
        return StreamingResponse(
            generate(),
            media_type="text/event-stream"
        )
                
    except Exception as e:
        # Clean up the temporary file in case of error
        if os.path.exists(temp_file.name):
            os.unlink(temp_file.name)
        print(colored(f"Error processing audio transcription: {e}", "red"))
        raise HTTPException(status_code=500, detail=f"Error processing audio: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:app", host="127.0.0.1", port=8000, reload=True) 