/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #2d3748;
}

::-webkit-scrollbar-thumb {
    background: #4a5568;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #718096;
}

/* Loading animation */
.loading-dots .dot {
    opacity: 0;
    animation: fade 1.5s infinite;
}

.loading-dots .dot:nth-child(2) {
    animation-delay: 0.5s;
}

.loading-dots .dot:nth-child(3) {
    animation-delay: 1s;
}

@keyframes fade {
    0%, 100% { opacity: 0; }
    50% { opacity: 1; }
}

/* Tooltip styling */
[data-tip]:hover:before {
    content: attr(data-tip);
    position: absolute;
    background-color: hsl(var(--n));
    color: hsl(var(--nc));
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    white-space: nowrap;
    z-index: 100;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%) translateY(-5px);
} 