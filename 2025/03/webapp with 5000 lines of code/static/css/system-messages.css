/* System message styles */
.system-message-item {
    padding: 0.5rem;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 0.2s;
    position: relative;
}

.system-message-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.system-message-title {
    font-weight: 500;
    font-size: 0.875rem;
}

.system-message-preview {
    position: absolute;
    top: 0;
    left: 100%;
    width: 250px;
    background-color: hsl(var(--b2));
    border: 1px solid hsl(var(--b3));
    border-radius: 0.5rem;
    padding: 0.75rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    z-index: 20;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.2s, visibility 0.2s;
    pointer-events: none;
    font-size: 0.75rem;
    max-height: 200px;
    overflow-y: auto;
}

.system-message-item:hover .system-message-preview {
    opacity: 1;
    visibility: visible;
}

.system-message-active {
    background-color: rgba(var(--p), 0.2);
    border-left: 2px solid hsl(var(--p));
}

.system-message-controls {
    display: none;
    margin-left: 0.5rem;
}

.system-message-item:hover .system-message-controls {
    display: flex;
}

.active-system-message-indicator {
    display: inline-block;
    width: 6px;
    height: 6px;
    background-color: hsl(var(--su));
    border-radius: 50%;
    margin-right: 0.25rem;
}

/* Clear system message button styling */
#clear-system-message {
    padding: 0.5rem;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 0.875rem;
    text-align: center;
    background-color: rgba(255, 255, 255, 0.05);
}

#clear-system-message:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

#clear-system-message.system-message-active {
    background-color: rgba(var(--p), 0.2);
    border-left: 2px solid hsl(var(--p));
} 