/* Conversation sidebar styles */
.conversation-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.5rem;
    border-radius: 0.375rem;
    transition: all 0.2s;
    cursor: pointer;
}

.conversation-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.conversation-item.active {
    background-color: rgba(var(--p), 0.2);
    border-left: 3px solid hsl(var(--p));
}

.conversation-title {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex-grow: 1;
}

.delete-conversation {
    opacity: 0;
    transition: opacity 0.2s;
}

.conversation-item:hover .delete-conversation {
    opacity: 1;
} 