/* Markdown styling for responses */
.markdown {
    white-space: pre-wrap;
}

.markdown code {
    background-color: rgba(0, 0, 0, 0.2);
    padding: 0.2em 0.4em;
    border-radius: 3px;
    font-family: monospace;
}

.markdown pre {
    background-color: rgba(0, 0, 0, 0.2);
    padding: 1em;
    border-radius: 5px;
    overflow-x: auto;
}

/* Streaming message styling */
.streaming-message {
    white-space: pre-wrap;
}

/* Cursor animation for streaming */
.streaming-message::after {
    content: "|";
    display: inline-block;
    animation: cursor-blink 1s step-end infinite;
}

@keyframes cursor-blink {
    from, to { opacity: 0; }
    50% { opacity: 1; }
}

/* When streaming is complete, remove cursor */
.streaming-complete::after {
    content: none;
} 