/* Document upload styles */
.document-preview {
    position: relative;
    width: 100%;
    padding: 0.75rem;
    border-radius: 0.375rem;
    background-color: rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.document-preview .document-icon {
    width: 24px;
    height: 24px;
    flex-shrink: 0;
}

.document-preview .document-info {
    flex-grow: 1;
    overflow: hidden;
}

.document-preview .document-name {
    font-size: 0.8rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.document-preview .document-size {
    font-size: 0.7rem;
    opacity: 0.7;
}

.document-preview .remove-document {
    opacity: 0;
    transition: opacity 0.2s;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    flex-shrink: 0;
}

.document-preview:hover .remove-document {
    opacity: 1;
}

/* Document in message styles */
.chat-document-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-top: 8px;
    width: 100%;
}

.chat-document {
    padding: 0.75rem;
    border-radius: 8px;
    background-color: rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s;
}

.chat-document:hover {
    background-color: rgba(0, 0, 0, 0.2);
}

.chat-document .document-icon {
    width: 24px;
    height: 24px;
}

.chat-document .document-name {
    font-size: 0.9rem;
    flex-grow: 1;
}

/* Document paste indicator */
.document-paste-indicator {
    position: absolute;
    bottom: 8px;
    right: 40px;
    background-color: hsl(var(--wa));
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    display: flex;
    align-items: center;
    gap: 4px;
    transition: all 0.3s;
    opacity: 0;
    transform: translateY(10px);
    pointer-events: none;
}

.document-paste-indicator.show {
    opacity: 1;
    transform: translateY(0);
}

/* Attachment indicator */
textarea.has-attachments {
    border-color: hsl(var(--p));
    box-shadow: 0 0 0 1px hsla(var(--p), 0.5);
}

/* Add these styles to document-upload.css */
.file-attachments-indicator {
    display: flex;
    gap: 8px;
    align-items: center;
}

.file-attachments-indicator span {
    display: flex;
    align-items: center;
    gap: 4px;
}

.file-attachments-indicator svg {
    width: 14px;
    height: 14px;
}

.file-attachments-indicator.show {
    display: flex;
}

/* New chat notification */
.new-chat-notification {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%) translateY(-100px);
    background-color: hsl(var(--p));
    color: white;
    padding: 10px 20px;
    border-radius: 8px;
    font-size: 0.9rem;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    opacity: 0;
    transition: all 0.3s ease;
    z-index: 1000;
}

.new-chat-notification.show {
    transform: translateX(-50%) translateY(0);
    opacity: 1;
} 