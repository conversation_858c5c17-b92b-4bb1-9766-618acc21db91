/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #2d3748;
}

::-webkit-scrollbar-thumb {
    background: #4a5568;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #718096;
}

/* Loading animation */
.loading-dots .dot {
    opacity: 0;
    animation: fade 1.5s infinite;
}

.loading-dots .dot:nth-child(2) {
    animation-delay: 0.5s;
}

.loading-dots .dot:nth-child(3) {
    animation-delay: 1s;
}

@keyframes fade {
    0%, 100% { opacity: 0; }
    50% { opacity: 1; }
}

/* Markdown styling for responses */
.markdown {
    white-space: pre-wrap;
}

.markdown code {
    background-color: rgba(0, 0, 0, 0.2);
    padding: 0.2em 0.4em;
    border-radius: 3px;
    font-family: monospace;
}

.markdown pre {
    background-color: rgba(0, 0, 0, 0.2);
    padding: 1em;
    border-radius: 5px;
    overflow-x: auto;
}

/* Streaming message styling */
.streaming-message {
    white-space: pre-wrap;
}

/* Cursor animation for streaming */
.streaming-message::after {
    content: "|";
    display: inline-block;
    animation: cursor-blink 1s step-end infinite;
}

@keyframes cursor-blink {
    from, to { opacity: 0; }
    50% { opacity: 1; }
}

/* When streaming is complete, remove cursor */
.streaming-complete::after {
    content: none;
}

/* Conversation sidebar styles */
.conversation-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.5rem;
    border-radius: 0.375rem;
    transition: all 0.2s;
    cursor: pointer;
}

.conversation-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.conversation-item.active {
    background-color: rgba(var(--p), 0.2);
    border-left: 3px solid hsl(var(--p));
}

.conversation-title {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex-grow: 1;
}

.delete-conversation {
    opacity: 0;
    transition: opacity 0.2s;
}

.conversation-item:hover .delete-conversation {
    opacity: 1;
}

/* Mobile responsive sidebar */
@media (max-width: 1024px) {
    .conversation-sidebar {
        position: fixed;
        left: 0;
        top: 0;
        bottom: 0;
        z-index: 50;
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .conversation-sidebar.open {
        transform: translateX(0);
    }
    
    .sidebar-backdrop {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 40;
        display: none;
    }
    
    .sidebar-backdrop.open {
        display: block;
    }
}

/* System message styles */
.system-message-item {
    padding: 0.5rem;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 0.2s;
    position: relative;
}

.system-message-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.system-message-title {
    font-weight: 500;
    font-size: 0.875rem;
}

.system-message-preview {
    position: absolute;
    top: 0;
    left: 100%;
    width: 250px;
    background-color: hsl(var(--b2));
    border: 1px solid hsl(var(--b3));
    border-radius: 0.5rem;
    padding: 0.75rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    z-index: 20;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.2s, visibility 0.2s;
    pointer-events: none;
    font-size: 0.75rem;
    max-height: 200px;
    overflow-y: auto;
}

.system-message-item:hover .system-message-preview {
    opacity: 1;
    visibility: visible;
}

.system-message-active {
    background-color: rgba(var(--p), 0.2);
    border-left: 2px solid hsl(var(--p));
}

.system-message-controls {
    display: none;
    margin-left: 0.5rem;
}

.system-message-item:hover .system-message-controls {
    display: flex;
}

.active-system-message-indicator {
    display: inline-block;
    width: 6px;
    height: 6px;
    background-color: hsl(var(--su));
    border-radius: 50%;
    margin-right: 0.25rem;
}

/* Clear system message button styling */
#clear-system-message {
    padding: 0.5rem;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 0.875rem;
    text-align: center;
    background-color: rgba(255, 255, 255, 0.05);
}

#clear-system-message:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

#clear-system-message.system-message-active {
    background-color: rgba(var(--p), 0.2);
    border-left: 2px solid hsl(var(--p));
}

/* Tooltip styling */
[data-tip]:hover:before {
    content: attr(data-tip);
    position: absolute;
    background-color: hsl(var(--n));
    color: hsl(var(--nc));
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    white-space: nowrap;
    z-index: 100;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%) translateY(-5px);
} 