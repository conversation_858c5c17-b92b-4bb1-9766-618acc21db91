/* Audio upload styles */
.audio-preview {
    position: relative;
    width: 100%;
    padding: 0.75rem;
    border-radius: 0.375rem;
    background-color: rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.audio-preview .audio-icon {
    width: 24px;
    height: 24px;
    color: hsl(var(--er));
    flex-shrink: 0;
}

.audio-preview .audio-info {
    flex-grow: 1;
    overflow: hidden;
}

.audio-preview .audio-name {
    font-size: 0.8rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.audio-preview .audio-size {
    font-size: 0.7rem;
    opacity: 0.7;
}

.audio-preview .remove-audio {
    opacity: 0;
    transition: opacity 0.2s;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    flex-shrink: 0;
}

.audio-preview:hover .remove-audio {
    opacity: 1;
}

/* Audio transcription styles */
.transcription-container {
    position: relative;
    margin-top: 4px;
}

.transcription-container .transcription-label {
    font-size: 0.75rem;
    opacity: 0.7;
    margin-bottom: 4px;
}

.transcription-content {
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 0.375rem;
    padding: 8px;
    font-size: 0.9rem;
    white-space: pre-wrap;
}

.transcription-streaming::after {
    content: "|";
    display: inline-block;
    animation: cursor-blink 1s step-end infinite;
}

@keyframes cursor-blink {
    from, to { opacity: 0; }
    50% { opacity: 1; }
}

/* Audio paste indicator */
.audio-paste-indicator {
    position: absolute;
    bottom: 8px;
    right: 40px;
    background-color: hsl(var(--er));
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    display: flex;
    align-items: center;
    gap: 4px;
    transition: all 0.3s;
    opacity: 0;
    transform: translateY(10px);
    pointer-events: none;
}

.audio-paste-indicator.show {
    opacity: 1;
    transform: translateY(0);
} 