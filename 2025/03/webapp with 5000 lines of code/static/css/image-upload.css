/* Image upload styles */
.image-preview {
    position: relative;
    width: 100%;
    height: 80px;
    border-radius: 0.375rem;
    overflow: hidden;
    background-color: rgba(0, 0, 0, 0.2);
}

.image-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.image-preview .remove-image {
    position: absolute;
    top: 4px;
    right: 4px;
    opacity: 0;
    transition: opacity 0.2s;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

.image-preview:hover .remove-image {
    opacity: 1;
}

/* Update chat image container styles */
.chat-image-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 8px;
    margin-top: 8px;
    width: 100%;
    max-width: 400px;
}

.chat-image {
    width: 100%;
    height: 120px;
    object-fit: cover;
    border-radius: 8px;
    cursor: pointer;
    transition: transform 0.2s;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.chat-image:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Pasted image indicator */
.image-paste-indicator {
    position: absolute;
    bottom: 8px;
    right: 40px;
    background-color: hsl(var(--p));
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    display: flex;
    align-items: center;
    gap: 4px;
    transition: all 0.3s;
    opacity: 0;
    transform: translateY(10px);
    pointer-events: none;
}

.image-paste-indicator.show {
    opacity: 1;
    transform: translateY(0);
}

/* Image modal */
.image-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s;
}

.image-modal.open {
    opacity: 1;
    pointer-events: auto;
}

.image-modal-content {
    max-width: 90%;
    max-height: 90%;
}

.image-modal .close-button {
    position: absolute;
    top: 20px;
    right: 20px;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

/* Improved image preview container */
#image-preview-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(70px, 1fr));
    gap: 8px;
    width: 100%;
}

.image-preview {
    position: relative;
    width: 100%;
    height: 70px;
    border-radius: 0.375rem;
    overflow: hidden;
    background-color: rgba(0, 0, 0, 0.2);
} 