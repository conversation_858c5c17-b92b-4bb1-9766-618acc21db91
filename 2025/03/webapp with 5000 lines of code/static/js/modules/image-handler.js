/**
 * Image Handler for processing and displaying images
 */
class ImageHandler {
    constructor() {
        this.uploadedImages = [];
        this.maxImages = 5; // Maximum number of images per message
    }

    /**
     * Reset uploaded images array
     */
    clearImages() {
        this.uploadedImages = [];
    }

    /**
     * Process a file to convert it to base64
     * @param {File} file - Image file
     * @returns {Promise} - Promise resolving to base64 string
     */
    processFile(file) {
        return new Promise((resolve, reject) => {
            // Validate file is an image
            if (!file.type.startsWith('image/')) {
                reject(new Error('File must be an image'));
                return;
            }

            const reader = new FileReader();
            reader.onload = (e) => {
                const base64String = e.target.result;
                resolve({
                    name: file.name,
                    type: file.type,
                    size: file.size,
                    data: base64String
                });
            };
            reader.onerror = () => reject(new Error('Error reading file'));
            reader.readAsDataURL(file);
        });
    }

    /**
     * Add an image to the upload queue
     * @param {Object} imageData - Image data object
     * @returns {boolean} - True if image was added
     */
    addImage(imageData) {
        if (this.uploadedImages.length >= this.maxImages) {
            return false;
        }
        
        this.uploadedImages.push(imageData);
        return true;
    }

    /**
     * Remove an image from the upload queue
     * @param {number} index - Index of the image to remove
     */
    removeImage(index) {
        if (index >= 0 && index < this.uploadedImages.length) {
            this.uploadedImages.splice(index, 1);
        }
    }

    /**
     * Get all uploaded images
     * @returns {Array} - Array of image data objects
     */
    getImages() {
        return this.uploadedImages;
    }

    /**
     * Check if any images are uploaded
     * @returns {boolean} - True if images are present
     */
    hasImages() {
        return this.uploadedImages.length > 0;
    }

    /**
     * Format images for OpenAI API
     * @param {string} textMessage - Text message to accompany images
     * @returns {Array} - Formatted input for OpenAI API
     */
    formatImagesForAPI(textMessage) {
        const content = [
            { type: "input_text", text: textMessage }
        ];
        
        // Add each image to the content array
        this.uploadedImages.forEach(img => {
            content.push({
                type: "input_image",
                image_url: img.data
            });
        });
        
        return [
            {
                role: "user",
                content: content
            }
        ];
    }
}

export default ImageHandler; 