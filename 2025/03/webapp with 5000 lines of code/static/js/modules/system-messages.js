import { saveSystemMessages, loadSystemMessages } from './storage.js';

/**
 * System Message Manager for handling custom system messages
 */
class SystemMessageManager {
    constructor(renderCallback, updateIndicatorCallback) {
        this.renderCallback = renderCallback;
        this.updateIndicatorCallback = updateIndicatorCallback;
        this.systemMessages = [];
        this.activeSystemMessageId = null;
        this.editingSystemMessageId = null;
        
        // DOM Elements for System Message Modal
        this.modal = document.getElementById('system-message-modal');
        this.titleInput = document.getElementById('system-message-title');
        this.contentInput = document.getElementById('system-message-content');
    }

    /**
     * Initialize system messages from storage
     */
    initialize() {
        try {
            this.systemMessages = loadSystemMessages();
            
            // Start with no active system message
            this.activeSystemMessageId = null;
            
            this.render();
        } catch (error) {
            console.error('Error initializing system messages:', error);
            this.systemMessages = [];
        }
    }

    /**
     * Save system messages to storage
     */
    save() {
        saveSystemMessages(this.systemMessages);
    }

    /**
     * Render the system message list
     */
    render() {
        if (this.renderCallback) {
            this.renderCallback(this.systemMessages, this.activeSystemMessageId);
        }
        
        if (this.updateIndicatorCallback) {
            this.updateIndicatorCallback(!!this.activeSystemMessageId);
        }
    }

    /**
     * Create a new system message
     * @param {string} title - Message title
     * @param {string} content - Message content
     * @returns {string} - The ID of the new message
     */
    createSystemMessage(title, content) {
        const id = 'sysmsg-' + Date.now();
        
        const newMessage = {
            id: id,
            title: title,
            content: content,
            timestamp: Date.now()
        };
        
        this.systemMessages.push(newMessage);
        this.save();
        this.render();
        
        return id;
    }

    /**
     * Update an existing system message
     * @param {string} id - Message ID
     * @param {string} title - New title
     * @param {string} content - New content
     */
    updateSystemMessage(id, title, content) {
        const index = this.systemMessages.findIndex(msg => msg.id === id);
        
        if (index !== -1) {
            this.systemMessages[index].title = title;
            this.systemMessages[index].content = content;
            this.systemMessages[index].timestamp = Date.now();
            
            this.save();
            this.render();
        }
    }

    /**
     * Delete a system message
     * @param {string} id - Message ID
     */
    deleteSystemMessage(id) {
        const index = this.systemMessages.findIndex(msg => msg.id === id);
        
        if (index !== -1) {
            this.systemMessages.splice(index, 1);
            
            if (this.activeSystemMessageId === id) {
                this.activeSystemMessageId = null;
            }
            
            this.save();
            this.render();
        }
    }

    /**
     * Set the active system message
     * @param {string|null} id - Message ID or null to clear
     */
    setActiveSystemMessage(id) {
        this.activeSystemMessageId = id;
        this.render();
    }

    /**
     * Clear the active system message
     */
    clearActiveSystemMessage() {
        this.activeSystemMessageId = null;
        this.render();
    }

    /**
     * Get the content of the active system message
     * @returns {string|null} - Message content or null
     */
    getActiveSystemMessageContent() {
        if (!this.activeSystemMessageId) return null;
        
        const message = this.systemMessages.find(msg => msg.id === this.activeSystemMessageId);
        return message ? message.content : null;
    }

    /**
     * Open the modal to edit a system message
     * @param {string} id - Message ID
     */
    editSystemMessage(id) {
        const message = this.systemMessages.find(msg => msg.id === id);
        
        if (message) {
            this.titleInput.value = message.title;
            this.contentInput.value = message.content;
            this.editingSystemMessageId = id;
            
            this.modal.showModal();
        }
    }

    /**
     * Open the modal to create a new system message
     */
    openCreateSystemMessageModal() {
        this.titleInput.value = '';
        this.contentInput.value = '';
        this.editingSystemMessageId = null;
        
        this.modal.showModal();
    }

    /**
     * Save the system message from the modal
     */
    saveSystemMessageFromModal() {
        const title = this.titleInput.value.trim();
        const content = this.contentInput.value.trim();
        
        if (!title || !content) {
            alert('Please provide both a title and content for the system message.');
            return;
        }
        
        if (this.editingSystemMessageId) {
            this.updateSystemMessage(this.editingSystemMessageId, title, content);
        } else {
            const newId = this.createSystemMessage(title, content);
            this.setActiveSystemMessage(newId);
        }
        
        this.modal.close();
    }
}

export default SystemMessageManager; 