/**
 * Document Handler for processing and displaying document files
 */
class DocumentHandler {
    constructor() {
        this.uploadedDocuments = [];
        this.maxDocuments = 3; // Maximum number of documents per message
    }

    /**
     * Reset uploaded documents array
     */
    clearDocuments() {
        this.uploadedDocuments = [];
    }

    /**
     * Process a file to convert it to base64 if needed
     * @param {File} file - Document file
     * @returns {Promise} - Promise resolving to document data object
     */
    processFile(file) {
        return new Promise((resolve, reject) => {
            // Validate file is a supported document type
            if (!this.isSupportedDocumentType(file.type)) {
                reject(new Error('Unsupported file type. Please upload PDF or text files only.'));
                return;
            }

            const reader = new FileReader();
            
            // Handle different types of files
            if (file.type === 'application/pdf') {
                // For PDFs, we need the binary data as base64
                reader.onload = (e) => {
                    const base64String = e.target.result;
                    resolve({
                        name: file.name,
                        type: file.type,
                        size: file.size,
                        data: base64String,
                        isPdf: true
                    });
                };
                reader.readAsDataURL(file);
            } else {
                // For text files, we can read as text
                reader.onload = (e) => {
                    const textContent = e.target.result;
                    resolve({
                        name: file.name,
                        type: file.type,
                        size: file.size,
                        data: textContent,
                        isPdf: false
                    });
                };
                reader.readAsText(file);
            }
            
            reader.onerror = () => reject(new Error('Error reading file'));
        });
    }

    /**
     * Check if file type is supported
     * @param {string} fileType - MIME type of the file
     * @returns {boolean} - True if the file type is supported
     */
    isSupportedDocumentType(fileType) {
        return fileType === 'application/pdf' || 
               fileType === 'text/plain' ||
               fileType.includes('text');
    }

    /**
     * Add a document to the upload queue
     * @param {Object} documentData - Document data object
     * @returns {boolean} - True if document was added
     */
    addDocument(documentData) {
        if (this.uploadedDocuments.length >= this.maxDocuments) {
            return false;
        }
        
        this.uploadedDocuments.push(documentData);
        return true;
    }

    /**
     * Remove a document from the upload queue
     * @param {number} index - Index of the document to remove
     */
    removeDocument(index) {
        if (index >= 0 && index < this.uploadedDocuments.length) {
            this.uploadedDocuments.splice(index, 1);
        }
    }

    /**
     * Get all uploaded documents
     * @returns {Array} - Array of document data objects
     */
    getDocuments() {
        return this.uploadedDocuments;
    }

    /**
     * Check if any documents are uploaded
     * @returns {boolean} - True if documents are present
     */
    hasDocuments() {
        return this.uploadedDocuments.length > 0;
    }

    /**
     * Format documents for OpenAI API
     * @param {string} textMessage - Text message to accompany documents
     * @returns {Array} - Formatted input for OpenAI API
     */
    formatDocumentsForAPI(textMessage) {
        const content = [];
        
        // Add each document to the content array
        this.uploadedDocuments.forEach(doc => {
            if (doc.isPdf) {
                content.push({
                    type: "input_file",
                    filename: doc.name,
                    file_data: doc.data
                });
            } else {
                // For text files, we include the content in the message
                const textContent = `Content from ${doc.name}:\n\n${doc.data}`;
                content.push({
                    type: "input_text",
                    text: textContent
                });
            }
        });
        
        // Add the user's message last
        content.push({
            type: "input_text",
            text: textMessage
        });
        
        return [
            {
                role: "user",
                content: content
            }
        ];
    }
}

export default DocumentHandler; 