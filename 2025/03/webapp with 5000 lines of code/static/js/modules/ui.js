/**
 * UI manager for animations and visual elements
 */
class UIManager {
    constructor() {
        this.loadingAnimation = document.getElementById('loading-animation');
        this.sidebar = document.getElementById('sidebar');
        this.systemMessageIndicator = document.getElementById('system-message-indicator');
    }

    /**
     * Show loading animation
     */
    showLoading() {
        this.loadingAnimation.classList.remove('hidden');
        anime({
            targets: '.loading-dots .dot',
            scale: [0.5, 1],
            opacity: [0, 1, 0],
            easing: 'easeInOutSine',
            duration: 1000,
            loop: true
        });
    }

    /**
     * Hide loading animation
     */
    hideLoading() {
        this.loadingAnimation.classList.add('hidden');
        anime.remove('.loading-dots .dot');
    }

    /**
     * Toggle sidebar for mobile view
     */
    toggleSidebar() {
        this.sidebar.classList.toggle('open');
        
        // Create or remove backdrop
        let backdrop = document.querySelector('.sidebar-backdrop');
        if (!backdrop && this.sidebar.classList.contains('open')) {
            backdrop = document.createElement('div');
            backdrop.className = 'sidebar-backdrop open';
            document.body.appendChild(backdrop);
            
            // Close sidebar when backdrop is clicked
            backdrop.addEventListener('click', () => this.toggleSidebar());
        } else if (backdrop) {
            backdrop.classList.toggle('open');
            if (!backdrop.classList.contains('open')) {
                setTimeout(() => {
                    backdrop.remove();
                }, 300);
            }
        }
    }
    
    /**
     * Update system message indicator visibility
     * @param {boolean} active - Whether a system message is active
     */
    updateSystemMessageIndicator(active) {
        if (active) {
            this.systemMessageIndicator.classList.remove('hidden');
        } else {
            this.systemMessageIndicator.classList.add('hidden');
        }
    }
    
    /**
     * Scroll chat container to bottom
     * @param {HTMLElement} container - Chat container element
     */
    scrollToBottom(container) {
        container.scrollTop = container.scrollHeight;
    }
}

export default UIManager; 