/**
 * WebSocket connection manager
 */
class WebSocketManager {
    constructor(onMessage, onError) {
        this.socket = null;
        this.isReady = false;
        this.onMessage = onMessage;
        this.onError = onError;
    }

    /**
     * Initialize WebSocket connection
     */
    initialize() {
        this.socket = new WebSocket(`ws://${window.location.host}/ws`);
        
        this.socket.onopen = () => {
            console.log('WebSocket connection established');
            this.isReady = true;
        };
        
        this.socket.onmessage = (event) => {
            const data = JSON.parse(event.data);
            if (this.onMessage) {
                this.onMessage(data);
            }
        };
        
        this.socket.onerror = (error) => {
            console.error('WebSocket error:', error);
            this.isReady = false;
            if (this.onError) {
                this.onError(error);
            }
        };
        
        this.socket.onclose = () => {
            console.log('WebSocket connection closed');
            this.isReady = false;
        };
    }

    /**
     * Send a message through the WebSocket
     * @param {string} message - Message to send
     * @param {string|null} systemMessage - System message
     * @param {Array} history - Conversation history
     */
    sendMessage(message, systemMessage = null, history = []) {
        if (!this.socket || this.socket.readyState !== WebSocket.OPEN) {
            console.error('WebSocket is not open');
            return;
        }
        
        const payload = {
            message: message,
        };
        
        if (systemMessage) {
            payload.systemMessage = systemMessage;
        }
        
        if (history && history.length > 0) {
            payload.history = history;
        }
        
        this.socket.send(JSON.stringify(payload));
    }

    /**
     * Check if WebSocket is ready
     * @returns {boolean} - True if ready
     */
    isSocketReady() {
        return this.isReady;
    }
}

export default WebSocketManager; 