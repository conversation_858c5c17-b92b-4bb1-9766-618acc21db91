import { saveConversations, loadConversations } from './storage.js';

/**
 * Conversation Manager for handling chat conversations
 */
class ConversationManager {
    constructor(message<PERSON>and<PERSON>, renderCallback) {
        this.messageHandler = messageHandler;
        this.renderCallback = renderCallback;
        this.conversations = [];
        this.activeConversationId = null;
    }

    /**
     * Initialize conversations from storage
     */
    initialize() {
        try {
            // First, load all past conversations from storage
            const data = loadConversations();
            if (data && data.conversations && data.conversations.length > 0) {
                // Load all existing conversations
                this.conversations = data.conversations;
                
                // If there was an active conversation saved, load it
                if (data.activeConversationId && this.conversations.find(c => c.id === data.activeConversationId)) {
                    this.activeConversationId = data.activeConversationId;
                    
                    // Load the active conversation messages
                    const activeConv = this.conversations.find(c => c.id === this.activeConversationId);
                    this.loadConversationMessages(activeConv);
                } else {
                    // If no active conversation was saved or it can't be found, create a new one
                    this.createNewConversation();
                }
            } else {
                // If no conversations exist at all, create a new one
                this.createNewConversation();
            }
            
            // Render the conversation list
            if (this.renderCallback) {
                this.renderCallback(this.conversations, this.activeConversationId);
            }
        } catch (error) {
            console.error('Error initializing conversations:', error);
            // If there's an error, ensure we have a new conversation
            if (!this.activeConversationId) {
                this.createNewConversation();
            }
        }
    }

    /**
     * Save conversations to storage
     */
    save() {
        saveConversations(this.conversations, this.activeConversationId);
    }

    /**
     * Create a new conversation
     * @param {string|null} systemMessageId - ID of system message to associate
     */
    createNewConversation(systemMessageId = null) {
        // Generate a unique ID
        const id = 'conv-' + Date.now();
        
        // Create a new conversation object
        const newConversation = {
            id: id,
            title: 'New conversation',
            timestamp: Date.now(),
            messages: [],
            systemMessageId: systemMessageId
        };
        
        // Add to the list of conversations
        this.conversations.unshift(newConversation);
        
        // Set as the active conversation
        this.activeConversationId = id;
        
        // Save the updated conversations
        this.save();
        
        // Clear the chat display
        this.messageHandler.clearMessages(true);
        
        // Render the conversation list
        if (this.renderCallback) {
            this.renderCallback(this.conversations, this.activeConversationId);
        }
        
        return id;
    }

    /**
     * Load a conversation
     * @param {string} id - Conversation ID
     * @returns {Object|null} - The loaded conversation or null if not found
     */
    loadConversation(id) {
        const conversation = this.conversations.find(conv => conv.id === id);
        
        if (!conversation) {
            console.error('Conversation not found:', id);
            return null;
        }
        
        // Set as the active conversation
        this.activeConversationId = id;
        
        // Clear the chat display
        this.messageHandler.clearMessages(false);
        
        // Add the welcome message if there are no messages
        if (conversation.messages.length === 0) {
            this.messageHandler.clearMessages(true);
        } else {
            // Add all messages to the chat display
            conversation.messages.forEach(msg => {
                this.messageHandler.addMessageToDisplay(msg.content, msg.role);
            });
        }
        
        // Save the updated active conversation
        this.save();
        
        // Render the conversation list
        if (this.renderCallback) {
            this.renderCallback(this.conversations, this.activeConversationId);
        }
        
        return conversation;
    }

    /**
     * Delete a conversation
     * @param {string} id - Conversation ID
     */
    deleteConversation(id) {
        // Find the index of the conversation
        const index = this.conversations.findIndex(conv => conv.id === id);
        
        if (index === -1) {
            console.error('Conversation not found:', id);
            return;
        }
        
        // Remove the conversation
        this.conversations.splice(index, 1);
        
        // If the deleted conversation was the active one, load another one
        if (id === this.activeConversationId) {
            if (this.conversations.length > 0) {
                this.loadConversation(this.conversations[0].id);
            } else {
                this.createNewConversation();
            }
        } else {
            // Just update the conversation list
            if (this.renderCallback) {
                this.renderCallback(this.conversations, this.activeConversationId);
            }
        }
        
        // Save the updated conversations
        this.save();
    }

    /**
     * Update the current conversation with the latest messages
     * @param {boolean} force - Force updating conversation history
     */
    updateCurrentConversation(force = false) {
        if (!this.activeConversationId) return;
        
        // Get all messages from the DOM
        const messages = document.querySelectorAll('.chat-message');
        const conversationData = [];
        
        messages.forEach(msg => {
            const role = msg.getAttribute('data-role');
            const content = msg.querySelector('.chat-bubble').textContent.trim();
            
            // Skip empty messages
            if (content) {
                conversationData.push({ role, content });
            }
        });
        
        // Get the current conversation
        const conversation = this.conversations.find(conv => conv.id === this.activeConversationId);
        if (!conversation) return;
        
        // Only update if the content has changed or force is true
        if (force || JSON.stringify(conversation.messages) !== JSON.stringify(conversationData)) {
            conversation.messages = conversationData;
            
            // Only update timestamp and save if this is a real update
            if (conversationData.length > 0) {
                conversation.timestamp = new Date().toISOString();
                this.save();
            }
        }
    }

    /**
     * Update the title of a new conversation based on the first message
     */
    maybeUpdateConversationTitle() {
        if (!this.activeConversationId) return;
        
        const conversation = this.conversations.find(conv => conv.id === this.activeConversationId);
        if (!conversation) return;
        
        // Only update if it's a new conversation
        if (conversation.title === 'New conversation' && conversation.messages.length >= 2) {
            const userMessage = conversation.messages.find(msg => msg.role === 'user');
            if (userMessage) {
                // Use the first 30 characters of the user message as the title
                const title = userMessage.content.slice(0, 30) + (userMessage.content.length > 30 ? '...' : '');
                conversation.title = title;
                
                // Save and update the list
                this.save();
                if (this.renderCallback) {
                    this.renderCallback(this.conversations, this.activeConversationId);
                }
            }
        }
    }

    /**
     * Set the system message for the active conversation
     * @param {string|null} systemMessageId - System message ID or null to clear
     */
    setActiveConversationSystemMessage(systemMessageId) {
        if (!this.activeConversationId) return;
        
        const conversation = this.conversations.find(conv => conv.id === this.activeConversationId);
        if (conversation) {
            conversation.systemMessageId = systemMessageId;
            this.save();
        }
    }

    /**
     * Get the active conversation
     * @returns {Object|null} - The active conversation or null
     */
    getActiveConversation() {
        if (!this.activeConversationId) return null;
        return this.conversations.find(conv => conv.id === this.activeConversationId);
    }

    /**
     * Get the system message ID for the active conversation
     * @returns {string|null} - System message ID or null
     */
    getActiveConversationSystemMessageId() {
        const conversation = this.getActiveConversation();
        return conversation ? conversation.systemMessageId : null;
    }

    /**
     * Load conversation messages into the chat display without changing activeConversationId
     * @param {Object} conversation - Conversation object
     */
    loadConversationMessages(conversation) {
        // Clear the chat display
        this.messageHandler.clearMessages(false);
        
        // Add the welcome message if there are no messages
        if (!conversation.messages || conversation.messages.length === 0) {
            this.messageHandler.clearMessages(true);
        } else {
            // Add all messages to the chat display
            conversation.messages.forEach(msg => {
                this.messageHandler.addMessageToDisplay(msg.content, msg.role);
            });
        }
    }
}

export default ConversationManager; 