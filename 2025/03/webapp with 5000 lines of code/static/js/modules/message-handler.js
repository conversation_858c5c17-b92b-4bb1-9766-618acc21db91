/**
 * Message Handler for creating and displaying messages
 */
class MessageHandler {
    constructor(chatContainer, updateConversation) {
        this.chatContainer = chatContainer;
        this.currentStreamingMessage = null;
        this.updateConversation = updateConversation;
    }

    /**
     * Create a new streaming message container
     * @returns {HTMLElement} - The message bubble element
     */
    createStreamingMessage() {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'chat chat-start';
        
        const messageBubble = document.createElement('div');
        messageBubble.className = 'chat-bubble chat-bubble-primary markdown streaming-message';
        messageDiv.appendChild(messageBubble);
        
        // Add to chat container
        this.chatContainer.appendChild(messageDiv);
        
        // Set as current streaming message
        this.currentStreamingMessage = messageBubble;
        
        return messageBubble;
    }

    /**
     * Append token to streaming message
     * @param {string} token - Token to append
     */
    appendToStreamingMessage(token) {
        if (!this.currentStreamingMessage) {
            this.currentStreamingMessage = this.createStreamingMessage();
        }
        this.currentStreamingMessage.textContent += token;
        
        // Auto scroll to bottom
        this.chatContainer.scrollTop = this.chatContainer.scrollHeight;
    }

    /**
     * Mark streaming as complete
     */
    completeStreamingMessage() {
        if (this.currentStreamingMessage) {
            this.currentStreamingMessage.classList.add('streaming-complete');
            this.currentStreamingMessage = null;
        }
    }

    /**
     * Add a message to the chat display only (doesn't update the conversation)
     * @param {string} message - Message content
     * @param {string} sender - Message sender ('user', 'assistant', or 'system')
     */
    addMessageToDisplay(message, sender) {
        const messageDiv = document.createElement('div');
        messageDiv.className = sender === 'user' 
            ? 'chat chat-end' 
            : sender === 'assistant' 
                ? 'chat chat-start' 
                : 'chat chat-start';
        
        const messageBubble = document.createElement('div');
        messageBubble.className = sender === 'user' 
            ? 'chat-bubble chat-bubble-secondary' 
            : sender === 'assistant' 
                ? 'chat-bubble chat-bubble-primary markdown' 
                : 'chat-bubble chat-bubble-accent';
        
        messageBubble.textContent = message;
        messageDiv.appendChild(messageBubble);
        
        this.chatContainer.appendChild(messageDiv);
        
        // Auto scroll to bottom
        this.chatContainer.scrollTop = this.chatContainer.scrollHeight;
    }

    /**
     * Add a message to the chat and update the conversation
     * @param {string} message - Message content
     * @param {string} sender - Message sender ('user', 'assistant', or 'system')
     */
    addMessage(message, sender) {
        this.addMessageToDisplay(message, sender);
        if (this.updateConversation) {
            this.updateConversation();
        }
    }

    /**
     * Clear all messages from the chat container
     * @param {boolean} addWelcomeMessage - Whether to add welcome message
     */
    clearMessages(addWelcomeMessage = true) {
        this.chatContainer.innerHTML = '';
        
        if (addWelcomeMessage) {
            this.chatContainer.innerHTML = `
                <div class="chat chat-start">
                    <div class="chat-bubble chat-bubble-primary">
                        Hello! I'm your AI assistant. How can I help you today?
                    </div>
                </div>
            `;
        }
    }

    /**
     * Extract all messages from the chat display
     * @returns {Array} - Array of message objects
     */
    extractMessagesFromDisplay() {
        const messages = [];
        const chatBubbles = this.chatContainer.querySelectorAll('.chat');
        
        chatBubbles.forEach(bubble => {
            const isUser = bubble.classList.contains('chat-end');
            const content = bubble.querySelector('.chat-bubble').textContent;
            
            messages.push({
                role: isUser ? 'user' : 'assistant',
                content: content
            });
        });
        
        return messages;
    }

    /**
     * Add a message with images to the chat display
     * @param {string} message - Message content
     * @param {Array} images - Array of image data
     * @param {string} sender - Message sender ('user' or 'assistant')
     */
    addMessageWithImages(message, images, sender) {
        const messageDiv = document.createElement('div');
        messageDiv.className = sender === 'user' 
            ? 'chat chat-end' 
            : 'chat chat-start';
        
        const messageBubble = document.createElement('div');
        messageBubble.className = sender === 'user' 
            ? 'chat-bubble chat-bubble-secondary' 
            : 'chat-bubble chat-bubble-primary markdown';
        
        // Add text message if there is one
        if (message && message.trim() !== '') {
            const textContent = document.createElement('p');
            textContent.textContent = message;
            messageBubble.appendChild(textContent);
        }
        
        messageDiv.appendChild(messageBubble);
        
        // Add images if any
        if (images && images.length > 0) {
            const imageContainer = document.createElement('div');
            imageContainer.className = 'chat-image-container';
            
            images.forEach(img => {
                const imgElement = document.createElement('img');
                imgElement.src = img.data;
                imgElement.alt = 'Uploaded image';
                imgElement.className = 'chat-image';
                
                // Add click event to open image in full size
                imgElement.addEventListener('click', () => {
                    this.openImageModal(img.data);
                });
                
                imageContainer.appendChild(imgElement);
            });
            
            // Append image container to the message bubble
            messageBubble.appendChild(imageContainer);
        }
        
        this.chatContainer.appendChild(messageDiv);
        
        // Auto scroll to bottom
        this.chatContainer.scrollTop = this.chatContainer.scrollHeight;
        
        if (this.updateConversation) {
            this.updateConversation();
        }
    }

    /**
     * Create and open image modal
     * @param {string} imageSrc - Image source URL
     */
    openImageModal(imageSrc) {
        // Get the existing modal from HTML
        const modal = document.querySelector('.image-modal');
        const img = modal.querySelector('.image-modal-content');
        
        // Update image source
        img.src = imageSrc;
        
        // Show the modal
        modal.classList.add('open');
        
        // Create event handlers that can be removed
        const closeModal = () => {
            modal.classList.remove('open');
            closeButton.removeEventListener('click', closeModal);
            modal.removeEventListener('click', backgroundClick);
        };
        
        const backgroundClick = (e) => {
            if (e.target === modal) {
                closeModal();
            }
        };
        
        // Add event listeners
        const closeButton = modal.querySelector('.close-button');
        closeButton.addEventListener('click', closeModal);
        modal.addEventListener('click', backgroundClick);
    }

    /**
     * Add a message with documents to the chat display
     * @param {string} message - Message content
     * @param {Array} documents - Array of document data
     * @param {string} sender - Message sender ('user' or 'assistant')
     */
    addMessageWithDocuments(message, documents, sender) {
        const messageDiv = document.createElement('div');
        messageDiv.className = sender === 'user' 
            ? 'chat chat-end' 
            : 'chat chat-start';
        
        const messageBubble = document.createElement('div');
        messageBubble.className = sender === 'user' 
            ? 'chat-bubble chat-bubble-secondary' 
            : 'chat-bubble chat-bubble-primary markdown';
        
        // Add documents if any
        if (documents && documents.length > 0) {
            const documentContainer = document.createElement('div');
            documentContainer.className = 'chat-document-container';
            
            documents.forEach(doc => {
                const docElement = document.createElement('div');
                docElement.className = 'chat-document';
                
                // Choose icon based on document type
                const iconSvg = doc.isPdf 
                    ? `<svg xmlns="http://www.w3.org/2000/svg" class="document-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                      </svg>`
                    : `<svg xmlns="http://www.w3.org/2000/svg" class="document-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>`;
                
                docElement.innerHTML = `
                    ${iconSvg}
                    <div class="document-name">${doc.name}</div>
                `;
                
                documentContainer.appendChild(docElement);
            });
            
            messageBubble.appendChild(documentContainer);
        }
        
        // Add text message after documents
        if (message && message.trim() !== '') {
            const textContent = document.createElement('p');
            textContent.className = documents && documents.length > 0 ? 'mt-2' : '';
            textContent.textContent = message;
            messageBubble.appendChild(textContent);
        }
        
        messageDiv.appendChild(messageBubble);
        this.chatContainer.appendChild(messageDiv);
        
        // Auto scroll to bottom
        this.chatContainer.scrollTop = this.chatContainer.scrollHeight;
        
        if (this.updateConversation) {
            this.updateConversation();
        }
    }
}

export default MessageHandler; 