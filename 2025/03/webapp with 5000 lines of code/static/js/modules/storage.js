// Constants
const STORAGE_KEY = 'chatgpt_clone_data';
const SYSTEM_MESSAGES_KEY = 'chatgpt_clone_system_messages';

/**
 * Save data to localStorage
 * @param {string} key - Storage key
 * @param {any} data - Data to store
 */
function saveToStorage(key, data) {
    try {
        localStorage.setItem(key, JSON.stringify(data));
    } catch (error) {
        console.error(`Error saving to storage (${key}):`, error);
    }
}

/**
 * Load data from localStorage
 * @param {string} key - Storage key
 * @param {any} defaultValue - Default value if nothing is stored
 * @returns {any} - The stored data or defaultValue
 */
function loadFromStorage(key, defaultValue = null) {
    try {
        const data = localStorage.getItem(key);
        return data ? JSON.parse(data) : defaultValue;
    } catch (error) {
        console.error(`Error loading from storage (${key}):`, error);
        return defaultValue;
    }
}

/**
 * Save conversations data
 * @param {Array} conversations - Conversations array
 * @param {string} activeConversationId - ID of active conversation
 */
function saveConversations(conversations, activeConversationId) {
    const data = {
        conversations,
        activeConversationId
    };
    saveToStorage(STORAGE_KEY, data);
}

/**
 * Load conversations data
 * @returns {Object} - Conversations data object
 */
function loadConversations() {
    return loadFromStorage(STORAGE_KEY, { conversations: [], activeConversationId: null });
}

/**
 * Save system messages
 * @param {Array} systemMessages - System messages array
 */
function saveSystemMessages(systemMessages) {
    saveToStorage(SYSTEM_MESSAGES_KEY, systemMessages);
}

/**
 * Load system messages
 * @returns {Array} - System messages array
 */
function loadSystemMessages() {
    return loadFromStorage(SYSTEM_MESSAGES_KEY, []);
}

export {
    saveConversations,
    loadConversations,
    saveSystemMessages,
    loadSystemMessages
}; 