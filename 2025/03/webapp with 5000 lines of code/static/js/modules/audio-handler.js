/**
 * Audio Handler for processing and transcribing audio files
 */
class AudioHandler {
    constructor() {
        this.uploadedAudio = null;
        this.maxFileSize = 25 * 1024 * 1024; // 25MB max file size
    }

    /**
     * Reset uploaded audio
     */
    clearAudio() {
        this.uploadedAudio = null;
    }

    /**
     * Process an audio file to prepare it for upload
     * @param {File} file - Audio file
     * @returns {Promise} - Promise resolving to audio data object
     */
    processFile(file) {
        return new Promise((resolve, reject) => {
            // Validate file is an audio file
            if (!file.type.startsWith('audio/')) {
                reject(new Error('File must be an audio file'));
                return;
            }

            // Check file size
            if (file.size > this.maxFileSize) {
                reject(new Error(`File size exceeds maximum of ${this.maxFileSize / (1024 * 1024)}MB`));
                return;
            }

            // Just store the file object for later form submission
            resolve({
                name: file.name,
                type: file.type,
                size: file.size,
                file: file
            });
        });
    }

    /**
     * Set the audio file for transcription
     * @param {Object} audioData - Audio data object
     */
    setAudio(audioData) {
        this.uploadedAudio = audioData;
    }

    /**
     * Get the uploaded audio
     * @returns {Object|null} - Audio data object or null
     */
    getAudio() {
        return this.uploadedAudio;
    }

    /**
     * Check if audio is uploaded
     * @returns {boolean} - True if audio is present
     */
    hasAudio() {
        return this.uploadedAudio !== null;
    }

    /**
     * Prepare FormData for audio transcription
     * @returns {FormData} - FormData object for API request
     */
    prepareFormData() {
        if (!this.hasAudio()) return null;

        const formData = new FormData();
        formData.append('file', this.uploadedAudio.file);
        formData.append('model', 'gpt-4o-mini-transcribe');
        formData.append('response_format', 'text');
        
        return formData;
    }
}

export default AudioHandler; 