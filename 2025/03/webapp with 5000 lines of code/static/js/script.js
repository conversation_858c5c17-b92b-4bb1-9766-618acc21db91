document.addEventListener('DOMContentLoaded', () => {
    // DOM Elements
    const chatContainer = document.getElementById('chat-container');
    const messageInput = document.getElementById('message-input');
    const sendButton = document.getElementById('send-button');
    const loadingAnimation = document.getElementById('loading-animation');
    const conversationList = document.getElementById('conversation-list');
    const newChatButton = document.getElementById('new-chat-btn');
    const toggleSidebarButton = document.getElementById('toggle-sidebar');
    const sidebar = document.getElementById('sidebar');
    const mobileMenuButton = document.getElementById('mobile-menu-btn');
    
    // DOM Elements for System Messages
    const systemMessageList = document.getElementById('system-message-list');
    const createSystemMsgBtn = document.getElementById('create-system-msg-btn');
    const systemMessageModal = document.getElementById('system-message-modal');
    const systemMessageTitle = document.getElementById('system-message-title');
    const systemMessageContent = document.getElementById('system-message-content');
    const systemMessageSave = document.getElementById('system-message-save');
    const systemMessageCancel = document.getElementById('system-message-cancel');
    
    // Constants
    const STORAGE_KEY = 'chatgpt_clone_data';
    const SYSTEM_MESSAGES_KEY = 'chatgpt_clone_system_messages';
    
    // State
    let socketReady = false;
    let currentStreamingMessage = null;
    let conversations = [];
    let activeConversationId = null;
    let systemMessages = [];
    let editingSystemMessageId = null;
    let activeSystemMessageId = null;
    
    // Initialize WebSocket connection
    const socket = new WebSocket(`ws://${window.location.host}/ws`);
    
    socket.onopen = (event) => {
        console.log('WebSocket connection established');
        socketReady = true;
    };
    
    socket.onmessage = (event) => {
        const data = JSON.parse(event.data);
        
        if (data.error) {
            hideLoading();
            addMessage('Error: ' + data.error, 'system');
        } 
        // Handle token stream
        else if (data.type === "token") {
            // If this is the first token, create a new message
            if (!currentStreamingMessage) {
                currentStreamingMessage = createStreamingMessage();
            }
            
            // Append token to the current message
            appendToStreamingMessage(currentStreamingMessage, data.token);
            
            // Update the conversation in real-time
            updateCurrentConversation();
            
            // Auto scroll to bottom
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }
        // Handle completion of streaming
        else if (data.type === "complete") {
            hideLoading();
            completeStreamingMessage();
            
            // Final update to the conversation
            updateCurrentConversation();
            
            // Update conversation title if it's new
            maybeUpdateConversationTitle();
        }
        // Handle traditional (non-streaming) responses
        else if (data.response) {
            hideLoading();
            addMessage(data.response, 'assistant');
            
            // Update the conversation
            updateCurrentConversation();
            
            // Update conversation title if it's new
            maybeUpdateConversationTitle();
        }
    };
    
    socket.onerror = (error) => {
        console.error('WebSocket error:', error);
        hideLoading();
        addMessage('WebSocket error occurred. Falling back to HTTP.', 'system');
    };
    
    socket.onclose = (event) => {
        console.log('WebSocket connection closed');
        socketReady = false;
    };
    
    // ---- Conversation Management Functions ----
    
    // Initialize conversations from localStorage
    function initConversations() {
        try {
            const storedData = localStorage.getItem(STORAGE_KEY);
            if (storedData) {
                const data = JSON.parse(storedData);
                conversations = data.conversations || [];
                activeConversationId = data.activeConversationId;
            }
            
            // If no conversations exist or no active conversation, create a new one
            if (conversations.length === 0 || !activeConversationId) {
                createNewConversation();
            } else {
                // Load the active conversation
                loadConversation(activeConversationId);
            }
            
            // Render the conversation list
            renderConversationList();
        } catch (error) {
            console.error('Error initializing conversations:', error);
            // If there's an error, create a new conversation
            createNewConversation();
        }
    }
    
    // Save conversations to localStorage
    function saveConversations() {
        try {
            const data = {
                conversations: conversations,
                activeConversationId: activeConversationId
            };
            localStorage.setItem(STORAGE_KEY, JSON.stringify(data));
        } catch (error) {
            console.error('Error saving conversations:', error);
        }
    }
    
    // Create a new conversation
    function createNewConversation() {
        // Generate a unique ID
        const id = 'conv-' + Date.now();
        
        // Create a new conversation object with no system message
        const newConversation = {
            id: id,
            title: 'New conversation',
            timestamp: Date.now(),
            messages: [],
            systemMessageId: null // No system message by default
        };
        
        // Add to the list of conversations
        conversations.unshift(newConversation);
        
        // Set as the active conversation
        activeConversationId = id;
        
        // Save the updated conversations
        saveConversations();
        
        // Clear the chat display
        chatContainer.innerHTML = `
            <div class="chat chat-start">
                <div class="chat-bubble chat-bubble-primary">
                    Hello! I'm your AI assistant. How can I help you today?
                </div>
            </div>
        `;
        
        // Update the conversation list
        renderConversationList();
    }
    
    // Load a conversation
    function loadConversation(id) {
        const conversation = conversations.find(conv => conv.id === id);
        
        if (!conversation) {
            console.error('Conversation not found:', id);
            return;
        }
        
        // Set as the active conversation
        activeConversationId = id;
        
        // Set the active system message if the conversation has one, or null if it doesn't
        activeSystemMessageId = conversation.systemMessageId || null;
        
        // Clear the chat display
        chatContainer.innerHTML = '';
        
        // Add the welcome message if there are no messages
        if (conversation.messages.length === 0) {
            chatContainer.innerHTML = `
                <div class="chat chat-start">
                    <div class="chat-bubble chat-bubble-primary">
                        Hello! I'm your AI assistant. How can I help you today?
                    </div>
                </div>
            `;
        } else {
            // Add all messages to the chat display
            conversation.messages.forEach(msg => {
                addMessageToDisplay(msg.content, msg.role);
            });
        }
        
        // Save the updated active conversation
        saveConversations();
        
        // Update the conversation list
        renderConversationList();
        
        // Update the system message list to show the active one
        renderSystemMessageList();
        
        // Update the system message indicator
        updateSystemMessageIndicator();
    }
    
    // Delete a conversation
    function deleteConversation(id, event) {
        // Prevent the click from bubbling up
        if (event) {
            event.stopPropagation();
        }
        
        // Find the index of the conversation
        const index = conversations.findIndex(conv => conv.id === id);
        
        if (index === -1) {
            console.error('Conversation not found:', id);
            return;
        }
        
        // Remove the conversation
        conversations.splice(index, 1);
        
        // If the deleted conversation was the active one, load another one
        if (id === activeConversationId) {
            if (conversations.length > 0) {
                loadConversation(conversations[0].id);
            } else {
                createNewConversation();
            }
        } else {
            // Just update the conversation list
            renderConversationList();
        }
        
        // Save the updated conversations
        saveConversations();
    }
    
    // Render the conversation list
    function renderConversationList() {
        conversationList.innerHTML = '';
        
        conversations.forEach(conv => {
            const item = document.createElement('div');
            item.className = `conversation-item ${conv.id === activeConversationId ? 'active' : ''}`;
            item.setAttribute('data-id', conv.id);
            
            // Format date
            const date = new Date(conv.timestamp);
            const formattedDate = date.toLocaleDateString(undefined, { month: 'short', day: 'numeric' });
            
            item.innerHTML = `
                <div class="conversation-title">${conv.title}</div>
                <div class="flex items-center">
                    <span class="text-xs opacity-70 mr-2">${formattedDate}</span>
                    <button class="delete-conversation btn btn-xs btn-circle btn-ghost">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
            `;
            
            // Add click event to load the conversation
            item.addEventListener('click', () => {
                if (conv.id !== activeConversationId) {
                    loadConversation(conv.id);
                }
            });
            
            // Add click event to the delete button
            const deleteButton = item.querySelector('.delete-conversation');
            deleteButton.addEventListener('click', (event) => {
                deleteConversation(conv.id, event);
            });
            
            conversationList.appendChild(item);
        });
    }
    
    // Update the current conversation with the messages in the chat display
    function updateCurrentConversation() {
        if (!activeConversationId) return;
        
        const conversation = conversations.find(conv => conv.id === activeConversationId);
        if (!conversation) return;
        
        // Extract messages from the chat display
        const messages = [];
        const chatBubbles = chatContainer.querySelectorAll('.chat');
        
        chatBubbles.forEach(bubble => {
            const isUser = bubble.classList.contains('chat-end');
            const content = bubble.querySelector('.chat-bubble').textContent;
            
            messages.push({
                role: isUser ? 'user' : 'assistant',
                content: content
            });
        });
        
        // Update the conversation
        conversation.messages = messages;
        conversation.timestamp = Date.now();
        
        // Save the updated conversations
        saveConversations();
    }
    
    // Update the title of a new conversation based on the first message
    function maybeUpdateConversationTitle() {
        if (!activeConversationId) return;
        
        const conversation = conversations.find(conv => conv.id === activeConversationId);
        if (!conversation) return;
        
        // Only update if it's a new conversation
        if (conversation.title === 'New conversation' && conversation.messages.length >= 2) {
            const userMessage = conversation.messages.find(msg => msg.role === 'user');
            if (userMessage) {
                // Use the first 30 characters of the user message as the title
                const title = userMessage.content.slice(0, 30) + (userMessage.content.length > 30 ? '...' : '');
                conversation.title = title;
                
                // Save and update the list
                saveConversations();
                renderConversationList();
            }
        }
    }
    
    // ---- Message Handling Functions ----
    
    // Create a new streaming message container
    function createStreamingMessage() {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'chat chat-start';
        
        const messageBubble = document.createElement('div');
        messageBubble.className = 'chat-bubble chat-bubble-primary markdown streaming-message';
        messageDiv.appendChild(messageBubble);
        
        // Add to chat container
        chatContainer.appendChild(messageDiv);
        
        return messageBubble;
    }
    
    // Append token to streaming message
    function appendToStreamingMessage(element, token) {
        element.textContent += token;
    }
    
    // Mark streaming as complete
    function completeStreamingMessage() {
        if (currentStreamingMessage) {
            currentStreamingMessage.classList.add('streaming-complete');
            currentStreamingMessage = null;
        }
    }
    
    // Add a message to the chat display only (doesn't update the conversation)
    function addMessageToDisplay(message, sender) {
        const messageDiv = document.createElement('div');
        messageDiv.className = sender === 'user' 
            ? 'chat chat-end' 
            : sender === 'assistant' 
                ? 'chat chat-start' 
                : 'chat chat-start';
        
        const messageBubble = document.createElement('div');
        messageBubble.className = sender === 'user' 
            ? 'chat-bubble chat-bubble-secondary' 
            : sender === 'assistant' 
                ? 'chat-bubble chat-bubble-primary markdown' 
                : 'chat-bubble chat-bubble-accent';
        
        messageBubble.textContent = message;
        messageDiv.appendChild(messageBubble);
        
        chatContainer.appendChild(messageDiv);
        
        // Auto scroll to bottom
        chatContainer.scrollTop = chatContainer.scrollHeight;
    }
    
    // Add a message to the chat and update the conversation
    function addMessage(message, sender) {
        addMessageToDisplay(message, sender);
        updateCurrentConversation();
    }
    
    // Send message function using WebSocket
    function sendMessageWebSocket(message, systemMessage = null) {
        if (socketReady) {
            // Reset streaming message state
            currentStreamingMessage = null;
            
            // Include the system message if provided
            const payload = systemMessage 
                ? JSON.stringify({ message, systemMessage }) 
                : message;
                
            socket.send(payload);
        } else {
            // Fallback to HTTP if WebSocket is not available
            sendMessageHttp(message, systemMessage);
        }
    }
    
    // Send message function using HTTP
    async function sendMessageHttp(message, systemMessage = null) {
        try {
            const payload = {
                message: message
            };
            
            if (systemMessage) {
                payload.systemMessage = systemMessage;
            }
            
            const response = await fetch('/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(payload),
            });
            
            const data = await response.json();
            hideLoading();
            
            if (data.response) {
                addMessage(data.response, 'assistant');
                maybeUpdateConversationTitle();
            }
        } catch (error) {
            console.error('Error:', error);
            hideLoading();
            addMessage('An error occurred while sending the message.', 'system');
        }
    }
    
    // Show loading animation
    function showLoading() {
        loadingAnimation.classList.remove('hidden');
        anime({
            targets: '.loading-dots .dot',
            scale: [0.5, 1],
            opacity: [0, 1, 0],
            easing: 'easeInOutSine',
            duration: 1000,
            loop: true
        });
    }
    
    // Hide loading animation
    function hideLoading() {
        loadingAnimation.classList.add('hidden');
        anime.remove('.loading-dots .dot');
    }
    
    // Send message event
    function sendMessage() {
        const message = messageInput.value.trim();
        if (message) {
            addMessage(message, 'user');
            showLoading();
            
            // Get the system message if one is active
            const systemMessageContent = getActiveSystemMessageContent();
            
            // Prefer WebSocket, fallback to HTTP
            if (socketReady) {
                sendMessageWebSocket(message, systemMessageContent);
            } else {
                sendMessageHttp(message, systemMessageContent);
            }
            
            messageInput.value = '';
        }
    }
    
    // Mobile sidebar toggle
    function toggleSidebar() {
        sidebar.classList.toggle('open');
        
        // Create or remove backdrop
        let backdrop = document.querySelector('.sidebar-backdrop');
        if (!backdrop && sidebar.classList.contains('open')) {
            backdrop = document.createElement('div');
            backdrop.className = 'sidebar-backdrop open';
            document.body.appendChild(backdrop);
            
            // Close sidebar when backdrop is clicked
            backdrop.addEventListener('click', toggleSidebar);
        } else if (backdrop) {
            backdrop.classList.toggle('open');
            if (!backdrop.classList.contains('open')) {
                setTimeout(() => {
                    backdrop.remove();
                }, 300);
            }
        }
    }
    
    // ---- System Message Functions ----
    
    // Initialize system messages from localStorage
    function initSystemMessages() {
        try {
            const storedMessages = localStorage.getItem(SYSTEM_MESSAGES_KEY);
            if (storedMessages) {
                systemMessages = JSON.parse(storedMessages);
            }
            
            // Start with no active system message
            activeSystemMessageId = null;
            
            renderSystemMessageList();
        } catch (error) {
            console.error('Error initializing system messages:', error);
            systemMessages = [];
        }
    }
    
    // Save system messages to localStorage
    function saveSystemMessages() {
        try {
            localStorage.setItem(SYSTEM_MESSAGES_KEY, JSON.stringify(systemMessages));
        } catch (error) {
            console.error('Error saving system messages:', error);
        }
    }
    
    // Create a new system message
    function createSystemMessage(title, content) {
        const id = 'sysmsg-' + Date.now();
        
        const newMessage = {
            id: id,
            title: title,
            content: content,
            timestamp: Date.now()
        };
        
        systemMessages.push(newMessage);
        saveSystemMessages();
        renderSystemMessageList();
        
        return id;
    }
    
    // Update an existing system message
    function updateSystemMessage(id, title, content) {
        const index = systemMessages.findIndex(msg => msg.id === id);
        
        if (index !== -1) {
            systemMessages[index].title = title;
            systemMessages[index].content = content;
            systemMessages[index].timestamp = Date.now();
            
            saveSystemMessages();
            renderSystemMessageList();
        }
    }
    
    // Delete a system message
    function deleteSystemMessage(id, event) {
        if (event) {
            event.stopPropagation();
        }
        
        const index = systemMessages.findIndex(msg => msg.id === id);
        
        if (index !== -1) {
            systemMessages.splice(index, 1);
            
            if (activeSystemMessageId === id) {
                activeSystemMessageId = null;
            }
            
            saveSystemMessages();
            renderSystemMessageList();
        }
    }
    
    // Set the active system message for the current conversation
    function setActiveSystemMessage(id) {
        activeSystemMessageId = id;
        
        // Update the conversation with the system message
        if (activeConversationId) {
            const conversation = conversations.find(conv => conv.id === activeConversationId);
            if (conversation) {
                conversation.systemMessageId = id;
                saveConversations();
            }
        }
        
        renderSystemMessageList();
        updateSystemMessageIndicator();
    }
    
    // Render the system message list
    function renderSystemMessageList() {
        systemMessageList.innerHTML = '';
        
        if (systemMessages.length === 0) {
            systemMessageList.innerHTML = '<div class="text-xs text-center text-opacity-70 p-2">No saved messages</div>';
            return;
        }
        
        // Update the clear button to show it's active when no system message is selected
        const clearButton = document.getElementById('clear-system-message');
        if (activeSystemMessageId === null) {
            clearButton.classList.add('system-message-active');
        } else {
            clearButton.classList.remove('system-message-active');
        }
        
        systemMessages.forEach(msg => {
            const item = document.createElement('div');
            item.className = `system-message-item ${msg.id === activeSystemMessageId ? 'system-message-active' : ''}`;
            item.setAttribute('data-id', msg.id);
            
            // Truncate the title if it's too long
            const displayTitle = msg.title.length > 20 ? msg.title.slice(0, 20) + '...' : msg.title;
            
            item.innerHTML = `
                <div class="flex justify-between items-center">
                    <div class="system-message-title">
                        ${msg.id === activeSystemMessageId ? '<span class="active-system-message-indicator"></span>' : ''}
                        ${displayTitle}
                    </div>
                    <div class="system-message-controls">
                        <button class="edit-system-message btn btn-xs btn-ghost">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                            </svg>
                        </button>
                        <button class="delete-system-message btn btn-xs btn-ghost text-error">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>
                </div>
                <div class="system-message-preview">${msg.content}</div>
            `;
            
            // Add click event to select the system message
            item.addEventListener('click', () => {
                setActiveSystemMessage(msg.id);
            });
            
            // Add click event to the edit button
            const editButton = item.querySelector('.edit-system-message');
            editButton.addEventListener('click', (event) => {
                event.stopPropagation();
                editSystemMessage(msg.id);
            });
            
            // Add click event to the delete button
            const deleteButton = item.querySelector('.delete-system-message');
            deleteButton.addEventListener('click', (event) => {
                deleteSystemMessage(msg.id, event);
            });
            
            systemMessageList.appendChild(item);
        });
    }
    
    // Open the modal to edit a system message
    function editSystemMessage(id) {
        const message = systemMessages.find(msg => msg.id === id);
        
        if (message) {
            systemMessageTitle.value = message.title;
            systemMessageContent.value = message.content;
            editingSystemMessageId = id;
            
            systemMessageModal.showModal();
        }
    }
    
    // Open the modal to create a new system message
    function openCreateSystemMessageModal() {
        systemMessageTitle.value = '';
        systemMessageContent.value = '';
        editingSystemMessageId = null;
        
        systemMessageModal.showModal();
    }
    
    // Save the system message from the modal
    function saveSystemMessageFromModal() {
        const title = systemMessageTitle.value.trim();
        const content = systemMessageContent.value.trim();
        
        if (!title || !content) {
            alert('Please provide both a title and content for the system message.');
            return;
        }
        
        if (editingSystemMessageId) {
            updateSystemMessage(editingSystemMessageId, title, content);
        } else {
            const newId = createSystemMessage(title, content);
            setActiveSystemMessage(newId);
        }
        
        systemMessageModal.close();
    }
    
    // Get the system message for the current conversation
    function getActiveSystemMessageContent() {
        if (!activeSystemMessageId) return null;
        
        const message = systemMessages.find(msg => msg.id === activeSystemMessageId);
        return message ? message.content : null;
    }
    
    // Add event listener for the clear button
    document.getElementById('clear-system-message').addEventListener('click', () => {
        clearActiveSystemMessage();
    });
    
    // Clear active system message function
    function clearActiveSystemMessage() {
        activeSystemMessageId = null;
        
        // Update the conversation with no system message
        if (activeConversationId) {
            const conversation = conversations.find(conv => conv.id === activeConversationId);
            if (conversation) {
                conversation.systemMessageId = null;
                saveConversations();
            }
        }
        
        renderSystemMessageList();
        updateSystemMessageIndicator();
    }
    
    // Function to update the system message indicator
    function updateSystemMessageIndicator() {
        const indicator = document.getElementById('system-message-indicator');
        if (activeSystemMessageId) {
            indicator.classList.remove('hidden');
        } else {
            indicator.classList.add('hidden');
        }
    }
    
    // ---- Event Listeners ----
    
    // Send button click
    sendButton.addEventListener('click', sendMessage);
    
    // Enter key to send message
    messageInput.addEventListener('keydown', (event) => {
        if (event.key === 'Enter' && !event.shiftKey) {
            event.preventDefault();
            sendMessage();
        }
    });
    
    // New chat button
    newChatButton.addEventListener('click', createNewConversation);
    
    // Toggle sidebar for mobile
    if (toggleSidebarButton) {
        toggleSidebarButton.addEventListener('click', toggleSidebar);
    }
    
    // Mobile menu button click
    if (mobileMenuButton) {
        mobileMenuButton.addEventListener('click', toggleSidebar);
    }
    
    // Event listeners for system message functionality
    createSystemMsgBtn.addEventListener('click', openCreateSystemMessageModal);
    systemMessageSave.addEventListener('click', saveSystemMessageFromModal);
    systemMessageCancel.addEventListener('click', () => {
        systemMessageModal.close();
    });
    
    // Initialize conversations and system messages on page load
    initConversations();
    initSystemMessages();
    
    // Update the system message indicator
    updateSystemMessageIndicator();
    
    // Focus the input on page load
    messageInput.focus();
}); 