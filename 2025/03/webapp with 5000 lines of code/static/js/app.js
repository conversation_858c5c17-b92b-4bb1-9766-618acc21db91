import WebSocketManager from './modules/websocket.js';
import UIManager from './modules/ui.js';
import MessageHandler from './modules/message-handler.js';
import ConversationManager from './modules/conversation.js';
import SystemMessageManager from './modules/system-messages.js';
import ImageHandler from './modules/image-handler.js';
import DocumentHandler from './modules/document-handler.js';
import AudioHandler from './modules/audio-handler.js';

document.addEventListener('DOMContentLoaded', () => {
    // DOM Elements
    const chatContainer = document.getElementById('chat-container');
    const messageInput = document.getElementById('message-input');
    const sendButton = document.getElementById('send-button');
    const conversationList = document.getElementById('conversation-list');
    const newChatButton = document.getElementById('new-chat-btn');
    const toggleSidebarButton = document.getElementById('toggle-sidebar');
    const mobileMenuButton = document.getElementById('mobile-menu-btn');
    const systemMessageList = document.getElementById('system-message-list');
    const createSystemMsgBtn = document.getElementById('create-system-msg-btn');
    const systemMessageSave = document.getElementById('system-message-save');
    const systemMessageCancel = document.getElementById('system-message-cancel');
    const clearSystemMessageBtn = document.getElementById('clear-system-message');
    const fileInput = document.getElementById('file-input');
    const imagePreviewContainer = document.getElementById('image-preview-container');
    const noImagesMessage = document.getElementById('no-images-message');
    const imageUploadIndicator = document.getElementById('image-upload-indicator');
    const documentInput = document.getElementById('document-input');
    const documentPreviewContainer = document.getElementById('document-preview-container');
    const noDocumentsMessage = document.getElementById('no-documents-message');
    const documentUploadIndicator = document.getElementById('document-upload-indicator');
    const audioInput = document.getElementById('audio-input');
    const audioPreviewContainer = document.getElementById('audio-preview-container');
    const noAudioMessage = document.getElementById('no-audio-message');
    const audioUploadIndicator = document.getElementById('audio-upload-indicator');
    const transcribeButton = document.getElementById('transcribe-button');
    
    // Initialize managers
    const ui = new UIManager();
    
    const messageHandler = new MessageHandler(chatContainer, () => {
        conversation.updateCurrentConversation();
    });
    
    const conversation = new ConversationManager(messageHandler, (conversations, activeId) => {
        renderConversationList(conversations, activeId);
    });
    
    const systemMessages = new SystemMessageManager(
        (messages, activeId) => {
            renderSystemMessageList(messages, activeId);
        },
        (isActive) => {
            ui.updateSystemMessageIndicator(isActive);
        }
    );
    
    const ws = new WebSocketManager(
        (data) => handleWebSocketMessage(data),
        (error) => handleWebSocketError(error)
    );
    
    const imageHandler = new ImageHandler();
    const documentHandler = new DocumentHandler();
    const audioHandler = new AudioHandler();
    
    // Initialize websocket
    ws.initialize();
    
    // Initialize conversation and system messages
    conversation.initialize();
    systemMessages.initialize();
    
    // Focus the input on page load
    messageInput.focus();
    
    // Add a paste indicator element to the document
    const pasteIndicator = document.createElement('div');
    pasteIndicator.className = 'image-paste-indicator';
    pasteIndicator.innerHTML = `
        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
        <span>Image pasted</span>
    `;
    document.body.appendChild(pasteIndicator);
    
    // Add a document paste indicator element to the document
    const documentPasteIndicator = document.createElement('div');
    documentPasteIndicator.className = 'document-paste-indicator';
    documentPasteIndicator.innerHTML = `
        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        <span>Document pasted</span>
    `;
    document.body.appendChild(documentPasteIndicator);
    
    // Add an audio paste indicator element to the document
    const audioPasteIndicator = document.createElement('div');
    audioPasteIndicator.className = 'audio-paste-indicator';
    audioPasteIndicator.innerHTML = `
        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
        </svg>
        <span>Audio file ready</span>
    `;
    document.body.appendChild(audioPasteIndicator);
    
    /**
     * Handle WebSocket messages
     * @param {Object} data - Message data
     */
    function handleWebSocketMessage(data) {
        if (data.error) {
            ui.hideLoading();
            messageHandler.addMessage('Error: ' + data.error, 'system');
        } 
        // Handle token stream
        else if (data.type === "token") {
            // Append token to the message
            messageHandler.appendToStreamingMessage(data.token);
            
            // Update the conversation in real-time
            conversation.updateCurrentConversation();
        }
        // Handle completion of streaming
        else if (data.type === "complete") {
            ui.hideLoading();
            messageHandler.completeStreamingMessage();
            
            // Final update to the conversation
            conversation.updateCurrentConversation();
            
            // Update conversation title if it's new
            conversation.maybeUpdateConversationTitle();
        }
        // Handle traditional (non-streaming) responses
        else if (data.response) {
            ui.hideLoading();
            messageHandler.addMessage(data.response, 'assistant');
            
            // Update conversation title if it's new
            conversation.maybeUpdateConversationTitle();
        }
    }
    
    /**
     * Handle WebSocket errors
     * @param {Error} error - Error object
     */
    function handleWebSocketError(error) {
        ui.hideLoading();
        messageHandler.addMessage('WebSocket error occurred. Falling back to HTTP.', 'system');
    }
    
    /**
     * Send message to the API with streaming images support
     */
    async function sendMessage() {
        const message = messageInput.value.trim();
        
        // Skip empty messages with no attachments
        if (!message && !imageHandler.hasImages() && !documentHandler.hasDocuments()) return;
        
        // If we have documents
        if (documentHandler.hasDocuments()) {
            const documents = documentHandler.getDocuments();
            
            // Add the user message with documents to the chat
            messageHandler.addMessageWithDocuments(message, documents, 'user');
            ui.showLoading();
            
            // Format the message with documents for the API
            const formattedInput = documentHandler.formatDocumentsForAPI(message);
            
            console.log(`Sending message with ${documents.length} documents`);
            
            try {
                // Use the documents endpoint for document processing
                const response = await fetch('/chat-with-documents', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'text/event-stream'
                    },
                    body: JSON.stringify({
                        input: formattedInput,
                        systemMessage: systemMessages.getActiveSystemMessageContent()
                    }),
                });
                
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                
                // Create a streaming message for the response
                messageHandler.currentStreamingMessage = null;
                
                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;
                    
                    const chunk = decoder.decode(value);
                    const lines = chunk.split('\n\n');
                    
                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            try {
                                const eventData = JSON.parse(line.substring(6));
                                
                                if (eventData.type === 'token') {
                                    messageHandler.appendToStreamingMessage(eventData.token);
                                    conversation.updateCurrentConversation();
                                } else if (eventData.type === 'complete' || eventData.type === 'end') {
                                    messageHandler.completeStreamingMessage();
                                    ui.hideLoading();
                                    conversation.maybeUpdateConversationTitle();
                                }
                            } catch (e) {
                                console.error('Error parsing streaming data:', e);
                            }
                        }
                    }
                }
                
            } catch (error) {
                console.error('Error:', error);
                ui.hideLoading();
                messageHandler.addMessage('An error occurred while sending the message with documents.', 'system');
            }
            
            // Clear documents after sending
            documentHandler.clearDocuments();
            updateDocumentPreview();
        } 
        // If we have images
        else if (imageHandler.hasImages()) {
            const images = imageHandler.getImages();
            
            // Add the user message with images to the chat
            messageHandler.addMessageWithImages(message, images, 'user');
            ui.showLoading();
            
            // Format the message with images for the API
            const formattedInput = imageHandler.formatImagesForAPI(message);
            
            console.log(`Sending message with ${images.length} images`);
            
            try {
                // Use EventSource for streaming image responses
                const response = await fetch('/chat-with-images', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'text/event-stream'
                    },
                    body: JSON.stringify({
                        input: formattedInput,
                        systemMessage: systemMessages.getActiveSystemMessageContent()
                    }),
                });
                
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                
                // Create a streaming message for the response
                messageHandler.currentStreamingMessage = null;
                
                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;
                    
                    const chunk = decoder.decode(value);
                    const lines = chunk.split('\n\n');
                    
                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            try {
                                const eventData = JSON.parse(line.substring(6));
                                
                                if (eventData.type === 'token') {
                                    messageHandler.appendToStreamingMessage(eventData.token);
                                    conversation.updateCurrentConversation();
                                } else if (eventData.type === 'complete' || eventData.type === 'end') {
                                    messageHandler.completeStreamingMessage();
                                    ui.hideLoading();
                                    conversation.maybeUpdateConversationTitle();
                                }
                            } catch (e) {
                                console.error('Error parsing streaming data:', e);
                            }
                        }
                    }
                }
                
            } catch (error) {
                console.error('Error:', error);
                ui.hideLoading();
                messageHandler.addMessage('An error occurred while sending the message with images.', 'system');
            }
            
            // Clear images after sending
            imageHandler.clearImages();
            updateImagePreview();
        } else {
            // For regular text messages without images or documents
            messageHandler.addMessage(message, 'user');
            ui.showLoading();
            
            // Get the system message if one is active
            const systemMessageContent = systemMessages.getActiveSystemMessageContent();
            
            // Get the conversation history from the DOM
            const conversationHistory = getConversationHistoryFromDOM();
            
            // Prefer WebSocket, fallback to HTTP
            if (ws.isSocketReady()) {
                ws.sendMessage(message, systemMessageContent, conversationHistory);
            } else {
                await sendMessageHttp(message, systemMessageContent, conversationHistory);
            }
        }
        
        // Clear the input
        messageInput.value = '';
    }
    
    /**
     * Get conversation history from the DOM
     * @returns {Array} - Conversation history
     */
    function getConversationHistoryFromDOM() {
        const messages = document.querySelectorAll('.chat-message');
        const history = [];
        
        // Include up to the last 10 messages for context
        const startIdx = Math.max(0, messages.length - 10);
        
        for (let i = startIdx; i < messages.length - 1; i++) {
            const msg = messages[i];
            const role = msg.getAttribute('data-role');
            const content = msg.querySelector('.chat-bubble').textContent.trim();
            
            if (content) {
                history.push({ role, content });
            }
        }
        
        return history;
    }
    
    /**
     * Send message using HTTP
     * @param {string} message - Message to send
     * @param {string|null} systemMessage - System message to include
     */
    async function sendMessageHttp(message, systemMessage = null, conversationHistory = []) {
        try {
            const payload = {
                message: message,
                conversationHistory: conversationHistory
            };
            
            if (systemMessage) {
                payload.systemMessage = systemMessage;
            }
            
            const response = await fetch('/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(payload),
            });
            
            const data = await response.json();
            ui.hideLoading();
            
            if (data.response) {
                messageHandler.addMessage(data.response, 'assistant');
                conversation.maybeUpdateConversationTitle();
            }
        } catch (error) {
            console.error('Error:', error);
            ui.hideLoading();
            messageHandler.addMessage('An error occurred while sending the message.', 'system');
        }
    }
    
    /**
     * Render the conversation list
     * @param {Array} conversations - Array of conversations
     * @param {string} activeId - ID of the active conversation
     */
    function renderConversationList(conversations, activeId) {
        conversationList.innerHTML = '';
        
        conversations.forEach(conv => {
            const item = document.createElement('div');
            item.className = `conversation-item ${conv.id === activeId ? 'active' : ''}`;
            item.setAttribute('data-id', conv.id);
            
            // Format date
            const date = new Date(conv.timestamp);
            const formattedDate = date.toLocaleDateString(undefined, { month: 'short', day: 'numeric' });
            
            item.innerHTML = `
                <div class="conversation-title">${conv.title}</div>
                <div class="flex items-center">
                    <span class="text-xs opacity-70 mr-2">${formattedDate}</span>
                    <button class="delete-conversation btn btn-xs btn-circle btn-ghost">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
            `;
            
            // Add click event to load the conversation
            item.addEventListener('click', () => {
                if (conv.id !== activeId) {
                    loadConversation(conv.id);
                }
            });
            
            // Add click event to the delete button
            const deleteButton = item.querySelector('.delete-conversation');
            deleteButton.addEventListener('click', (event) => {
                event.stopPropagation();
                conversation.deleteConversation(conv.id);
            });
            
            conversationList.appendChild(item);
        });
    }
    
    /**
     * Load a conversation and sync system message
     * @param {string} id - Conversation ID
     */
    function loadConversation(id) {
        const conv = conversation.loadConversation(id);
        if (conv) {
            // Sync the system message with the conversation
            systemMessages.setActiveSystemMessage(conv.systemMessageId);
        }
    }
    
    /**
     * Render the system message list
     * @param {Array} messages - Array of system messages
     * @param {string|null} activeId - ID of the active system message
     */
    function renderSystemMessageList(messages, activeId) {
        systemMessageList.innerHTML = '';
        
        // Update the clear button to show it's active when no system message is selected
        if (activeId === null) {
            clearSystemMessageBtn.classList.add('system-message-active');
        } else {
            clearSystemMessageBtn.classList.remove('system-message-active');
        }
        
        if (messages.length === 0) {
            systemMessageList.innerHTML = '<div class="text-xs text-center text-opacity-70 p-2">No saved messages</div>';
            return;
        }
        
        messages.forEach(msg => {
            const item = document.createElement('div');
            item.className = `system-message-item ${msg.id === activeId ? 'system-message-active' : ''}`;
            item.setAttribute('data-id', msg.id);
            
            // Truncate the title if it's too long
            const displayTitle = msg.title.length > 20 ? msg.title.slice(0, 20) + '...' : msg.title;
            
            item.innerHTML = `
                <div class="flex justify-between items-center">
                    <div class="system-message-title">
                        ${msg.id === activeId ? '<span class="active-system-message-indicator"></span>' : ''}
                        ${displayTitle}
                    </div>
                    <div class="system-message-controls">
                        <button class="edit-system-message btn btn-xs btn-ghost">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                            </svg>
                        </button>
                        <button class="delete-system-message btn btn-xs btn-ghost text-error">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>
                </div>
                <div class="system-message-preview">${msg.content}</div>
            `;
            
            // Add click event to select the system message
            item.addEventListener('click', () => {
                systemMessages.setActiveSystemMessage(msg.id);
                conversation.setActiveConversationSystemMessage(msg.id);
            });
            
            // Add click event to the edit button
            const editButton = item.querySelector('.edit-system-message');
            editButton.addEventListener('click', (event) => {
                event.stopPropagation();
                systemMessages.editSystemMessage(msg.id);
            });
            
            // Add click event to the delete button
            const deleteButton = item.querySelector('.delete-system-message');
            deleteButton.addEventListener('click', (event) => {
                event.stopPropagation();
                systemMessages.deleteSystemMessage(msg.id);
                if (activeId === msg.id) {
                    conversation.setActiveConversationSystemMessage(null);
                }
            });
            
            systemMessageList.appendChild(item);
        });
    }
    
    // Add image handling functions
    /**
     * Process uploaded files
     * @param {FileList} files - Uploaded files
     */
    async function handleFileUpload(files) {
        if (!files || files.length === 0) return;
        
        console.log(`Processing ${files.length} files for upload`);
        
        try {
            const remainingSlots = imageHandler.maxImages - imageHandler.getImages().length;
            
            if (remainingSlots <= 0) {
                alert(`Maximum ${imageHandler.maxImages} images allowed per message`);
                return;
            }
            
            // Process files up to the maximum allowed
            const filesToProcess = Array.from(files).slice(0, remainingSlots);
            
            // Use Promise.all to process all files concurrently
            const processedImages = await Promise.all(
                filesToProcess.map(file => imageHandler.processFile(file))
            );
            
            // Add all processed images to the handler
            processedImages.forEach(imageData => {
                imageHandler.addImage(imageData);
            });
            
            // Update the UI
            updateImagePreview();
            
            // Show warning if some files were not processed
            if (files.length > remainingSlots) {
                alert(`Only ${remainingSlots} out of ${files.length} images were added. Maximum ${imageHandler.maxImages} images allowed per message.`);
            }
        } catch (error) {
            console.error('Error processing uploaded files:', error);
            alert('Error processing image(s): ' + error.message);
        }
    }
    
    /**
     * Update the image preview container
     */
    function updateImagePreview() {
        imagePreviewContainer.innerHTML = '';
        const images = imageHandler.getImages();
        
        console.log(`Updating preview with ${images.length} images`);
        
        if (images.length === 0) {
            noImagesMessage.style.display = 'block';
            imageUploadIndicator.classList.add('hidden');
        } else {
            noImagesMessage.style.display = 'none';
            imageUploadIndicator.classList.remove('hidden');
            
            images.forEach((img, index) => {
                const preview = document.createElement('div');
                preview.className = 'image-preview mb-2';
                
                const imgElement = document.createElement('img');
                imgElement.src = img.data;
                imgElement.alt = 'Image preview';
                
                const removeButton = document.createElement('div');
                removeButton.className = 'remove-image';
                removeButton.innerHTML = `
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                `;
                
                removeButton.addEventListener('click', () => {
                    imageHandler.removeImage(index);
                    updateImagePreview();
                });
                
                preview.appendChild(imgElement);
                preview.appendChild(removeButton);
                imagePreviewContainer.appendChild(preview);
            });
        }
        
        // Update attachment indicators
        updateAttachmentIndicators();
    }
    
    /**
     * Handle paste events to capture images
     * @param {ClipboardEvent} e - Paste event
     */
    function handlePaste(e) {
        const items = e.clipboardData.items;
        
        let imageFound = false;
        // Process all image items in the clipboard
        for (let i = 0; i < items.length; i++) {
            if (items[i].type.indexOf('image') !== -1) {
                imageFound = true;
                const file = items[i].getAsFile();
                
                if (file) {
                    // Process each image file individually
                    imageHandler.processFile(file).then(imageData => {
                        // Check if we're not exceeding the max images limit
                        if (imageHandler.getImages().length < imageHandler.maxImages) {
                            imageHandler.addImage(imageData);
                            updateImagePreview();
                        } else {
                            console.warn(`Maximum ${imageHandler.maxImages} images allowed per message`);
                        }
                    }).catch(error => {
                        console.error('Error processing pasted image:', error);
                    });
                }
            }
        }
        
        if (imageFound) {
            // Show the paste indicator
            pasteIndicator.classList.add('show');
            setTimeout(() => {
                pasteIndicator.classList.remove('show');
            }, 3000);
        }
        
        return imageFound;
    }
    
    /**
     * Handle streaming for image responses using EventSource
     * @param {Object} formattedInput - Formatted input for the API
     * @param {string|null} systemMessage - System message content
     */
    async function handleImageStreamingWithEventSource(formattedInput, systemMessage) {
        // First, create a streaming message container
        messageHandler.currentStreamingMessage = null;
        
        try {
            // Create a POST request for EventSource
            const params = new URLSearchParams();
            params.append('input', JSON.stringify(formattedInput));
            if (systemMessage) {
                params.append('systemMessage', systemMessage);
            }
            
            // Use fetch with streaming response
            const response = await fetch('/chat-with-images-stream?' + params.toString());
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const reader = response.body.getReader();
            const decoder = new TextDecoder("utf-8");
            
            let buffer = '';
            
            while (true) {
                const { value, done } = await reader.read();
                if (done) break;
                
                buffer += decoder.decode(value, { stream: true });
                
                const lines = buffer.split('\n\n');
                buffer = lines.pop() || '';
                
                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        try {
                            const data = JSON.parse(line.slice(6));
                            
                            if (data.type === 'token') {
                                messageHandler.appendToStreamingMessage(data.token);
                                conversation.updateCurrentConversation();
                            } else if (data.type === 'complete' || data.type === 'end') {
                                ui.hideLoading();
                                messageHandler.completeStreamingMessage();
                                conversation.maybeUpdateConversationTitle();
                            }
                        } catch (e) {
                            console.error('Error parsing SSE data:', e);
                        }
                    }
                }
            }
        } catch (error) {
            console.error('Error with EventSource:', error);
            ui.hideLoading();
            messageHandler.addMessage('An error occurred while streaming the response.', 'system');
        }
    }
    
    // Add document handling functions
    /**
     * Process uploaded document files
     * @param {FileList} files - Uploaded files
     */
    async function handleDocumentUpload(files) {
        if (!files || files.length === 0) return;
        
        console.log(`Processing ${files.length} document files for upload`);
        
        try {
            const remainingSlots = documentHandler.maxDocuments - documentHandler.getDocuments().length;
            
            if (remainingSlots <= 0) {
                alert(`Maximum ${documentHandler.maxDocuments} documents allowed per message`);
                return;
            }
            
            // Process files up to the maximum allowed
            const filesToProcess = Array.from(files).slice(0, remainingSlots);
            
            // Use Promise.all to process all files concurrently
            const processedDocuments = await Promise.all(
                filesToProcess.map(file => documentHandler.processFile(file))
            );
            
            // Add all processed documents to the handler
            processedDocuments.forEach(documentData => {
                documentHandler.addDocument(documentData);
            });
            
            // Update the UI
            updateDocumentPreview();
            
            // Show warning if some files were not processed
            if (files.length > remainingSlots) {
                alert(`Only ${remainingSlots} out of ${files.length} documents were added. Maximum ${documentHandler.maxDocuments} documents allowed per message.`);
            }
        } catch (error) {
            console.error('Error processing uploaded document files:', error);
            alert('Error processing document(s): ' + error.message);
        }
    }
    
    /**
     * Update the document preview container
     */
    function updateDocumentPreview() {
        documentPreviewContainer.innerHTML = '';
        const documents = documentHandler.getDocuments();
        
        console.log(`Updating preview with ${documents.length} documents`);
        
        if (documents.length === 0) {
            noDocumentsMessage.style.display = 'block';
            documentUploadIndicator.classList.add('hidden');
        } else {
            noDocumentsMessage.style.display = 'none';
            documentUploadIndicator.classList.remove('hidden');
            
            documents.forEach((doc, index) => {
                const preview = document.createElement('div');
                preview.className = 'document-preview mb-2';
                
                // Choose icon based on document type
                const iconSvg = doc.isPdf 
                    ? `<svg xmlns="http://www.w3.org/2000/svg" class="document-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                      </svg>`
                    : `<svg xmlns="http://www.w3.org/2000/svg" class="document-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>`;
                
                // Format file size
                const formattedSize = formatFileSize(doc.size);
                
                preview.innerHTML = `
                    ${iconSvg}
                    <div class="document-info">
                        <div class="document-name">${doc.name}</div>
                        <div class="document-size">${formattedSize}</div>
                    </div>
                    <div class="remove-document">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </div>
                `;
                
                const removeButton = preview.querySelector('.remove-document');
                removeButton.addEventListener('click', () => {
                    documentHandler.removeDocument(index);
                    updateDocumentPreview();
                });
                
                documentPreviewContainer.appendChild(preview);
            });
        }
        
        // Update attachment indicators
        updateAttachmentIndicators();
    }
    
    /**
     * Format file size in bytes to human-readable format
     * @param {number} bytes - File size in bytes
     * @returns {string} - Formatted file size
     */
    function formatFileSize(bytes) {
        if (bytes < 1024) return bytes + ' B';
        else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + ' KB';
        else return (bytes / 1048576).toFixed(1) + ' MB';
    }
    
    /**
     * Handle paste events to capture documents
     * @param {ClipboardEvent} e - Paste event
     */
    function handleDocumentPaste(e) {
        const items = e.clipboardData.items;
        let documentFound = false;
        
        // Check for file items
        for (let i = 0; i < items.length; i++) {
            if (items[i].kind === 'file') {
                const file = items[i].getAsFile();
                if (file && documentHandler.isSupportedDocumentType(file.type)) {
                    documentFound = true;
                    documentHandler.processFile(file).then(documentData => {
                        if (documentHandler.getDocuments().length < documentHandler.maxDocuments) {
                            documentHandler.addDocument(documentData);
                            updateDocumentPreview();
                            
                            // Show the paste indicator
                            documentPasteIndicator.classList.add('show');
                            setTimeout(() => {
                                documentPasteIndicator.classList.remove('show');
                            }, 3000);
                        } else {
                            console.warn(`Maximum ${documentHandler.maxDocuments} documents allowed per message`);
                        }
                    }).catch(error => {
                        console.error('Error processing pasted document:', error);
                    });
                }
            }
        }
        
        return documentFound;
    }
    
    // Add this function to check for attached files (images or documents)
    function updateAttachmentIndicators() {
        const hasImages = imageHandler.hasImages();
        const hasDocuments = documentHandler.hasDocuments();
        const hasAudio = audioHandler.hasAudio();
        const fileAttachmentsIndicator = document.querySelector('.file-attachments-indicator');
        const imagesCountSpan = fileAttachmentsIndicator.querySelector('.images-count');
        const documentsCountSpan = fileAttachmentsIndicator.querySelector('.documents-count');
        const audioCountSpan = fileAttachmentsIndicator.querySelector('.audio-count') || 
                               document.createElement('span');
        
        audioCountSpan.className = 'audio-count';
        
        // Update the audio indicator
        if (hasAudio) {
            audioUploadIndicator.classList.remove('hidden');
            
            // Show audio indicator
            audioCountSpan.innerHTML = `
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3z"/>
                    <path d="M19 10v2a7 7 0 0 1-14 0v-2"/>
                    <line x1="12" y1="19" x2="12" y2="23"/>
                    <line x1="8" y1="23" x2="16" y2="23"/>
                </svg>
                1`;
            
            if (!fileAttachmentsIndicator.contains(audioCountSpan)) {
                fileAttachmentsIndicator.appendChild(audioCountSpan);
            }
        } else {
            audioUploadIndicator.classList.add('hidden');
            audioCountSpan.innerHTML = '';
            if (fileAttachmentsIndicator.contains(audioCountSpan)) {
                fileAttachmentsIndicator.removeChild(audioCountSpan);
            }
        }
        
        // Update the image indicator
        if (hasImages) {
            imageUploadIndicator.classList.remove('hidden');
            
            // Show image count
            const imageCount = imageHandler.getImages().length;
            imagesCountSpan.innerHTML = `
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                    <circle cx="8.5" cy="8.5" r="1.5"/>
                    <polyline points="21 15 16 10 5 21"/>
                </svg>
                ${imageCount}`;
        } else {
            imageUploadIndicator.classList.add('hidden');
            imagesCountSpan.innerHTML = '';
        }
        
        // Update the document indicator
        if (hasDocuments) {
            documentUploadIndicator.classList.remove('hidden');
            
            // Show document count
            const docCount = documentHandler.getDocuments().length;
            documentsCountSpan.innerHTML = `
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                    <polyline points="14 2 14 8 20 8"/>
                    <line x1="16" y1="13" x2="8" y2="13"/>
                    <line x1="16" y1="17" x2="8" y2="17"/>
                    <polyline points="10 9 9 9 8 9"/>
                </svg>
                ${docCount}`;
        } else {
            documentUploadIndicator.classList.add('hidden');
            documentsCountSpan.innerHTML = '';
        }
        
        // Show or hide the file attachments indicator
        if (hasImages || hasDocuments || hasAudio) {
            fileAttachmentsIndicator.classList.remove('hidden');
            fileAttachmentsIndicator.classList.add('show');
            messageInput.classList.add('has-attachments');
        } else {
            fileAttachmentsIndicator.classList.add('hidden');
            fileAttachmentsIndicator.classList.remove('show');
            messageInput.classList.remove('has-attachments');
        }
    }
    
    // ---- Event Listeners ----
    
    // Send button click
    sendButton.addEventListener('click', sendMessage);
    
    // Enter key to send message
    messageInput.addEventListener('keydown', (event) => {
        if (event.key === 'Enter' && !event.shiftKey) {
            event.preventDefault();
            sendMessage();
        }
    });
    
    // New chat button
    newChatButton.addEventListener('click', () => {
        conversation.createNewConversation(systemMessages.activeSystemMessageId);
        imageHandler.clearImages();
        updateImagePreview();
        documentHandler.clearDocuments();
        updateDocumentPreview();
        audioHandler.clearAudio();
        updateAudioPreview();
        showNewChatNotification();
    });
    
    // Toggle sidebar for mobile
    if (toggleSidebarButton) {
        toggleSidebarButton.addEventListener('click', () => ui.toggleSidebar());
    }
    
    // Mobile menu button
    if (mobileMenuButton) {
        mobileMenuButton.addEventListener('click', () => ui.toggleSidebar());
    }
    
    // System message events
    createSystemMsgBtn.addEventListener('click', () => {
        systemMessages.openCreateSystemMessageModal();
    });
    
    systemMessageSave.addEventListener('click', () => {
        systemMessages.saveSystemMessageFromModal();
    });
    
    systemMessageCancel.addEventListener('click', () => {
        systemMessages.modal.close();
    });
    
    clearSystemMessageBtn.addEventListener('click', () => {
        systemMessages.clearActiveSystemMessage();
        conversation.setActiveConversationSystemMessage(null);
    });
    
    // Add event listeners for image handling
    fileInput.addEventListener('change', (e) => {
        handleFileUpload(e.target.files);
        // Reset the input so the same file can be uploaded again
        fileInput.value = '';
    });
    
    messageInput.addEventListener('paste', (e) => {
        const containsImage = handlePaste(e);
        const containsDocument = handleDocumentPaste(e);
        // Don't prevent default, so text can still be pasted
    });
    
    // Add event listeners for document handling
    documentInput.addEventListener('change', (e) => {
        handleDocumentUpload(e.target.files);
        // Reset the input so the same file can be uploaded again
        documentInput.value = '';
    });
    
    // Add audio handling functions
    /**
     * Process uploaded audio file
     * @param {FileList} files - Uploaded files
     */
    async function handleAudioUpload(files) {
        if (!files || files.length === 0) return;
        
        console.log(`Processing audio file for upload`);
        
        try {
            // Only use the first file
            const file = files[0];
            
            // Process the audio file
            const audioData = await audioHandler.processFile(file);
            audioHandler.setAudio(audioData);
            
            // Update the UI
            updateAudioPreview();
        } catch (error) {
            console.error('Error processing uploaded audio file:', error);
            alert('Error processing audio: ' + error.message);
        }
    }
    
    /**
     * Update the audio preview container
     */
    function updateAudioPreview() {
        audioPreviewContainer.innerHTML = '';
        const audio = audioHandler.getAudio();
        
        if (!audio) {
            noAudioMessage.style.display = 'block';
            audioUploadIndicator.classList.add('hidden');
            transcribeButton.classList.add('hidden');
        } else {
            noAudioMessage.style.display = 'none';
            audioUploadIndicator.classList.remove('hidden');
            transcribeButton.classList.remove('hidden');
            
            const preview = document.createElement('div');
            preview.className = 'audio-preview mb-2';
            
            // Format file size
            const formattedSize = formatFileSize(audio.size);
            
            preview.innerHTML = `
                <svg xmlns="http://www.w3.org/2000/svg" class="audio-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                </svg>
                <div class="audio-info">
                    <div class="audio-name">${audio.name}</div>
                    <div class="audio-size">${formattedSize}</div>
                </div>
                <div class="remove-audio">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </div>
            `;
            
            const removeButton = preview.querySelector('.remove-audio');
            removeButton.addEventListener('click', () => {
                audioHandler.clearAudio();
                updateAudioPreview();
            });
            
            audioPreviewContainer.appendChild(preview);
        }
        
        // Update attachment indicators
        updateAttachmentIndicators();
    }
    
    /**
     * Transcribe audio file and stream results
     */
    async function transcribeAudio() {
        if (!audioHandler.hasAudio()) return;
        
        try {
            // Prepare the form data
            const formData = audioHandler.prepareFormData();
            if (!formData) return;
            
            // Show a temporary message in the dropdown
            const tempMessage = document.createElement('div');
            tempMessage.className = 'text-xs text-center p-2';
            tempMessage.textContent = 'Transcribing...';
            audioPreviewContainer.innerHTML = '';
            audioPreviewContainer.appendChild(tempMessage);
            
            // Add a message to the chat for the audio file
            const audioFileName = audioHandler.getAudio().name;
            
            // Create a message container for the transcription
            messageHandler.addMessage(`🎤 Transcribing audio: ${audioFileName}`, 'user');
            
            // Get the last message container to append transcription
            const lastMessage = chatContainer.lastElementChild;
            
            // Add a transcription container
            const transcriptionContainer = document.createElement('div');
            transcriptionContainer.className = 'transcription-container';
            transcriptionContainer.innerHTML = `
                <div class="transcription-label">Transcription:</div>
                <div class="transcription-content transcription-streaming"></div>
            `;
            lastMessage.querySelector('.chat-bubble').appendChild(transcriptionContainer);
            
            const transcriptionContent = transcriptionContainer.querySelector('.transcription-content');
            
            // Show loading animation
            ui.showLoading();
            
            // Make request to transcribe
            const response = await fetch('/transcribe-audio', {
                method: 'POST',
                body: formData
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            // Process the streaming response
            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let transcriptText = '';
            
            while (true) {
                const { done, value } = await reader.read();
                if (done) break;
                
                const chunk = decoder.decode(value);
                const lines = chunk.split('\n\n');
                
                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        try {
                            const eventData = JSON.parse(line.substring(6));
                            
                            if (eventData.type === 'transcript.text.delta') {
                                transcriptText += eventData.text || '';
                                transcriptionContent.textContent = transcriptText;
                                
                                // Auto scroll to bottom
                                chatContainer.scrollTop = chatContainer.scrollHeight;
                            } else if (eventData.type === 'transcript.text.done') {
                                // Final transcript
                                transcriptText = eventData.text || transcriptText;
                                transcriptionContent.textContent = transcriptText;
                                transcriptionContent.classList.remove('transcription-streaming');
                                
                                // Add the completed transcription as a separate message
                                messageHandler.addMessage(`📝 Transcription: ${transcriptText}`, 'user');
                                
                                // Force update conversation history with the new message
                                conversation.updateCurrentConversation(true);
                            }
                        } catch (e) {
                            console.error('Error parsing streaming data:', e);
                        }
                    }
                }
            }
            
            // Hide loading animation
            ui.hideLoading();
            
            // Clear audio after transcription
            audioHandler.clearAudio();
            updateAudioPreview();
            
        } catch (error) {
            console.error('Error transcribing audio:', error);
            ui.hideLoading();
            messageHandler.addMessage(`Error transcribing audio: ${error.message}`, 'system');
            
            // Reset audio preview
            updateAudioPreview();
        }
    }
    
    // Add event listeners for audio handling
    audioInput.addEventListener('change', (e) => {
        handleAudioUpload(e.target.files);
        // Reset the input so the same file can be uploaded again
        audioInput.value = '';
    });
    
    transcribeButton.addEventListener('click', () => {
        transcribeAudio();
    });
});

function showNewChatNotification() {
    const notification = document.createElement('div');
    notification.className = 'notification new-chat-notification';
    notification.textContent = 'New conversation started';
    
    document.body.appendChild(notification);
    
    // Animate the notification
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);
    
    // Remove the notification after a few seconds
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
} 