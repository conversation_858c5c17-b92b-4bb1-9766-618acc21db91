<!DOCTYPE html>
<html lang="en" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatGPT Clone</title>
    <!-- <PERSON><PERSON> and Tailwind CSS -->
    <link href="https://cdn.jsdelivr.net/npm/daisyui@3.5.0/dist/full.css" rel="stylesheet" type="text/css" />
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- AnimeJS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js"></script>
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/static/css/main.css">
</head>
<body class="min-h-screen bg-base-200">
    <div class="flex h-screen">
        <!-- Conversation History Sidebar -->
        <div id="sidebar" class="w-64 bg-base-300 p-4 flex flex-col h-full shadow-lg conversation-sidebar">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-lg font-bold text-primary">Conversations</h2>
                <button id="new-chat-btn" class="btn btn-sm btn-primary">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
                    </svg>
                    New Chat
                </button>
            </div>
            
            <!-- Conversations list -->
            <div id="conversation-list" class="flex-grow overflow-y-auto mb-4 space-y-2">
                <!-- Conversations will be loaded here -->
            </div>
            
            <!-- Mobile toggle button -->
            <div class="lg:hidden absolute top-4 right-4">
                <button id="toggle-sidebar" class="btn btn-sm btn-circle">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
                    </svg>
                </button>
            </div>
        </div>
        
        <!-- Main chat area -->
        <div class="flex-1 flex flex-col h-screen">
            <header class="text-center p-4 bg-base-300 shadow-md">
                <h1 class="text-3xl font-bold text-primary">ChatGPT Clone</h1>
                <p class="text-sm text-accent">Powered by OpenAI Responses API</p>
            </header>
            
            <main class="flex-grow flex flex-col overflow-hidden p-4">
                <div class="fixed top-4 left-4 lg:hidden z-50">
                    <button id="mobile-menu-btn" class="btn btn-sm btn-circle">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
                        </svg>
                    </button>
                </div>
                <div id="chat-container" class="flex-grow overflow-y-auto p-4 space-y-4 bg-base-300 rounded-lg shadow-lg">
                    <!-- Chat messages will appear here -->
                    <div class="chat chat-start">
                        <div class="chat-bubble chat-bubble-primary">
                            Hello! I'm your AI assistant. How can I help you today?
                        </div>
                    </div>
                </div>
                
                <!-- Loading animation -->
                <div id="loading-animation" class="hidden py-4 flex justify-center">
                    <div class="loading-dots flex space-x-2">
                        <div class="dot bg-primary w-3 h-3 rounded-full"></div>
                        <div class="dot bg-primary w-3 h-3 rounded-full"></div>
                        <div class="dot bg-primary w-3 h-3 rounded-full"></div>
                    </div>
                </div>
                
                <div class="mt-4 flex flex-col">
                    <!-- Control buttons above the textarea -->
                    <div class="flex items-center gap-2 mb-2 ml-2">
                        <!-- System Messages Button -->
                        <div class="dropdown dropdown-top dropdown-hover" data-tip="System Messages">
                            <label tabindex="0" class="btn btn-sm btn-circle btn-ghost text-primary relative">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                </svg>
                                <div id="system-message-indicator" class="absolute top-0 right-0 w-2 h-2 bg-success rounded-full hidden"></div>
                            </label>
                            <div tabindex="0" class="dropdown-content z-[1] p-2 shadow-xl bg-base-200 rounded-box w-64 max-h-96 overflow-y-auto">
                                <div class="flex justify-between items-center mb-2">
                                    <h3 class="font-bold text-primary">System Messages</h3>
                                    <button id="create-system-msg-btn" class="btn btn-xs btn-primary">New</button>
                                </div>
                                <div class="divider my-1"></div>
                                
                                <!-- Add Clear option -->
                                <div id="clear-system-message" class="system-message-item mb-2 bg-base-100 flex items-center justify-center">
                                    <span class="text-sm">Clear System Message</span>
                                </div>
                                
                                <div class="divider my-1"></div>
                                <div id="system-message-list" class="space-y-1">
                                    <!-- System messages will be loaded here -->
                                    <div class="text-xs text-center text-opacity-70 p-2">No saved messages</div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Image Upload Button -->
                        <div class="dropdown dropdown-top dropdown-hover" data-tip="Upload Images">
                            <label tabindex="0" class="btn btn-sm btn-circle btn-ghost text-primary relative">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                </svg>
                                <div id="image-upload-indicator" class="absolute top-0 right-0 w-2 h-2 bg-info rounded-full hidden"></div>
                            </label>
                            <div tabindex="0" class="dropdown-content z-[1] p-2 shadow-xl bg-base-200 rounded-box w-64">
                                <div class="flex justify-between items-center mb-2">
                                    <h3 class="font-bold text-primary">Images</h3>
                                    <input type="file" id="file-input" accept="image/*" multiple class="hidden" />
                                    <label for="file-input" class="btn btn-xs btn-primary cursor-pointer">Upload</label>
                                </div>
                                <div class="divider my-1"></div>
                                <div id="image-preview-container" class="space-y-2">
                                    <div class="text-xs text-center text-opacity-70 p-2" id="no-images-message">No images selected</div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Document Upload Button -->
                        <div class="dropdown dropdown-top dropdown-hover" data-tip="Upload Documents">
                            <label tabindex="0" class="btn btn-sm btn-circle btn-ghost text-primary relative">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                                <div id="document-upload-indicator" class="absolute top-0 right-0 w-2 h-2 bg-warning rounded-full hidden"></div>
                            </label>
                            <div tabindex="0" class="dropdown-content z-[1] p-2 shadow-xl bg-base-200 rounded-box w-64">
                                <div class="flex justify-between items-center mb-2">
                                    <h3 class="font-bold text-primary">Documents</h3>
                                    <input type="file" id="document-input" accept=".pdf,.txt,text/plain" multiple class="hidden" />
                                    <label for="document-input" class="btn btn-xs btn-primary cursor-pointer">Upload</label>
                                </div>
                                <div class="divider my-1"></div>
                                <div id="document-preview-container" class="space-y-2">
                                    <div class="text-xs text-center text-opacity-70 p-2" id="no-documents-message">No documents selected</div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Audio Upload Button -->
                        <div class="dropdown dropdown-top dropdown-hover" data-tip="Transcribe Audio">
                            <label tabindex="0" class="btn btn-sm btn-circle btn-ghost text-primary relative">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                                </svg>
                                <div id="audio-upload-indicator" class="absolute top-0 right-0 w-2 h-2 bg-error rounded-full hidden"></div>
                            </label>
                            <div tabindex="0" class="dropdown-content z-[1] p-2 shadow-xl bg-base-200 rounded-box w-64">
                                <div class="flex justify-between items-center mb-2">
                                    <h3 class="font-bold text-primary">Audio Transcription</h3>
                                    <input type="file" id="audio-input" accept="audio/*" class="hidden" />
                                    <label for="audio-input" class="btn btn-xs btn-primary cursor-pointer">Upload</label>
                                </div>
                                <div class="divider my-1"></div>
                                <div id="audio-preview-container" class="space-y-2">
                                    <div class="text-xs text-center text-opacity-70 p-2" id="no-audio-message">No audio selected</div>
                                </div>
                                <div class="mt-2 flex justify-end">
                                    <button id="transcribe-button" class="btn btn-xs btn-error hidden">Transcribe</button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- File attachments indicator -->
                        <div class="ml-2 text-xs opacity-70 file-attachments-indicator hidden">
                            <span class="images-count"></span>
                            <span class="documents-count"></span>
                        </div>
                    </div>
                    
                    <!-- Input area with send button -->
                    <div class="flex">
                        <textarea id="message-input" class="flex-grow textarea textarea-bordered resize-none" placeholder="Type your message here..." rows="2"></textarea>
                        <button id="send-button" class="btn btn-primary ml-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                            </svg>
                        </button>
                    </div>
                </div>
            </main>
            
            <footer class="p-4 text-center text-sm text-base-content opacity-70 bg-base-300">
                <p>© 2024 ChatGPT Clone</p>
            </footer>
        </div>
    </div>
    
    <script type="module" src="/static/js/app.js"></script>
    
    <!-- System Message Modal -->
    <dialog id="system-message-modal" class="modal">
        <div class="modal-box">
            <h3 class="font-bold text-lg text-primary">Create System Message</h3>
            <div class="py-4 space-y-4">
                <div class="form-control">
                    <label class="label">
                        <span class="label-text">Title</span>
                    </label>
                    <input id="system-message-title" type="text" placeholder="E.g., 'Coding Assistant'" class="input input-bordered w-full" />
                </div>
                <div class="form-control">
                    <label class="label">
                        <span class="label-text">Message Content</span>
                    </label>
                    <textarea id="system-message-content" class="textarea textarea-bordered h-32" placeholder="E.g., 'You are a helpful coding assistant who specializes in JavaScript'"></textarea>
                </div>
            </div>
            <div class="modal-action">
                <button id="system-message-cancel" class="btn">Cancel</button>
                <button id="system-message-save" class="btn btn-primary">Save</button>
            </div>
        </div>
        <form method="dialog" class="modal-backdrop">
            <button>close</button>
        </form>
    </dialog>
    
    <!-- Add this at the end of the body before the script tags -->
    <div class="image-modal">
        <img class="image-modal-content" src="" alt="Full size image">
        <div class="close-button">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
        </div>
    </div>
</body>
</html> 