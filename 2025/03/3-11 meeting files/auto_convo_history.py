import os
import sys
from termcolor import colored
from openai import OpenAI

# MAJOR VARIABLES
MODEL = "gpt-4o-mini"  # Default model
MAX_RETRIES = 3
API_KEY = os.getenv("OPENAI_API_KEY")

def print_colored(text, color="white", on_color=None, attrs=None):
    """Print colored text using termcolor"""
    print(colored(text, color, on_color, attrs))

def initialize_client():
    """Initialize the OpenAI client with API key"""
    try:
        if not API_KEY:
            print_colored("Error: OPENAI_API_KEY environment variable not set.", "red")
            print_colored("Please set your OpenAI API key as an environment variable.", "yellow")
            sys.exit(1)
        
        print_colored("Initializing OpenAI client...", "cyan")
        return OpenAI(api_key=API_KEY)
    except Exception as e:
        print_colored(f"Error initializing OpenAI client: {str(e)}", "red")
        sys.exit(1)

def get_ai_response(client, user_input, previous_response_id=None):
    """Get response from OpenAI API with automatic conversation tracking"""
    try:
        print_colored("Sending request to OpenAI...", "cyan")
        
        # If we have a previous response ID, use it to continue the conversation
        if previous_response_id:
            response = client.responses.create(
                model=MODEL,
                previous_response_id=previous_response_id,
                input=user_input  # Using string input directly
            )
        else:
            # First message in the conversation
            response = client.responses.create(
                model=MODEL,
                input=user_input
            )
        
        return response
    except Exception as e:
        print_colored(f"Error getting AI response: {str(e)}", "red")
        return None

def main():
    """Main function to run the chat application"""
    print_colored("=" * 50, "blue")
    print_colored("Welcome to Terminal Chat with OpenAI", "green", attrs=["bold"])
    print_colored("Type 'exit', 'quit', or 'bye' to end the conversation", "yellow")
    print_colored("=" * 50, "blue")
    
    client = initialize_client()
    previous_response_id = None
    
    while True:
        # Get user input
        user_input = input(colored("\nYou: ", "green"))
        
        # Check if user wants to exit
        if user_input.lower() in ["exit", "quit", "bye"]:
            print_colored("\nGoodbye! Thanks for chatting.", "magenta")
            break
        
        # Get AI response
        response = get_ai_response(client, user_input, previous_response_id)
        
        if response:
            # Store the response ID for the next iteration
            previous_response_id = response.id
            
            # Print the AI response
            print(colored("\nAI: ", "blue") + response.output_text)
        else:
            print_colored("Failed to get a response. Please try again.", "red")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print_colored("\n\nChat terminated by user.", "yellow")
    except Exception as e:
        print_colored(f"\nAn unexpected error occurred: {str(e)}", "red") 