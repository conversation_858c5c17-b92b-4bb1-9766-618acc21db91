# OpenAI Responses API Demo

This repository demonstrates the usage of OpenAI's Responses API with web search capabilities and multimodal inputs.

## Features

- Basic response generation
- Web search-enabled responses
- Conversation continuation
- Response retrieval by ID
- Multimodal inputs (text + image)
- Support for both local images and URL images
- Colorful terminal output for better user experience

## Scripts

1. `responses_api_demo.py` - Uses local images for multimodal inputs
2. `responses_api_url_image_demo.py` - Uses URL images for multimodal inputs

## Requirements

- Python 3.7+
- OpenAI API key
- Pillow and NumPy for image handling
- Requests for URL image handling

## Installation

1. Clone this repository
2. Install the required packages:
   ```
   pip install -r requirements.txt
   ```
3. Set your OpenAI API key as an environment variable:
   ```
   # On Windows
   set OPENAI_API_KEY=your-api-key-here
   
   # On macOS/Linux
   export OPENAI_API_KEY=your-api-key-here
   ```

## Usage

### Local Image Demo

Run the script:
```
python responses_api_demo.py
```

The script will guide you through several demonstrations of the Responses API:
1. Creating a basic response
2. Retrieving a response by ID
3. Continuing a conversation
4. Creating a response with web search capabilities
5. Creating multimodal responses (text + local image)
6. Creating multimodal responses with web search

For the multimodal demo, you can either:
- Provide your own image path when prompted
- Let the script generate a sample image for you

### URL Image Demo

Run the script:
```
python responses_api_url_image_demo.py
```

This script offers the same functionality but uses images from URLs instead of local files:
1. Creating a basic response
2. Retrieving a response by ID
3. Continuing a conversation
4. Creating a response with web search capabilities
5. Creating multimodal responses (text + URL image)
6. Creating multimodal responses with web search

For the multimodal demo, you can either:
- Provide your own image URL when prompted
- Use the default image URL

## About the Responses API

The OpenAI Responses API is a new interface for interacting with OpenAI models. It provides:
- Structured outputs
- Tool use (like web search)
- Conversation history management
- Response retrieval
- Multimodal inputs (text + images)

For more information, visit the [OpenAI Cookbook example](https://cookbook.openai.com/examples/responses_api/responses_example). 