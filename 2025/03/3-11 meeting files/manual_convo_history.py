import os
import sys
from termcolor import colored
from openai import OpenAI

# Constants
MODEL = "gpt-4o-mini"
MAX_HISTORY = 20  # Maximum number of messages to keep in history
API_KEY = os.getenv("OPENAI_API_KEY")

def print_colored(text, color="white", prefix=""):
    """Print colored text with optional prefix"""
    print(colored(f"{prefix}{text}", color))

def get_ai_response(client, history):
    """Get response from OpenAI API"""
    print_colored("Thinking...", "yellow", "🤖 ")
    
    try:
        response = client.responses.create(
            model=MODEL,
            input=history,
            store=False
        )
        return response
    except Exception as e:
        print_colored(f"Error getting response: {str(e)}", "red", "❌ ")
        return None

def main():
    """Main function to run the chatbot"""
    # Check for API key
    if not API_KEY:
        print_colored("Error: OPENAI_API_KEY environment variable not set", "red", "❌ ")
        print_colored("Please set your OpenAI API key as an environment variable", "yellow")
        print_colored("Example: export OPENAI_API_KEY='your-api-key'", "yellow")
        sys.exit(1)
    
    try:
        client = OpenAI(api_key=API_KEY)
    except Exception as e:
        print_colored(f"Error initializing OpenAI client: {str(e)}", "red", "❌ ")
        sys.exit(1)
    
    # Initialize conversation history
    history = []
    
    # Welcome message
    print_colored("=" * 50, "cyan")
    print_colored("Welcome to Terminal ChatBot!", "cyan", "🤖 ")
    print_colored("Type 'exit' or 'quit' to end the conversation", "cyan")
    print_colored("Type 'clear' to clear the conversation history", "cyan")
    print_colored("=" * 50, "cyan")
    
    # Main conversation loop
    while True:
        # Get user input
        user_input = input(colored("You: ", "green"))
        
        # Check for exit command
        if user_input.lower() in ["exit", "quit"]:
            print_colored("Goodbye!", "cyan", "🤖 ")
            break
        
        # Check for clear command
        if user_input.lower() == "clear":
            history = []
            print_colored("Conversation history cleared!", "yellow", "🔄 ")
            continue
        
        # Add user message to history
        history.append({"role": "user", "content": user_input})
        
        # Get AI response
        response = get_ai_response(client, history)
        
        if response:
            # Print AI response
            print_colored(response.output_text, "blue", "🤖 ")
            
            # Add AI response to history
            history += [{"role": el.role, "content": el.content} for el in response.output]
            
            # Trim history if it gets too long
            if len(history) > MAX_HISTORY:
                # Remove oldest messages but keep the system message if present
                excess = len(history) - MAX_HISTORY
                if history[0]["role"] == "system":
                    history = [history[0]] + history[excess+1:]
                else:
                    history = history[excess:]
                print_colored(f"Trimmed conversation history to {MAX_HISTORY} messages", "yellow", "🔄 ")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print_colored("\nExiting chatbot. Goodbye!", "cyan", "🤖 ")
        sys.exit(0)
    except Exception as e:
        print_colored(f"Unexpected error: {str(e)}", "red", "❌ ")
        sys.exit(1) 