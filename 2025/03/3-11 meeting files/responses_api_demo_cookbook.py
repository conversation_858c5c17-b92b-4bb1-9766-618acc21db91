#!/usr/bin/env python3
# OpenAI Responses API Demo
# This script demonstrates the capabilities of the new Responses API

import os
import json
import time
from termcolor import colored
from openai import OpenAI
from openai import AsyncOpenAI
import asyncio

# MAJOR VARIABLES
API_KEY = os.getenv("OPENAI_API_KEY")
MODEL_BASIC = "gpt-4o-mini"
MODEL_ADVANCED = "gpt-4o"
IMAGE_URL = "https://upload.wikimedia.org/wikipedia/commons/thumb/1/15/Cat_August_2010-4.jpg/2880px-Cat_August_2010-4.jpg"

def print_step(step_name):
    """Print a step in the process with formatting"""
    print(colored(f"\n[STEP] {step_name}", "cyan", attrs=["bold"]))

def print_response(title, content):
    """Print a response with formatting"""
    print(colored(f"\n{title}:", "green"))
    print(colored(f"{content}", "white"))

def print_error(error_msg, exception=None):
    """Print an error message with formatting"""
    print(colored(f"\n[ERROR] {error_msg}", "red", attrs=["bold"]))
    if exception:
        print(colored(f"Exception: {str(exception)}", "red"))

def initialize_client():
    """Initialize the OpenAI client"""
    try:
        print_step("Initializing OpenAI client")
        client = OpenAI(api_key=API_KEY)
        print(colored("✓ Client initialized successfully", "green"))
        return client
    except Exception as e:
        print_error("Failed to initialize OpenAI client", e)
        exit(1)

def basic_response(client):
    """Demonstrate a basic response"""
    try:
        print_step("Creating a basic response")
        response = client.responses.create(
            model=MODEL_BASIC,
            input="Tell me a joke",
        )
        joke_text = response.output[0].content[0].text
        print_response("Joke", joke_text)
        return response
    except Exception as e:
        print_error("Failed to create basic response", e)
        return None

def retrieve_response(client, response_id):
    """Demonstrate retrieving a response by ID"""
    try:
        print_step("Retrieving response by ID")
        fetched_response = client.responses.retrieve(response_id=response_id)
        fetched_text = fetched_response.output[0].content[0].text
        print_response("Retrieved Response", fetched_text)
        return fetched_response
    except Exception as e:
        print_error("Failed to retrieve response", e)
        return None

def continue_conversation(client, previous_response):
    """Demonstrate continuing a conversation"""
    try:
        print_step("Continuing the conversation")
        response_two = client.responses.create(
            model=MODEL_BASIC,
            input="Tell me another",
            previous_response_id=previous_response.id
        )
        joke_text = response_two.output[0].content[0].text
        print_response("Second Joke", joke_text)
        return response_two
    except Exception as e:
        print_error("Failed to continue conversation", e)
        return None

def fork_conversation(client, previous_response):
    """Demonstrate forking a conversation"""
    try:
        print_step("Forking the conversation")
        response_forked = client.responses.create(
            model=MODEL_BASIC,
            input="I didn't like that joke, tell me another and tell me the difference between the two jokes",
            previous_response_id=previous_response.id
        )
        forked_text = response_forked.output[0].content[0].text
        print_response("Forked Response", forked_text)
        return response_forked
    except Exception as e:
        print_error("Failed to fork conversation", e)
        return None

def web_search_demo(client):
    """Demonstrate web search capability"""
    try:
        print_step("Performing web search")
        response = client.responses.create(
            model=MODEL_ADVANCED,
            input="What's the latest news about AI?",
            tools=[
                {
                    "type": "web_search"
                }
            ]
        )
        
        # Print the raw response for debugging
        print(colored("\nRaw Web Search Response:", "yellow"))
        print(json.dumps(response.output, default=lambda o: o.__dict__, indent=2))
        
        # Extract and print the text content
        for item in response.output:
            if hasattr(item, 'content') and item.content:
                for content in item.content:
                    if hasattr(content, 'text') and content.text:
                        print_response("Web Search Results", content.text)
        
        return response
    except Exception as e:
        print_error("Failed to perform web search", e)
        return None

def multimodal_demo(client):
    """Demonstrate multimodal capabilities with web search"""
    try:
        print_step("Performing multimodal interaction with web search")
        response_multimodal = client.responses.create(
            model=MODEL_ADVANCED,
            input=[
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "input_text", 
                            "text": "Come up with keywords related to the image, and search on the web using the search tool for any news related to the keywords, summarize the findings and cite the sources."
                        },
                        {
                            "type": "input_image", 
                            "image_url": IMAGE_URL
                        }
                    ]
                }
            ],
            tools=[
                {"type": "web_search"}
            ]
        )
        
        # Print the raw response for debugging
        print(colored("\nRaw Multimodal Response:", "yellow"))
        print(json.dumps(response_multimodal.__dict__, default=lambda o: o.__dict__, indent=2))
        
        # Extract and print the text content
        for item in response_multimodal.output:
            if hasattr(item, 'content') and item.content:
                for content in item.content:
                    if hasattr(content, 'text') and content.text:
                        print_response("Multimodal Results", content.text)
        
        return response_multimodal
    except Exception as e:
        print_error("Failed to perform multimodal interaction", e)
        return None

async def parallel_responses_demo():
    """Demonstrate parallel API calls with AsyncOpenAI"""
    try:
        print_step("Performing parallel API calls")
        async_client = AsyncOpenAI(api_key=API_KEY)
        
        # Create three tasks to run in parallel
        tasks = [
            async_client.responses.create(
                model=MODEL_BASIC,
                input=f"Tell me a joke about {topic}"
            ) for topic in ["cats", "dogs", "computers"]
        ]
        
        # Run the tasks concurrently
        responses = await asyncio.gather(*tasks)
        
        # Print the results
        for i, response in enumerate(responses):
            joke_text = response.output[0].content[0].text
            print_response(f"Parallel Joke {i+1}", joke_text)
            
        return responses
    except Exception as e:
        print_error("Failed to perform parallel API calls", e)
        return None

def main():
    """Main function to run the demo"""
    print(colored("\n=== OPENAI RESPONSES API DEMO ===", "magenta", attrs=["bold"]))
    
    # Check if API key is set
    if not API_KEY:
        print_error("OPENAI_API_KEY environment variable is not set")
        exit(1)
    
    # Initialize client
    client = initialize_client()
    
    # Basic response demo
    response = basic_response(client)
    if not response:
        return
    
    # Retrieve response demo
    fetched_response = retrieve_response(client, response.id)
    if not fetched_response:
        return
    
    # Continue conversation demo
    continued_response = continue_conversation(client, response)
    if not continued_response:
        return
    
    # Fork conversation demo
    forked_response = fork_conversation(client, response)
    if not forked_response:
        return
    
    # Web search demo
    web_search_response = web_search_demo(client)
    if not web_search_response:
        return
    
    # Multimodal demo
    multimodal_response = multimodal_demo(client)
    if not multimodal_response:
        return
    
    # Parallel responses demo (async)
    print_step("Running parallel responses demo")
    asyncio.run(parallel_responses_demo())
    
    print(colored("\n=== DEMO COMPLETED SUCCESSFULLY ===", "magenta", attrs=["bold"]))

if __name__ == "__main__":
    main()