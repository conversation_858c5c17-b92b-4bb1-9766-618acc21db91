#!/usr/bin/env python3
# Async OpenAI Responses API Demo
# This script demonstrates the usage of OpenAI's Responses API with async functionality

import os
import json
import sys
import asyncio
from termcolor import colored
from openai import AsyncOpenAI

# CONFIGURATION VARIABLES
API_KEY = os.getenv("OPENAI_API_KEY")
MODEL = "gpt-4o"
QUERIES = [
    "What's the latest news about AI?",
    "Explain quantum computing in simple terms",
    "What are the benefits of exercise?",
    "How does climate change affect biodiversity?"
]

def print_step(message):
    """Print a step in the process with color."""
    print(colored(f"\n[STEP] {message}", "cyan"))

def print_error(message, error=None):
    """Print an error message with color."""
    print(colored(f"\n[ERROR] {message}", "red"))
    if error:
        print(colored(f"Details: {str(error)}", "red"))

def print_success(message):
    """Print a success message with color."""
    print(colored(f"\n[SUCCESS] {message}", "green"))

def print_info(message):
    """Print an informational message with color."""
    print(colored(f"\n[INFO] {message}", "yellow"))

def setup_async_client():
    """Set up and return the AsyncOpenAI client."""
    try:
        if not API_KEY:
            print_error("OpenAI API key not found. Please set the OPENAI_API_KEY environment variable.")
            sys.exit(1)
        
        print_step("Setting up AsyncOpenAI client")
        client = AsyncOpenAI(api_key=API_KEY)
        print_success("AsyncOpenAI client initialized successfully")
        return client
    except Exception as e:
        print_error("Failed to initialize AsyncOpenAI client", e)
        sys.exit(1)

async def create_response(client, query, index):
    """Create a response for a query."""
    try:
        print_step(f"Creating response {index+1} for query: '{query}'")
        response = await client.responses.create(
            model=MODEL,
            input=query,
        )
        print_success(f"Response {index+1} created successfully")
        return response
    except Exception as e:
        print_error(f"Failed to create response {index+1}", e)
        return None

async def create_web_search_response(client, query, index):
    """Create a response with web search tool."""
    try:
        print_step(f"Creating web search response {index+1} for query: '{query}'")
        response = await client.responses.create(
            model=MODEL,
            input=query,
            tools=[
                {
                    "type": "web_search"
                }
            ]
        )
        print_success(f"Web search response {index+1} created successfully")
        return response
    except Exception as e:
        print_error(f"Failed to create web search response {index+1}", e)
        return None

def print_response_text(response, index):
    """Print the text content of a response."""
    try:
        if not response:
            return
        
        # Find the message output in the response
        for output in response.output:
            if hasattr(output, 'content') and output.content:
                for content in output.content:
                    if hasattr(content, 'text') and content.text:
                        print_info(f"Response {index+1} content:")
                        print(content.text[:200] + "..." if len(content.text) > 200 else content.text)
                        print("\n" + "-"*80 + "\n")
                        return
        
        # If we didn't find text content in the expected structure
        print_info(f"Response {index+1} structure:")
        print(json.dumps(response.model_dump(), indent=2))
    except Exception as e:
        print_error(f"Failed to print response {index+1} text", e)

async def run_parallel_responses():
    """Run multiple responses in parallel."""
    client = setup_async_client()
    
    print_info("Creating multiple responses in parallel...")
    tasks = [create_response(client, query, i) for i, query in enumerate(QUERIES)]
    responses = await asyncio.gather(*tasks)
    
    for i, response in enumerate(responses):
        if response:
            print_response_text(response, i)
    
    return responses

async def run_parallel_web_search():
    """Run multiple web search responses in parallel."""
    client = setup_async_client()
    
    print_info("Creating multiple web search responses in parallel...")
    tasks = [create_web_search_response(client, query, i) for i, query in enumerate(QUERIES[:2])]  # Using only first 2 queries
    responses = await asyncio.gather(*tasks)
    
    for i, response in enumerate(responses):
        if response:
            print_response_text(response, i)

async def main():
    """Main async function to demonstrate the Responses API."""
    print_info("Async OpenAI Responses API Demo")
    
    # Demo 1: Parallel basic responses
    await run_parallel_responses()
    
    # Demo 2: Parallel web search responses
    await run_parallel_web_search()
    
    print_info("Async demo completed!")

if __name__ == "__main__":
    asyncio.run(main()) 