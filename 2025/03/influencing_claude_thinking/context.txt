<PERSON>'s law

Article
Talk
Read
Edit
View history

Tools
Appearance hide
Text

Small

Standard

Large
Width

Standard

Wide
Color (beta)

Automatic

Light

Dark
This is a good article. Click here for more information.
From Wikipedia, the free encyclopedia
refer to caption
A semi-log plot of transistor counts for microprocessors against dates of introduction, nearly doubling every two years
Semiconductor
device
fabrication

MOSFET scaling
(process nodes)
20 μm – 1968
10 μm – 1971
6 μm – 1974
3 μm – 1977
 1.5 μm – 1981
1 μm – 1984
800 nm – 1987
600 nm – 1990
350 nm – 1993
250 nm – 1996
180 nm – 1999
130 nm – 2001
90 nm – 2003
65 nm – 2005
45 nm – 2007
32 nm – 2009
28 nm – 2010
22 nm – 2012
14 nm – 2014
10 nm – 2016
7 nm – 2018
5 nm – 2020
3 nm – 2022
Future
2 nm ~ 2025
1 nm ~ 2027
Half-nodes
Density
CMOS
Device (multi-gate)
<PERSON>'s law
Transistor count
Semiconductor
Industry
Nanoelectronics
vte
Futures studies

Concepts
Accelerating changeCashless societyGlobal catastrophic riskFuture EarthMathematicsRaceClimateSpace explorationUniverseHistorical materialismKondratiev waveKardashev scaleMoore's lawPeak oilPopulation cycleResource depletionSingularityS<PERSON><PERSON>'s law
Techniques
BackcastingCausal layered analysisChain-linked modelConsensus forecastCross impact analysisDelphi Real-time DelphiForesightFuture-proofFutures wheelFuture workshopHorizon scanningReference class forecastingScenario planningSystems analysisThreatcastingTrend analysis
Technology assessment and forecasting
Critical designDesign fictionExploratory engineeringFTAHype cycleScience fiction prototypingSpeculative designTRLTechnology scouting
Related topics
FutarchyTranshumanism
vte
Moore's law is the observation that the number of transistors in an integrated circuit (IC) doubles about every two years. Moore's law is an observation and projection of a historical trend. Rather than a law of physics, it is an empirical relationship. It is an experience-curve law, a type of law quantifying efficiency gains from experience in production.

The observation is named after Gordon Moore, the co-founder of Fairchild Semiconductor and Intel and former CEO of the latter, who in 1965 noted that the number of components per integrated circuit had been doubling every year,[a] and projected this rate of growth would continue for at least another decade. In 1975, looking forward to the next decade, he revised the forecast to doubling every two years, a compound annual growth rate (CAGR) of 41%. Moore's empirical evidence did not directly imply that the historical trend would continue, nevertheless, his prediction has held since 1975 and has since become known as a law.

Moore's prediction has been used in the semiconductor industry to guide long-term planning and to set targets for research and development, thus functioning to some extent as a self-fulfilling prophecy. Advancements in digital electronics, such as the reduction in quality-adjusted microprocessor prices, the increase in memory capacity (RAM and flash), the improvement of sensors, and even the number and size of pixels in digital cameras, are strongly linked to Moore's law. These ongoing changes in digital electronics have been a driving force of technological and social change, productivity, and economic growth.

Industry experts have not reached a consensus on exactly when Moore's law will cease to apply. Microprocessor architects report that semiconductor advancement has slowed industry-wide since around 2010, slightly below the pace predicted by Moore's law. In September 2022, Nvidia CEO Jensen Huang considered Moore's law dead,[2] while Intel CEO Pat Gelsinger was of the opposite view.[3]

History
In 1959, Douglas Engelbart studied the projected downscaling of integrated circuit (IC) size, publishing his results in the article "Microelectronics, and the Art of Similitude".[4][5][6] Engelbart presented his findings at the 1960 International Solid-State Circuits Conference, where Moore was present in the audience.[7]

In 1965, Gordon Moore, who at the time was working as the director of research and development at Fairchild Semiconductor, was asked to contribute to the thirty-fifth-anniversary issue of Electronics magazine with a prediction on the future of the semiconductor components industry over the next ten years.[8] His response was a brief article entitled "Cramming more components onto integrated circuits".[1][9][b] Within his editorial, he speculated that by 1975 it would be possible to contain as many as 65000 components on a single quarter-square-inch (~ 1.6 cm2) semiconductor.

The complexity for minimum component costs has increased at a rate of roughly a factor of two per year. Certainly over the short term this rate can be expected to continue, if not to increase. Over the longer term, the rate of increase is a bit more uncertain, although there is no reason to believe it will not remain nearly constant for at least 10 years.[1]

Moore posited a log–linear relationship between device complexity (higher circuit density at reduced cost) and time.[12][13] In a 2015 interview, Moore noted of the 1965 article: "... I just did a wild extrapolation saying it's going to continue to double every year for the next 10 years."[14] One historian of the law cites Stigler's law of eponymy, to introduce the fact that the regular doubling of components was known to many working in the field.[13]

In 1974, Robert H. Dennard at IBM recognized the rapid MOSFET scaling technology and formulated what became known as Dennard scaling, which describes that as MOS transistors get smaller, their power density stays constant such that the power use remains in proportion with area.[15][16] Evidence from the semiconductor industry shows that this inverse relationship between power density and areal density broke down in the mid-2000s.[17]

At the 1975 IEEE International Electron Devices Meeting, Moore revised his forecast rate,[18][19] predicting semiconductor complexity would continue to double annually until about 1980, after which it would decrease to a rate of doubling approximately every two years.[19][20][21] He outlined several contributing factors for this exponential behavior:[12][13]

The advent of metal–oxide–semiconductor (MOS) technology
The exponential rate of increase in die sizes, coupled with a decrease in defective densities, with the result that semiconductor manufacturers could work with larger areas without losing reduction yields
Finer minimum dimensions
What Moore called "circuit and device cleverness"
Shortly after 1975, Caltech professor Carver Mead popularized the term Moore's law.[22][23] Moore's law eventually came to be widely accepted as a goal for the semiconductor industry, and it was cited by competitive semiconductor manufacturers as they strove to increase processing power. Moore viewed his eponymous law as surprising and optimistic: "Moore's law is a violation of Murphy's law. Everything gets better and better."[24] The observation was even seen as a self-fulfilling prophecy.[25][26]

The doubling period is often misquoted as 18 months because of a separate prediction by Moore's colleague, Intel executive David House.[27] In 1975, House noted that Moore's revised law of doubling transistor count every 2 years in turn implied that computer chip performance would roughly double every 18 months,[28] with no increase in power consumption.[29] Mathematically, Moore's law predicted that transistor count would double every 2 years due to shrinking transistor dimensions and other improvements.[30] As a consequence of shrinking dimensions, Dennard scaling predicted that power consumption per unit area would remain constant. Combining these effects, David House deduced that computer chip performance would roughly double every 18 months. Also due to Dennard scaling, this increased performance would not be accompanied by increased power, i.e., the energy-efficiency of silicon-based computer chips roughly doubles every 18 months. Dennard scaling ended in the 2000s.[17] Koomey later showed that a similar rate of efficiency improvement predated silicon chips and Moore's law, for technologies such as vacuum tubes.

Large early portable computer next to a modern smartphone
A 1982 Osborne Executive portable computer, with a 4 MHz 8-bit Zilog Z80 CPU, and a 2007 Apple iPhone with a 412 MHz 32-bit ARM11 CPU; the Executive has 100 times the weight, almost 500 times the volume, approximately 10 times the inflation-adjusted cost, and 1/100th the clock frequency of the smartphone.
Microprocessor architects report that since around 2010, semiconductor advancement has slowed industry-wide below the pace predicted by Moore's law.[17] Brian Krzanich, the former CEO of Intel, cited Moore's 1975 revision as a precedent for the current deceleration, which results from technical challenges and is "a natural part of the history of Moore's law".[31][32][33] The rate of improvement in physical dimensions known as Dennard scaling also ended in the mid-2000s. As a result, much of the semiconductor industry has shifted its focus to the needs of major computing applications rather than semiconductor scaling.[25][34][17] Nevertheless, leading semiconductor manufacturers TSMC and Samsung Electronics have claimed to keep pace with Moore's law[35][36][37][38][39][40] with 10, 7, and 5 nm nodes in mass production.[35][36][41][42][43]

Moore's second law
Further information: Moore's second law
As the cost of computer power to the consumer falls, the cost for producers to fulfill Moore's law follows an opposite trend: R&D, manufacturing, and test costs have increased steadily with each new generation of chips. The cost of the tools, principally EUVL (Extreme ultraviolet lithography), used to manufacture chips doubles every 4 years.[44] Rising manufacturing costs are an important consideration for the sustaining of Moore's law.[45] This led to the formulation of Moore's second law, also called Rock's law (named after Arthur Rock), which is that the capital cost of a semiconductor fabrication plant also increases exponentially over time.[46][47]

Major enabling factors
See also: List of semiconductor scale examples and Transistor count
A semi-log plot of NAND flash design rule dimensions in nanometers against dates of introduction. The downward linear regression indicates an exponential decrease in feature dimensions over time.
The trend of MOSFET scaling for NAND flash memory allows the doubling of floating-gate MOSFET components manufactured in the same wafer area in less than 18 months.
Numerous innovations by scientists and engineers have sustained Moore's law since the beginning of the IC era. Some of the key innovations are listed below, as examples of breakthroughs that have advanced integrated circuit and semiconductor device fabrication technology, allowing transistor counts to grow by more than seven orders of magnitude in less than five decades.

Integrated circuit (IC): The raison d'être for Moore's law. The germanium hybrid IC was invented by Jack Kilby at Texas Instruments in 1958,[48] followed by the invention of the silicon monolithic IC chip by Robert Noyce at Fairchild Semiconductor in 1959.[49]
Complementary metal–oxide–semiconductor (CMOS): The CMOS process was invented by Chih-Tang Sah and Frank Wanlass at Fairchild Semiconductor in 1963.[50][51][52]
Dynamic random-access memory (DRAM): DRAM was developed by Robert H. Dennard at IBM in 1967.[53]
Chemically amplified photoresist: Invented by Hiroshi Ito, C. Grant Willson and J. M. J. Fréchet at IBM circa 1980,[54][55][56] which was 5–10 times more sensitive to ultraviolet light.[57] IBM introduced chemically amplified photoresist for DRAM production in the mid-1980s.[58][59]
Deep UV excimer laser photolithography: Invented by Kanti Jain[60] at IBM circa 1980.[61][62][63] Prior to this, excimer lasers had been mainly used as research devices since their development in the 1970s.[64][65] From a broader scientific perspective, the invention of excimer laser lithography has been highlighted as one of the major milestones in the 50-year history of the laser.[66][67]
Interconnect innovations: Interconnect innovations of the late 1990s, including chemical-mechanical polishing or chemical mechanical planarization (CMP), trench isolation, and copper interconnects—although not directly a factor in creating smaller transistors—have enabled improved wafer yield, additional layers of metal wires, closer spacing of devices, and lower electrical resistance.[68][69][70]
Computer industry technology road maps predicted in 2001 that Moore's law would continue for several generations of semiconductor chips.[71]

Recent trends

This section is in list format but may read better as prose. You can help by converting this section, if appropriate. Editing help is available. (March 2025)
animated plot showing electron density and current as gate voltage varies
A simulation of electron density as gate voltage (Vg) varies in a nanowire MOSFET. The threshold voltage is around 0.45 V. Nanowire MOSFETs lie toward the end of the ITRS road map for scaling devices below 10 nm gate lengths.
One of the key technical challenges of engineering future nanoscale transistors is the design of gates. As device dimensions shrink, controlling the current flow in the thin channel becomes more difficult. Modern nanoscale transistors typically take the form of multi-gate MOSFETs, with the FinFET being the most common nanoscale transistor. The FinFET has gate dielectric on three sides of the channel. In comparison, the gate-all-around MOSFET (GAAFET) structure has even better gate control.

A gate-all-around MOSFET (GAAFET) was first demonstrated in 1988, by a Toshiba research team led by Fujio Masuoka, who demonstrated a vertical nanowire GAAFET that he called a surrounding gate transistor (SGT).[72][73] Masuoka, best known as the inventor of flash memory, later left Toshiba and founded Unisantis Electronics in 2004 to research surrounding-gate technology along with Tohoku University.[74]
In 2006, a team of Korean researchers from the Korea Advanced Institute of Science and Technology (KAIST) and the National Nano Fab Center developed a 3 nm transistor, the world's smallest nanoelectronic device at the time, based on FinFET technology.[75][76]
In 2010, researchers at the Tyndall National Institute in Cork, Ireland announced a junctionless transistor. A control gate wrapped around a silicon nanowire can control the passage of electrons without the use of junctions or doping. They claim these may be produced at 10 nm scale using existing fabrication techniques.[77]
In 2011, researchers at the University of Pittsburgh announced the development of a single-electron transistor, 1.5 nm in diameter, made out of oxide-based materials. Three wires converge on a central island that can house one or two electrons. Electrons tunnel from one wire to another through the island. Conditions on the third wire result in distinct conductive properties including the ability of the transistor to act as a solid-state memory.[78] Nanowire transistors could spur the creation of microscopic computers.[79][80][81]
In 2012, a research team at the University of New South Wales announced the development of the first working transistor consisting of a single atom placed precisely in a silicon crystal (not just picked from a large sample of random transistors).[82] Moore's law predicted this milestone to be reached for ICs in the lab by 2020.
In 2015, IBM demonstrated 7 nm node chips with silicon–germanium transistors produced using EUVL. The company believed this transistor density would be four times that of the then-current 14 nm chips.[83]
Samsung and TSMC plan to manufacture 3 nm GAAFET nodes by 2021–2022.[84][85] Note that node names, such as 3 nm, have no relation to the physical size of device elements (transistors).
A Toshiba research team including T. Imoto, M. Matsui and C. Takubo developed a system block module wafer bonding process for manufacturing three-dimensional integrated circuit (3D IC) packages in 2001.[86][87] In April 2007, Toshiba introduced an eight-layer 3D IC, the 16 GB THGAM embedded NAND flash memory chip that was manufactured with eight stacked 2 GB NAND flash chips.[88] In September 2007, Hynix introduced 24-layer 3D IC, a 16 GB flash memory chip that was manufactured with 24 stacked NAND flash chips using a wafer bonding process.[89]
V-NAND, also known as 3D NAND, allows flash memory cells to be stacked vertically using charge trap flash technology originally presented by John Szedon in 1967, significantly increasing the number of transistors on a flash memory chip. 3D NAND was first announced by Toshiba in 2007.[90] V-NAND was first commercially manufactured by Samsung Electronics in 2013.[91][92][93]
In 2008, researchers at HP Labs announced a working memristor, a fourth basic passive circuit element whose existence only had been theorized previously. The memristor's unique properties permit the creation of smaller and better-performing electronic devices.[94]
In 2014, bioengineers at Stanford University developed a circuit modeled on the human brain. Sixteen Neurocore chips simulate one million neurons and billions of synaptic connections, claimed to be 9000 times faster as well as more energy efficient than a typical PC.[95]
In 2015, Intel and Micron announced 3D XPoint, a non-volatile memory claimed to be significantly faster with similar density compared to NAND. Production scheduled to begin in 2016 was delayed until the second half of 2017.[96][97][98]
In 2017, Samsung combined its V-NAND technology with eUFS 3D IC stacking to produce a 512 GB flash memory chip, with eight stacked 64-layer V-NAND dies.[99] In 2019, Samsung produced a 1 TB flash chip with eight stacked 96-layer V-NAND dies, along with quad-level cell (QLC) technology (4-bit per transistor),[100][101] equivalent to 2 trillion transistors, the highest transistor count of any IC chip.
In 2020, Samsung Electronics planned to produce the 5 nm node, using FinFET and EUV technology.[36][needs update]
In May 2021, IBM announced the creation of the first 2 nm computer chip, with parts supposedly being smaller than human DNA.[102]
Microprocessor architects report that semiconductor advancement has slowed industry-wide since around 2010, below the pace predicted by Moore's law.[17] Brian Krzanich, the former CEO of Intel, announced, "Our cadence today is closer to two and a half years than two."[103] Intel stated in 2015 that improvements in MOSFET devices have slowed, starting at the 22 nm feature width around 2012, and continuing at 14 nm.[104] Pat Gelsinger, Intel CEO, stated at the end of 2023 that "we're no longer in the golden era of Moore's Law, it's much, much harder now, so we're probably doubling effectively closer to every three years now, so we've definitely seen a slowing."[105]

The physical limits to transistor scaling have been reached due to source-to-drain leakage, limited gate metals and limited options for channel material. Other approaches are being investigated, which do not rely on physical scaling. These include the spin state of electron spintronics, tunnel junctions, and advanced confinement of channel materials via nano-wire geometry.[106] Spin-based logic and memory options are being developed actively in labs.[107][108]

Alternative materials research
The vast majority of current transistors on ICs are composed principally of doped silicon and its alloys. As silicon is fabricated into single nanometer transistors, short-channel effects adversely changes desired material properties of silicon as a functional transistor. Below are several non-silicon substitutes in the fabrication of small nanometer transistors.

One proposed material is indium gallium arsenide, or InGaAs. Compared to their silicon and germanium counterparts, InGaAs transistors are more promising for future high-speed, low-power logic applications. Because of intrinsic characteristics of III–V compound semiconductors, quantum well and tunnel effect transistors based on InGaAs have been proposed as alternatives to more traditional MOSFET designs.

In the early 2000s, the atomic layer deposition high-κ film and pitch double-patterning processes were invented by Gurtej Singh Sandhu at Micron Technology, extending Moore's law for planar CMOS technology to 30 nm class and smaller.
In 2009, Intel announced the development of 80 nm InGaAs quantum well transistors. Quantum well devices contain a material sandwiched between two layers of material with a wider band gap. Despite being double the size of leading pure silicon transistors at the time, the company reported that they performed equally as well while consuming less power.[109]
In 2011, researchers at Intel demonstrated 3-D tri-gate InGaAs transistors with improved leakage characteristics compared to traditional planar designs. The company claims that their design achieved the best electrostatics of any III–V compound semiconductor transistor.[110] At the 2015 International Solid-State Circuits Conference, Intel mentioned the use of III–V compounds based on such an architecture for their 7 nm node.[111][112]
In 2011, researchers at the University of Texas at Austin developed an InGaAs tunneling field-effect transistors capable of higher operating currents than previous designs. The first III–V TFET designs were demonstrated in 2009 by a joint team from Cornell University and Pennsylvania State University.[113][114]
In 2012, a team in MIT's Microsystems Technology Laboratories developed a 22 nm transistor based on InGaAs that, at the time, was the smallest non-silicon transistor ever built. The team used techniques used in silicon device fabrication and aimed for better electrical performance and a reduction to 10-nanometer scale.[115]
Biological computing research shows that biological material has superior information density and energy efficiency compared to silicon-based computing.[116]

refer to caption
Scanning probe microscopy image of graphene in its hexagonal lattice structure
Various forms of graphene are being studied for graphene electronics, e.g. graphene nanoribbon transistors have shown promise since its appearance in publications in 2008. (Bulk graphene has a band gap of zero and thus cannot be used in transistors because of its constant conductivity, an inability to turn off. The zigzag edges of the nanoribbons introduce localized energy states in the conduction and valence bands and thus a bandgap that enables switching when fabricated as a transistor. As an example, a typical GNR of width of 10 nm has a desirable bandgap energy of 0.4 eV.[117][118]) More research will need to be performed, however, on sub-50 nm graphene layers, as its resistivity value increases and thus electron mobility decreases.[117]

Forecasts and roadmaps
In April 2005, Gordon Moore stated in an interview that the projection cannot be sustained indefinitely: "It can't continue forever. The nature of exponentials is that you push them out and eventually disaster happens." He also noted that transistors eventually would reach the limits of miniaturization at atomic levels:

In terms of size [of transistors] you can see that we're approaching the size of atoms which is a fundamental barrier, but it'll be two or three generations before we get that far—but that's as far out as we've ever been able to see. We have another 10 to 20 years before we reach a fundamental limit. By then they'll be able to make bigger chips and have transistor budgets in the billions.[119]

— Gordon Moore in 2006
In 2016 the International Technology Roadmap for Semiconductors, after using Moore's Law to drive the industry since 1998, produced its final roadmap. It no longer centered its research and development plan on Moore's law. Instead, it outlined what might be called the More than Moore strategy in which the needs of applications drive chip development, rather than a focus on semiconductor scaling. Application drivers range from smartphones to AI to data centers.[120]

IEEE began a road-mapping initiative in 2016, Rebooting Computing, named the International Roadmap for Devices and Systems (IRDS).[121]

Some forecasters, including Gordon Moore,[122] predict that Moore's law will end by around 2025.[123][120][124] Although Moore's Law will reach a physical limit, some forecasters are optimistic about the continuation of technological progress in a variety of other areas, including new chip architectures, quantum computing, and AI and machine learning.[125][126] Nvidia CEO Jensen Huang declared Moore's law dead in 2022;[2] several days later, Intel CEO Pat Gelsinger countered with the opposite claim.[3]

Consequences
Digital electronics have contributed to world economic growth in the late twentieth and early twenty-first centuries.[127] The primary driving force of economic growth is the growth of productivity,[128] which Moore's law factors into. Moore (1995) expected that "the rate of technological progress is going to be controlled from financial realities".[129] The reverse could and did occur around the late-1990s, however, with economists reporting that "Productivity growth is the key economic indicator of innovation."[130] Moore's law describes a driving force of technological and social change, productivity, and economic growth.[131][132][128]

An acceleration in the rate of semiconductor progress contributed to a surge in U.S. productivity growth,[133][134][135] which reached 3.4% per year in 1997–2004, outpacing the 1.6% per year during both 1972–1996 and 2005–2013.[136] As economist Richard G. Anderson notes, "Numerous studies have traced the cause of the productivity acceleration to technological innovations in the production of semiconductors that sharply reduced the prices of such components and of the products that contain them (as well as expanding the capabilities of such products)."[137]

The primary negative implication of Moore's law is that obsolescence pushes society up against the Limits to Growth. As technologies continue to rapidly improve, they render predecessor technologies obsolete. In situations in which security and survivability of hardware or data are paramount, or in which resources are limited, rapid obsolescence often poses obstacles to smooth or continued operations.[138]


Intel transistor gate length trend. Transistor scaling
Other formulations and similar observations
Several measures of digital technology are improving at exponential rates related to Moore's law, including the size, cost, density, and speed of components. Moore wrote only about the density of components, "a component being a transistor, resistor, diode or capacitor",[129] at minimum cost.

Transistors per integrated circuit – The most popular formulation is of the doubling of the number of transistors on ICs every two years. At the end of the 1970s, Moore's law became known as the limit for the number of transistors on the most complex chips. The graph at the top of this article shows this trend holds true today. As of 2022, the commercially available processor possessing one of the highest numbers of transistors is an AD102 graphics processor with more than 76,3 billion transistors.[139]

Density at minimum cost per transistor – This is the formulation given in Moore's 1965 paper.[1] It is not just about the density of transistors that can be achieved, but about the density of transistors at which the cost per transistor is the lowest.[140]

As more transistors are put on a chip, the cost to make each transistor decreases, but the chance that the chip will not work due to a defect increases. In 1965, Moore examined the density of transistors at which cost is minimized, and observed that, as transistors were made smaller through advances in photolithography, this number would increase at "a rate of roughly a factor of two per year".[1]

Dennard scaling – This posits that power usage would decrease in proportion to area (both voltage and current being proportional to length) of transistors. Combined with Moore's law, performance per watt would grow at roughly the same rate as transistor density, doubling every 1–2 years. According to Dennard scaling transistor dimensions would be scaled by 30% (0.7×) every technology generation, thus reducing their area by 50%. This would reduce the delay by 30% (0.7×) and therefore increase operating frequency by about 40% (1.4×). Finally, to keep electric field constant, voltage would be reduced by 30%, reducing energy by 65% and power (at 1.4× frequency) by 50%.[c] Therefore, in every technology generation transistor density would double, circuit becomes 40% faster, while power consumption (with twice the number of transistors) stays the same.[141] Dennard scaling ended in 2005–2010, due to leakage currents.[17]

The exponential processor transistor growth predicted by Moore does not always translate into exponentially greater practical CPU performance. Since around 2005–2007, Dennard scaling has ended, so even though Moore's law continued after that, it has not yielded proportional dividends in improved performance.[15][142] The primary reason cited for the breakdown is that at small sizes, current leakage poses greater challenges, and also causes the chip to heat up, which creates a threat of thermal runaway and therefore, further increases energy costs.[15][142][17]

The breakdown of Dennard scaling prompted a greater focus on multicore processors, but the gains offered by switching to more cores are lower than the gains that would be achieved had Dennard scaling continued.[143][144] In another departure from Dennard scaling, Intel microprocessors adopted a non-planar tri-gate FinFET at 22 nm in 2012 that is faster and consumes less power than a conventional planar transistor.[145] The rate of performance improvement for single-core microprocessors has slowed significantly.[146] Single-core performance was improving by 52% per year in 1986–2003 and 23% per year in 2003–2011, but slowed to just seven percent per year in 2011–2018.[146]

Quality adjusted price of IT equipment – The price of information technology (IT), computers and peripheral equipment, adjusted for quality and inflation, declined 16% per year on average over the five decades from 1959 to 2009.[147][148] The pace accelerated, however, to 23% per year in 1995–1999 triggered by faster IT innovation,[130] and later, slowed to 2% per year in 2010–2013.[147][149]

While quality-adjusted microprocessor price improvement continues,[150] the rate of improvement likewise varies, and is not linear on a log scale. Microprocessor price improvement accelerated during the late 1990s, reaching 60% per year (halving every nine months) versus the typical 30% improvement rate (halving every two years) during the years earlier and later.[151][152] Laptop microprocessors in particular improved 25–35% per year in 2004–2010, and slowed to 15–25% per year in 2010–2013.[153]

The number of transistors per chip cannot explain quality-adjusted microprocessor prices fully.[151][154][155] Moore's 1995 paper does not limit Moore's law to strict linearity or to transistor count, "The definition of 'Moore's Law' has come to refer to almost anything related to the semiconductor industry that on a semi-log plot approximates a straight line. I hesitate to review its origins and by doing so restrict its definition."[129]

Hard disk drive areal density – A similar prediction (sometimes called Kryder's law) was made in 2005 for hard disk drive areal density.[156] The prediction was later viewed as over-optimistic. Several decades of rapid progress in areal density slowed around 2010, from 30 to 100% per year to 10–15% per year, because of noise related to smaller grain size of the disk media, thermal stability, and writability using available magnetic fields.[157][158]

Fiber-optic capacity – The number of bits per second that can be sent down an optical fiber increases exponentially, faster than Moore's law. Keck's law, in honor of Donald Keck.[159]

Network capacity – According to Gerald Butters,[160][161] the former head of Lucent's Optical Networking Group at Bell Labs, there is another version, called Butters' Law of Photonics,[162] a formulation that deliberately parallels Moore's law. Butters' law says that the amount of data coming out of an optical fiber is doubling every nine months.[163] Thus, the cost of transmitting a bit over an optical network decreases by half every nine months. The availability of wavelength-division multiplexing (sometimes called WDM) increased the capacity that could be placed on a single fiber by as much as a factor of 100. Optical networking and dense wavelength-division multiplexing (DWDM) is rapidly bringing down the cost of networking, and further progress seems assured. As a result, the wholesale price of data traffic collapsed in the dot-com bubble. Nielsen's Law says that the bandwidth available to users increases by 50% annually.[164]

Pixels per dollar – Similarly, Barry Hendy of Kodak Australia has plotted pixels per dollar as a basic measure of value for a digital camera, demonstrating the historical linearity (on a log scale) of this market and the opportunity to predict the future trend of digital camera price, LCD and LED screens, and resolution.[165][166][167][168]

The great Moore's law compensator (TGMLC), also known as Wirth's law – generally is referred to as software bloat and is the principle that successive generations of computer software increase in size and complexity, thereby offsetting the performance gains predicted by Moore's law. In a 2008 article in InfoWorld, Randall C. Kennedy,[169] formerly of Intel, introduces this term using successive versions of Microsoft Office between the year 2000 and 2007 as his premise. Despite the gains in computational performance during this time period according to Moore's law, Office 2007 performed the same task at half the speed on a prototypical year 2007 computer as compared to Office 2000 on a year 2000 computer.

Library expansion – was calculated in 1945 by Fremont Rider to double in capacity every 16 years, if sufficient space were made available.[170] He advocated replacing bulky, decaying printed works with miniaturized microform analog photographs, which could be duplicated on-demand for library patrons or other institutions. He did not foresee the digital technology that would follow decades later to replace analog microform with digital imaging, storage, and transmission media. Automated, potentially lossless digital technologies allowed vast increases in the rapidity of information growth in an era that now sometimes is called the Information Age.

Carlson curve – is a term coined by The Economist[171] to describe the biotechnological equivalent of Moore's law, and is named after author Rob Carlson.[172] Carlson accurately predicted that the doubling time of DNA sequencing technologies (measured by cost and performance) would be at least as fast as Moore's law.[173] Carlson Curves illustrate the rapid (in some cases hyperexponential) decreases in cost, and increases in performance, of a variety of technologies, including DNA sequencing, DNA synthesis, and a range of physical and computational tools used in protein expression and in determining protein structures.

Eroom's law – is a pharmaceutical drug development observation that was deliberately written as Moore's Law spelled backward in order to contrast it with the exponential advancements of other forms of technology (such as transistors) over time. It states that the cost of developing a new drug roughly doubles every nine years.

Experience curve effects says that each doubling of the cumulative production of virtually any product or service is accompanied by an approximate constant percentage reduction in the unit cost. The acknowledged first documented qualitative description of this dates from 1885.[174][175] A power curve was used to describe this phenomenon in a 1936 discussion of the cost of airplanes.[176]

Edholm's law – Phil Edholm observed that the bandwidth of telecommunication networks (including the Internet) is doubling every 18 months.[177] The bandwidths of online communication networks has risen from bits per second to terabits per second. The rapid rise in online bandwidth is largely due to the same MOSFET scaling that enabled Moore's law, as telecommunications networks are built from MOSFETs.[178]

Haitz's law predicts that the brightness of LEDs increases as their manufacturing cost goes down.

Swanson's law is the observation that the price of solar photovoltaic modules tends to drop 20 percent for every doubling of cumulative shipped volume. At present rates, costs go down 75% about every 10 years.

See also