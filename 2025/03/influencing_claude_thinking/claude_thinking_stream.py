import os
import sys
import json
from termcolor import colored
import anthropic

# CONSTANTS
API_KEY = os.getenv("ANTHROPIC_API_KEY")
MODEL = "claude-3-7-sonnet-20250219"
MAX_TOKENS = 63000
THINKING_BUDGET = 50000
PROMPT = "What is 27 * 453?"
THINKING_GUIDANCE = "when you are thinking, consider this problem from 10 distinct perspectives in full.  before you start thinking always say to yourself 'I am ready to tackle this problem' and then start thinking."

def stream_with_thinking(prompt):
    """Stream <PERSON>'s thinking and response with colored output and save as JSON."""
    try:
        print(colored("\n[INFO] Initializing Anthropic client...", "cyan"))
        client = anthropic.Anthropic(api_key=API_KEY)
        
        if not API_KEY:
            print(colored("[ERROR] ANTHROPIC_API_KEY environment variable not set!", "red"))
            sys.exit(1)
        
        # Append thinking guidance if it's not empty
        full_prompt = prompt
        if THINKING_GUIDANCE:
            full_prompt = f"{prompt}\n\n{THINKING_GUIDANCE}"
            print(colored(f"[INFO] Added thinking guidance to prompt", "cyan"))
        
        print(colored(f"[INFO] Sending prompt to {MODEL}...", "cyan"))
        print(colored(f"[INFO] Prompt: \"{full_prompt}\"", "yellow"))
        
        with client.messages.stream(
            model=MODEL,
            max_tokens=MAX_TOKENS,
            thinking={
                "type": "enabled",
                "budget_tokens": THINKING_BUDGET
            },
            messages=[{
                "role": "user",
                "content": full_prompt
            }]
        ) as stream:
            print(colored("\n[INFO] Streaming response...", "cyan"))
            
            thinking_text = ""
            response_text = ""
            
            for event in stream:
                if event.type == "content_block_start":
                    block_type = event.content_block.type
                    print(colored(f"\n[INFO] Starting {block_type} block...", "green"))
                
                elif event.type == "content_block_delta":
                    if event.delta.type == "thinking_delta":
                        thinking_text += event.delta.thinking
                        print(colored(event.delta.thinking, "magenta"), end="", flush=True)
                    
                    elif event.delta.type == "text_delta":
                        response_text += event.delta.text
                        print(colored(event.delta.text, "blue"), end="", flush=True)
                
                elif event.type == "content_block_stop":
                    print(colored("\n[INFO] Block complete.", "green"))
            
            print("\n")
            print(colored("=" * 50, "yellow"))
            print(colored("THINKING SUMMARY:", "yellow"))
            print(colored(thinking_text, "magenta"))
            print(colored("=" * 50, "yellow"))
            print(colored("FINAL RESPONSE:", "yellow"))
            print(colored(response_text, "blue"))
            print(colored("=" * 50, "yellow"))
            
            # Save to a single JSON file
            output_data = {
                "prompt": prompt,
                "full_prompt": full_prompt,
                "thinking": thinking_text,
                "response": response_text
            }
            
            print(colored("[INFO] Saving output to JSON file...", "cyan"))
            
            try:
                with open("claude_output.json", "w", encoding="utf-8") as f:
                    json.dump(output_data, f, indent=2)
                print(colored("[INFO] Saved output to claude_output.json", "green"))
            
            except Exception as e:
                print(colored(f"[ERROR] Failed to save JSON file: {str(e)}", "red"))
        
    except Exception as e:
        print(colored(f"[ERROR] An error occurred: {str(e)}", "red"))
        sys.exit(1)

if __name__ == "__main__":
    print(colored("[INFO] Claude Thinking Stream Demo", "cyan"))
    stream_with_thinking(PROMPT) 