import os
import sys
import json
from termcolor import colored
import anthropic

# CONSTANTS
API_KEY = os.getenv("ANTHROPIC_API_KEY")
MODEL = "claude-3-7-sonnet-20250219"
MAX_TOKENS = 63000
THINKING_BUDGET = 50000
THINKING_GUIDANCE = "When you are thinking, consider this problem from 3 distinct perspectives in full. each thinking for each perspective should be at least 10000 words. Before you start thinking always say to yourself 'I am ready to tackle this problem' and then start thinking."

def read_context_file(filename):
    """Read the context file content."""
    try:
        print(colored(f"[INFO] Reading context from {filename}...", "cyan"))
        with open(filename, "r", encoding="utf-8") as f:
            return f.read()
    except Exception as e:
        print(colored(f"[ERROR] Failed to read context file: {str(e)}", "red"))
        sys.exit(1)

def extract_perspectives(response_text):
    """Extract the separate perspectives from the response."""
    try:
        perspectives = {}
        
        # Find all perspective blocks using the tags
        for i in range(1, 4):  # We expect 3 perspectives
            start_tag = f"<perspective_{i}>"
            end_tag = f"</perspective_{i}>"
            
            if start_tag in response_text and end_tag in response_text:
                start_idx = response_text.find(start_tag) + len(start_tag)
                end_idx = response_text.find(end_tag)
                
                if start_idx < end_idx:
                    perspectives[f"perspective_{i}"] = response_text[start_idx:end_idx].strip()
        
        return perspectives
    except Exception as e:
        print(colored(f"[ERROR] Failed to extract perspectives: {str(e)}", "red"))
        return {}

def stream_with_thinking(context_content):
    """Stream Claude's thinking and response with colored output and save as JSON."""
    try:
        print(colored("\n[INFO] Initializing Anthropic client...", "cyan"))
        client = anthropic.Anthropic(api_key=API_KEY)
        
        if not API_KEY:
            print(colored("[ERROR] ANTHROPIC_API_KEY environment variable not set!", "red"))
            sys.exit(1)
        
        # Create the prompt
        prompt = f"""Here is an article about Moore's Law. Please consider the philosophical implications of Moore's Law.

In your response, please present three unique and distinct philosophical perspectives on Moore's Law. 
Format each perspective within perspective tags like this:
<perspective_1>Your first perspective here</perspective_1>
<perspective_2>Your second perspective here</perspective_2>
<perspective_3>Your third perspective here</perspective_3>

CONTEXT:
{context_content}"""
        
        # Append thinking guidance
        full_prompt = f"{prompt}\n\n{THINKING_GUIDANCE}"
        print(colored(f"[INFO] Added thinking guidance to prompt", "cyan"))
        
        print(colored(f"[INFO] Sending prompt to {MODEL}...", "cyan"))
        
        with client.messages.stream(
            model=MODEL,
            max_tokens=MAX_TOKENS,
            thinking={
                "type": "enabled",
                "budget_tokens": THINKING_BUDGET
            },
            messages=[{
                "role": "user",
                "content": full_prompt
            }]
        ) as stream:
            print(colored("\n[INFO] Streaming response...", "cyan"))
            
            thinking_text = ""
            response_text = ""
            
            for event in stream:
                if event.type == "content_block_start":
                    block_type = event.content_block.type
                    print(colored(f"\n[INFO] Starting {block_type} block...", "green"))
                
                elif event.type == "content_block_delta":
                    if event.delta.type == "thinking_delta":
                        thinking_text += event.delta.thinking
                        print(colored(event.delta.thinking, "magenta"), end="", flush=True)
                    
                    elif event.delta.type == "text_delta":
                        response_text += event.delta.text
                        print(colored(event.delta.text, "blue"), end="", flush=True)
                
                elif event.type == "content_block_stop":
                    print(colored("\n[INFO] Block complete.", "green"))
            
            print("\n")
            print(colored("=" * 50, "yellow"))
            print(colored("THINKING SUMMARY:", "yellow"))
            print(colored(thinking_text, "magenta"))
            print(colored("=" * 50, "yellow"))
            print(colored("FINAL RESPONSE:", "yellow"))
            print(colored(response_text, "blue"))
            print(colored("=" * 50, "yellow"))
            
            # Extract perspectives from the response
            perspectives = extract_perspectives(response_text)
            
            # Save to JSON files
            output_data = {
                "prompt": prompt,
                "thinking": thinking_text,
                "response": response_text,
                "perspectives": perspectives
            }
            
            print(colored("[INFO] Saving outputs to JSON files...", "cyan"))
            
            try:
                # Save the full output
                with open("claude_output.json", "w", encoding="utf-8") as f:
                    json.dump(output_data, f, indent=2)
                print(colored("[INFO] Saved full output to claude_output.json", "green"))
                
                # Save just the perspectives
                with open("claude_perspectives.json", "w", encoding="utf-8") as f:
                    json.dump({"perspectives": perspectives}, f, indent=2)
                print(colored("[INFO] Saved perspectives to claude_perspectives.json", "green"))
            
            except Exception as e:
                print(colored(f"[ERROR] Failed to save JSON files: {str(e)}", "red"))
        
    except Exception as e:
        print(colored(f"[ERROR] An error occurred: {str(e)}", "red"))
        sys.exit(1)

if __name__ == "__main__":
    print(colored("[INFO] Claude Thinking Perspectives Demo", "cyan"))
    
    # Read the context file
    context_file = "context.txt"
    context_content = read_context_file(context_file)
    
    # Process with Claude
    stream_with_thinking(context_content) 