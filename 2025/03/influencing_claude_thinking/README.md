# Claude Thinking Stream Demos

This repository contains two Python scripts demonstrating how to use the Anthropic API to stream <PERSON>'s extended thinking process. Both scripts display the thinking process and final response with colored output and save the results to JSON files.

## Setup

1.  **Install required packages:**
    ```bash
    pip install -r requirements.txt
    ```
    Make sure `requirements.txt` includes `anthropic` and `termcolor`.

2.  **Set your Anthropic API key as an environment variable:**
    Replace `your_api_key_here` with your actual key.
    ```bash
    # Windows CMD
    set ANTHROPIC_API_KEY=your_api_key_here

    # Windows PowerShell
    $env:ANTHROPIC_API_KEY="your_api_key_here"

    # Linux/macOS
    export ANTHROPIC_API_KEY=your_api_key_here
    ```

## Scripts

### 1. Basic Thinking Stream (`claude_thinking_stream.py`)

This script sends a predefined prompt (with optional thinking guidance) to <PERSON> and streams the thinking process and final response.

**Usage:**

```bash
python claude_thinking_stream.py
```

**Functionality:**

*   Uses the `PROMPT` and `THINKING_GUIDANCE` variables defined at the top of the script.
*   Appends the thinking guidance to the prompt if `THINKING_GUIDANCE` is not empty.
*   Streams <PERSON>'s thinking process (displayed in magenta) and final response (displayed in blue).
*   Shows a summary of the thinking and response in the terminal.
*   Saves the original prompt, the full prompt (with guidance), the thinking text, and the final response to `claude_output.json`.

### 2. Thinking Stream with Context (`claude_thinking_with_context.py`)

This script reads a text file (`context.txt`), passes its content as context to Claude, and prompts the model to analyze it from multiple perspectives based on thinking guidance.

**Prerequisites:**

*   Ensure you have a `context.txt` file in the same directory containing the text you want Claude to analyze.

**Usage:**

```bash
python claude_thinking_with_context.py
```

**Functionality:**

*   Reads the content of `context.txt`.
*   Constructs a prompt asking Claude to consider the philosophical implications of the context, providing specific formatting instructions for the response (`<perspective_1>`, etc.).
*   Appends the `THINKING_GUIDANCE` (requesting 3 distinct perspectives) to the prompt.
*   Streams Claude's thinking process (displayed in magenta) and final response (displayed in blue).
*   Shows a summary of the thinking and response in the terminal.
*   Extracts the distinct perspectives from the final response based on the `<perspective_x>` tags.
*   Saves the prompt, thinking text, full response, and extracted perspectives to `claude_output.json`.
*   Saves just the extracted perspectives to `claude_perspectives.json`.

## Common Features

*   Colored terminal output using `termcolor` for better readability.
*   Real-time streaming of results.
*   Configuration constants (Model, Tokens, API Key source) at the top of each script.
*   Error handling with descriptive messages.
*   Saves output(s) to JSON files using `utf-8` encoding. 