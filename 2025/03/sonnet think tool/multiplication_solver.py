import anthropic
import json
import os
from termcolor import colored

# Multiplication problem
MULTIPLICATION_PROBLEM = "123456 x 789012"

# Initialize thought log
THOUGHT_LOG = []

def think_tool(thought):
    """Tool to think through steps of a problem and log the thoughts."""
    print(colored(f"THINKING: {thought}", "yellow"))
    THOUGHT_LOG.append(thought)
    return thought

try:
    # Set up Anthropic client
    client = anthropic.Anthropic(api_key=os.getenv("ANTHROPIC_API_KEY"))
    
    print(colored(f"Solving multiplication problem: {MULTIPLICATION_PROBLEM}", "cyan"))
    
    # Define tool
    think_tool_def = {
        "name": "think",
        "description": "Use the tool to think about something. It will not obtain new information or change the database, but just append the thought to the log. Use it when complex reasoning or some cache memory is needed.",
        "input_schema": {
            "type": "object",
            "properties": {
                "thought": {
                    "type": "string",
                    "description": "A thought to think about."
                }
            },
            "required": ["thought"]
        }
    }
    
    # Initialize the conversation
    messages = [
        {
            "role": "user",
            "content": [
                {
                    "type": "text",
                    "text": f"Calculate {MULTIPLICATION_PROBLEM}. Use the think tool to work through the steps, then provide the final answer."
                }
            ]
        }
    ]
    
    final_response = ""
    
    # Continue the conversation until we get a final response
    while True:
        print(colored("Sending message to Claude...", "blue"))
        
        # Create the message
        message = client.messages.create(
            model="claude-3-7-sonnet-20250219",
            max_tokens=1000,
            temperature=1,
            messages=messages,
            tools=[think_tool_def]
        )
        
        has_tool_use = False
        response_texts = []
        
        # Process the response
        for content in message.content:
            if content.type == "tool_use":
                has_tool_use = True
                thought = content.input.get("thought", "")
                
                # Execute the tool
                result = think_tool(thought)
                
                # Add the tool response to the messages
                messages.append({
                    "role": "assistant",
                    "content": [
                        {
                            "type": "tool_use",
                            "id": content.id,
                            "name": "think",
                            "input": {"thought": thought}
                        }
                    ]
                })
                
                # Format tool result properly according to Anthropic API
                messages.append({
                    "role": "user",
                    "content": [
                        {
                            "type": "tool_result",
                            "tool_use_id": content.id,
                            "content": thought
                        }
                    ]
                })
            
            elif content.type == "text":
                response_texts.append(content.text)
        
        # If no tool was used, we have our final response
        if not has_tool_use:
            final_response = "\n".join(response_texts)
            break
    
    # Save simplified results to JSON file
    simple_results = {
        "thought": THOUGHT_LOG[0] if THOUGHT_LOG else "",
        "answer": final_response
    }
    
    with open("multiplication_results.json", "w", encoding="utf-8") as f:
        json.dump(simple_results, f, indent=4)
    
    # Print final response
    print(colored("\nFINAL RESPONSE:", "green"))
    print(colored(final_response, "green"))
    
    print(colored("\nResults saved to multiplication_results.json", "cyan"))
    
except Exception as e:
    print(colored(f"ERROR: {str(e)}", "red"))
    print(colored(f"Error details: {type(e).__name__}", "red")) 