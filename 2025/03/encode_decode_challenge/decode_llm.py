import os
import re
import string
from termcolor import colored
from openai import OpenAI

# Major variables
API_KEY = os.getenv("OPENAI_API_KEY")
MODEL = "gpt-4o"
SYSTEM_PROMPT = """
You are a cipher decoder. I will give you a message where each letter of the alphabet has been replaced with a number from 1-26.
Your job is to decode the message and return the original English text.
Return your decoded message between <decoded> and </decoded> tags.
"""

def decode_with_gpt(encoded_message, original_message):
    """Use GPT to decode the message and calculate accuracy."""
    try:
        print(colored("Initializing OpenAI client...", "cyan"))
        client = OpenAI(api_key=API_KEY)
        
        print(colored(f"Sending encoded message to {MODEL}...", "blue"))
        completion = client.chat.completions.create(
            model=MODEL,
            messages=[
                {"role": "system", "content": SYSTEM_PROMPT},
                {"role": "user", "content": f"Decode this message: {encoded_message}"}
            ]
        )
        
        response = completion.choices[0].message.content
        print(colored(f"Received response from model", "green"))
        
        # Extract decoded text between tags
        decoded_match = re.search(r'<decoded>(.*?)</decoded>', response, re.DOTALL)
        if decoded_match:
            decoded_text = decoded_match.group(1).strip()
            print(colored(f"Extracted decoded text: {decoded_text}", "yellow"))
        else:
            decoded_text = response
            print(colored("No tags found, using full response", "yellow"))
        
        # Calculate accuracy
        accuracy = calculate_accuracy(original_message, decoded_text)
        
        return decoded_text, accuracy
        
    except Exception as e:
        print(colored(f"Error during decoding: {str(e)}", "red"))
        raise

def calculate_accuracy(original, decoded):
    """Calculate the percentage of words correctly decoded."""
    try:
        print(colored("Calculating accuracy...", "cyan"))
        
        # Normalize both texts for comparison
        original_words = original.lower().split()
        decoded_words = decoded.lower().split()
        
        # Count matching words
        correct_count = 0
        for orig_word in original_words:
            # Remove punctuation for comparison
            clean_orig = orig_word.translate(str.maketrans('', '', string.punctuation))
            if clean_orig in decoded_words:
                correct_count += 1
                decoded_words.remove(clean_orig)  # Remove to avoid double counting
        
        total_words = len(original_words)
        accuracy_pct = (correct_count / total_words) * 100 if total_words > 0 else 0
        
        print(colored(f"Words correctly decoded: {correct_count}/{total_words}", "green"))
        return accuracy_pct
        
    except Exception as e:
        print(colored(f"Error calculating accuracy: {str(e)}", "red"))
        return 0.0

if __name__ == "__main__":
    try:
        # Import the encoding function
        from basic_encoding import encode_string, SAMPLE_TEXT
        
        print(colored("Starting the encoding-decoding experiment...", "cyan"))
        
        # Encode the sample text
        encoded_message, cipher = encode_string(SAMPLE_TEXT)
        print(colored("\nOriginal message:", "blue"))
        print(SAMPLE_TEXT)
        print(colored("\nEncoded message:", "yellow"))
        print(encoded_message)
        
        # Decode using GPT
        decoded_text, accuracy = decode_with_gpt(encoded_message, SAMPLE_TEXT)
        
        # Display results
        print(colored("\n===== RESULTS =====", "green"))
        print(colored("Original text:", "blue"))
        print(SAMPLE_TEXT)
        print(colored("\nDecoded text:", "yellow"))
        print(decoded_text)
        print(colored(f"\nAccuracy: {accuracy:.2f}%", "green" if accuracy > 70 else "red"))
        
    except Exception as e:
        print(colored(f"Program error: {str(e)}", "red")) 