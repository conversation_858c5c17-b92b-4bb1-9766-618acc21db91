import random
import string
from termcolor import colored

# Major variables
SAMPLE_TEXT = """As of my last update in October 2023, there is no widely recognized public figure or notable individual named <PERSON><PERSON> in fields such as entertainment, science, business, or politics. It's possible that <PERSON><PERSON> could be a private individual, a rising figure in a specific niche, or someone who has gained prominence after my last update.

If you have more context or details about this person, feel free to share, and I can try to provide more relevant information!"""

def create_alphabet_cipher():
    """Create a random mapping of alphabet letters to numbers 1-26."""
    numbers = list(range(1, 27))
    random.shuffle(numbers)
    return dict(zip(string.ascii_lowercase, numbers))

def encode_string(input_string, cipher=None):
    """Encode a string using a random alphabet-to-number cipher."""
    try:
        print(colored("Starting encoding process...", "cyan"))
        
        # Create cipher if not provided
        if cipher is None:
            cipher = create_alphabet_cipher()
            print(colored(f"Generated cipher: {cipher}", "green"))
        
        # Convert input to lowercase
        input_string = input_string.lower()
        print(colored(f"Normalized input: {input_string}", "blue"))
        
        # Encode the string
        encoded = []
        for char in input_string:
            if char in cipher:
                encoded.append(str(cipher[char]))
                print(colored(f"Encoded '{char}' to '{cipher[char]}'", "yellow"))
            else:
                encoded.append(char)
                print(colored(f"Character '{char}' not in alphabet, keeping as is", "magenta"))
        
        result = ' '.join(encoded)
        print(colored(f"Final encoded string: {result}", "green"))
        return result, cipher
    
    except Exception as e:
        print(colored(f"Error during encoding: {str(e)}", "red"))
        raise

# Example usage
if __name__ == "__main__":
    try:
        encoded_message, cipher = encode_string(SAMPLE_TEXT)
        print(colored(f"\nOriginal message: {SAMPLE_TEXT}", "cyan"))
        print(colored(f"Encoded message: {encoded_message}", "green"))
        print(colored(f"Cipher used: {cipher}", "yellow"))
    except Exception as e:
        print(colored(f"Program error: {str(e)}", "red"))