import os
import re
import json
import string
import concurrent.futures
from termcolor import colored
from openai import OpenAI
import anthropic
from google import genai
from google.genai import types

# THIS SCRIPT IS NOT FULLY WORKING FOR CLAUDE AND GEMINI FOR SOME REASON, <PERSON><PERSON><PERSON><PERSON> WAS GIVING ME A LOT OF TROUBLE AND i WAS TIRED. WE WILL GIVE THIS ANOTHER TRY LATER.

# Major variables
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
ANTHROPIC_API_KEY = os.getenv("ANTHROPIC_API_KEY")
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
MODELS = ["gpt-4o", "o3-mini", "claude-3-7-sonnet-20250219", "gemini-2.5-pro-exp-03-25"]
SAMPLE_TEXT = """To five gallons of water put five quarts of the juice of white
elderberries, pressed gently through a sieve without bruising the
seeds"""
SYSTEM_PROMPT = """
You are a decoder. Your job is to figure out the original English text from an encoded message.
Return your decoded message between <decoded> and </decoded> tags.
"""
RESULTS_FILE = "decoder_results.json"

def decode_with_model(model, encoded_message):
    """Use an AI model to decode the message."""
    try:
        print(colored(f"Sending encoded message to {model}...", "blue"))
        
        # Handle Claude models
        if "claude" in model:
            try:
                client = anthropic.Anthropic(api_key=ANTHROPIC_API_KEY)
                
                response = client.messages.create(
                    model=model,
                    max_tokens=40000,
                    thinking={
                        "type": "enabled",
                        "budget_tokens": 20000
                    },
                    system=SYSTEM_PROMPT,
                    messages=[
                        {"role": "user", "content": f"Decode this message: {encoded_message}"}
                    ]
                )
                
                # Extract the text content from the response
                response_text = ""
                thinking_content = ""
                
                for content in response.content:
                    if content.type == "text":
                        response_text += content.text
                    elif content.type == "thinking":
                        thinking_content = content.thinking
                
                print(colored(f"Received response from {model}", "green"))
                print(colored(f"[{model}] Thinking process used: {len(thinking_content.split())} words", "magenta"))
                
                # Extract decoded text between tags
                decoded_match = re.search(r'<decoded>(.*?)</decoded>', response_text, re.DOTALL)
                if decoded_match:
                    decoded_text = decoded_match.group(1).strip()
                    print(colored(f"[{model}] Extracted decoded text: {decoded_text}", "yellow"))
                else:
                    decoded_text = response_text
                    print(colored(f"[{model}] No tags found, using full response", "yellow"))
                
                return decoded_text, thinking_content
                
            except Exception as e:
                print(colored(f"Error with Anthropic API: {str(e)}", "red"))
                return f"Error: {str(e)}", ""
        
        # Handle Gemini models
        elif "gemini" in model:
            try:
                # Initialize Gemini client
                client = genai.Client(api_key=GEMINI_API_KEY)
                
                # Create content for Gemini
                contents = [
                    types.Content(
                        role="user",
                        parts=[
                            types.Part.from_text(
                                text=f"{SYSTEM_PROMPT}\n\nDecode this message: {encoded_message}"
                            ),
                        ],
                    ),
                ]
                
                # Set generation config
                generate_content_config = types.GenerateContentConfig(
                    temperature=0.2,  # Lower temperature for more focused decoding
                    top_p=0.95,
                    top_k=64,
                    max_output_tokens=1000,
                    response_mime_type="text/plain",
                )
                
                # Collect the full response
                full_response = ""
                for chunk in client.models.generate_content_stream(
                    model=model,
                    contents=contents,
                    config=generate_content_config,
                ):
                    if chunk.text:
                        full_response += chunk.text
                
                print(colored(f"Received response from {model}", "green"))
                
                # Extract decoded text between tags
                decoded_match = re.search(r'<decoded>(.*?)</decoded>', full_response, re.DOTALL)
                if decoded_match:
                    decoded_text = decoded_match.group(1).strip()
                    print(colored(f"[{model}] Extracted decoded text: {decoded_text}", "yellow"))
                else:
                    decoded_text = full_response
                    print(colored(f"[{model}] No tags found, using full response", "yellow"))
                
                return decoded_text, ""  # No thinking output from Gemini API
                
            except Exception as e:
                print(colored(f"Error with Gemini API: {str(e)}", "red"))
                return f"Error: {str(e)}", ""
        
        # Handle OpenAI models
        else:
            client = OpenAI(api_key=OPENAI_API_KEY)
            
            # Different parameters for different models
            kwargs = {
                "model": model,
                "messages": [
                    {"role": "system", "content": SYSTEM_PROMPT},
                    {"role": "user", "content": f"Decode this message: {encoded_message}"}
                ]
            }
            
            # Add reasoning_effort for o3-mini
            if "o3-mini" in model:
                kwargs["reasoning_effort"] = "high"
                
            completion = client.chat.completions.create(**kwargs)
            
            response = completion.choices[0].message.content
            print(colored(f"Received response from {model}", "green"))
            
            # Extract decoded text between tags
            decoded_match = re.search(r'<decoded>(.*?)</decoded>', response, re.DOTALL)
            if decoded_match:
                decoded_text = decoded_match.group(1).strip()
                print(colored(f"[{model}] Extracted decoded text: {decoded_text}", "yellow"))
            else:
                decoded_text = response
                print(colored(f"[{model}] No tags found, using full response", "yellow"))
            
            return decoded_text, ""
        
    except Exception as e:
        print(colored(f"Error during decoding with {model}: {str(e)}", "red"))
        return f"Error: {str(e)}", ""

def calculate_accuracy(original, decoded):
    """Calculate the percentage of words correctly decoded in their exact positions."""
    try:
        print(colored("Calculating position-based accuracy...", "cyan"))
        
        # Normalize both texts for comparison
        original_words = [word.lower().translate(str.maketrans('', '', string.punctuation)) 
                         for word in original.lower().split() if word.lower().translate(str.maketrans('', '', string.punctuation))]
        
        decoded_words = [word.lower().translate(str.maketrans('', '', string.punctuation)) 
                        for word in decoded.lower().split() if word.lower().translate(str.maketrans('', '', string.punctuation))]
        
        # Count matching words in the same position
        correct_count = 0
        total_words = len(original_words)
        
        # Compare words at the same positions
        for i, orig_word in enumerate(original_words):
            if i < len(decoded_words) and orig_word == decoded_words[i]:
                correct_count += 1
                print(colored(f"Position {i+1}: '{orig_word}' correctly positioned ✓", "green"))
            elif i < len(decoded_words):
                print(colored(f"Position {i+1}: '{orig_word}' != '{decoded_words[i]}' ✗", "red"))
            else:
                print(colored(f"Position {i+1}: '{orig_word}' missing in decoded text ✗", "red"))
        
        # Calculate position-exact accuracy
        accuracy_pct = (correct_count / total_words) * 100 if total_words > 0 else 0
        
        print(colored(f"Words correctly decoded in position: {correct_count}/{total_words}", "green"))
        print(colored(f"Position-exact accuracy: {accuracy_pct:.2f}%", "cyan"))
        
        return accuracy_pct
        
    except Exception as e:
        print(colored(f"Error calculating accuracy: {str(e)}", "red"))
        return 0.0

def main():
    try:
        # Import only the encoding function
        from basic_encoding import encode_string
        
        print(colored("Starting the parallel model comparison experiment...", "cyan"))
        
        # Encode the sample text
        encoded_message, cipher = encode_string(SAMPLE_TEXT)
        print(colored("\nOriginal message:", "blue"))
        print(SAMPLE_TEXT)
        print(colored("\nEncoded message:", "yellow"))
        print(encoded_message)
        
        # Run decoding with all models in parallel using ThreadPoolExecutor
        decoded_results = {}
        
        with concurrent.futures.ThreadPoolExecutor() as executor:
            # Submit tasks for all models
            future_to_model = {executor.submit(decode_with_model, model, encoded_message): model for model in MODELS}
            
            # Process completed tasks
            for future in concurrent.futures.as_completed(future_to_model):
                model = future_to_model[future]
                try:
                    decoded_text, thinking = future.result()
                    decoded_results[model] = (decoded_text, thinking)
                    print(colored(f"Completed processing for {model}", "green"))
                except Exception as e:
                    print(colored(f"Model {model} generated an exception: {str(e)}", "red"))
                    decoded_results[model] = (f"Error: {str(e)}", "")
        
        # Calculate accuracy for each model
        results = {}
        for model, result in decoded_results.items():
            decoded_text, thinking = result
            accuracy = calculate_accuracy(SAMPLE_TEXT, decoded_text)
            results[model] = {
                "decoded_text": decoded_text,
                "accuracy": accuracy
            }
            
            if thinking:
                results[model]["thinking"] = thinking
            
            # Display results for this model
            print(colored(f"\n===== RESULTS for {model} =====", "green"))
            print(colored("Decoded text:", "yellow"))
            print(decoded_text)
            print(colored(f"Accuracy: {accuracy:.2f}%", "green" if accuracy > 70 else "red"))
            
            if thinking:
                print(colored("Model used extended thinking:", "cyan"))
                print(colored(f"Thinking length: {len(thinking.split())} words", "magenta"))
        
        # Save results to JSON file
        with open(RESULTS_FILE, "w", encoding="utf-8") as f:
            json.dump({
                "original_text": SAMPLE_TEXT,
                "encoded_message": encoded_message,
                "cipher": {k: v for k, v in cipher.items()},
                "results": results
            }, f, indent=2)
            
        print(colored(f"\nResults saved to {RESULTS_FILE}", "green"))
        
    except Exception as e:
        print(colored(f"Program error: {str(e)}", "red"))

if __name__ == "__main__":
    main()