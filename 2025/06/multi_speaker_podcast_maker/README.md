# AI Podcast Generator Collection

This repository contains three different approaches to generating AI-powered podcast conversations using state-of-the-art text-to-speech technology.

## 🎙️ Scripts Overview

### 1_single_speaker_gen_simple.py
**Simple Single-Speaker TTS**
- Uses Gemini 2.5 Flash Preview TTS
- Single voice for entire content
- Basic implementation for simple audio generation

### 2_multi_speaker_gen_simple.py  
**Multi-Speaker Conversation TTS**
- Uses Gemini 2.5 Flash Preview TTS
- Multiple speakers with different voices
- Pre-written emotional conversation (sad to happy theme)
- Speaker-specific voice assignments

### 3_openai_conversation_gen.py
**Advanced AI-Generated Podcast** ⭐
- **Conversation Generation**: OpenAI o3 model creates dynamic conversations
- **Multi-Speaker TTS**: Gemini 2.5 Flash Preview TTS for audio synthesis
- **Configurable**: Custom topics, speakers, tones, and length
- **Smart Naming**: Files automatically named based on topic
- **JSON Output**: Structured conversation data saved alongside audio

## 🎵 Voice Options

Voice selections and demos available at: https://aistudio.google.com/live

Popular voice options include:
- **Kore** - Natural, clear voice
- **Puck** - Expressive, animated voice  
- **<PERSON>ron** - Deep, authoritative voice
- And many more options available

## 💰 Pricing Information

### Gemini 2.5 Flash Preview TTS
According to [Google AI Studio](https://aistudio.google.com/live):
- **Input (Text)**: Free of charge for free tier users and $0.50 per 1M tokens for paid users
- **Audio Output**: $10.00 per 1M tokens

### OpenAI o3 (for script #3)
- Pricing varies based on OpenAI's current rates
- JSON mode and reasoning effort may affect costs

⚠️ **Cost Disclaimer**: Longer audio files will generate significantly more tokens and cost more. A 10-minute conversation could cost several dollars. Monitor your usage carefully to avoid unexpected charges.

## 🚀 Setup Instructions

### Prerequisites
```bash
# Install required packages
pip install google-genai openai termcolor
```

### Environment Variables
```bash
# For Gemini TTS (all scripts)
set GEMINI_API_KEY=your_gemini_api_key_here

# For OpenAI o3 (script #3 only)  
set OPENAI_API_KEY=your_openai_api_key_here
```

### Running the Scripts

**Simple Scripts:**
```bash
python 1_single_speaker_gen_simple.py
python 2_multi_speaker_gen_simple.py
```

**Advanced AI Generator:**
```bash
python 3_openai_conversation_gen.py
```

## 📁 Output Files

### Scripts 1 & 2:
- `out.wav` - Generated audio file

### Script 3:
- `{topic_name}.wav` - Audio file (named after topic)
- `{topic_name}.json` - Conversation data in structured format

## ⚙️ Configuration (Script #3)

Edit the configuration variables in `3_openai_conversation_gen.py`:

```python
# Speaker setup
SPEAKERS = {
    "Speaker1": {
        "name": "Morgan",
        "tone": "enthusiastic and curious but confrontational", 
        "voice": "Kore"
    },
    "Speaker2": {
        "name": "Alex",
        "tone": "thoughtful and analytical",
        "voice": "Charon" 
    }
}

# Conversation parameters
LENGTH = "20 seconds"  # "30 seconds", "2 minutes", "5 minutes", etc.
TOPIC = "Your custom topic here"
```

## 🛡️ Safety Features

- **Cost Protection**: Script #3 includes warnings for conversations over 3 minutes
- **Colorful Progress**: Clear visual feedback during generation
- **Error Handling**: Graceful handling of API errors
- **File Validation**: Automatic filename sanitization

## 🔧 Technical Details

- **Audio Format**: 24kHz, 16-bit, mono WAV files
- **Models Used**: 
  - Gemini 2.5 Flash Preview TTS (audio generation)
  - OpenAI o3 (conversation generation for script #3)
- **Multi-speaker Support**: Different voices automatically assigned per speaker
- **JSON Structure**: Conversations saved with speaker metadata and emotional cues

## 📝 Notes

- Preview models may change before becoming stable
- Rate limits may apply to both OpenAI and Google APIs
- Audio quality is optimized for speech clarity and naturalness
- Longer conversations will have proportionally higher costs

## 🎯 Best Practices

1. **Start Small**: Test with short conversations first
2. **Monitor Costs**: Check your API usage regularly  
3. **Voice Testing**: Try different voice combinations for best results
4. **Topic Selection**: Specific topics generate better conversations
5. **Length Management**: Be mindful of conversation length vs. cost

## 🤝 Contributing

Feel free to modify speakers, topics, and voices to suit your needs. The scripts are designed to be easily customizable for different use cases. 