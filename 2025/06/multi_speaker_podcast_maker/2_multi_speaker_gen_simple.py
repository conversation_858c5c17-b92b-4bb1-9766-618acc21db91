from google import genai
from google.genai import types
import wave
import os

# Set up the wave file to save the output:
def wave_file(filename, pcm, channels=1, rate=24000, sample_width=2):
   with wave.open(filename, "wb") as wf:
      wf.setnchannels(channels)
      wf.setsampwidth(sample_width)
      wf.setframerate(rate)
      wf.writeframes(pcm)

client = genai.Client(api_key=os.getenv("GEMINI_API_KEY"))

prompt = """TTS the following conversation between <PERSON> and <PERSON>:
<PERSON> is super sad but gradually gets better as <PERSON> tries to cheer him up.

         Joe: How's it going today <PERSON>?
         Jane: Not too bad, how about you?
         <PERSON>: I am kinda sad actually
         <PERSON>: I am sorry to hear that but I am here for you if you want to talk about it
         <PERSON>: I just feel like everything is going wrong lately
         Jane: I understand that feeling. What's been bothering you the most?
         Joe: Work has been really stressful and I feel overwhelmed
         <PERSON>: That sounds really tough. Have you been able to take any breaks for yourself?
         Joe: Not really, I've been working non-stop
         <PERSON>: You know what? Why don't we go for a walk together? Fresh air always helps me clear my head
         Joe: That actually sounds nice. Thanks for being such a good friend <PERSON>: Of course! That's what friends are for. I'm always here when you need someone to talk to
         <PERSON>: I'm already feeling a bit better just talking about it
         <PERSON>: I'm so glad to hear that! Remember, tough times don't last but tough people do
         """

response = client.models.generate_content(
   model="gemini-2.5-flash-preview-tts",
   contents=prompt,
   config=types.GenerateContentConfig(
      response_modalities=["AUDIO"],
      speech_config=types.SpeechConfig(
         multi_speaker_voice_config=types.MultiSpeakerVoiceConfig(
            speaker_voice_configs=[
               types.SpeakerVoiceConfig(
                  speaker='Joe',
                  voice_config=types.VoiceConfig(
                     prebuilt_voice_config=types.PrebuiltVoiceConfig(
                        voice_name='Kore',
                     )
                  )
               ),
               types.SpeakerVoiceConfig(
                  speaker='Jane',
                  voice_config=types.VoiceConfig(
                     prebuilt_voice_config=types.PrebuiltVoiceConfig(
                        voice_name='Charon',
                     )
                  )
               ),
            ]
         )
      )
   )
)

data = response.candidates[0].content.parts[0].inline_data.data

file_name='out.wav'
wave_file(file_name, data) # Saves the file to current directory