from openai import OpenAI
from google import genai
from google.genai import types
import wave
import os
import json
from termcolor import colored

# Set up the wave file to save the output:
def wave_file(filename, pcm, channels=1, rate=24000, sample_width=2):
   with wave.open(filename, "wb") as wf:
      wf.setnchannels(channels)
      wf.setsampwidth(sample_width)
      wf.setframerate(rate)
      wf.writeframes(pcm)

# OpenAI client setup
print(colored("🤖 Initializing OpenAI client...", "cyan"))
openai_client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

# Gemini client setup
print(colored("🎵 Initializing Gemini TTS client...", "cyan"))
gemini_client = genai.Client(api_key=os.getenv("GEMINI_API_KEY"))

# Speaker configuration
SPEAKERS = {
    "Speaker1": {
        "name": "<PERSON>",
        "tone": "enthusiastic and curious but confrontational",
        "voice": "Aoede"
    },
    "Speaker2": {
        "name": "<PERSON>",
        "tone": "thoughtful and analytical", 
        "voice": "Charon"
    }
}

# Conversation length parameter
# COST WARNING! IT COSTS $10 PER MILLION TOKENS! NOT SURE HOW THAT TRANSLATES TO AUDIO LENGTH
LENGTH = "20 seconds"  # 20 seconds, 1 minute, 2 minutes etc

# Topic for the conversation
TOPIC = "importance of AI in the future"

# Cost warning for longer conversations
def check_duration_cost_warning(length_str):
    """Check if conversation length might be expensive and get user confirmation"""
    import re
    
    # Extract number from length string
    numbers = re.findall(r'\d+', length_str.lower())
    if not numbers:
        return True
    
    duration_num = int(numbers[0])
    
    # Check if it contains "minute" and is over 3 minutes
    if 'minute' in length_str.lower() and duration_num > 3:
        print(colored(f"\n⚠️  WARNING: Long Conversation Detected!", "red", attrs=['bold']))
        print(colored(f"📊 Requested length: {length_str}", "yellow"))
        print(colored(f"💰 Cost: $10 per million tokens", "yellow"))
        
        while True:
            user_input = input(colored("\n❓ Do you want to continue? (yes/no): ", "cyan")).lower().strip()
            if user_input in ['yes', 'y']:
                print(colored("✅ Proceeding with long conversation generation...", "green"))
                return True
            elif user_input in ['no', 'n']:
                print(colored("❌ Conversation generation cancelled.", "red"))
                return False
            else:
                print(colored("Please enter 'yes' or 'no'", "yellow"))
    
    return True

# Check for cost warning before proceeding
if not check_duration_cost_warning(LENGTH):
    exit(0)

# Generate conversation using OpenAI o3
system_prompt = """You are a podcast conversation generator. Create a natural, engaging podcast-style conversation between 2 people. 

The conversation should:
- Feel like a real podcast discussion
- Include natural interruptions, agreements, and follow-up questions
- Have emotional cues and tones indicated in parentheses for TTS
- Be appropriate for the specified length
- Cover interesting topics that would engage listeners

Return the conversation in JSON format with the following structure:
{
  "conversation": [
    {
      "speaker": "speaker_name",
      "text": "dialogue with (emotional cues) included",
      "tone": "speaker's tone for this line"
    }
  ],
  "topic": "conversation topic",
  "duration_estimate": "estimated duration in minutes"
}"""

user_prompt = f"""Generate a {LENGTH} podcast conversation between {SPEAKERS['Speaker1']['name']} (who is {SPEAKERS['Speaker1']['tone']}) and {SPEAKERS['Speaker2']['name']} (who is {SPEAKERS['Speaker2']['tone']}).

The topic should be: {TOPIC}

The conversation should include emotional cues in parentheses like (excited), (thoughtful), (surprised) etc. that will help with text-to-speech generation.

Make it engaging and natural like a real podcast discussion."""

print(colored(f"\n🎙️ Generating {LENGTH} conversation about: {TOPIC}", "yellow"))
print(colored(f"   Speakers: {SPEAKERS['Speaker1']['name']} & {SPEAKERS['Speaker2']['name']}", "yellow"))
print(colored("   Using OpenAI o3 model...", "yellow"))

response = openai_client.chat.completions.create(
    model="o3",
    messages=[
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": user_prompt}
    ],
    response_format={"type": "json_object"}
)

# Parse the JSON response
conversation_data = json.loads(response.choices[0].message.content)

print(colored("\n✅ Conversation generated successfully!", "green"))
print(colored(f"📝 Topic: {conversation_data.get('topic')}", "magenta"))
print(colored(f"⏱️  Estimated duration: {conversation_data.get('duration_estimate')}", "magenta"))
print(colored(f"💬 Total lines: {len(conversation_data['conversation'])}", "magenta"))

print(colored("\n📖 Conversation preview:", "blue"))
for i, line in enumerate(conversation_data["conversation"][:3]):
    speaker_color = "cyan" if line['speaker'] == SPEAKERS['Speaker1']['name'] else "white"
    print(colored(f"   {line['speaker']}: {line['text']}", speaker_color))
print(colored("   ...", "blue"))

# Convert conversation to TTS format
tts_prompt = "TTS the following podcast conversation:\n\n"
for line in conversation_data["conversation"]:
    speaker_name = line["speaker"]
    dialogue = line["text"]
    tts_prompt += f"         {speaker_name}: {dialogue}\n"

print(colored("\n🎵 Converting to audio with Gemini TTS...", "yellow"))
print(colored(f"   Voice for {SPEAKERS['Speaker1']['name']}: {SPEAKERS['Speaker1']['voice']}", "yellow"))
print(colored(f"   Voice for {SPEAKERS['Speaker2']['name']}: {SPEAKERS['Speaker2']['voice']}", "yellow"))

# Generate audio using Gemini TTS
response = gemini_client.models.generate_content(
   model="gemini-2.5-flash-preview-tts",
   contents=tts_prompt,
   config=types.GenerateContentConfig(
      response_modalities=["AUDIO"],
      speech_config=types.SpeechConfig(
         multi_speaker_voice_config=types.MultiSpeakerVoiceConfig(
            speaker_voice_configs=[
               types.SpeakerVoiceConfig(
                  speaker=SPEAKERS['Speaker1']['name'],
                  voice_config=types.VoiceConfig(
                     prebuilt_voice_config=types.PrebuiltVoiceConfig(
                        voice_name=SPEAKERS['Speaker1']['voice'],
                     )
                  )
               ),
               types.SpeakerVoiceConfig(
                  speaker=SPEAKERS['Speaker2']['name'],
                  voice_config=types.VoiceConfig(
                     prebuilt_voice_config=types.PrebuiltVoiceConfig(
                        voice_name=SPEAKERS['Speaker2']['voice'],
                     )
                  )
               ),
            ]
         )
      )
   )
)

data = response.candidates[0].content.parts[0].inline_data.data

# Create sanitized filename from topic (30 characters max)
def sanitize_filename(text, max_length=30):
    # Remove special characters and replace spaces with underscores
    sanitized = "".join(c for c in text if c.isalnum() or c in (' ', '-', '_')).rstrip()
    sanitized = sanitized.replace(' ', '_')
    return sanitized[:max_length]

topic_filename = sanitize_filename(TOPIC)
file_name = f'{topic_filename}.wav'
json_filename = f'{topic_filename}.json'

wave_file(file_name, data)

print(colored(f"\n🎧 Audio saved as: {file_name}", "green"))

# Also save the conversation JSON for reference
with open(json_filename, 'w') as f:
    json.dump(conversation_data, f, indent=2)

print(colored(f"📄 Conversation data saved as: {json_filename}", "green"))
print(colored("\n🎉 Podcast generation complete! Enjoy your AI-generated conversation!", "green", attrs=['bold'])) 