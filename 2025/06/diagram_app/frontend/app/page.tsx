'use client';

import { useState, useRef, useEffect, useCallback } from 'react';

interface Topic {
  title: string;
}

interface Chapter {
  title: string;
  topics: Topic[];
}

interface BookOutline {
  title: string;
  chapters: Chapter[];
}

interface Position {
  x: number;
  y: number;
}

interface DraggableElement {
  id: string;
  position: Position;
  isDragging: boolean;
}

export default function InfiniteCanvas() {
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [outline, setOutline] = useState<BookOutline | null>(null);
  const [pan, setPan] = useState<Position>({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState<Position>({ x: 0, y: 0 });
  const [zoom, setZoom] = useState(1);
  const [elementPositions, setElementPositions] = useState<Record<string, Position>>({});
  const [draggingElement, setDraggingElement] = useState<string | null>(null);
  const [inputPosition, setInputPosition] = useState<Position>({ x: 0, y: 0 });
  const [isCanvasReady, setIsCanvasReady] = useState(false);
  const canvasRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (canvasRef.current) {
      const { width, height } = canvasRef.current.getBoundingClientRect();
      setInputPosition({ x: width / 2, y: height / 2 });
    }
    setIsCanvasReady(true);
  }, []);

  const handleSubmit = async () => {
    if (!input.trim() || isLoading) return;

    setIsLoading(true);
    try {
      const response = await fetch('http://127.0.0.1:8000/generate-outline', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ text: input }),
      });

      if (!response.ok) {
        throw new Error('Failed to generate outline');
      }

      const data = await response.json();
      setOutline(data);
      
      // Initialize positions for all elements relative to the input position
      const positions: Record<string, Position> = {};
      data.chapters.forEach((chapter: Chapter, chapterIndex: number) => {
        const chapterId = `chapter-${chapterIndex}`;
        const chapterYOffset = 150;
        const chapterYSpacing = 120;
        const topicXOffset = 300;
        const topicYSpacing = 60;

        const chapterX = inputPosition.x;
        const chapterY = inputPosition.y + chapterYOffset + (chapterIndex * chapterYSpacing);
        positions[chapterId] = { x: chapterX, y: chapterY };
        
        chapter.topics.forEach((topic: Topic, topicIndex: number) => {
          const topicId = `topic-${chapterIndex}-${topicIndex}`;
          const topicY = chapterY + (topicIndex * topicYSpacing) - ((chapter.topics.length - 1) * (topicYSpacing/2));
          positions[topicId] = { 
            x: chapterX + topicXOffset, 
            y: topicY
          };
        });
      });
      setElementPositions(positions);
    } catch (error) {
      console.error('Error:', error);
      alert('Failed to generate outline. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleElementMouseDown = useCallback((e: React.MouseEvent, elementId: string) => {
    e.stopPropagation();
    setDraggingElement(elementId);
    const canvasRect = canvasRef.current?.getBoundingClientRect();
    if (canvasRect) {
      if (elementId === 'input') {
        setDragStart({
          x: (e.clientX - canvasRect.left - pan.x) / zoom - inputPosition.x,
          y: (e.clientY - canvasRect.top - pan.y) / zoom - inputPosition.y,
        });
      } else {
        setDragStart({
          x: (e.clientX - canvasRect.left - pan.x) / zoom - (elementPositions[elementId]?.x || 0),
          y: (e.clientY - canvasRect.top - pan.y) / zoom - (elementPositions[elementId]?.y || 0),
        });
      }
    }
  }, [elementPositions, inputPosition, pan, zoom]);

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    const target = e.target as HTMLElement;
    // Check if clicking on canvas background or main canvas element
    if (target === canvasRef.current || 
        target.classList.contains('canvas-background') ||
        (target.closest('.canvas-background') && !target.closest('[data-draggable]'))) {
      setIsDragging(true);
      setDragStart({ x: e.clientX - pan.x, y: e.clientY - pan.y });
    }
  }, [pan]);

  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    if (draggingElement) {
      const canvasRect = canvasRef.current?.getBoundingClientRect();
      if (canvasRect) {
        const newX = (e.clientX - canvasRect.left - pan.x) / zoom - dragStart.x;
        const newY = (e.clientY - canvasRect.top - pan.y) / zoom - dragStart.y;
        
        if (draggingElement === 'input') {
          const deltaX = newX - inputPosition.x;
          const deltaY = newY - inputPosition.y;
          
          setInputPosition({ x: newX, y: newY });
          
          // Move all diagram elements when input is moved
          setElementPositions(prev => {
            const newPositions = { ...prev };
            Object.keys(prev).forEach(key => {
              newPositions[key] = {
                x: prev[key].x + deltaX,
                y: prev[key].y + deltaY,
              };
            });
            return newPositions;
          });
        } else {
          setElementPositions(prev => {
            const newPositions = { ...prev };
            const oldPosition = prev[draggingElement];
            const deltaX = newX - oldPosition.x;
            const deltaY = newY - oldPosition.y;
            
            newPositions[draggingElement] = { x: newX, y: newY };
            
            // Move child elements if dragging a chapter
            if (draggingElement.startsWith('chapter-')) {
              const chapterIndex = parseInt(draggingElement.split('-')[1]);
              Object.keys(prev).forEach(key => {
                if (key.startsWith(`topic-${chapterIndex}-`)) {
                  newPositions[key] = {
                    x: prev[key].x + deltaX,
                    y: prev[key].y + deltaY,
                  };
                }
              });
            }
            
            return newPositions;
          });
        }
      }
    } else if (isDragging) {
      setPan({
        x: e.clientX - dragStart.x,
        y: e.clientY - dragStart.y,
      });
    }
  }, [isDragging, dragStart, draggingElement, inputPosition, pan, zoom]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
    setDraggingElement(null);
  }, []);

  const handleWheel = useCallback((e: React.WheelEvent) => {
    e.preventDefault();
    const delta = e.deltaY > 0 ? 0.9 : 1.1;
    setZoom(prev => Math.max(0.1, Math.min(3, prev * delta)));
  }, []);

  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (e.key === 'Enter' && e.ctrlKey) {
        handleSubmit();
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [input, isLoading]);

  const renderDiagram = () => {
    if (!outline) return null;

    return (
      <div className="absolute inset-0">
        {/* Connection lines */}
        <svg className="absolute inset-0 pointer-events-none" style={{ width: '100%', height: '100%', zIndex: 1 }}>
          {outline.chapters.map((chapter, chapterIndex) => {
            const chapterId = `chapter-${chapterIndex}`;
            const chapterPos = elementPositions[chapterId];
            if (!chapterPos) return null;

            return (
              <g key={`lines-${chapterIndex}`}>
                {/* Line from input bottom to chapter */}
                <line
                  x1={inputPosition.x}
                  y1={inputPosition.y + 50}
                  x2={chapterPos.x}
                  y2={chapterPos.y - 30}
                  stroke="#e5e7eb"
                  strokeWidth="2"
                />
                
                {/* Lines from chapter to topics */}
                {chapter.topics.map((topic, topicIndex) => {
                  const topicId = `topic-${chapterIndex}-${topicIndex}`;
                  const topicPos = elementPositions[topicId];
                  if (!topicPos) return null;

                  return (
                    <line
                      key={`topic-line-${topicIndex}`}
                      x1={chapterPos.x + 120}
                      y1={chapterPos.y + 30}
                      x2={topicPos.x}
                      y2={topicPos.y + 20}
                      stroke="#e5e7eb"
                      strokeWidth="1"
                    />
                  );
                })}
              </g>
            );
          })}
        </svg>

        {/* Chapters */}
        {outline.chapters.map((chapter, chapterIndex) => {
          const chapterId = `chapter-${chapterIndex}`;
          const chapterPos = elementPositions[chapterId];
          if (!chapterPos) return null;

          return (
            <div
              key={chapterIndex}
              className="absolute bg-blue-50/90 backdrop-blur-sm rounded-xl p-4 shadow-md border border-blue-200/50 cursor-move pointer-events-auto select-none"
              data-draggable="chapter"
              style={{
                left: chapterPos.x - 120,
                top: chapterPos.y - 30,
                width: 240,
                zIndex: 2,
              }}
              onMouseDown={(e) => handleElementMouseDown(e, chapterId)}
            >
              <h3 className="text-lg font-semibold text-blue-800">
                {chapter.title}
              </h3>
            </div>
          );
        })}

        {/* Topics */}
        {outline.chapters.map((chapter, chapterIndex) =>
          chapter.topics.map((topic, topicIndex) => {
            const topicId = `topic-${chapterIndex}-${topicIndex}`;
            const topicPos = elementPositions[topicId];
            if (!topicPos) return null;

            return (
              <div
                key={topicId}
                className="absolute bg-green-50/90 backdrop-blur-sm rounded-lg p-3 shadow-sm border border-green-200/50 cursor-move pointer-events-auto select-none"
                data-draggable="topic"
                style={{
                  left: topicPos.x - 100,
                  top: topicPos.y - 20,
                  width: 200,
                  zIndex: 2,
                }}
                onMouseDown={(e) => handleElementMouseDown(e, topicId)}
              >
                <h4 className="text-md font-medium text-green-800">
                  {topic.title}
                </h4>
              </div>
            );
          })
        )}
      </div>
    );
  };

  return (
    <div className={`w-screen h-screen overflow-hidden bg-gradient-to-br from-gray-50 to-white relative transition-opacity duration-300 ${isCanvasReady ? 'opacity-100' : 'opacity-0'}`}>
      {/* Infinite Canvas */}
      <div
        ref={canvasRef}
        className="w-full h-full cursor-grab active:cursor-grabbing canvas-background"
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
        onWheel={handleWheel}
        style={{
          transform: `translate(${pan.x}px, ${pan.y}px) scale(${zoom})`,
          transformOrigin: 'center center',
        }}
      >
        {/* Central Input Rectangle */}
        <div 
          className="absolute cursor-move select-none"
          data-draggable="input"
          style={{
            left: inputPosition.x - 192,
            top: inputPosition.y - 50,
            zIndex: 10,
          }}
          onMouseDown={(e) => {
            // Only make draggable if not interacting with input or button
            const target = e.target as HTMLElement;
            if (!target.closest('input') && !target.closest('button')) {
              handleElementMouseDown(e, 'input');
            }
          }}
        >
          <div className="bg-white/95 backdrop-blur-sm rounded-3xl shadow-2xl border border-gray-200/50 p-8 w-96 pointer-events-auto">
            <div className="flex items-center space-x-4">
              <input
                type="text"
                value={input}
                onChange={(e) => setInput(e.target.value)}
                placeholder="Enter your topic or idea..."
                className="flex-1 px-4 py-3 rounded-2xl border border-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-700 placeholder-gray-400"
                onKeyPress={(e) => e.key === 'Enter' && handleSubmit()}
                disabled={isLoading}
              />
              <button
                onClick={handleSubmit}
                disabled={isLoading || !input.trim()}
                className="p-3 rounded-2xl bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors duration-200 shadow-md"
              >
                {isLoading ? (
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                ) : (
                  <svg
                    className="w-5 h-5 text-white"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 10l7-7m0 0l7 7m-7-7v18"
                    />
                  </svg>
                )}
              </button>
            </div>
            <p className="text-xs text-gray-500 mt-3 text-center">
              Press Enter or click the arrow to generate your book outline
            </p>
          </div>
        </div>

        {/* Canvas Background for better panning */}
        <div 
          className="absolute canvas-background"
          style={{
            left: -5000,
            top: -5000,
            width: 10000,
            height: 10000,
            zIndex: -10,
          }}
        />

        {/* Diagram */}
        {renderDiagram()}
      </div>

      {/* Zoom Controls */}
      <div className="absolute bottom-6 right-6 flex flex-col space-y-2">
        <button
          onClick={() => setZoom(prev => Math.min(3, prev * 1.2))}
          className="p-2 bg-white/90 backdrop-blur-sm rounded-lg shadow-md hover:bg-white transition-colors"
        >
          <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
        </button>
        <button
          onClick={() => setZoom(prev => Math.max(0.1, prev / 1.2))}
          className="p-2 bg-white/90 backdrop-blur-sm rounded-lg shadow-md hover:bg-white transition-colors"
        >
          <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
          </svg>
        </button>
        <button
          onClick={() => {
            setZoom(1);
            setPan({ x: 0, y: 0 });
          }}
          className="p-2 bg-white/90 backdrop-blur-sm rounded-lg shadow-md hover:bg-white transition-colors"
        >
          <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5a2 2 0 012-2h4a2 2 0 012 2v2H8V5z" />
          </svg>
        </button>
      </div>

      {/* Instructions */}
      <div className="absolute top-6 left-6 bg-white/90 backdrop-blur-sm rounded-2xl p-4 shadow-md border border-gray-200/50 max-w-sm">
        <h3 className="font-semibold text-gray-800 mb-2">How to use:</h3>
        <ul className="text-sm text-gray-600 space-y-1">
          <li>• Enter your topic in the center box</li>
          <li>• Click the arrow or press Enter to generate</li>
          <li>• Drag to pan around the canvas</li>
          <li>• Scroll to zoom in/out</li>
        </ul>
      </div>
    </div>
  );
}
