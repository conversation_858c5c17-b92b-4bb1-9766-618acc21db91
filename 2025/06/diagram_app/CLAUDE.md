# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

An infinite canvas book outline generator that creates interactive diagrams from user input using GPT-4.1. The app features a minimalistic design with glassmorphism effects, infinite pan/zoom, and drag-and-drop functionality.

## Architecture

- **Frontend**: Next.js 15 with TypeScript, Tailwind CSS 4, React 19 (no external UI libraries)
- **Backend**: FastAPI with Uvicorn, OpenAI API integration
- **Design**: Pure CSS with glassmorphism, no component libraries
- **Data Flow**: User input → FastAPI → OpenAI GPT-4.1 → Structured JSON → Interactive diagram

## Key Commands

### Frontend (in /frontend)
```bash
npm run dev          # Start development server with Turbopack
npm run build        # Build for production  
npm run start        # Start production server
npm run lint         # Run ESLint
```

### Backend (in /backend)
```bash
python main.py       # Start FastAPI server on localhost:8000
pip install -r requirements.txt  # Install dependencies
```

## Core Components

### Frontend Structure
- `app/page.tsx` - Main infinite canvas component with pan/zoom/drag logic
- `app/layout.tsx` - Root layout with metadata
- `app/globals.css` - Tailwind styles

### Backend Structure  
- `main.py` - FastAPI app with CORS, outline generation endpoint
- Pydantic models: `UserInput`, `Topic`, `Chapter`, `BookOutline`

## Development Notes

### Canvas System
- Infinite scrollable canvas with transform-based pan/zoom
- Element positioning uses absolute coordinates 
- Drag system handles input box, chapters, and topics independently
- SVG overlay for connection lines between elements

### API Integration
- Single POST endpoint: `/generate-outline`
- Uses OpenAI JSON mode for structured responses
- Error handling with HTTPException

### State Management
- Pure React hooks (useState, useRef, useCallback)
- Complex position tracking for draggable elements
- Canvas transform state (pan/zoom) separate from element positions

### Environment Setup
Backend requires `.env` file with `OPENAI_API_KEY=your_key_here`

## Design Principles
- No external UI libraries - pure CSS with Tailwind
- Glassmorphism effects with backdrop-blur
- Responsive drag interactions
- Smooth animations with CSS transitions