from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Dict, Any
import openai
import os
import json
import uvicorn

app = FastAPI()

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Set your OpenAI API key
openai.api_key = os.getenv("OPENAI_API_KEY")

class UserInput(BaseModel):
    text: str

class Topic(BaseModel):
    title: str

class Chapter(BaseModel):
    title: str
    topics: List[Topic]

class BookOutline(BaseModel):
    title: str
    chapters: List[Chapter]

@app.post("/generate-outline", response_model=BookOutline)
async def generate_outline(user_input: UserInput):
    try:
        response = openai.chat.completions.create(
            model="gpt-4.1",
            messages=[
                {
                    "role": "system",
                    "content": """You are an expert at creating comprehensive book outlines. 
                    Create a detailed book outline based on the user's input. 
                    Return a JSON object with a title and chapters array. 
                    Each chapter should have a title and topics array.
                    Each topic should have only a title.
                    Make it comprehensive but concise. Aim for 3-5 chapters with 2-4 topics each."""
                },
                {
                    "role": "user",
                    "content": f"Create a book outline for: {user_input.text}"
                }
            ],
            response_format={"type": "json_object"},
            temperature=0.7
        )
        
        outline_data = json.loads(response.choices[0].message.content)
        return BookOutline(**outline_data)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating outline: {str(e)}")

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

if __name__ == "__main__":
    uvicorn.run("main:app", host="127.0.0.1", port=8000, reload=True) 