# Infinite Canvas Book Outline Generator

A beautiful, minimalistic infinite canvas app that generates book outlines using GPT-4.1. Enter any topic and watch as a comprehensive book structure unfolds as an interactive diagram.

## Features

- 🎨 **Infinite Canvas**: Pan and zoom around a limitless workspace
- 📝 **Smart Input**: Clean, rounded rectangle input area with submit button
- 🤖 **AI-Powered**: Uses GPT-4.1 to generate comprehensive book outlines
- 📊 **Visual Diagrams**: Automatically generates connected diagrams showing chapters and topics
- ✨ **Beautiful UI**: Minimalistic design with glassmorphism effects
- 🎯 **No External Libraries**: Built with pure React and FastAPI

## Setup Instructions

### Backend Setup

1. Navigate to the backend directory:
   ```bash
   cd backend
   ```

2. Install Python dependencies:
   ```bash
   pip install -r requirements.txt
   ```

3. Create a `.env` file and add your OpenAI API key:
   ```
   OPENAI_API_KEY=your_openai_api_key_here
   ```

4. Start the FastAPI server:
   ```bash
   python main.py
   ```

The backend will run on `http://127.0.0.1:8000`

### Frontend Setup

1. Navigate to the frontend directory:
   ```bash
   cd frontend
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Start the development server:
   ```bash
   npm run dev
   ```

The frontend will run on `http://localhost:3000`

## How to Use

1. **Enter Your Topic**: Type any subject or idea in the center input box
2. **Generate Outline**: Click the upward arrow or press Enter
3. **Explore the Diagram**: The AI will generate a book outline that appears as connected boxes
4. **Navigate**: 
   - Drag to pan around the canvas
   - Scroll to zoom in/out
   - Use the zoom controls in the bottom-right corner

## Architecture

- **Frontend**: Next.js with TypeScript and Tailwind CSS
- **Backend**: FastAPI with Pydantic models
- **AI**: OpenAI GPT-4.1 with JSON mode for structured responses
- **Styling**: Pure CSS with glassmorphism and modern design principles

## API Endpoints

- `POST /generate-outline`: Generate a book outline from user input
- `GET /health`: Health check endpoint

The app creates beautiful, structured diagrams showing:
- **Title**: Main book title at the top
- **Chapters**: Blue boxes with chapter titles and descriptions
- **Topics**: Green boxes with specific topics under each chapter
- **Connections**: Clean lines connecting all elements

Enjoy creating beautiful book outlines! ✨ 