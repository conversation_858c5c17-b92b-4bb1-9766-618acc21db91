import os
import json
import asyncio
import base64
import re # Added for filename sanitization
from openai import AsyncOpenAI
import aiofiles # For async file operations
from moviepy.editor import ImageClip, AudioFileClip, concatenate_videoclips, concatenate_audioclips
import time # For potential unique filenames
from termcolor import cprint # Added for colored output
import imageio_ffmpeg # Added to bundle ffmpeg
from moviepy.config import change_settings # Added to configure moviepy

# --- Configure MoviePy to use bundled FFmpeg --- 
try:
    change_settings({"FFMPEG_BINARY": imageio_ffmpeg.get_ffmpeg_exe()})
    print("INFO: MoviePy configured to use bundled FFmpeg from imageio-ffmpeg.")
except Exception as e:
    print(f"WARNING: Could not configure MoviePy to use bundled FFmpeg: {e}")
    print("INFO: Ensure FFmpeg is installed system-wide and in PATH if bundling fails.")

API_KEY = os.getenv("OPENAI_API_KEY") or "ENTER YOUR OWN API KEY HERE"

# --- Configuration ---
# Scene Generation
SCENE_MODEL = "gpt-4.1"
# your character for the video
CHARACTER_IMAGE_PATH = "sample_characters/21.png" #choose a character which you want to use for the video from the sample_characters folder
NUM_SCENES = 3  # Number of logical scenes/story beats, use minimum of 2
IMAGES_PER_SCENE = 3 # Number of images to generate for each logical scene
PER_SCENE_AUDIO_LENGTH = "10 to 20 words" # you can use this to influence the audio length of each scene
AUDIO_VOICE = "ash"  # Can be one of: alloy, ash, ballad, coral, echo, fable, onyx, nova, sage, shimmer, verse


# ENTER YOUR PROMPT HERE. 
# IF LEFT BLANK, YOU WILL BE PROMPTED FOR ONE IN THE TERMINAL. 
# make this as detailed as you like! and have fun!!

VIDEO_PROMPT = """"""

# Image Generation

IMAGE_MODEL = "gpt-image-1" # As requested
# OUTPUT_IMAGE_DIR = "output_images" # Removed - now dynamic
IMAGE_SIZE = "1024x1024" # can also be portrait: 1024x1536 or square: 1024x1024 or landscape: 1536x1024
IMAGE_QUALITY = "medium" # quality of the image
BACKGROUND = "whatever fits the scene description" # can be "white" or "black" or "whatever fits the scene description" or a specific description of a scene

BASE_IMAGE_PROMPT_TEMPLATE = "Edit the character shown in the provided base image against a {background} background so they fit and it well integrates with the and acts within the following scene description: {scene_description}. Keep the entire image fit into the style of the reference image you are shown. Apply the rule of thirds and keep most of the background minimally blank. Sometimes, colorful graphics with a ragged, hand-drawn feel can be displayed on one side of the image, with the main character on the other."

# Audio Generation
AUDIO_MODEL = "gpt-4o-mini-tts"

# OUTPUT_AUDIO_DIR = "output_audio" # Removed - now dynamic

# Video Creation
OUTPUT_VIDEO_FILENAME = "final_video.mp4" # Just the filename now
FPS = 24 # Frames per second for the final video

# Scene Data Output
# OUTPUT_SCENE_DATA_DIR = "output_scenes" # Removed - now dynamic

# Top Level Output Directory
TOP_LEVEL_OUTPUT_DIR = "generated_content"

# Initialize Async OpenAI client (expects OPENAI_API_KEY environment variable)
client = AsyncOpenAI(api_key=API_KEY)

def sanitize_filename(text: str) -> str:
    """Sanitizes the first 3 words of a string to be safe for use as a filename."""
    words = text.split()
    # Take first 3 words, or fewer if not enough words
    first_three_words = words[:5]
    # Join with underscore
    base_name = "_".join(first_three_words)
    
    # Remove invalid filename characters
    sanitized = re.sub(r'[\\/*?"<>|:]', '', base_name)
    # Replace any remaining spaces (shouldn't be any, but just in case) with underscores
    sanitized = sanitized.replace(' ', '_')
    # Convert to lowercase for consistency
    sanitized = sanitized.lower()
    
    # Truncate to max_length (optional, could be removed if 3 words are always short enough)
    # max_length = 30 # Example max length
    # sanitized = sanitized[:max_length]
    
    # Ensure it's not empty after sanitization
    if not sanitized:
        return "generated_run"
    return sanitized

async def generate_scene_data(user_prompt: str, num_scenes: int, images_per_scene: int) -> list[dict]:
    """
    Generates scene descriptions (list per scene), narration text, and detailed audio instructions.

    Returns:
        A list of dictionaries, each representing a story beat containing:
        - "scene_descriptions": A list of strings (length = images_per_scene).
        - "narration_text": A single string for the beat.
        - "audio_instructions": A dict containing voice, punctuation, etc.
        Returns an empty list on failure.
    """
    system_prompt = f"""
You are a creative assistant generating assets for a short video featuring a character.

Based on the user's prompt, create a storyline broken into exactly {num_scenes} distinct logical scenes (story beats).
For each logical scene, provide:
1.  `narration_text`: A single short spoken line (around {PER_SCENE_AUDIO_LENGTH}) covering the entire logical scene.
2.  `scene_descriptions`: A list containing exactly {images_per_scene} concise visual descriptions. These should show a mini-progression or different facets of the logical scene, suitable for generating sequential images. Describe each element in the scene with the overall story in mind. because the text to image model won't see the other scene descriptions, everything needed should be present in each scene description.
3.  `audio_instructions`: A JSON object containing specific TTS instructions (voice, punctuation, delivery, phrasing, tone) for the scene's narration.
NOTE: Scenes should follow closely to the what the narration text says for the scene.

Output the result as a JSON object containing a single key "scenes", which is a list of these scene objects.

Example User Prompt: "Character finds a mysterious glowing orb."
Example JSON Output:
{{
  "scenes": [

    {{
      "narration_text": "Okay, just keep walking... what was that noise over there? Did I see something?",    
      "scene_descriptions": [
          "walking cautiously through a dark forest, looking left with wide eyes.",
          "stopping suddenly, peering ahead into the shadows.",
          "eyes locking onto a faint light source deeper in the trees."
          IMPORTANT: You must exactly generate a total of {images_per_scene} images for each scene. not more, not less.
       ],
      "audio_instructions": {{
          "voice": "slightly nervous whisper",
          "punctuation": "hesitant pauses",
          "delivery": "hesitant, quiet",
          "phrasing": "questioning, self-talk",
          "tone": "nervous, slightly alert"
      }}
    }}
    # ... (continue for other scenes if num_scenes > 1)
    IMPORTANT: You must exactly generate a total of {num_scenes} scenes. not more, not less.
  ]
}}
"""
    cprint(f"Generating scene data for prompt: '{user_prompt}'...", "yellow")
    try:
        response = await client.chat.completions.create(
            model=SCENE_MODEL,
            response_format={"type": "json_object"},
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ],
        )
        content = response.choices[0].message.content
        if content:
            scene_data = json.loads(content)
            # --- Updated Validation --- 
            valid = False
            if "scenes" in scene_data and isinstance(scene_data["scenes"], list) and len(scene_data["scenes"]) == num_scenes:
                valid = True # Assume valid initially
                for s in scene_data["scenes"]:
                    if not (
                        isinstance(s, dict) and
                        "scene_descriptions" in s and isinstance(s["scene_descriptions"], list) and len(s["scene_descriptions"]) == images_per_scene and all(isinstance(desc, str) for desc in s["scene_descriptions"]) and
                        "narration_text" in s and isinstance(s["narration_text"], str) and
                        "audio_instructions" in s and isinstance(s["audio_instructions"], dict) and all(k in s["audio_instructions"] for k in ["voice", "punctuation", "delivery", "phrasing", "tone"])
                    ):
                        valid = False
                        break # Invalid scene found
            
            if valid:
                 cprint("Scene data generated successfully.", "green")
                 return scene_data["scenes"]
            else:
                cprint(f"Error: Invalid JSON structure, scene count, descriptions count/type, or audio_instructions format received. Expected {num_scenes} scenes, {images_per_scene} descriptions per scene.", "red")
                cprint(f"Received content: {content}", "red")
                return []
        else:
            cprint("Error: Empty content received from scene generation API.", "red")
            return []

    except Exception as e:
        cprint(f"An error occurred during scene data generation: {e}", "red")
        return []

async def generate_image_for_scene(scene_description: str, scene_index: int, image_sub_index: int, image_output_dir: str) -> bool:
    """
    Generates/Edits an image for a given scene description and saves it to the specified directory.
    """
    full_prompt = BASE_IMAGE_PROMPT_TEMPLATE.format(scene_description=scene_description, background=BACKGROUND)
    # Include sub-index in filename, join with specific image dir
    output_path = os.path.join(image_output_dir, f"scene_{scene_index+1}_img_{image_sub_index+1}.png") 
    cprint(f"  Generating image {image_sub_index+1} for scene {scene_index+1}...", "magenta")

    try:
        # Call the DALL-E API (edit endpoint) asynchronously
        # Note: Passing image bytes directly as 'image_data'
        result = await client.images.edit(
            model=IMAGE_MODEL,
            image=[open(CHARACTER_IMAGE_PATH, "rb")], # Pass image bytes
            prompt=full_prompt,
            size=IMAGE_SIZE,
            quality=IMAGE_QUALITY, 
            n=1,
        )

        image_base64 = result.data[0].b64_json
        if image_base64:
            image_bytes = base64.b64decode(image_base64)
            async with aiofiles.open(output_path, "wb") as f:
                await f.write(image_bytes)
            cprint(f"  Successfully saved image to '{output_path}'", "green")
            return True
        else:
             cprint(f"Error: No image data received for scene {scene_index+1}, image {image_sub_index+1}.", "red")
             return False

    except Exception as e:
        cprint(f"An error occurred during image generation for scene {scene_index+1}, image {image_sub_index+1}: {e}", "red")
        return False

async def generate_audio_for_scene(narration_text: str, audio_instructions: dict, scene_index: int, audio_output_dir: str) -> bool:
    """
    Generates audio for the narration text and saves it to the specified directory.
    """
    # Join with specific audio dir
    output_path = os.path.join(audio_output_dir, f"scene_{scene_index+1}.mp3") 
    
    instructions_string = f"Voice: {audio_instructions.get('voice', 'default')}. Punctuation: {audio_instructions.get('punctuation', 'default')}. Delivery: {audio_instructions.get('delivery', 'default')}. Phrasing: {audio_instructions.get('phrasing', 'default')}. Tone: {audio_instructions.get('tone', 'default')}."
    
    cprint(f"  Generating audio for scene {scene_index+1} using instructions: {audio_instructions}...", "magenta")

    try:
        response = await client.audio.speech.create(
            model=AUDIO_MODEL,
            voice=AUDIO_VOICE,
            input=narration_text,
            instructions=instructions_string,
        )
        response.write_to_file(output_path)
        cprint(f"  Successfully saved audio to '{output_path}'", "green")
        return True

    except Exception as e:
        cprint(f"An error occurred during audio generation for scene {scene_index+1}: {e}", "red")
        return False

async def generate_assets_for_scene(scene_data: dict, scene_index: int, image_dir: str, audio_dir: str):
    """
    Generates multiple images and one audio clip for a single scene beat concurrently,
    saving them to the provided directories.
    """
    cprint(f"\nProcessing Scene Beat {scene_index + 1}:", "cyan")
    # Print all descriptions
    for i, desc in enumerate(scene_data['scene_descriptions']):
        cprint(f"  Img {i+1} Desc: {desc}", "yellow")
    cprint(f"  Narration: {scene_data['narration_text']}", "yellow")
    cprint(f"  Audio Instructions: {scene_data['audio_instructions']}", "yellow")

    # Create multiple image generation tasks, passing image_dir
    scene_descriptions = scene_data['scene_descriptions']
    img_tasks = [asyncio.create_task(generate_image_for_scene(desc, scene_index, img_idx, image_dir))
                 for img_idx, desc in enumerate(scene_descriptions)]
    
    # Create the single audio task, passing audio_dir
    audio_task = asyncio.create_task(generate_audio_for_scene(scene_data['narration_text'], scene_data['audio_instructions'], scene_index, audio_dir))

    # Wait for all tasks for this scene beat to complete
    all_tasks = img_tasks + [audio_task] # Combine tasks
    results = await asyncio.gather(*all_tasks, return_exceptions=True)

    # Check results - results list order matches all_tasks list order
    img_results = results[:len(img_tasks)] # First N results are for images
    audio_result = results[-1] # Last result is for audio

    for i, res in enumerate(img_results):
        if isinstance(res, Exception):
            cprint(f"  Image generation failed for scene {scene_index+1}, image {i+1}.", "red")
            # Error details are already printed inside generate_image_for_scene

    if isinstance(audio_result, Exception):
         cprint(f"  Audio generation failed for scene {scene_index+1}.", "red")
         # Error details are already printed inside generate_audio_for_scene

async def create_video_from_assets(num_scenes: int, images_per_scene: int, image_dir: str, audio_dir: str, output_video_path: str):
    """
    Combines generated images and audio from specified directories into a final video file.
    """
    cprint(f"\n--- Creating Video: '{output_video_path}' --- ", "cyan", attrs=["bold"])
    
    all_scene_video_segments = []
    all_audio_clips = []
    total_duration = 0
    files_ok = True

    cprint("Loading assets and creating scene segments...", "cyan")
    for i in range(num_scenes):
        scene_beat_index = i + 1
        cprint(f" Processing assets for scene beat {scene_beat_index}...", "yellow")
        
        # --- Audio for the scene beat --- 
        audio_path = os.path.join(audio_dir, f"scene_{scene_beat_index}.mp3") # Use audio_dir
        if not os.path.exists(audio_path):
            cprint(f"Error: Required audio file not found: {audio_path}", "red")
            files_ok = False
            continue # Skip this scene beat if audio is missing
        
        try:
            audio_clip = AudioFileClip(audio_path)
            scene_audio_duration = audio_clip.duration
            if scene_audio_duration <= 0:
                cprint(f"Warning: Audio duration for {audio_path} is zero or negative. Skipping scene beat {scene_beat_index}.", "red")
                audio_clip.close()
                continue
            all_audio_clips.append(audio_clip)
            total_duration += scene_audio_duration
        except Exception as e:
            cprint(f"Error loading audio clip {audio_path}: {e}", "red")
            files_ok = False
            continue
            
        # --- Images for the scene beat --- 
        image_files_for_scene = []
        scene_image_clips = []
        image_duration = scene_audio_duration / images_per_scene # Duration per image
        
        for img_idx in range(images_per_scene):
            img_sub_index = img_idx + 1
            img_path = os.path.join(image_dir, f"scene_{scene_beat_index}_img_{img_sub_index}.png") # Use image_dir
            image_files_for_scene.append(img_path)
            if not os.path.exists(img_path):
                cprint(f"Error: Required image file not found: {img_path}", "red")
                files_ok = False
                # Don't break immediately, report all missing for this scene
            else:
                try:
                    img_clip = ImageClip(img_path).set_duration(image_duration)
                    scene_image_clips.append(img_clip)
                except Exception as e:
                    cprint(f"Error loading image clip {img_path}: {e}", "red")
                    files_ok = False

        if not files_ok or len(scene_image_clips) != images_per_scene:
            cprint(f"Skipping video segment creation for scene beat {scene_beat_index} due to missing/invalid images.", "red")
            # Clean up already loaded clips for this scene if needed
            for clip in scene_image_clips: clip.close()
            # We keep the audio clip in the list for now, maybe filter later or let concatenation fail?
            # Let's remove the corresponding audio clip to keep sync if images failed
            if audio_clip in all_audio_clips: 
                all_audio_clips.remove(audio_clip)
                total_duration -= scene_audio_duration
                audio_clip.close()
            files_ok = False # Ensure overall status reflects failure
            continue # Move to next scene beat

        # Concatenate images for this scene beat
        if scene_image_clips:
            scene_video_segment = concatenate_videoclips(scene_image_clips, method="compose")
            all_scene_video_segments.append(scene_video_segment)
            cprint(f"  Created video segment for scene beat {scene_beat_index} ({len(scene_image_clips)} images, duration={scene_audio_duration:.2f}s).", "green")
        else: # Should not happen if logic above is correct, but as safety
             cprint(f"Internal Warning: No image clips available for scene beat {scene_beat_index} despite checks.", "red")
             files_ok = False
             if audio_clip in all_audio_clips: # Clean up audio if images failed unexpectedly
                 all_audio_clips.remove(audio_clip)
                 total_duration -= scene_audio_duration
                 audio_clip.close()
             
    # --- Final Assembly --- 
    if not files_ok or not all_scene_video_segments or not all_audio_clips:
        cprint("Video creation cancelled due to missing or invalid assets during processing.", "red")
        # Clean up any loaded clips
        for clip in all_audio_clips + all_scene_video_segments: clip.close()
        return

    try:
        # Concatenate all scene video segments
        cprint("Concatenating final video track...", "cyan")
        final_video_clip = concatenate_videoclips(all_scene_video_segments, method="compose")

        # Concatenate all audio clips
        cprint("Concatenating final audio track...", "cyan")
        final_audio_clip = concatenate_audioclips(all_audio_clips)

        # Set audio and write file
        final_video_clip = final_video_clip.set_audio(final_audio_clip)
        
        # --- Calculate target duration for trimming --- 
        trim_seconds = 0.2
        target_duration = max(0, total_duration - trim_seconds) # Ensure not negative
        cprint(f"Targeting final duration: {target_duration:.2f}s (Trimming last {trim_seconds}s)", "yellow")

        # --- Trim the final clip using moviepy ---
        if target_duration > 0 and target_duration < final_video_clip.duration:
            final_video_clip = final_video_clip.subclip(0, target_duration)
            cprint(f"Clip trimmed to {final_video_clip.duration:.2f}s using subclip.", "yellow")

        # Prepare ffmpeg parameters (removed trim parameter)
        output_ffmpeg_params = [
            "-preset", "medium", 
            "-crf", "23",
            # "-to", str(target_duration) # Removed - trimming done via subclip
        ]

        # --- Determine number of threads for encoding --- 
        num_cores = os.cpu_count()
        threads_to_use = num_cores if num_cores else 4 # Default to 4 if cpu_count is None
        cprint(f"Using {threads_to_use} threads for video encoding.", "yellow")

        cprint(f"Writing final video to '{output_video_path}' (Duration: {total_duration:.2f}s, FPS: {FPS})...", "cyan") 
        final_video_clip.write_videofile(
            output_video_path, 
            fps=FPS,
            codec='libx264',
            audio_codec='aac',
            temp_audiofile='temp-audio.m4a',
            remove_temp=True,
            ffmpeg_params=output_ffmpeg_params, # Use params without -to
            threads=threads_to_use # Added threads parameter
        )

        cprint(f"--- Video creation successful: '{output_video_path}' --- ", "green", attrs=["bold"])

    except Exception as e:
        cprint(f"An error occurred during final video assembly or writing: {e}", "red")
    finally:
        # Ensure all clips are closed
        cprint("Closing all media clips...", "yellow")
        for clip in all_audio_clips + all_scene_video_segments: # Includes segments and individual audio
             try: clip.close() 
             except Exception as ce: cprint(f" Minor error closing clip: {ce}", "red")
        # Need to close the final concatenated clips too if they were created
        if 'final_video_clip' in locals() and final_video_clip: 
            try: final_video_clip.close()
            except Exception as ce: cprint(f" Minor error closing final video clip: {ce}", "red")
        if 'final_audio_clip' in locals() and final_audio_clip: 
            try: final_audio_clip.close()
            except Exception as ce: cprint(f" Minor error closing final audio clip: {ce}", "red")

async def main():
    cprint("--- Character Video Asset Generator ---", "cyan", attrs=["bold"])

    # --- Pre-checks ---
    if not os.path.exists(CHARACTER_IMAGE_PATH):
        cprint(f"Error: Base image '{CHARACTER_IMAGE_PATH}' not found.", "red")
        return
    if 'OPENAI_API_KEY' not in os.environ:
         cprint("Error: OPENAI_API_KEY environment variable not set.", "red")
         return

    # --- User Input ---
    # Use cprint for the input prompt, although input() itself won't be colored
    if not VIDEO_PROMPT:
        prompt_text = f"Enter a general theme or story prompt (e.g., 'character builds a sandcastle'): "
        cprint(prompt_text, "yellow", end="") 
        user_general_prompt = input() # Get input after colored prompt
    else:
        user_general_prompt = VIDEO_PROMPT
    
    if not user_general_prompt:
        cprint("No prompt entered. Exiting.", "yellow")
        return

    # --- Determine Unique Output Directory --- 
    sanitized_prompt = sanitize_filename(user_general_prompt)
    base_run_path = os.path.join(TOP_LEVEL_OUTPUT_DIR, sanitized_prompt)
    run_output_dir = base_run_path
    counter = 1
    while os.path.exists(run_output_dir):
        run_output_dir = f"{base_run_path}_{counter}"
        counter += 1
        
    cprint(f"Outputting generated assets to: {run_output_dir}", "cyan")
    
    # --- Create Output Directories --- 
    try:
        # Define specific subdirs for this run
        image_dir = os.path.join(run_output_dir, "images")
        audio_dir = os.path.join(run_output_dir, "audio")
        scenes_dir = os.path.join(run_output_dir, "scenes")
        # Create all necessary directories 
        os.makedirs(image_dir, exist_ok=True) 
        os.makedirs(audio_dir, exist_ok=True)
        os.makedirs(scenes_dir, exist_ok=True) 
        cprint(f"Created output directories under '{run_output_dir}'", "green")
    except OSError as e:
         cprint(f"Error creating output directories under '{run_output_dir}': {e}", "red")
         return # Exit if we can't create directories

    # --- Generate Scene Descriptions and Narrations ---
    scenes = await generate_scene_data(user_general_prompt, NUM_SCENES, IMAGES_PER_SCENE)

    if not scenes:
        cprint("Could not generate scene data. Exiting.", "red")
        return

    # --- Save Scene Data to JSON (in its specific subdir) --- 
    try:
        # Create the data structure to save
        data_to_save = {
            "video_prompt": user_general_prompt,
            "scenes": scenes 
        }
        
        json_filename = f"{sanitized_prompt}_scenes.json"
        json_filepath = os.path.join(scenes_dir, json_filename) # Use scenes_dir
        async with aiofiles.open(json_filepath, "w") as f:
            await f.write(json.dumps(data_to_save, indent=4)) # Save the new structure
        cprint(f"Successfully saved scene data (including prompt) to '{json_filepath}'", "green")
    except Exception as e:
        cprint(f"Warning: Could not save scene data to JSON file '{json_filepath}': {e}", "yellow")

    # --- Generate Image and Audio Assets Concurrently ---
    cprint(f"\nGenerating {IMAGES_PER_SCENE} images and 1 audio clip per scene for {len(scenes)} scenes...", "cyan", attrs=["bold"])
    # Pass specific image_dir and audio_dir to the asset generator
    asset_generation_tasks = [generate_assets_for_scene(scene, i, image_dir, audio_dir) for i, scene in enumerate(scenes)]
    await asyncio.gather(*asset_generation_tasks)

    cprint("\n--- Asset Generation Complete ---", "green", attrs=["bold"])
    cprint(f"Images saved in: '{image_dir}'", "green")
    cprint(f"Audio saved in: '{audio_dir}'", "green")
    # print("Next step: Combine these assets into a video using a video editing tool or library (e.g., MoviePy, FFmpeg).")

    # --- Create Final Video --- 
    # Check if assets for the first scene beat seem to exist in their new location
    first_img = os.path.join(image_dir, f"scene_1_img_1.png") 
    first_audio = os.path.join(audio_dir, f"scene_1.mp3")
    final_video_path = os.path.join(run_output_dir, OUTPUT_VIDEO_FILENAME) # Full path for video
    
    if scenes and os.path.exists(first_img) and os.path.exists(first_audio):
         # Pass specific dirs and full video path
         await create_video_from_assets(len(scenes), IMAGES_PER_SCENE, image_dir, audio_dir, final_video_path) 
    else:
        cprint("\nSkipping video creation as essential assets seem to be missing.", "yellow")

if __name__ == "__main__":
    # Directory creation is now handled within main() after getting user input
    asyncio.run(main())

    # Updated Dependency Note
    cprint("\nNOTE: Dependencies: moviepy, imageio, imageio-ffmpeg, termcolor, aiofiles, openai", "grey")
    cprint("Install with: pip install moviepy imageio imageio-ffmpeg termcolor aiofiles openai", "grey")
    # cprint("FFmpeg needs to be installed separately on your system.", "grey") # Removed
    # cprint("Search online for 'install ffmpeg windows/mac/linux'.", "grey") # Removed