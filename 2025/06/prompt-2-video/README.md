# Prompt to Video

A Python application that generates short, animated videos from text prompts using character images and OpenAI APIs.

**COST: Main cost is per image cost at $0.063 cents per image at medium quality. 4 scene, 3 image per scene with 10-20 words per scene will be around 20-30 seconds and will cost $0.80 roughly speaking. Square images are $0.42 so squaer videos are cheaper**

NOTE: Just save your character image you want to use under sample_characters folder and point to it in the script

## Overview

Prompt to Video lets you create engaging stick figure videos from simple text prompts. The application handles the entire process automatically:

1. Generates scene descriptions, narration, and audio instructions from your text prompt
2. Creates images featuring your chosen character in each scene
3. Generates narration audio with appropriate voice and emotion
4. Combines everything into a cohesive video with synchronized audio and visuals

## Configuration Options

The script has several configurable options at the top of the file:

| Variable                   | Description                                                                        |
| -------------------------- | ---------------------------------------------------------------------------------- |
| `SCENE_MODEL`            | OpenAI model used for scene generation (default: "gpt-4o")                         |
| `NUM_SCENES`             | Number of logical scenes/story beats (default: 3)                                  |
| `IMAGES_PER_SCENE`       | Number of images to generate for each scene (default: 3)                           |
| `PER_SCENE_AUDIO_LENGTH` | Controls the length of audio per scene (e.g., "10 to 20 words")                    |
| `VIDEO_PROMPT`           | Your text prompt for video generation                                              |
| `CHARACTER_IMAGE_PATH`   | Path to the character image to use in the video (from sample_characters folder)    |
| `IMAGE_MODEL`            | OpenAI model for image generation (default: "gpt-image-1")                         |
| `IMAGE_SIZE`             | Size of generated images (default: "1536x1024", can be "1024x1536" or "1024x1024") |
| `IMAGE_QUALITY`          | Quality setting for images (default: "medium")                                     |
| `BACKGROUND`             | Background setting for images (default: "whatever fits the scene description")     |
| `AUDIO_MODEL`            | OpenAI model for TTS audio generation (default: "gpt-4o-mini-tts")                 |
| `AUDIO_VOICE`            | Voice for text-to-speech (default: "ash")                                          |
| `OUTPUT_VIDEO_FILENAME`  | Filename for the final video (default: "final_video.mp4")                          |
| `FPS`                    | Frames per second for the final video (default: 24)                                |
| `TOP_LEVEL_OUTPUT_DIR`   | Directory for storing all generated content (default: "generated_content")         |

## Requirements

This application requires:

- Python 3.8+
- OpenAI API key (set as environment variable `OPENAI_API_KEY`)
- Various Python packages (see requirements.txt)
- FFmpeg (bundled via imageio-ffmpeg)

### Python Dependencies

Install all requirements with:

```
pip install -r requirements.txt
```

### FFmpeg

The application attempts to use the bundled FFmpeg from imageio-ffmpeg. If you encounter any issues with FFmpeg, please refer to the [official installation guide](https://www.hostinger.com/tutorials/how-to-install-ffmpeg) for manual installation instructions.

## Usage

1. Set your OpenAI API key as an environment variable:

   ```
   export OPENAI_API_KEY="your-api-key-here"  # Unix/Mac
   # or
   set OPENAI_API_KEY="your-api-key-here"     # Windows
   ```
2. Configure the script by editing the variables at the top of `prompt_to_video.py`

   - Choose a character image from the `sample_characters` folder
   - Set your video prompt
   - Adjust other parameters as needed
3. Run the script:

   ```
   python prompt_to_video.py
   ```
4. If you didn't set a VIDEO_PROMPT in the script, you'll be prompted to enter one
5. The script will generate all necessary assets and create a final video in the `generated_content` directory

## How It Works

1. **Scene Generation**: Using OpenAI's GPT model, the script creates logical scenes with descriptions, narration text, and audio instructions.
2. **Image Generation**: For each scene description, the script generates images showing your character in the described scenario.
3. **Audio Generation**: The script creates voiceovers for each scene with appropriate emotional attributes.
4. **Video Assembly**: All assets are combined into a final video with images timed to match the audio narration.

## Output Structure

All generated content is stored in the `generated_content` directory, with each prompt getting its own subdirectory containing:

- `images/`: Generated scene images
- `audio/`: Generated audio narration
- `scenes/`: JSON files with scene descriptions and metadata
- A final video file named according to `OUTPUT_VIDEO_FILENAME`

## Notes

- Image generation and audio synthesis use OpenAI's APIs and count towards your usage
- Higher quality images and more complex prompts may take longer to process
- The script handles error cases gracefully and provides colored terminal output for monitoring progress

## Support

For questions, issues, or feedback, you can reach out via:

- **X (Twitter):** [@hive_echo](https://x.com/hive_echo)
- **Email:** <EMAIL>

## License

This software is free to use for personal and commercial purposes. However, it may not be redistributed or resold or used as an online paid service for others.

The creator of this software is not responsible for how it is used. Users are responsible for ensuring their usage complies with OpenAI's terms of service and any applicable laws and regulations.
