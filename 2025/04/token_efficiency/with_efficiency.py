import anthropic
from termcolor import colored

client = anthropic.Anthropic()

response = client.beta.messages.create(
    max_tokens=1024,
    model="claude-3-7-sonnet-20250219",
    tools=[{
      "name": "get_weather",
      "description": "Get the current weather in a given location",
      "input_schema": {
        "type": "object",
        "properties": {
          "location": {
            "type": "string",
            "description": "The city and state, e.g. San Francisco, CA"
          }
        },
        "required": [
          "location"
        ]
      }
    }],
    messages=[{
      "role": "user",
      "content": "Tell me the weather in San Francisco."
    }],
    betas=["token-efficient-tools-2025-02-19"]
)

# Print usage statistics with colored output
usage = response.usage
print(colored("Token Usage Statistics:", "cyan", attrs=["bold"]))
print(colored(f"Input tokens: {usage.input_tokens}", "green"))
print(colored(f"Output tokens: {usage.output_tokens}", "yellow"))
print(colored(f"Cache creation input tokens: {usage.cache_creation_input_tokens}", "magenta"))
print(colored(f"Cache read input tokens: {usage.cache_read_input_tokens}", "blue"))
