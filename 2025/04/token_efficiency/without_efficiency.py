import anthropic
from termcolor import colored

client = anthropic.Anthropic()

response = client.beta.messages.create(
    max_tokens=1024,
    model="claude-3-7-sonnet-20250219",
    tools=[{
      "name": "get_weather",
      "description": "Get the current weather in a given location",
      "input_schema": {
        "type": "object",
        "properties": {
          "location": {
            "type": "string",
            "description": "The city and state, e.g. San Francisco, CA"
          }
        },
        "required": [
          "location"
        ]
      }
    }],
    messages=[{
      "role": "user",
      "content": "Tell me the weather in San Francisco."
    }],
)

print(colored(f"Input tokens: {colored(response.usage.input_tokens, 'green')}", 'cyan'))
print(colored(f"Output tokens: {colored(response.usage.output_tokens, 'green')}", 'cyan'))
print(colored(f"Cache creation input tokens: {colored(response.usage.cache_creation_input_tokens, 'green')}", 'cyan'))
print(colored(f"Cache read input tokens: {colored(response.usage.cache_read_input_tokens, 'green')}", 'cyan'))
