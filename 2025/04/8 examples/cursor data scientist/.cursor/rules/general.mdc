---
description: 
globs: 
alwaysApply: true
---

read first 50 rows from [train.csv](mdc:train.csv) 
create a ML script to solve this with maximum accuracy and run it
create submission.csv by testing it against [test.csv](mdc:test.csv)
your script shiould print evaluation metrics which you can use to improve the ML script you have written
continue until you achieve 82%+ accuracy

---
use GPU support(if available)
use pytorch instead of TF when needed
document and save past experiments and past submission.csv files into a backup folder after each experiment
use your creativity to fully explore the dataset and find new ways to tackle this problem
you must avoid overfitting
