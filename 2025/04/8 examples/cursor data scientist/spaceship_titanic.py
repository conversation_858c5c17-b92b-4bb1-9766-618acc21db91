import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split, cross_val_score, StratifiedKFold
from sklearn.preprocessing import StandardScaler, OneHotEncoder
from sklearn.impute import SimpleImputer
from sklearn.compose import ColumnTransformer
from sklearn.pipeline import Pipeline
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
from termcolor import colored

# Set random seed for reproducibility
np.random.seed(42)

# Load the data
print(colored("Loading data...", "green"))
train_data = pd.read_csv('train.csv')
test_data = pd.read_csv('test.csv')

# Save copies of original data for reference
original_train = train_data.copy()
original_test = test_data.copy()

# Exploratory Data Analysis
print(colored("Exploring data...", "green"))
print(f"Train data shape: {train_data.shape}")
print(f"Test data shape: {test_data.shape}")

# Check for missing values
print(colored("\nMissing values in train data:", "yellow"))
print(train_data.isnull().sum())

# Extract PassengerId for submission
test_passenger_ids = test_data['PassengerId']

# Data Preprocessing
print(colored("\nPreprocessing data...", "green"))

# Extract information from PassengerId 
train_data['Group'] = train_data['PassengerId'].str.split('_').str[0].astype(int)
train_data['Member'] = train_data['PassengerId'].str.split('_').str[1].astype(int)
test_data['Group'] = test_data['PassengerId'].str.split('_').str[0].astype(int)
test_data['Member'] = test_data['PassengerId'].str.split('_').str[1].astype(int)

# Extract deck, num, and side from Cabin
def extract_cabin_info(df):
    if pd.isnull(df['Cabin']):
        return pd.Series([np.nan, np.nan, np.nan])
    parts = df['Cabin'].split('/')
    deck = parts[0] if len(parts) > 0 else np.nan
    num = parts[1] if len(parts) > 1 else np.nan 
    side = parts[2] if len(parts) > 2 else np.nan
    return pd.Series([deck, num, side])

train_cabin = train_data.apply(extract_cabin_info, axis=1)
train_data['Deck'] = train_cabin[0]
train_data['Cabin_num'] = train_cabin[1].astype('float', errors='ignore')
train_data['Cabin_side'] = train_cabin[2]

test_cabin = test_data.apply(extract_cabin_info, axis=1)
test_data['Deck'] = test_cabin[0]
test_data['Cabin_num'] = test_cabin[1].astype('float', errors='ignore')
test_data['Cabin_side'] = test_cabin[2]

# Create total spending feature
spend_columns = ['RoomService', 'FoodCourt', 'ShoppingMall', 'Spa', 'VRDeck']
train_data['TotalSpend'] = train_data[spend_columns].sum(axis=1)
test_data['TotalSpend'] = test_data[spend_columns].sum(axis=1)

# Separate features and target
print(colored("\nPreparing features and target...", "green"))
X = train_data.drop(['PassengerId', 'Name', 'Cabin', 'Transported'], axis=1)
y = train_data['Transported']

X_test = test_data.drop(['PassengerId', 'Name', 'Cabin'], axis=1)

# Split data for validation
X_train, X_val, y_train, y_val = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)

# Define preprocessing pipelines
numeric_features = ['Age', 'RoomService', 'FoodCourt', 'ShoppingMall', 'Spa', 'VRDeck', 
                    'Group', 'Member', 'Cabin_num', 'TotalSpend']
categorical_features = ['HomePlanet', 'CryoSleep', 'Destination', 'VIP', 'Deck', 'Cabin_side']

numeric_transformer = Pipeline(steps=[
    ('imputer', SimpleImputer(strategy='median')),
    ('scaler', StandardScaler())
])

categorical_transformer = Pipeline(steps=[
    ('imputer', SimpleImputer(strategy='most_frequent')),
    ('onehot', OneHotEncoder(handle_unknown='ignore'))
])

preprocessor = ColumnTransformer(
    transformers=[
        ('num', numeric_transformer, numeric_features),
        ('cat', categorical_transformer, categorical_features)
    ])

# Build and train model
print(colored("\nTraining model...", "green"))
model = Pipeline(steps=[
    ('preprocessor', preprocessor),
    ('classifier', RandomForestClassifier(n_estimators=100, random_state=42))
])

model.fit(X_train, y_train)

# Evaluate model
print(colored("\nEvaluating model...", "green"))
y_pred = model.predict(X_val)
accuracy = accuracy_score(y_val, y_pred)
print(f"Validation Accuracy: {accuracy:.4f}")

# Cross-validation
cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
cv_scores = cross_val_score(model, X, y, cv=cv, scoring='accuracy')
print(f"Cross-validation accuracy: {cv_scores.mean():.4f} ± {cv_scores.std():.4f}")

# Display classification report
print(colored("\nClassification Report:", "yellow"))
print(classification_report(y_val, y_pred))

# Display confusion matrix
plt.figure(figsize=(8, 6))
cm = confusion_matrix(y_val, y_pred)
sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
            xticklabels=['False', 'True'], 
            yticklabels=['False', 'True'])
plt.xlabel('Predicted')
plt.ylabel('Actual')
plt.title('Confusion Matrix')
plt.savefig('confusion_matrix.png')

# Make predictions on test data
print(colored("\nMaking predictions on test data...", "green"))
test_predictions = model.predict(X_test)

# Create submission file
submission = pd.DataFrame({
    'PassengerId': test_passenger_ids,
    'Transported': test_predictions
})

submission.to_csv('submission.csv', index=False)
print(colored("Submission file created!", "green"))

# Feature importance
print(colored("\nAnalyzing feature importance...", "green"))
feature_names = (
    numeric_features +
    list(model.named_steps['preprocessor']
         .named_transformers_['cat']
         .named_steps['onehot']
         .get_feature_names_out(categorical_features))
)

importances = model.named_steps['classifier'].feature_importances_
indices = np.argsort(importances)[::-1]

# Print feature ranking
print("Feature ranking:")
for f in range(min(20, len(feature_names))):
    print(f"{f+1}. {feature_names[indices[f]]}: {importances[indices[f]]:.4f}")

# Plot feature importance
plt.figure(figsize=(12, 8))
plt.title("Feature Importance")
plt.bar(range(min(20, len(feature_names))), 
        importances[indices[:20]],
        align="center")
plt.xticks(range(min(20, len(feature_names))), 
           [feature_names[i] for i in indices[:20]], 
           rotation=90)
plt.tight_layout()
plt.savefig('feature_importance.png')

print(colored("\nExperiment 1 completed successfully!", "green")) 