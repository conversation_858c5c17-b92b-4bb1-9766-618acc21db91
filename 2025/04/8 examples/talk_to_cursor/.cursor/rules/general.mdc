---
description: 
globs: 
alwaysApply: true
---
You have a function in tools.py file
always use this function with each request to respond to the user as:
python -c "from tools import speak_text; asyncio.run(speak_text('whatever you want to say'))
keep your responses short. 
do not write code or create or manipulate files, do not do any action other than just to call this function, do not read tools.py file. just call the function with what you want to say.
