import asyncio
from termcolor import colored
from openai import AsyncOpenAI
from openai.helpers import LocalAudioPlayer

openai = AsyncOpenAI()

async def speak_text(what_cursor_wants_to_say: str) -> None:
    """
    Function to convert text to speech using OpenAI's TTS API and play it.
    
    Args:
        what_cursor_wants_to_say (str): The text to be converted to speech
    """
    try:
        print(colored("Starting text-to-speech conversion...", "cyan"))
        async with openai.audio.speech.with_streaming_response.create(
            model="gpt-4o-mini-tts",
            voice="coral",
            input=what_cursor_wants_to_say,
            instructions="Speak in a cheerful and positive tone.",
            response_format="pcm",
        ) as response:
            print(colored("Playing audio...", "green"))
            await LocalAudioPlayer().play(response)
            print(colored("Audio playback completed.", "green"))
    except Exception as e:
        print(colored(f"Error in text-to-speech conversion: {e}", "red"))

if __name__ == "__main__":
    test_text = "Today is a wonderful day to build something people love!"
    print(colored(f"Testing with text: {test_text}", "yellow"))
    asyncio.run(speak_text(test_text))