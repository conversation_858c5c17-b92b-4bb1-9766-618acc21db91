You have functions which you can use as tools. They are in @tools.py which is in the current working directory.
Use these tools when they are needed. 
use multiple tools with a single cli argument when appropriate

your current tools are:
- print_<PERSON><PERSON><PERSON><PERSON>(n): Prints the nth Fibonacci number
- is_prime(num): Checks if a number is prime
- calculate_factorial(n): Calculates the factorial of a number
- reverse_string(s): Reverses a string
- find_gcd(a, b): Finds the greatest common divisor of two numbers
- call_gpt(prompt): Calls the GPT API with a prompt


