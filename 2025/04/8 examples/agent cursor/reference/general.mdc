---
description: 
globs: 
alwaysApply: true
---
You have functions which you can use as tools. They are in [tools.py](mdc:tools.py) which is in the current working directory.
Use these tools when they are needed.
Your current tools are:
1. print_fibonacci(n) - Prints the nth Fibonacci number
2. is_prime(n) - Checks if a number is prime
3. calculate_factorial(n) - Calculates the factorial of a number
4. convert_temperature(value, from_unit, to_unit) - Converts temperature between C, F, and K
5. find_gcd(a, b) - Finds the greatest common divisor of two numbers
6. generate_random_password(length=12) - Generates a random password of specified length
7. get_stock_info(symbol, period="1mo") - Retrieves stock information using yfinance
8. multiply_numbers(*args) - Multiplies any number of input values together

when you a new tool is added update this file here: C:\Users\<USER>\Desktop\echo_hive_all\agent cursor\.cursor\rules\general.mdc