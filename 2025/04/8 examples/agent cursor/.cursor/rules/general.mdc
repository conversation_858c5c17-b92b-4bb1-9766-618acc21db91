---
description: 
globs: 
alwaysApply: true
---
You have functions which you can use as tools. They are in [tools.py](mdc:tools.py) which is in the current working directory.
Use these tools when they are needed. 
use multiple tools with a single cli argument when appropriate

your current tools are:
def print_fibonacci(n):
    if n <= 0:
        fib = 0
    elif n == 1:
        fib = 1
    else:
        a, b = 0, 1
        for _ in range(2, n + 1):
            a, b = b, a + b
        fib = b
    print(f"The {n}th Fibonacci number is: {fib}")


def is_prime(num):
    """Check if a number is prime."""
    if num <= 1:
        print(f"{num} is not prime")
    if num <= 3:
        print(f"{num} is prime")
    if num % 2 == 0 or num % 3 == 0:
        print(f"{num} is not prime")
    i = 5
    while i * i <= num:
        if num % i == 0 or num % (i + 2) == 0:
            print(f"{num} is not prime")
        i += 6
    print(f"{num} is prime")


def calculate_factorial(n):
    """Calculate the factorial of a number."""
    if n < 0:
        print(f"The factorial of {n} is undefined")
    result = 1
    for i in range(1, n + 1):
        result *= i
    print(f"The factorial of {n} is {result}")


def reverse_string(s):
    """Reverse a string."""
    print(f"The reversed string is {s[::-1]}")


def find_gcd(a, b):
    """Find the greatest common divisor of two numbers."""
    while b:
        a, b = b, a % b
    print(f"The GCD of {a} and {b} is {a}")

def call_gpt(prompt):
    import openai
    import os

    openai.api_key = os.getenv("OPENAI_API_KEY")
    response = openai.chat.completions.create(
        model="gpt-4o-mini",
        messages=[{"role": "user", "content": prompt}]
    )
    print(response.choices[0].message.content)







