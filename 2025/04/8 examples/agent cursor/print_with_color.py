from termcolor import colored

def colored_fibonacci(n):
    print(colored(f"Starting Fibonacci calculation for n={n}", "cyan"))
    if n <= 0:
        fib = 0
        print(colored("Base case: n <= 0", "yellow"))
    elif n == 1:
        fib = 1
        print(colored("Base case: n = 1", "yellow"))
    else:
        print(colored("Computing Fibonacci through iteration", "green"))
        a, b = 0, 1
        for i in range(2, n + 1):
            a, b = b, a + b
            print(colored(f"Step {i}: a={a}, b={b}", "blue"))
        fib = b
    print(colored(f"Result: The {n}th Fibonacci number is: {fib}", "magenta"))

def colored_is_prime(num):
    print(colored(f"Checking if {num} is prime", "cyan"))
    if num <= 1:
        print(colored(f"Base case: {num} <= 1 is not prime", "red"))
        return False
    if num <= 3:
        print(colored(f"Base case: {num} <= 3 is prime", "green"))
        return True
    if num % 2 == 0:
        print(colored(f"{num} is divisible by 2, not prime", "red"))
        return False
    if num % 3 == 0:
        print(colored(f"{num} is divisible by 3, not prime", "red"))
        return False
    
    print(colored("Checking divisibility using 6k±1 optimization", "yellow"))
    i = 5
    while i * i <= num:
        if num % i == 0:
            print(colored(f"{num} is divisible by {i}, not prime", "red"))
            return False
        if num % (i + 2) == 0:
            print(colored(f"{num} is divisible by {i+2}, not prime", "red"))
            return False
        i += 6
        print(colored(f"Checked divisibility up to {i}", "blue"))
    
    print(colored(f"{num} is prime", "green"))
    return True

def colored_reverse_string(s):
    print(colored(f"Reversing string: '{s}'", "cyan"))
    print(colored(f"Using string slicing with step -1: s[::-1]", "yellow"))
    result = s[::-1]
    print(colored(f"Result: '{result}'", "magenta"))
    return result

# Example usage
if __name__ == "__main__":
    print(colored("=== FIBONACCI DEMONSTRATION ===", "white", "on_blue"))
    colored_fibonacci(5)
    
    print("\n" + colored("=== PRIME CHECK DEMONSTRATION ===", "white", "on_green"))
    colored_is_prime(56)
    
    print("\n" + colored("=== STRING REVERSAL DEMONSTRATION ===", "white", "on_magenta"))
    colored_reverse_string("hello") 