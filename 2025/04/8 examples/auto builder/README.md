# Deep Research Terminal App

A command-line application that performs comprehensive web-based research on any topic using an innovative question-driven approach that mimics human investigation.

## Features

- Conducts deep research using an intelligent question-driven methodology
- Mimics how human researchers naturally investigate topics:
  - Starts with key questions
  - Builds understanding progressively
  - Explores knowledge gaps
  - Synthesizes discoveries into cohesive insights
- Uses an iterative process where each step builds on previous knowledge
- Generates essential research questions to thoroughly investigate topics
- Applies accumulated context to enhance each subsequent inquiry
- Identifies knowledge gaps and performs targeted follow-up research
- Uses OpenAI's GPT-4o for question generation, web searches, and synthesis
- Interactive menu system for improved user experience
- Structures final reports with:
  - Executive Summary
  - Key Findings
  - Detailed Analysis organized by themes
  - Implications
  - Conclusion
- Maintains all citations from original sources
- Provides option to save research reports as markdown files
- Automatically timestamps and organizes saved reports
- <PERSON>rowse and view previously saved reports
- Supports direct research mode via command-line arguments
- Implements fallback mechanisms for error handling

## Requirements

- Python 3.7+
- OpenAI API key

## Installation

1. Clone this repository or download the files
2. Install dependencies:
   ```
   pip install -r requirements.txt
   ```
3. Set your OpenAI API key as an environment variable:
   ```
   # On Windows:
   set OPENAI_API_KEY=your_api_key_here
   
   # On macOS/Linux:
   export OPENAI_API_KEY=your_api_key_here
   ```

## Usage

### Interactive Mode

Run the application without arguments for interactive menu:
```
python main.py
```

The menu provides options to:
1. Conduct new research
2. View saved reports
3. Exit

### Command-line Mode

Run direct research without the interactive menu:
```
python main.py --direct "Your research topic"
```

This will perform research on the specified topic, display the results, and automatically save the report.

## Research Process

The app follows a question-driven research process:
1. Generates 4-6 essential research questions about the topic
2. Explores each question in sequence, building contextual knowledge
3. Uses accumulated knowledge to inform each subsequent question
4. Identifies knowledge gaps and creates targeted follow-up questions
5. Explores those gaps for a more comprehensive understanding
6. Synthesizes all findings into a cohesive report organized by themes

## Example

```
===== Deep Research App =====
1. Conduct new research
2. View saved reports
3. Exit

Enter your choice (1-3):
```

## Note

This application requires an internet connection and consumes OpenAI API credits based on usage. The question-driven approach is designed to be more efficient by building on previous findings rather than conducting independent searches. 