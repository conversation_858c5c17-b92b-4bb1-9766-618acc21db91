## Deep Research App Specification

- Terminal-based application that performs deep research on user-provided topics.
- Features an interactive menu system for improved user experience.
- Main menu with options to conduct new research, view saved reports, or exit.
- Prompts user for a research topic to investigate.
- Implements a question-driven research process that mimics human investigation:
  - Generates 4-6 essential research questions about the topic
  - Explores each question with web search, building cumulative knowledge
  - Identifies knowledge gaps and performs follow-up research
  - Synthesizes findings into a comprehensive report
- Uses an iterative approach that builds on previously gathered information.
- Each subsequent search is informed by what has already been learned.
- Uses OpenAI's GPT-4o model for question generation, web searches, and synthesis.
- Structures the final research report with:
  - Executive Summary
  - Key Findings
  - Detailed Analysis organized by themes
  - Implications
  - Conclusion
- Maintains citations to sources throughout the process.
- Displays the formatted research report in the terminal.
- Allows saving research reports to markdown files with timestamps.
- Creates a "reports" directory to store saved research reports.
- Provides a menu to browse and view previously saved reports sorted by date.
- Supports direct research mode via command-line arguments (`--direct "topic"`).
- Uses `termcolor` for colorful and informative terminal output.
- Handles errors gracefully with fallback mechanisms.
- Requires an OpenAI API key set as environment variable.
