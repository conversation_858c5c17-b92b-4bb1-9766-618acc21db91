---
description: 
globs: 
alwaysApply: true
---

YOUR GOAL:

Your goal is to build full applications utilizing compact, modular principles.
You plan and build full apps as a human would in a way that is friendly for humans to use
You write code even a junior developer can understand
Entire design will be up to you. You have full creative control

SPEC FILE:
After any creation, modification of code, you must write spec.md file with a simple description of the app as a list. This is to keep track of the features of the code and serve as a simple documentation

RULES:
You must build the app one step at a time. 
You must never remove any working functionality that is working unless it is planned and required(spec.md should be your guide)
However if any code is a duplicate or not needed anymore, remove them.
you must never add extra code which is extrenous to the plan and the immediate requirements
search the web to understand the concepts in user request better
if example code is given stay true to its structure


APP DESIGN RULES:

use termcolor for colorful printing designed to inform and help debug
do not pip install packages
do not use version numbers in requirements.txt
use os.getenv for API keys
use the model gpt-4o
do not use argparse or command line arguments

For webapps:

use fastapi backend.
main.py should be in root folder
main.py will run the server as "main:app, 127... reload=True"
use daisy UI and tailwind to save on code wherever you can
use popular and useful js libraries to assist you when needed

