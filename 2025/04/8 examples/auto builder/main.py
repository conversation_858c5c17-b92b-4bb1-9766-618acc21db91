import os
import json
import datetime
import sys
import time
from termcolor import cprint
from openai import OpenAI

def generate_research_questions(client, topic):
    """
    Generate key research questions about the topic.
    """
    cprint(f"\n--- Generating key research questions for: {topic} ---", "cyan")
    
    try:
        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "system", "content": "You are a research expert who identifies the most important questions needed to deeply understand a topic. Generate questions that would reveal key insights, important nuances, and critical information."},
                {"role": "user", "content": f"Generate 4-6 essential research questions to thoroughly investigate '{topic}'. These should be fundamental questions that, when answered, would provide comprehensive understanding. Create a mix of factual, analytical, and exploratory questions. Format as a simple list of questions without numbering."}
            ]
        )
        
        questions_text = response.choices[0].message.content
        # Parse the response into a list of questions
        questions = [line.strip().strip('- ') for line in questions_text.split('\n') if line.strip()]
        
        # Filter out empty lines and keep only 4-6 questions
        questions = [q for q in questions if q][:6]
        
        cprint(f"Generated {len(questions)} key research questions.", "green")
        return questions
    except Exception as e:
        cprint(f"Error generating questions: {str(e)}", "red")
        # Return basic questions as fallback
        return [
            f"What is {topic}?", 
            f"What are the key aspects of {topic}?", 
            f"What are recent developments in {topic}?",
            f"What are different perspectives on {topic}?"
        ]

def explore_question(client, topic, question, knowledge_so_far=""):
    """
    Explore a research question with web search and synthesize findings.
    """
    cprint(f"\n--- Exploring: {question} ---", "yellow")
    
    # If we have accumulated knowledge, include it in the prompt for context
    context_prompt = ""
    if knowledge_so_far:
        context_prompt = f"\nUsing the following information we've already discovered:\n\n{knowledge_so_far}\n\n"
    
    exploration_prompt = f"""Research the following question about {topic}: "{question}"
    
    {context_prompt}Focus on finding specific, detailed information with supporting evidence and examples.
    Consider multiple perspectives and sources.
    Provide a thorough answer with citations to reliable sources."""
    
    try:
        response = client.responses.create(
            model="gpt-4o",
            tools=[{"type": "web_search_preview"}],
            tool_choice={"type": "web_search_preview"},
            input=exploration_prompt
        )
        
        # Extract content from response
        answer = extract_content_with_citations(response)
        cprint(f"Found answer to: {question}", "green")
        return answer
    except Exception as e:
        cprint(f"Error exploring question '{question}': {str(e)}", "red")
        return f"Unable to research the question: {question} due to an error."

def identify_knowledge_gaps(client, topic, questions, answers):
    """
    Identify knowledge gaps or areas that need deeper exploration.
    """
    cprint("\n--- Identifying knowledge gaps and areas for deeper exploration ---", "cyan")
    
    # Prepare current knowledge corpus
    knowledge_corpus = ""
    for i, (question, answer) in enumerate(zip(questions, answers)):
        knowledge_corpus += f"Question {i+1}: {question}\nAnswer: {answer}\n\n"
    
    try:
        prompt = f"""Based on our research so far on '{topic}', identify 2-3 specific aspects that:
        1. Need deeper exploration
        2. Are missing important details
        3. Would benefit from additional research
        
        Current research:
        {knowledge_corpus[:7000]}
        
        For each gap, formulate a precise question that would help fill the knowledge gap.
        Format as a simple list of questions."""
        
        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "system", "content": "You are a research expert who identifies knowledge gaps and formulates targeted questions to fill those gaps."},
                {"role": "user", "content": prompt}
            ]
        )
        
        follow_up_text = response.choices[0].message.content
        follow_up_questions = [line.strip().strip('- ') for line in follow_up_text.split('\n') if line.strip()]
        follow_up_questions = [q for q in follow_up_questions if q and '?' in q][:3]  # Keep only actual questions
        
        if follow_up_questions:
            cprint(f"Identified {len(follow_up_questions)} areas for deeper exploration.", "green")
        else:
            cprint("No specific knowledge gaps identified.", "yellow")
        
        return follow_up_questions
    except Exception as e:
        cprint(f"Error identifying knowledge gaps: {str(e)}", "red")
        return []

def synthesize_research(client, topic, questions, answers, follow_up_questions=None, follow_up_answers=None):
    """
    Synthesize all research into a comprehensive report.
    """
    cprint("\n--- Synthesizing research into a comprehensive report ---", "cyan")
    
    # Prepare research content
    research_content = f"# Research on: {topic}\n\n"
    
    # Initial questions and answers
    research_content += "## Initial Research\n\n"
    for question, answer in zip(questions, answers):
        research_content += f"### {question}\n{answer}\n\n"
    
    # Follow-up questions and answers (if any)
    if follow_up_questions and follow_up_answers:
        research_content += "## Deeper Exploration\n\n"
        for question, answer in zip(follow_up_questions, follow_up_answers):
            research_content += f"### {question}\n{answer}\n\n"
    
    synthesis_prompt = f"""Synthesize the following research about '{topic}' into a comprehensive report.
    
    Structure the report with:
    1. Executive Summary
    2. Key Findings
    3. Detailed Analysis (organized by themes rather than questions)
    4. Implications
    5. Conclusion
    
    Maintain all relevant citations from the research.
    
    Research content:
    {research_content[:8500]}"""
    
    try:
        synthesis_response = client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "system", "content": "You are a research synthesis expert who creates cohesive, insightful reports from question-based research while maintaining accuracy and citations."},
                {"role": "user", "content": synthesis_prompt}
            ]
        )
        
        final_report = synthesis_response.choices[0].message.content
        
        # Create a structured response object
        class DeepResearchResponse:
            def __init__(self, text):
                self.output_text = text
        
        cprint("Research synthesis completed successfully!", "green")
        return DeepResearchResponse(final_report)
    
    except Exception as e:
        cprint(f"Error synthesizing research: {str(e)}", "red")
        
        # Fallback: return the raw research results if synthesis fails
        fallback_report = research_content
        
        return DeepResearchResponse(fallback_report)

def perform_deep_research(topic):
    """
    Perform deep research using a question-driven approach.
    """
    cprint(f"\n--- Starting question-driven research on: {topic} ---", "cyan")
    
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        cprint("Error: OPENAI_API_KEY environment variable not set.", "red")
        cprint("Please set your OpenAI API key as an environment variable.", "yellow")
        return None
    
    client = OpenAI(api_key=api_key)
    
    # Step 1: Generate key research questions
    questions = generate_research_questions(client, topic)
    
    # Step 2: Explore initial research questions
    cprint("\n--- Phase 1: Initial Exploration ---", "magenta", attrs=["bold"])
    answers = []
    accumulated_knowledge = ""
    
    for i, question in enumerate(questions):
        cprint(f"Question {i+1}/{len(questions)}", "yellow")
        answer = explore_question(client, topic, question, accumulated_knowledge)
        answers.append(answer)
        
        # Add to accumulated knowledge for context in subsequent questions
        accumulated_knowledge += f"\nQuestion: {question}\nAnswer: {answer}\n"
        
        # Small delay to avoid rate limiting
        time.sleep(1)
    
    # Step 3: Identify knowledge gaps for deeper exploration
    cprint("\n--- Phase 2: Deeper Exploration ---", "magenta", attrs=["bold"])
    follow_up_questions = identify_knowledge_gaps(client, topic, questions, answers)
    
    follow_up_answers = []
    if follow_up_questions:
        for i, question in enumerate(follow_up_questions):
            cprint(f"Follow-up {i+1}/{len(follow_up_questions)}", "yellow")
            answer = explore_question(client, topic, question, accumulated_knowledge)
            follow_up_answers.append(answer)
            
            # Add to accumulated knowledge
            accumulated_knowledge += f"\nQuestion: {question}\nAnswer: {answer}\n"
            
            # Small delay to avoid rate limiting
            time.sleep(1)
    
    # Step 4: Synthesize all research into a comprehensive report
    return synthesize_research(client, topic, questions, answers, follow_up_questions, follow_up_answers)

def extract_content_with_citations(response):
    """
    Extract the content and format citations from the response.
    """
    try:
        # Get the message content from the response
        if hasattr(response, 'output_text'):
            return response.output_text
        
        # If the response format is different, try to find the message content
        for item in response:
            if item.get('type') == 'message' and item.get('status') == 'completed':
                content_items = item.get('content', [])
                for content in content_items:
                    if content.get('type') == 'output_text':
                        return content.get('text', '')
        
        return "Could not extract content from the research response."
    except Exception as e:
        cprint(f"Error extracting content: {str(e)}", "red")
        return "Error extracting content from the research response."

def format_report(topic, report_content):
    """
    Format the research report for terminal display.
    """
    cprint("\n" + "="*80, "magenta")
    cprint(f" DEEP RESEARCH REPORT: {topic.upper()} ", "magenta", attrs=["bold"])
    cprint("="*80, "magenta")
    print("\n")
    
    # Print report content
    print(report_content)
    
    cprint("\n" + "="*80, "magenta")
    cprint(" END OF REPORT ", "magenta")
    cprint("="*80, "magenta")

def save_report_to_file(topic, report_content):
    """
    Save the research report to a markdown file.
    """
    try:
        # Create reports directory if it doesn't exist
        if not os.path.exists("reports"):
            os.makedirs("reports")
        
        # Format filename with timestamp
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        # Clean topic name for filename
        clean_topic = ''.join(c if c.isalnum() else '_' for c in topic)
        filename = f"reports/{timestamp}_{clean_topic}.md"
        
        # Prepare report content
        formatted_content = f"# Deep Research Report: {topic}\n\n"
        formatted_content += f"*Generated on: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*\n\n"
        formatted_content += report_content
        
        # Write to file
        with open(filename, "w", encoding="utf-8") as f:
            f.write(formatted_content)
        
        cprint(f"\nReport saved to: {filename}", "green")
        return filename
    except Exception as e:
        cprint(f"\nError saving report to file: {str(e)}", "red")
        return None

def list_saved_reports():
    """
    List all saved research reports.
    """
    if not os.path.exists("reports"):
        cprint("No reports directory found. No saved reports available.", "yellow")
        return []
    
    reports = []
    for filename in os.listdir("reports"):
        if filename.endswith(".md"):
            # Extract timestamp and topic from filename
            parts = filename[:-3].split('_', 1)  # Remove .md and split on first underscore
            if len(parts) >= 2:
                timestamp = parts[0]
                topic = ' '.join(parts[1:]).replace('_', ' ')
                
                # Format date for display
                try:
                    date_obj = datetime.datetime.strptime(timestamp, "%Y%m%d_%H%M%S")
                    formatted_date = date_obj.strftime("%Y-%m-%d %H:%M:%S")
                except ValueError:
                    formatted_date = "Unknown date"
                
                reports.append({
                    "filename": filename,
                    "topic": topic,
                    "timestamp": timestamp,
                    "formatted_date": formatted_date
                })
    
    # Sort by timestamp (newest first)
    reports.sort(key=lambda x: x["timestamp"], reverse=True)
    return reports

def display_reports_menu():
    """
    Display a menu of saved reports.
    """
    reports = list_saved_reports()
    
    if not reports:
        cprint("No saved reports found.", "yellow")
        return None
    
    cprint("\n=== Saved Research Reports ===", "blue", attrs=["bold"])
    for i, report in enumerate(reports):
        cprint(f"{i+1}. [{report['formatted_date']}] {report['topic']}", "cyan")
    
    cprint("\n0. Return to main menu", "yellow")
    
    choice = input("\nEnter the number of the report you want to view (or 0 to return): ")
    try:
        choice = int(choice)
        if choice == 0:
            return None
        if 1 <= choice <= len(reports):
            return reports[choice-1]
        else:
            cprint("Invalid selection. Please try again.", "red")
            return None
    except ValueError:
        cprint("Invalid input. Please enter a number.", "red")
        return None

def view_saved_report(report_info):
    """
    View a saved report.
    """
    try:
        filepath = os.path.join("reports", report_info["filename"])
        with open(filepath, "r", encoding="utf-8") as f:
            content = f.read()
        
        # Clear the screen
        os.system('cls' if os.name == 'nt' else 'clear')
        
        # Display the report
        print(content)
        
        # Wait for user to press Enter to continue
        input("\nPress Enter to return to the menu...")
        
    except Exception as e:
        cprint(f"Error reading report: {str(e)}", "red")

def display_main_menu():
    """
    Display the main menu.
    """
    while True:
        # Clear the screen
        os.system('cls' if os.name == 'nt' else 'clear')
        
        cprint("===== Deep Research App =====", "blue", attrs=["bold"])
        cprint("1. Conduct new research", "cyan")
        cprint("2. View saved reports", "cyan")
        cprint("3. Exit", "yellow")
        
        choice = input("\nEnter your choice (1-3): ")
        
        if choice == '1':
            conduct_research()
        elif choice == '2':
            report = display_reports_menu()
            if report:
                view_saved_report(report)
        elif choice == '3':
            cprint("Thank you for using the Deep Research App. Goodbye!", "blue")
            sys.exit(0)
        else:
            cprint("Invalid choice. Please try again.", "red")
            input("Press Enter to continue...")

def conduct_research():
    """
    Conduct new research.
    """
    # Clear the screen
    os.system('cls' if os.name == 'nt' else 'clear')
    
    cprint("=== New Research ===", "blue", attrs=["bold"])
    topic = input("\nPlease enter the topic you want to research: ")
    if not topic.strip():
        cprint("Error: Topic cannot be empty.", "red")
        input("\nPress Enter to return to the main menu...")
        return
    
    cprint(f"\nStarting deep research on: {topic}", "yellow", attrs=["bold"])
    
    # Perform deep research using question-driven approach
    research_response = perform_deep_research(topic)
    
    if research_response:
        # Extract and format the content with citations
        report_content = extract_content_with_citations(research_response)
        
        # Format and display the report
        format_report(topic, report_content)
        
        # Ask if user wants to save the report
        save_option = input("\nWould you like to save this report to a file? (y/n): ").strip().lower()
        if save_option == 'y' or save_option == 'yes':
            file_path = save_report_to_file(topic, report_content)
            if file_path:
                cprint(f"Report saved successfully to {file_path}", "green")
    else:
        cprint("\nUnable to generate research report due to errors.", "red")
    
    input("\nPress Enter to return to the main menu...")

def main():
    # Check for arguments
    if len(sys.argv) > 1 and sys.argv[1].lower() == '--direct':
        # Direct mode - conduct research without menu
        if len(sys.argv) > 2:
            topic = ' '.join(sys.argv[2:])
            conduct_research_with_topic(topic)
        else:
            cprint("Error: No topic provided for direct mode.", "red")
            cprint("Usage: python main.py --direct \"Your research topic\"", "yellow")
        return
    
    # Interactive menu mode
    display_main_menu()

def conduct_research_with_topic(topic):
    """
    Conduct research with a predetermined topic.
    """
    if not topic.strip():
        cprint("Error: Topic cannot be empty.", "red")
        return
    
    cprint(f"\nStarting deep research on: {topic}", "yellow", attrs=["bold"])
    
    # Perform deep research using question-driven approach
    research_response = perform_deep_research(topic)
    
    if research_response:
        # Extract and format the content with citations
        report_content = extract_content_with_citations(research_response)
        
        # Format and display the report
        format_report(topic, report_content)
        
        # Automatically save the report in direct mode
        file_path = save_report_to_file(topic, report_content)
        if file_path:
            cprint(f"Report saved to {file_path}", "green")
    else:
        cprint("\nUnable to generate research report due to errors.", "red")

if __name__ == "__main__":
    main() 