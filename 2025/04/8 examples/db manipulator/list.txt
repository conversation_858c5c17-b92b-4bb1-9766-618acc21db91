Functions in tools.py:

1. create_database(db_path: str = DEFAULT_DB_PATH) -> bool
2. create_table(db_path: str, table_name: str, columns: Dict[str, str], primary_key: str = None) -> bool
3. add_column(db_path: str, table_name: str, column_name: str, column_type: str) -> bool
4. insert_data(db_path: str, table_name: str, data: Union[Dict[str, Any], List[Dict[str, Any]]]) -> bool
5. update_data(db_path: str, table_name: str, updates: Dict[str, Any], condition: str, condition_values: Tuple = ()) -> int
6. delete_data(db_path: str, table_name: str, condition: str, condition_values: Tuple = ()) -> int
7. query_data(db_path: str, query: str, parameters: Tuple = ()) -> List[Tuple]
8. get_table_schema(db_path: str, table_name: str) -> List[Dict[str, Any]]
9. list_tables(db_path: str) -> List[str]
10. execute_transaction(db_path: str, operations: List[Tuple[str, Tuple]]) -> bool
11. backup_database(source_db_path: str, target_db_path: str) -> bool 