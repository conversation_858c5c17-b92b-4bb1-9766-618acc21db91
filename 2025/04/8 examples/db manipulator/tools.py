import sqlite3
import os
from termcolor import colored
import threading
from typing import List, Dict, Any, <PERSON>, Tuple, Optional

# Global variables
DB_LOCK = threading.Lock()  # Thread lock for database operations
DEFAULT_DB_PATH = "database.db"  # Default database path

def create_database(db_path: str = DEFAULT_DB_PATH) -> bool:
    """
    Create a new SQLite database file if it doesn't exist.
    
    Args:
        db_path (str): Path to the database file
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        if os.path.exists(db_path):
            print(colored(f"Database already exists at {db_path}", "yellow"))
            return True
            
        conn = sqlite3.connect(db_path, check_same_thread=False, isolation_level=None)
        conn.close()
        print(colored(f"Database created successfully at {db_path}", "green"))
        return True
    except Exception as e:
        print(colored(f"Error creating database: {str(e)}", "red"))
        return False

def create_table(db_path: str, table_name: str, columns: Dict[str, str], primary_key: str = None) -> bool:
    """
    Create a new table in the database.
    
    Args:
        db_path (str): Path to the database file
        table_name (str): Name of the table to create
        columns (Dict[str, str]): Dictionary mapping column names to their SQL types
        primary_key (str, optional): Column to use as primary key
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        with DB_LOCK:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # Build the CREATE TABLE SQL statement
            columns_str = []
            for col_name, col_type in columns.items():
                if primary_key and col_name == primary_key:
                    columns_str.append(f"{col_name} {col_type} PRIMARY KEY")
                else:
                    columns_str.append(f"{col_name} {col_type}")
            
            create_table_sql = f"CREATE TABLE IF NOT EXISTS {table_name} ({', '.join(columns_str)})"
            
            cursor.execute(create_table_sql)
            conn.commit()
            conn.close()
            
            print(colored(f"Table '{table_name}' created successfully in {db_path}", "green"))
            return True
    except Exception as e:
        print(colored(f"Error creating table '{table_name}': {str(e)}", "red"))
        return False

def add_column(db_path: str, table_name: str, column_name: str, column_type: str) -> bool:
    """
    Add a new column to an existing table.
    
    Args:
        db_path (str): Path to the database file
        table_name (str): Name of the table to modify
        column_name (str): Name of the column to add
        column_type (str): SQL type of the column
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        with DB_LOCK:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # Check if column already exists
            cursor.execute(f"PRAGMA table_info({table_name})")
            existing_columns = [col[1] for col in cursor.fetchall()]
            
            if column_name in existing_columns:
                print(colored(f"Column '{column_name}' already exists in table '{table_name}'", "yellow"))
                conn.close()
                return False
            
            # Add the column
            cursor.execute(f"ALTER TABLE {table_name} ADD COLUMN {column_name} {column_type}")
            conn.commit()
            conn.close()
            
            print(colored(f"Column '{column_name}' added to table '{table_name}' in {db_path}", "green"))
            return True
    except Exception as e:
        print(colored(f"Error adding column '{column_name}' to table '{table_name}': {str(e)}", "red"))
        return False

def insert_data(db_path: str, table_name: str, data: Union[Dict[str, Any], List[Dict[str, Any]]]) -> bool:
    """
    Insert data into a table.
    
    Args:
        db_path (str): Path to the database file
        table_name (str): Name of the table to insert into
        data (Union[Dict[str, Any], List[Dict[str, Any]]]): Single row as dictionary or list of row dictionaries
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        with DB_LOCK:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # Convert single dictionary to list for consistent processing
            if isinstance(data, dict):
                data = [data]
            
            for row in data:
                columns = ", ".join(row.keys())
                placeholders = ", ".join(["?" for _ in row.keys()])
                values = list(row.values())
                
                insert_sql = f"INSERT INTO {table_name} ({columns}) VALUES ({placeholders})"
                cursor.execute(insert_sql, values)
            
            conn.commit()
            conn.close()
            
            rows_count = len(data)
            print(colored(f"Successfully inserted {rows_count} row(s) into '{table_name}' in {db_path}", "green"))
            return True
    except Exception as e:
        print(colored(f"Error inserting data into table '{table_name}': {str(e)}", "red"))
        return False

def update_data(db_path: str, table_name: str, updates: Dict[str, Any], condition: str, condition_values: Tuple = ()) -> int:
    """
    Update data in a table based on a condition.
    
    Args:
        db_path (str): Path to the database file
        table_name (str): Name of the table to update
        updates (Dict[str, Any]): Dictionary of column:value pairs to update
        condition (str): WHERE condition string (e.g., "id = ?")
        condition_values (Tuple): Values for the condition placeholders
        
    Returns:
        int: Number of rows updated, -1 if error
    """
    try:
        with DB_LOCK:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # Build the SET clause
            set_clause = ", ".join([f"{col} = ?" for col in updates.keys()])
            values = list(updates.values()) + list(condition_values)
            
            update_sql = f"UPDATE {table_name} SET {set_clause} WHERE {condition}"
            cursor.execute(update_sql, values)
            
            rows_affected = cursor.rowcount
            conn.commit()
            conn.close()
            
            print(colored(f"Successfully updated {rows_affected} row(s) in '{table_name}' in {db_path}", "green"))
            return rows_affected
    except Exception as e:
        print(colored(f"Error updating data in table '{table_name}': {str(e)}", "red"))
        return -1

def delete_data(db_path: str, table_name: str, condition: str, condition_values: Tuple = ()) -> int:
    """
    Delete data from a table based on a condition.
    
    Args:
        db_path (str): Path to the database file
        table_name (str): Name of the table to delete from
        condition (str): WHERE condition string (e.g., "id = ?")
        condition_values (Tuple): Values for the condition placeholders
        
    Returns:
        int: Number of rows deleted, -1 if error
    """
    try:
        with DB_LOCK:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            delete_sql = f"DELETE FROM {table_name} WHERE {condition}"
            cursor.execute(delete_sql, condition_values)
            
            rows_affected = cursor.rowcount
            conn.commit()
            conn.close()
            
            print(colored(f"Successfully deleted {rows_affected} row(s) from '{table_name}' in {db_path}", "green"))
            return rows_affected
    except Exception as e:
        print(colored(f"Error deleting data from table '{table_name}': {str(e)}", "red"))
        return -1

def query_data(db_path: str, query: str, parameters: Tuple = ()) -> List[Tuple]:
    """
    Execute a custom SQL query and return results.
    
    Args:
        db_path (str): Path to the database file
        query (str): SQL query to execute
        parameters (Tuple): Parameters for the query
        
    Returns:
        List[Tuple]: Query results as a list of tuples, empty list if error
    """
    try:
        with DB_LOCK:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            cursor.execute(query, parameters)
            results = cursor.fetchall()
            
            conn.close()
            
            rows_count = len(results)
            print(colored(f"Query returned {rows_count} row(s) from {db_path}", "green"))
            return results
    except Exception as e:
        print(colored(f"Error executing query: {str(e)}", "red"))
        return []

def get_table_schema(db_path: str, table_name: str) -> List[Dict[str, Any]]:
    """
    Get the schema information for a table.
    
    Args:
        db_path (str): Path to the database file
        table_name (str): Name of the table
        
    Returns:
        List[Dict[str, Any]]: List of column information dictionaries
    """
    try:
        with DB_LOCK:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns_info = cursor.fetchall()
            
            conn.close()
            
            # Convert to more readable format
            schema = []
            for col in columns_info:
                schema.append({
                    "cid": col[0],
                    "name": col[1],
                    "type": col[2],
                    "notnull": col[3],
                    "default_value": col[4],
                    "pk": col[5]
                })
            
            print(colored(f"Successfully retrieved schema for table '{table_name}' from {db_path}", "green"))
            return schema
    except Exception as e:
        print(colored(f"Error getting schema for table '{table_name}': {str(e)}", "red"))
        return []

def list_tables(db_path: str) -> List[str]:
    """
    List all tables in the database.
    
    Args:
        db_path (str): Path to the database file
        
    Returns:
        List[str]: List of table names
    """
    try:
        with DB_LOCK:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
            tables = [row[0] for row in cursor.fetchall()]
            
            conn.close()
            
            tables_count = len(tables)
            print(colored(f"Found {tables_count} tables in {db_path}", "green"))
            return tables
    except Exception as e:
        print(colored(f"Error listing tables: {str(e)}", "red"))
        return []

def execute_transaction(db_path: str, operations: List[Tuple[str, Tuple]]) -> bool:
    """
    Execute multiple SQL operations as a single transaction.
    
    Args:
        db_path (str): Path to the database file
        operations (List[Tuple[str, Tuple]]): List of (query, parameters) tuples
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        with DB_LOCK:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # Start transaction
            conn.execute("BEGIN TRANSACTION")
            
            for query, params in operations:
                cursor.execute(query, params)
            
            # Commit transaction
            conn.commit()
            conn.close()
            
            print(colored(f"Successfully executed transaction with {len(operations)} operations in {db_path}", "green"))
            return True
    except Exception as e:
        # Rollback on error
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        print(colored(f"Transaction failed and was rolled back: {str(e)}", "red"))
        return False

def backup_database(source_db_path: str, target_db_path: str) -> bool:
    """
    Create a backup of the database.
    
    Args:
        source_db_path (str): Path to the source database file
        target_db_path (str): Path where the backup will be created
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        with DB_LOCK:
            source_conn = sqlite3.connect(source_db_path)
            target_conn = sqlite3.connect(target_db_path)
            
            source_conn.backup(target_conn)
            
            source_conn.close()
            target_conn.close()
            
            print(colored(f"Successfully backed up database from {source_db_path} to {target_db_path}", "green"))
            return True
    except Exception as e:
        print(colored(f"Error backing up database: {str(e)}", "red"))
        return False
