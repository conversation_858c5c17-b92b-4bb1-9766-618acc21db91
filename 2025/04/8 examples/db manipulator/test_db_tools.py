import os
import time
from termcolor import colored
from tools import (
    create_database, create_table, add_column, insert_data, update_data,
    delete_data, query_data, get_table_schema, list_tables, execute_transaction,
    backup_database
)

# Test database paths
TEST_DB = "test_database.db"
BACKUP_DB = "backup_database.db"

def print_test_header(test_name):
    """Print a formatted test header"""
    print("\n" + "="*80)
    print(colored(f"TEST: {test_name}", "cyan", attrs=["bold"]))
    print("="*80)

def clean_test_environment():
    """Remove test databases if they exist"""
    print_test_header("Cleaning Test Environment")
    
    try:
        if os.path.exists(TEST_DB):
            os.remove(TEST_DB)
            print(colored(f"Removed existing test database: {TEST_DB}", "yellow"))
            
        if os.path.exists(BACKUP_DB):
            os.remove(BACKUP_DB)
            print(colored(f"Removed existing backup database: {BACKUP_DB}", "yellow"))
    except Exception as e:
        print(colored(f"Error cleaning test environment: {str(e)}", "red"))

def test_create_database():
    """Test database creation"""
    print_test_header("Create Database")
    
    # Test creating a new database
    result = create_database(TEST_DB)
    assert result is True, "Database creation failed"
    assert os.path.exists(TEST_DB), "Database file not created"
    
    # Test creating an existing database (should return True but notify)
    result = create_database(TEST_DB)
    assert result is True, "Creating existing database should return True"

def test_create_table():
    """Test table creation"""
    print_test_header("Create Table")
    
    # Define table schema
    columns = {
        "id": "INTEGER",
        "name": "TEXT",
        "age": "INTEGER",
        "email": "TEXT"
    }
    
    # Create a table with a primary key
    result = create_table(TEST_DB, "users", columns, primary_key="id")
    assert result is True, "Table creation failed"
    
    # Create a second table for later tests
    columns = {
        "id": "INTEGER",
        "product_name": "TEXT",
        "price": "REAL"
    }
    result = create_table(TEST_DB, "products", columns, primary_key="id")
    assert result is True, "Second table creation failed"

def test_list_tables():
    """Test listing tables"""
    print_test_header("List Tables")
    
    tables = list_tables(TEST_DB)
    assert "users" in tables, "Users table not found"
    assert "products" in tables, "Products table not found"
    assert len(tables) == 2, f"Expected 2 tables, found {len(tables)}"

def test_add_column():
    """Test adding columns"""
    print_test_header("Add Column")
    
    # Add a new column
    result = add_column(TEST_DB, "users", "address", "TEXT")
    assert result is True, "Adding column failed"
    
    # Try adding same column again (should fail gracefully)
    result = add_column(TEST_DB, "users", "address", "TEXT")
    assert result is False, "Adding duplicate column should return False"

def test_get_table_schema():
    """Test getting table schema"""
    print_test_header("Get Table Schema")
    
    schema = get_table_schema(TEST_DB, "users")
    
    column_names = [col["name"] for col in schema]
    assert "id" in column_names, "id column not found in schema"
    assert "name" in column_names, "name column not found in schema"
    assert "age" in column_names, "age column not found in schema"
    assert "email" in column_names, "email column not found in schema"
    assert "address" in column_names, "address column not found in schema"
    
    # Verify primary key
    for col in schema:
        if col["name"] == "id":
            assert col["pk"] == 1, "id should be primary key"

def test_insert_data():
    """Test inserting data"""
    print_test_header("Insert Data")
    
    # Test inserting a single row
    user1 = {
        "id": 1,
        "name": "John Doe",
        "age": 30,
        "email": "<EMAIL>",
        "address": "123 Main St"
    }
    result = insert_data(TEST_DB, "users", user1)
    assert result is True, "Inserting single row failed"
    
    # Test inserting multiple rows
    users = [
        {
            "id": 2,
            "name": "Jane Smith",
            "age": 25,
            "email": "<EMAIL>",
            "address": "456 Oak Ave"
        },
        {
            "id": 3,
            "name": "Bob Johnson",
            "age": 40,
            "email": "<EMAIL>",
            "address": "789 Pine Rd"
        }
    ]
    result = insert_data(TEST_DB, "users", users)
    assert result is True, "Inserting multiple rows failed"
    
    # Insert products for later tests
    products = [
        {"id": 1, "product_name": "Laptop", "price": 999.99},
        {"id": 2, "product_name": "Phone", "price": 699.99},
        {"id": 3, "product_name": "Tablet", "price": 349.99}
    ]
    result = insert_data(TEST_DB, "products", products)
    assert result is True, "Inserting products failed"

def test_query_data():
    """Test querying data"""
    print_test_header("Query Data")
    
    # Test simple query
    results = query_data(TEST_DB, "SELECT * FROM users")
    assert len(results) == 3, f"Expected 3 users, found {len(results)}"
    
    # Test query with parameters
    results = query_data(TEST_DB, "SELECT * FROM users WHERE age > ?", (25,))
    assert len(results) == 2, f"Expected 2 users with age > 25, found {len(results)}"
    
    # Test query with multiple parameters
    results = query_data(TEST_DB, "SELECT * FROM users WHERE age > ? AND age < ?", (25, 35))
    assert len(results) == 1, f"Expected 1 user with 25 < age < 35, found {len(results)}"

def test_update_data():
    """Test updating data"""
    print_test_header("Update Data")
    
    # Update a single record
    updates = {"address": "Updated Address", "age": 31}
    rows_updated = update_data(TEST_DB, "users", updates, "id = ?", (1,))
    assert rows_updated == 1, f"Expected to update 1 row, updated {rows_updated}"
    
    # Verify update
    results = query_data(TEST_DB, "SELECT * FROM users WHERE id = 1")
    assert results[0][2] == 31, "Age was not updated correctly"
    assert results[0][4] == "Updated Address", "Address was not updated correctly"
    
    # Update multiple records
    price_updates = {"price": 599.99}
    rows_updated = update_data(TEST_DB, "products", price_updates, "price > ?", (500,))
    assert rows_updated == 2, f"Expected to update 2 rows, updated {rows_updated}"

def test_delete_data():
    """Test deleting data"""
    print_test_header("Delete Data")
    
    # Delete a single record
    rows_deleted = delete_data(TEST_DB, "users", "id = ?", (3,))
    assert rows_deleted == 1, f"Expected to delete 1 row, deleted {rows_deleted}"
    
    # Verify deletion
    results = query_data(TEST_DB, "SELECT * FROM users")
    assert len(results) == 2, f"Expected 2 users after deletion, found {len(results)}"
    
    # Attempt to delete a non-existent record
    rows_deleted = delete_data(TEST_DB, "users", "id = ?", (99,))
    assert rows_deleted == 0, f"Expected to delete 0 rows for non-existent ID, deleted {rows_deleted}"

def test_transaction():
    """Test transaction execution"""
    print_test_header("Execute Transaction")
    
    # First, check the current price of product 1
    product_before = query_data(TEST_DB, "SELECT price FROM products WHERE id = 1")
    original_price = product_before[0][0]
    expected_discounted_price = original_price * 0.9
    
    print(colored(f"Original price: {original_price}, Expected after 10% discount: {expected_discounted_price}", "blue"))
    
    # Create operations for a transaction
    operations = [
        ("INSERT INTO users (id, name, age, email, address) VALUES (?, ?, ?, ?, ?)", 
         (4, "Transaction User", 35, "<EMAIL>", "Transaction St")),
        ("UPDATE products SET price = price * 0.9 WHERE id = ?", (1,)),
        ("DELETE FROM products WHERE id = ?", (3,))
    ]
    
    # Execute transaction
    result = execute_transaction(TEST_DB, operations)
    assert result is True, "Transaction execution failed"
    
    # Verify transaction results
    user_results = query_data(TEST_DB, "SELECT * FROM users WHERE id = 4")
    assert len(user_results) == 1, "Transaction user not inserted"
    
    product_results = query_data(TEST_DB, "SELECT price FROM products WHERE id = 1")
    updated_price = product_results[0][0]
    print(colored(f"Updated price: {updated_price}", "blue"))
    
    # Use a delta comparison to account for floating point precision
    price_delta = abs(updated_price - expected_discounted_price)
    assert price_delta < 0.01, f"Product price not updated correctly. Expected ~{expected_discounted_price}, got {updated_price}"
    
    product_count = query_data(TEST_DB, "SELECT COUNT(*) FROM products")
    assert product_count[0][0] == 2, f"Expected 2 products after transaction, found {product_count[0][0]}"

def test_backup_database():
    """Test database backup"""
    print_test_header("Backup Database")
    
    # Create a backup
    result = backup_database(TEST_DB, BACKUP_DB)
    assert result is True, "Database backup failed"
    assert os.path.exists(BACKUP_DB), "Backup file not created"
    
    # Verify backup by checking tables
    tables = list_tables(BACKUP_DB)
    assert "users" in tables, "Users table not found in backup"
    assert "products" in tables, "Products table not found in backup"
    
    # Verify data in backup
    user_count = query_data(BACKUP_DB, "SELECT COUNT(*) FROM users")
    assert user_count[0][0] == 3, f"Expected 3 users in backup, found {user_count[0][0]}"

def run_all_tests():
    """Run all test functions sequentially"""
    print(colored("\n\n*** STARTING DATABASE TOOLS TESTS ***\n", "magenta", attrs=["bold"]))
    
    # First clean the test environment
    clean_test_environment()
    
    try:
        # Run tests in order
        test_create_database()
        test_create_table()
        test_list_tables()
        test_add_column()
        test_get_table_schema()
        test_insert_data()
        test_query_data()
        test_update_data()
        test_delete_data()
        test_transaction()
        test_backup_database()
        
        print(colored("\n\n*** ALL TESTS PASSED SUCCESSFULLY! ***\n", "green", attrs=["bold"]))
    except AssertionError as e:
        print(colored(f"\n\n*** TEST FAILED: {str(e)} ***\n", "red", attrs=["bold"]))
    except Exception as e:
        print(colored(f"\n\n*** UNEXPECTED ERROR: {str(e)} ***\n", "red", attrs=["bold"]))
    finally:
        # Clean up after tests
        clean_test_environment()

if __name__ == "__main__":
    run_all_tests() 