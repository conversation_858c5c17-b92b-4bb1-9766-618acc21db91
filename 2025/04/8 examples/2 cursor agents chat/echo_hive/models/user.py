from termcolor import colored

class User:
    def __init__(self, username, bio="", reputation=0):
        self.username = username
        self.bio = bio
        self.reputation = reputation
        self.messages = []
        self.followers = []
        self.following = []
        print(colored(f"Created user: {username}", "blue"))
    
    def post_message(self, content, network):
        """Create and publish a new message to the network"""
        from echo_hive.models.message import Message
        message = Message(self, content)
        self.messages.append(message)
        print(colored(f"User {self.username} posted: {content}", "green"))
        network.propagate_message(message)
        return message
    
    def follow(self, other_user):
        """Follow another user"""
        if other_user not in self.following:
            self.following.append(other_user)
            other_user.followers.append(self)
            print(colored(f"{self.username} is now following {other_user.username}", "cyan"))
    
    def unfollow(self, other_user):
        """Unfollow a user"""
        if other_user in self.following:
            self.following.remove(other_user)
            other_user.followers.remove(self)
            print(colored(f"{self.username} unfollowed {other_user.username}", "yellow"))
    
    def increase_reputation(self, amount=1):
        """Increase user's reputation score"""
        self.reputation += amount
        print(colored(f"{self.username}'s reputation increased to {self.reputation}", "green"))
    
    def get_feed(self, network):
        """Get personalized feed based on following"""
        # In a real implementation, this would filter network messages 
        # based on who the user follows and other factors
        return network.get_feed_for_user(self)
    
    def __str__(self):
        return f"User({self.username}, followers: {len(self.followers)}, reputation: {self.reputation})" 