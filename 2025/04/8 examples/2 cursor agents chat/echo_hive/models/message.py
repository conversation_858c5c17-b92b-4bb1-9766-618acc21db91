from dataclasses import dataclass, field
from typing import Dict, List, Optional, Set
import uuid
from datetime import datetime
import difflib

@dataclass
class Message:
    """
    Message model for Echo Hive representing content shared in the network
    with evolution tracking capabilities.
    """
    content: str
    author_id: str
    message_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    created_at: datetime = field(default_factory=datetime.now)
    
    # Track message evolution
    parent_id: Optional[str] = None  # ID of the message this was derived from
    root_id: Optional[str] = None  # ID of the original message in the chain
    generation: int = 0  # How many steps from the original message
    children_ids: List[str] = field(default_factory=list)  # IDs of derived messages
    
    # Track message propagation
    propagation_count: int = 0  # Number of times shared
    viewer_ids: Set[str] = field(default_factory=set)  # Users who have seen this
    
    # Message metadata
    topics: List[str] = field(default_factory=list)
    sentiment_score: Optional[float] = None
    engagement_score: float = 0.0
    
    def add_child(self, child_id: str) -> None:
        """Add a child message ID to track evolution"""
        self.children_ids.append(child_id)
    
    def increment_propagation(self) -> None:
        """Increment the propagation count when shared"""
        self.propagation_count += 1
    
    def add_viewer(self, user_id: str) -> None:
        """Record that a user has viewed this message"""
        self.viewer_ids.add(user_id)
    
    def update_sentiment(self, score: float) -> None:
        """Update the sentiment analysis score"""
        self.sentiment_score = score
    
    def add_topic(self, topic: str) -> None:
        """Add a topic tag to the message"""
        if topic not in self.topics:
            self.topics.append(topic)
    
    @classmethod
    def create_derived(cls, 
                       original_message: 'Message', 
                       new_content: str, 
                       new_author_id: str) -> 'Message':
        """
        Create a new message derived from an existing one,
        maintaining the evolution chain.
        """
        # Determine root ID (either original's root or original's ID if it's a root)
        root_id = original_message.root_id or original_message.message_id
        
        # Create new derived message
        derived = cls(
            content=new_content,
            author_id=new_author_id,
            parent_id=original_message.message_id,
            root_id=root_id,
            generation=original_message.generation + 1
        )
        
        return derived
    
    def calculate_diff(self, other_message: 'Message') -> str:
        """
        Calculate the difference between this message and another one
        Returns a string representation of the diff
        """
        diff = difflib.unified_diff(
            self.content.splitlines(),
            other_message.content.splitlines(),
            lineterm=''
        )
        return '\n'.join(diff)
    
    def calculate_similarity(self, other_message: 'Message') -> float:
        """
        Calculate similarity ratio between this message and another
        Returns a float between 0 (completely different) and 1 (identical)
        """
        return difflib.SequenceMatcher(
            None, 
            self.content, 
            other_message.content
        ).ratio()
    
    @property
    def evolution_depth(self) -> int:
        """Return how deep this message is in its evolution chain"""
        return self.generation
    
    @property
    def is_root(self) -> bool:
        """Check if this message is a root message (not derived)"""
        return self.parent_id is None
    
    @property 
    def has_evolved(self) -> bool:
        """Check if this message has evolved (has children)"""
        return len(self.children_ids) > 0 