--- File: .\main.py ---
from fastapi import <PERSON><PERSON><PERSON>, Request, Depends, HTTPException, status
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
import os
import requests
from authlib.integrations.starlette_client import OAuth
from starlette.middleware.sessions import SessionMiddleware
from termcolor import colored
import uvicorn
import json

# Constants
CLIENT_ID = "YOUR_CLIENT_ID"
CLIENT_SECRET = "567"
# NOTE: This is hardcoded for local development ONLY.
# In a real deployment, this URI must be generated dynamically
# (e.g., using request.url_for('auth_callback_route_name'))
# so it correctly reflects the current server's address (http/https, domain)
# and exactly matches one of the URIs registered in the Google Cloud Console.
REDIRECT_URI = "http://127.0.0.1:8000/auth/callback"

app = FastAPI(title="Simple Google Login")

# Setup session middleware
app.add_middleware(SessionMiddleware, secret_key=os.getenv("SECRET_KEY", "your-secret-key"))

# Setup templates and static files
templates = Jinja2Templates(directory="templates")
app.mount("/static", StaticFiles(directory="static"), name="static")

# Setup OAuth
oauth = OAuth()
oauth.register(
    name="google",
    client_id=CLIENT_ID,
    client_secret=CLIENT_SECRET,
    server_metadata_url="https://accounts.google.com/.well-known/openid-configuration",
    client_kwargs={"scope": "openid email profile"},
)

@app.get("/", response_class=HTMLResponse)
async def index(request: Request):
    print(colored("Serving index page", "green"))
    user = request.session.get("user")
    return templates.TemplateResponse("index.html", {"request": request, "user": user})

@app.get("/login")
async def login(request: Request):
    print(colored("Initiating Google login", "yellow"))
    try:
        redirect_uri = request.url_for("auth_callback")
        return await oauth.google.authorize_redirect(request, redirect_uri)
    except Exception as e:
        print(colored(f"Login error: {str(e)}", "red"))
        raise HTTPException(status_code=500, detail=f"Login failed: {str(e)}")

@app.get("/auth/callback")
async def auth_callback(request: Request):
    print(colored("Processing Google auth callback", "blue"))
    try:
        token = await oauth.google.authorize_access_token(request)
        user_info = token.get("userinfo")
        if user_info:
            request.session["user"] = dict(user_info)
            print(colored(f"User logged in: {user_info.get('email')}", "green"))
        return RedirectResponse(url="/")
    except Exception as e:
        print(colored(f"Auth callback error: {str(e)}", "red"))
        raise HTTPException(status_code=500, detail=f"Authentication failed: {str(e)}")

@app.get("/logout")
async def logout(request: Request):
    print(colored("User logging out", "yellow"))
    request.session.pop("user", None)
    return RedirectResponse(url="/")

if __name__ == "__main__":
    print(colored("Starting FastAPI server...", "cyan"))
    uvicorn.run("main:app", host="127.0.0.1", port=8000, reload=True) 
--- End File: .\main.py ---

--- File: .\README.md ---
# Simple Google Login with FastAPI

A simple single-page application demonstrating Google OAuth login with FastAPI. refer to this folder in Cursor to give it inspiration to setup a simple google auth forn your app

## Setup

1. Install dependencies:

```bash
pip install -r requirements.txt
```

2. Create a Google OAuth Client:

   - Go to the [Google Cloud Console](https://console.cloud.google.com/)
   - Create a new project
   - Navigate to "APIs & Services" > "Credentials"
   - Click "Create Credentials" > "OAuth client ID"
   - Select "Web application" as the application type
   - Add "http://localhost:8000/auth/callback" to the Authorized redirect URIs
   - Copy your Client ID and Client Secret
3. Set environment variables:

```bash
# Windows
set GOOGLE_CLIENT_ID=your-client-id
set GOOGLE_CLIENT_SECRET=your-client-secret


# Linux/Mac
export GOOGLE_CLIENT_ID=your-client-id
export GOOGLE_CLIENT_SECRET=your-client-secret

```

## Running the Application

Start the application:

```bash
python main.py
```

The application will be available at http://localhost:8000

## Features

- Google OAuth authentication
- Session management
- Responsive dark theme UI with DaisyUI/Tailwind
- Simple animations with Anime.js

--- End File: .\README.md ---

--- File: .\requirements.txt ---
fastapi
uvicorn
jinja2
authlib
requests
termcolor
python-multipart
itsdangerous
httpx 
--- End File: .\requirements.txt ---

--- File: .\templates\index.html ---
<!DOCTYPE html>
<html lang="en" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Google Login</title>
    <!-- Tailwind and DaisyUI CDN -->
    <link href="https://cdn.jsdelivr.net/npm/daisyui@3.7.4/dist/full.css" rel="stylesheet" type="text/css" />
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Anime.js for animations -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js"></script>
    <style>
        .login-container {
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .login-card {
            width: 100%;
            max-width: 400px;
            overflow: hidden;
            border-radius: 1rem;
        }
        .animation-target {
            opacity: 0;
            transform: translateY(20px);
        }
    </style>
</head>
<body class="bg-gray-900 text-white">
    <div class="login-container">
        <div class="login-card bg-gray-800 shadow-xl p-8 animation-target">
            <div class="text-center mb-8">
                <h1 class="text-3xl font-bold text-primary mb-2">Welcome TO LOGIN PAGE</h1>
                <p class="text-gray-400">Sign in to continue</p>
            </div>
            
            {% if user %}
                <div class="text-center">
                    <div class="avatar mb-4">
                        {% if user.picture %}
                            <div class="w-24 rounded-full ring ring-primary ring-offset-base-100 ring-offset-2">
                                <img src="{{ user.picture }}" alt="User avatar" />
                            </div>
                        {% else %}
                            <div class="w-24 rounded-full bg-primary text-primary-content flex items-center justify-center text-2xl">
                                {{ user.name[0] }}
                            </div>
                        {% endif %}
                    </div>
                    <h2 class="text-xl font-bold">{{ user.name }}</h2>
                    <p class="text-gray-400 mb-6">{{ user.email }}</p>
                    <a href="/logout" class="btn btn-error btn-outline w-full">Logout</a>
                </div>
            {% else %}
                <div class="card-body">
                    <a href="/login" class="btn btn-primary w-full flex items-center justify-center gap-2">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" class="fill-current">
                            <path d="M12.545,10.239v3.821h5.445c-0.712,2.315-2.647,3.972-5.445,3.972c-3.332,0-6.033-2.701-6.033-6.032 s2.701-6.032,6.033-6.032c1.498,0,2.866,0.549,3.921,1.453l2.814-2.814C17.503,2.988,15.139,2,12.545,2 C7.021,2,2.543,6.477,2.543,12s4.478,10,10.002,10c8.396,0,10.249-7.85,9.426-11.748L12.545,10.239z"/>
                        </svg>
                        Sign in with Google
                    </a>
                </div>
            {% endif %}
            
            <div class="mt-6 text-center text-gray-500 text-sm">
                <p>Simple Google Login Demo</p>
            </div>
        </div>
    </div>

    <script>
        // Animate elements when page loads
        document.addEventListener('DOMContentLoaded', function() {
            anime({
                targets: '.animation-target',
                opacity: 1,
                translateY: 0,
                duration: 800,
                easing: 'easeOutExpo',
                delay: 300
            });
        });
    </script>
</body>
</html> 
--- End File: .\templates\index.html ---

