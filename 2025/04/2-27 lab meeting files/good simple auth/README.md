# Simple Google Login with FastAPI

A simple single-page application demonstrating Google OAuth login with FastAPI. refer to this folder in Cursor to give it inspiration to setup a simple google auth forn your app

## Setup

1. Install dependencies:

```bash
pip install -r requirements.txt
```

2. Create a Google OAuth Client:

   - Go to the [Google Cloud Console](https://console.cloud.google.com/)
   - Create a new project
   - Navigate to "APIs & Services" > "Credentials"
   - Click "Create Credentials" > "OAuth client ID"
   - Select "Web application" as the application type
   - Add "http://localhost:8000/auth/callback" to the Authorized redirect URIs
   - Copy your Client ID and Client Secret
3. Set environment variables:

```bash
# Windows
set GOOGLE_CLIENT_ID=your-client-id
set GOOGLE_CLIENT_SECRET=your-client-secret


# Linux/Mac
export GOOGLE_CLIENT_ID=your-client-id
export GOOGLE_CLIENT_SECRET=your-client-secret

```

## Running the Application

Start the application:

```bash
python main.py
```

The application will be available at http://localhost:8000

## Features

- Google OAuth authentication
- Session management
- Responsive dark theme UI with DaisyUI/Tailwind
- Simple animations with Anime.js
