from fastapi import <PERSON><PERSON><PERSON>, Request, Depends, HTTPException, status
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.templating import Jin<PERSON>2Templates
from fastapi.staticfiles import StaticFiles
import os
import requests
from authlib.integrations.starlette_client import OAuth
from starlette.middleware.sessions import SessionMiddleware
from termcolor import colored
import uvicorn
import json

# Constants
CLIENT_ID = "YOUR_CLIENT_ID"
CLIENT_SECRET = "567"
# NOTE: This is hardcoded for local development ONLY.
# In a real deployment, this URI must be generated dynamically
# (e.g., using request.url_for('auth_callback_route_name'))
# so it correctly reflects the current server's address (http/https, domain)
# and exactly matches one of the URIs registered in the Google Cloud Console.
REDIRECT_URI = "http://127.0.0.1:8000/auth/callback"

app = FastAPI(title="Simple Google Login")

# Setup session middleware
app.add_middleware(SessionMiddleware, secret_key=os.getenv("SECRET_KEY", "your-secret-key"))

# Setup templates and static files
templates = Jinja2Templates(directory="templates")
app.mount("/static", StaticFiles(directory="static"), name="static")

# Setup OAuth
oauth = OAuth()
oauth.register(
    name="google",
    client_id=CLIENT_ID,
    client_secret=CLIENT_SECRET,
    server_metadata_url="https://accounts.google.com/.well-known/openid-configuration",
    client_kwargs={"scope": "openid email profile"},
)

@app.get("/", response_class=HTMLResponse)
async def index(request: Request):
    print(colored("Serving index page", "green"))
    user = request.session.get("user")
    return templates.TemplateResponse("index.html", {"request": request, "user": user})

@app.get("/login")
async def login(request: Request):
    print(colored("Initiating Google login", "yellow"))
    try:
        redirect_uri = request.url_for("auth_callback")
        return await oauth.google.authorize_redirect(request, redirect_uri)
    except Exception as e:
        print(colored(f"Login error: {str(e)}", "red"))
        raise HTTPException(status_code=500, detail=f"Login failed: {str(e)}")

@app.get("/auth/callback")
async def auth_callback(request: Request):
    print(colored("Processing Google auth callback", "blue"))
    try:
        token = await oauth.google.authorize_access_token(request)
        user_info = token.get("userinfo")
        if user_info:
            request.session["user"] = dict(user_info)
            print(colored(f"User logged in: {user_info.get('email')}", "green"))
        return RedirectResponse(url="/")
    except Exception as e:
        print(colored(f"Auth callback error: {str(e)}", "red"))
        raise HTTPException(status_code=500, detail=f"Authentication failed: {str(e)}")

@app.get("/logout")
async def logout(request: Request):
    print(colored("User logging out", "yellow"))
    request.session.pop("user", None)
    return RedirectResponse(url="/")

if __name__ == "__main__":
    print(colored("Starting FastAPI server...", "cyan"))
    uvicorn.run("main:app", host="127.0.0.1", port=8000, reload=True) 