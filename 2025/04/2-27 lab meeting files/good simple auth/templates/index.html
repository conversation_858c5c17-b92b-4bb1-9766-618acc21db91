<!DOCTYPE html>
<html lang="en" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Google Login</title>
    <!-- Tailwind and DaisyUI CDN -->
    <link href="https://cdn.jsdelivr.net/npm/daisyui@3.7.4/dist/full.css" rel="stylesheet" type="text/css" />
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Anime.js for animations -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js"></script>
    <style>
        .login-container {
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .login-card {
            width: 100%;
            max-width: 400px;
            overflow: hidden;
            border-radius: 1rem;
        }
        .animation-target {
            opacity: 0;
            transform: translateY(20px);
        }
    </style>
</head>
<body class="bg-gray-900 text-white">
    <div class="login-container">
        <div class="login-card bg-gray-800 shadow-xl p-8 animation-target">
            <div class="text-center mb-8">
                <h1 class="text-3xl font-bold text-primary mb-2">Welcome TO LOGIN PAGE</h1>
                <p class="text-gray-400">Sign in to continue</p>
            </div>
            
            {% if user %}
                <div class="text-center">
                    <div class="avatar mb-4">
                        {% if user.picture %}
                            <div class="w-24 rounded-full ring ring-primary ring-offset-base-100 ring-offset-2">
                                <img src="{{ user.picture }}" alt="User avatar" />
                            </div>
                        {% else %}
                            <div class="w-24 rounded-full bg-primary text-primary-content flex items-center justify-center text-2xl">
                                {{ user.name[0] }}
                            </div>
                        {% endif %}
                    </div>
                    <h2 class="text-xl font-bold">{{ user.name }}</h2>
                    <p class="text-gray-400 mb-6">{{ user.email }}</p>
                    <a href="/logout" class="btn btn-error btn-outline w-full">Logout</a>
                </div>
            {% else %}
                <div class="card-body">
                    <a href="/login" class="btn btn-primary w-full flex items-center justify-center gap-2">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" class="fill-current">
                            <path d="M12.545,10.239v3.821h5.445c-0.712,2.315-2.647,3.972-5.445,3.972c-3.332,0-6.033-2.701-6.033-6.032 s2.701-6.032,6.033-6.032c1.498,0,2.866,0.549,3.921,1.453l2.814-2.814C17.503,2.988,15.139,2,12.545,2 C7.021,2,2.543,6.477,2.543,12s4.478,10,10.002,10c8.396,0,10.249-7.85,9.426-11.748L12.545,10.239z"/>
                        </svg>
                        Sign in with Google
                    </a>
                </div>
            {% endif %}
            
            <div class="mt-6 text-center text-gray-500 text-sm">
                <p>Simple Google Login Demo</p>
            </div>
        </div>
    </div>

    <script>
        // Animate elements when page loads
        document.addEventListener('DOMContentLoaded', function() {
            anime({
                targets: '.animation-target',
                opacity: 1,
                translateY: 0,
                duration: 800,
                easing: 'easeOutExpo',
                delay: 300
            });
        });
    </script>
</body>
</html> 