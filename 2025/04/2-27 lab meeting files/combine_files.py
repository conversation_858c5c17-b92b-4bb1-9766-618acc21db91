import os

def combine_files_in_cwd(output_filename="combined_output.txt"):
    """
    Goes through every file in the current working directory (including subfolders)
    and combines their path and content into a single text file.

    Args:
        output_filename (str): The name of the file to save the combined content.
    """
    # Get the absolute path of the output file to avoid including it
    abs_output_path = os.path.abspath(output_filename)
    files_processed = 0
    files_skipped = 0
    files_error = 0

    print(f"Starting file combination into '{output_filename}'...")

    try:
        # Open the output file in write mode with UTF-8 encoding
        with open(output_filename, 'w', encoding='utf-8') as outfile:
            # Walk through the current directory and all subdirectories
            for dirpath, dirnames, filenames in os.walk('.'):
                # --- Directory Filtering ---
                # Exclude common directories like .git, virtual environments, etc.
                # Modify this list as needed
                excluded_dirs = ['.git', '.venv', 'venv', '__pycache__', 'node_modules', '.vscode', 'build', 'dist', '.idea']
                dirnames[:] = [d for d in dirnames if d not in excluded_dirs]

                for filename in filenames:
                    file_path = os.path.join(dirpath, filename)
                    abs_file_path = os.path.abspath(file_path)

                    # --- File Filtering ---
                    # Skip the output file itself
                    if abs_file_path == abs_output_path:
                        files_skipped += 1
                        continue

                    # Optional: Skip specific file types (e.g., binaries)
                    # excluded_extensions = ['.png', '.jpg', '.jpeg', '.gif', '.zip', '.exe', '.dll', '.so', '.o', '.pyc']
                    # if any(filename.lower().endswith(ext) for ext in excluded_extensions):
                    #     print(f"Skipping binary/excluded file: {file_path}")
                    #     files_skipped += 1
                    #     continue

                    # --- File Processing ---
                    outfile.write(f"--- File: {file_path} ---\n")
                    try:
                        # Open each file and read its content
                        # Using errors='ignore' to handle potential decoding errors gracefully
                        with open(file_path, 'r', encoding='utf-8', errors='ignore') as infile:
                            content = infile.read()
                            outfile.write(content)
                        files_processed += 1
                    except Exception as e:
                        # Log errors encountered during file reading
                        outfile.write(f"!!! Error reading file: {e} !!!\n")
                        files_error += 1
                        print(f"Error reading {file_path}: {e}") # Also print error to console

                    outfile.write(f"\n--- End File: {file_path} ---\n\n")

        # --- Summary ---
        print(f"\nFinished combining files.")
        print(f"  Total files processed: {files_processed}")
        print(f"  Files skipped (output file, excluded types, etc.): {files_skipped}")
        print(f"  Files with read errors: {files_error}")
        print(f"Output saved to '{abs_output_path}'")

    except IOError as e:
        print(f"Error: Could not open or write to output file '{output_filename}': {e}")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")

# --- Execution ---
# Call the function to perform the combination when the script is run.
# Change the output filename if desired.
if __name__ == "__main__":
    combine_files_in_cwd("project_combined_content.txt")
