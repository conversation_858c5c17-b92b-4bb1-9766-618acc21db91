import os
from firecrawl import <PERSON><PERSON>rawlA<PERSON>
from termcolor import colored
import re

FIRE_CRAWL_API_KEY = os.getenv("FIRECRAWL_API_KEY")

def deep_researcher(user_query: str):
    """
    Performs deep research on a given query using Firecrawl,
    prints the results, and saves them to a Markdown file.

    Args:
        user_query: The research topic or question.
    """
    if not FIRE_CRAWL_API_KEY:
        print(colored("Error: FIRE_CRAWL_API_KEY environment variable not set.", "red"))
        return

    print(colored(f"Starting deep research for query: '{user_query}'", "cyan"))

    try:
        # Initialize the client
        firecrawl = FirecrawlApp(api_key=FIRE_CRAWL_API_KEY)

        # Define research parameters (optional, using defaults for simplicity here)
        params = {
            "maxDepth": 3,
            "timeLimit": 60,
            "maxUrls": 10
        }

        # Define the callback function for real-time updates
        def on_activity(activity):
            print(colored(f"[{activity['type'].upper()}] {activity['message']}", "yellow"))

        # Run deep research
        print(colored("Running Firecrawl deep research...", "magenta"))
        results = firecrawl.deep_research(
            query=user_query,
            # params=params, # Uncomment to use custom params
            on_activity=on_activity
        )

        # Check if research was successful and data exists
        if results and results.get("success") and "data" in results:
            final_analysis = results["data"].get("finalAnalysis", "No final analysis provided.")
            sources = results["data"].get("sources", [])

            print(colored("\n--- Final Analysis ---", "green"))
            print(colored(final_analysis, "green"))

            # Prepare markdown content
            markdown_content = f"# Deep Research: {user_query}\n\n"
            markdown_content += "## Final Analysis\n\n"
            markdown_content += f"{final_analysis}\n\n"

            if sources:
                markdown_content += "## Sources\n\n"
                for i, source in enumerate(sources):
                    title = source.get('title', 'N/A')
                    url = source.get('url', 'N/A')
                    description = source.get('description', 'N/A')
                    markdown_content += f"{i+1}. **[{title}]({url})**\n"
                    markdown_content += f"   - {description}\n"

            # Sanitize filename
            safe_filename = re.sub(r'[^\w\s-]', '', user_query).strip().lower()
            safe_filename = re.sub(r'[-\s]+', '_', safe_filename)
            if not safe_filename:
                safe_filename = "deep_research_output"
            output_filename = f"{safe_filename}.md"

            # Save to markdown file
            try:
                with open(output_filename, "w", encoding="utf-8") as f:
                    f.write(markdown_content)
                print(colored(f"\nResearch results saved to: {output_filename}", "cyan"))
            except IOError as e:
                print(colored(f"Error saving results to file {output_filename}: {e}", "red"))

        elif results and not results.get("success"):
             print(colored(f"Firecrawl research failed. Status: {results.get('status', 'Unknown')}", "red"))
        else:
            print(colored("Firecrawl research did not return expected data.", "red"))

    except Exception as e:
        print(colored(f"An error occurred during the deep research process: {e}", "red"))

# Example Usage (Requires FIRE_CRAWL_API_KEY environment variable)
if __name__ == "__main__":
    test_query = "What are some of the funny prompts people use to get better code out of the LLMs?"
    deep_researcher(test_query)
