# Transformer Plays Snake (Behavioral Cloning)

This project demonstrates training a Transformer neural network to play the classic game of Snake using a Behavioral Cloning approach.

## Overview

Instead of using Reinforcement Learning (RL), where an agent learns through trial-and-error with rewards, this project uses Supervised Learning:

1.  **Expert Algorithm:** A reasonably competent Snake-playing algorithm (based on Breadth-First Search to find paths to food) is implemented (`expert_snake_player.py`).
2.  **Data Generation:** The expert algorithm plays the game many times, and its gameplay (`(game_state, chosen_action)` pairs) is recorded (`generate_data.py`).
3.  **Transformer Training:** A Transformer Encoder model (`transformer_model.py`) is trained on this recorded data. The model learns to predict the action the *expert* would take given a specific game state.
4.  **Gameplay:** The trained Transformer model can then be used to play the game by feeding it the current state and taking the action it predicts (`play_game.py`).

## Project Structure

```
.
├── checkpoints/          # Directory created to store model checkpoints and best model
├── config.py             # Game configuration (GRID_SIZE, cell values)
├── expert_snake_player.py # Implements the BFS-based expert algorithm
├── generate_data.py      # Runs the expert to generate training data (expert_snake_data.pkl)
├── play_game.py          # Loads the trained model and plays the game visually
├── requirements.txt      # Project dependencies
├── snake_game.py         # Core Snake game logic and Pygame rendering
├── train.py              # Trains the Transformer model on the generated data
├── transformer_model.py  # Defines the PyTorch Transformer Encoder architecture
├── utils.py              # Utility functions (plotting, model saving/loading)
└── README.md             # This file
```

## Setup

1.  **Clone the repository (if applicable):**
    ```bash
    git clone <repository-url>
    cd <repository-directory>
    ```

2.  **Create a virtual environment (recommended):**
    ```bash
    python -m venv venv
    # On Windows
    .\venv\Scripts\activate
    # On macOS/Linux
    source venv/bin/activate
    ```

3.  **Install dependencies:**
    ```bash
    pip install -r requirements.txt
    ```
    *Note: This includes `torch`. If you have a CUDA-enabled GPU, ensure you install the correct PyTorch version for CUDA support following the official PyTorch instructions: [https://pytorch.org/get-started/locally/](https://pytorch.org/get-started/locally/)*

## Usage

Follow these steps in order:

1.  **Generate Expert Data:**
    Run the data generation script. This will simulate games using the expert algorithm and save the state-action pairs to `expert_snake_data.pkl`.
    ```bash
    python generate_data.py
    ```
    *This may take some time depending on the `NUM_GAMES` setting in `generate_data.py`.*

2.  **Train the Transformer Model:**
    Run the training script. This loads the generated data, trains the Transformer, saves periodic checkpoints and the best model to the `checkpoints/` directory, and generates a `training_progress.png` plot.
    ```bash
    python train.py
    ```
    *Training progress (loss, accuracy) will be printed to the console, and the plot will be updated after each epoch. Training will use CUDA if available and detected by PyTorch.*

3.  **Play the Game with the Trained Model:**
    Once training is complete and `checkpoints/best_snake_transformer.pth` exists, run the play script to watch the model play.
    ```bash
    python play_game.py
    ```
    *A Pygame window will open showing the game. Press 'Q' or close the window to exit.*

## Configuration

-   **Game:** `config.py` contains the `GRID_SIZE` and integer representations for game elements.
-   **Data Generation:** `generate_data.py` has `NUM_GAMES` to control the amount of data generated.
-   **Training:** `train.py` contains hyperparameters like `LEARNING_RATE`, `BATCH_SIZE`, `NUM_EPOCHS`, etc.
-   **Model Architecture:** `transformer_model.py` defines the Transformer parameters (`EMBED_DIM`, `NUM_HEADS`, `NUM_LAYERS`, etc.).
-   **Gameplay:** `play_game.py` has `FRAME_DELAY` to control the visualization speed.

## Dependencies

See `requirements.txt` for the list of required Python packages.

-   `torch`: Deep learning framework.
-   `numpy`: Numerical computation.
-   `pygame`: Game rendering and visualization.
-   `matplotlib`: Plotting training progress.
-   `tqdm`: Progress bars.
-   `termcolor`: Colored terminal output. 