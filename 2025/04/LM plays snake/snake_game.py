import pygame
import random
from collections import deque
import numpy as np
from termcolor import cprint

# Configuration (assuming config.py is in the same directory or accessible)
from config import GRID_SIZE, EMPTY, SNAKE_HEAD, SNAKE_BODY, FOOD

# Constants for rendering (optional, if we add visualization)
CELL_SIZE = 30
WIDTH = GRID_SIZE * CELL_SIZE
HEIGHT = GRID_SIZE * CELL_SIZE
WHITE = (255, 255, 255)
GREEN = (0, 255, 0)
DARK_GREEN = (0, 155, 0)
RED = (255, 0, 0)
BLACK = (0, 0, 0)

class SnakeGame:
    def __init__(self):
        self.grid_size = GRID_SIZE
        self.reset()

    def reset(self):
        """Resets the game to the initial state."""
        cprint("Resetting game state", "yellow")
        # Initial snake position (center)
        start_x = self.grid_size // 2
        start_y = self.grid_size // 2
        self.snake = deque([(start_x, start_y)])
        self.direction = random.choice([(0, 1), (0, -1), (1, 0), (-1, 0)]) # Initial random direction

        # Place initial food
        self.food = self._place_food()

        self.score = 0
        self.game_over = False
        self.steps_since_last_food = 0
        self.max_steps_no_food = self.grid_size * self.grid_size * 2 # Heuristic limit

        return self.get_state()

    def _place_food(self):
        """Places food in a random empty cell."""
        while True:
            food_pos = (random.randint(0, self.grid_size - 1), random.randint(0, self.grid_size - 1))
            if food_pos not in self.snake:
                return food_pos

    def step(self, action):
        """
        Takes an action and updates the game state.

        Args:
            action (tuple): The change in direction (dx, dy). e.g., (0, -1) for UP.

        Returns:
            tuple: (next_state, reward, game_over)
        """
        if self.game_over:
            cprint("Attempted step after game over.", "red")
            return self.get_state(), 0, self.game_over

        # Prevent immediate reversal
        current_dx, current_dy = self.direction
        action_dx, action_dy = action
        if len(self.snake) > 1 and action_dx == -current_dx and action_dy == -current_dy:
            # If trying to reverse, continue in the current direction
            cprint("Attempted reversal, maintaining direction.", "magenta")
            action = self.direction
        else:
            self.direction = action

        # Get current head position
        head_x, head_y = self.snake[0]

        # Calculate new head position
        new_head_x = head_x + self.direction[0]
        new_head_y = head_y + self.direction[1]
        new_head = (new_head_x, new_head_y)

        # Check for collisions
        if (
            new_head_x < 0 or new_head_x >= self.grid_size or
            new_head_y < 0 or new_head_y >= self.grid_size or
            new_head in list(self.snake)[1:] # Collision with body (ignore current head)
        ):
            self.game_over = True
            cprint(f"Game Over: Collision at {new_head}", "red")
            reward = -10 # Penalty for dying
            return self.get_state(), reward, self.game_over

        # Move snake
        self.snake.appendleft(new_head)

        # Check for food eaten
        if new_head == self.food:
            self.score += 1
            cprint(f"Food eaten! Score: {self.score}", "green")
            self.food = self._place_food()
            reward = 10 # Reward for eating food
            self.steps_since_last_food = 0
        else:
            # Remove tail if no food eaten
            self.snake.pop()
            reward = 0 # No reward/penalty for just moving
            self.steps_since_last_food += 1

        # Check if snake is stuck starving
        if self.steps_since_last_food > self.max_steps_no_food:
            self.game_over = True
            cprint(f"Game Over: Starved (steps without food: {self.steps_since_last_food})", "red")
            reward = -10 # Penalty for starving

        return self.get_state(), reward, self.game_over

    def get_state(self):
        """Returns the current game state as a numpy grid."""
        grid = np.full((self.grid_size, self.grid_size), EMPTY, dtype=int)

        # Place food
        if self.food:
            fx, fy = self.food
            if 0 <= fx < self.grid_size and 0 <= fy < self.grid_size:
                 grid[fx, fy] = FOOD

        # Place snake body
        for i, (sx, sy) in enumerate(list(self.snake)):
             if 0 <= sx < self.grid_size and 0 <= sy < self.grid_size:
                if i == 0: # Head
                    grid[sx, sy] = SNAKE_HEAD
                else: # Body
                    grid[sx, sy] = SNAKE_BODY

        return grid

    def render(self, screen=None):
        """Renders the game state using Pygame (optional)."""
        if screen is None:
            # Initialize Pygame if not already done
            try:
                pygame.init()
                screen = pygame.display.set_mode((WIDTH, HEIGHT))
                pygame.display.set_caption('Snake Game')
            except Exception as e:
                cprint(f"Error initializing Pygame for rendering: {e}", "red")
                return None # Cannot render

        screen.fill(BLACK)
        grid_state = self.get_state()

        for r in range(self.grid_size):
            for c in range(self.grid_size):
                color = BLACK
                if grid_state[r, c] == SNAKE_HEAD:
                    color = DARK_GREEN
                elif grid_state[r, c] == SNAKE_BODY:
                    color = GREEN
                elif grid_state[r, c] == FOOD:
                    color = RED

                pygame.draw.rect(screen, color, (r * CELL_SIZE, c * CELL_SIZE, CELL_SIZE, CELL_SIZE))
                # Draw grid lines
                pygame.draw.rect(screen, WHITE, (r * CELL_SIZE, c * CELL_SIZE, CELL_SIZE, CELL_SIZE), 1)

        pygame.display.flip()
        return screen

    def get_possible_actions(self):
        """Returns a list of valid (non-reversing) actions."""
        actions = [(0, -1), (0, 1), (-1, 0), (1, 0)] # Up, Down, Left, Right
        if len(self.snake) <= 1:
            return actions # Can go any way if length 1

        current_dx, current_dy = self.direction
        opposite_action = (-current_dx, -current_dy)

        valid_actions = [a for a in actions if a != opposite_action]
        return valid_actions

# Example usage (for testing)
if __name__ == '__main__':
    game = SnakeGame()
    screen = None
    running = True
    clock = pygame.time.Clock()

    try:
        screen = game.render()
        if screen is None: raise Exception("Pygame init failed")

        while running:
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    running = False
                elif event.type == pygame.KEYDOWN:
                    action = game.direction # Default to current direction
                    if event.key == pygame.K_UP:
                        action = (0, -1)
                    elif event.key == pygame.K_DOWN:
                        action = (0, 1)
                    elif event.key == pygame.K_LEFT:
                        action = (-1, 0)
                    elif event.key == pygame.K_RIGHT:
                        action = (1, 0)

                    state, reward, done = game.step(action)
                    screen = game.render(screen)

                    if done:
                        cprint(f"Final Score: {game.score}", "blue")
                        pygame.time.wait(2000) # Pause before reset
                        game.reset()
                        screen = game.render(screen)

            # If not controlled by keydown, maybe let it run automatically?
            # For manual testing, we only step on keydown.

            clock.tick(5) # Limit frame rate

    except Exception as e:
        cprint(f"Error during game loop: {e}", "red")
    finally:
        pygame.quit() 