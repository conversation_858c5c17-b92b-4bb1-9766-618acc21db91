import numpy as np
from collections import deque
from termcolor import cprint
import random

# Assuming config.py defines GRID_SIZE, EMPTY, SNAKE_HEAD, SNAKE_BODY, FOOD
from config import GRID_SIZE, EMPTY, SNAKE_BODY
from snake_game import <PERSON><PERSON><PERSON> # To use game methods like get_state

class ExpertSnakePlayer:
    def __init__(self, game_instance: SnakeGame, verbose: bool = True):
        """
        Initializes the expert player.

        Args:
            game_instance: An instance of the SnakeGame class.
            verbose (bool): Whether to print internal expert decisions.
        """
        self.game = game_instance
        self.grid_size = self.game.grid_size
        self.verbose = verbose # Store verbose flag
        if self.verbose:
            cprint("Expert Snake Player initialized.", "cyan")

    def _is_safe(self, point, snake_body):
        """Check if a point is within bounds and not on the snake's body."""
        x, y = point
        if x < 0 or x >= self.grid_size or y < 0 or y >= self.grid_size:
            return False
        if point in snake_body:
            return False
        return True

    def _bfs_to_target(self, start, target, snake_body):
        """
        Performs Breadth-First Search to find the shortest path from start to target.

        Args:
            start: The starting point (head position).
            target: The target point (food position).
            snake_body: A set of coordinates occupied by the snake's body (excluding head).

        Returns:
            list: A list of coordinates representing the path, or None if no path found.
        """
        queue = deque([(start, [start])]) # (current_pos, path_so_far)
        visited = {start}

        while queue:
            (current_x, current_y), path = queue.popleft()

            if (current_x, current_y) == target:
                return path # Found the target

            # Explore neighbors (Up, Down, Left, Right)
            for dx, dy in [(0, -1), (0, 1), (-1, 0), (1, 0)]:
                next_x, next_y = current_x + dx, current_y + dy
                next_point = (next_x, next_y)

                # Check safety and if visited
                if self._is_safe(next_point, snake_body) and next_point not in visited:
                    visited.add(next_point)
                    new_path = list(path)
                    new_path.append(next_point)
                    queue.append((next_point, new_path))

        return None # No path found

    def get_action(self):
        """
        Determines the best action for the snake based on BFS pathfinding.

        Returns:
            tuple: The action (dx, dy) to take, e.g., (0, -1) for UP.
                   Returns current direction if no better move found or game over.
        """
        if self.game.game_over:
            cprint("Expert asked for action but game is over.", "red")
            return self.game.direction # Return current direction if game over

        head = self.game.snake[0]
        food = self.game.food
        # Need body as a set for efficient lookup, excluding the head itself for BFS
        snake_body_set = set(list(self.game.snake)[1:])

        # 1. Find path to food using BFS
        path_to_food = self._bfs_to_target(head, food, snake_body_set)

        if path_to_food and len(path_to_food) > 1:
            if self.verbose:
                cprint(f"Expert: Found path to food: {path_to_food}", "green")
            # Get the next step in the path
            next_step = path_to_food[1]
            action = (next_step[0] - head[0], next_step[1] - head[1])

            # Sanity check: Ensure this action is not reversing
            current_dx, current_dy = self.game.direction
            if len(self.game.snake) > 1 and action[0] == -current_dx and action[1] == -current_dy:
                if self.verbose:
                    cprint(f"Expert Warning: BFS suggested reversal ({action}), choosing safest alternative.", "magenta")
                # Fallback to survival if BFS suggests reversal (shouldn't happen with proper BFS)
                action = self._find_safest_move(head, snake_body_set)

            # Check if the chosen move is immediately fatal (e.g. path requires moving into space where tail will be)
            # A more complex check could simulate the move and see if a path still exists
            # For now, just check immediate safety
            if not self._is_safe(next_step, snake_body_set):
                 if self.verbose:
                     cprint(f"Expert Warning: Chosen BFS step {next_step} unsafe, choosing safest alternative.", "magenta")
                 action = self._find_safest_move(head, snake_body_set)

            if self.verbose:
                cprint(f"Expert Action (to food): {action}", "blue")
            return action

        else:
            # 2. If no path to food, try to survive by finding the safest move (e.g., move to space with most freedom or follow tail)
            if self.verbose:
                cprint("Expert: No path to food found, finding safest move.", "yellow")
            action = self._find_safest_move(head, snake_body_set)
            if self.verbose:
                cprint(f"Expert Action (survival): {action}", "blue")
            return action

    def _find_safest_move(self, head, snake_body_set):
        """Finds a safe move that doesn't immediately lead to collision."""
        possible_actions = self.game.get_possible_actions()
        safe_actions = []

        for dx, dy in possible_actions:
            next_x, next_y = head[0] + dx, head[1] + dy
            next_point = (next_x, next_y)
            if self._is_safe(next_point, snake_body_set):
                safe_actions.append((dx, dy))

        if not safe_actions:
            # No safe moves found - try any non-reversing move (will likely crash)
            cprint("Expert Warning: No safe moves found! Choosing a non-reversing one.", "red")
            if possible_actions:
                return random.choice(possible_actions)
            else:
                 # Should only happen if snake len 1 and trapped itself somehow?
                 return self.game.direction # Last resort
        else:
            # Optional: Add more intelligence - e.g., choose move furthest from body, closest to center, etc.
            # For now, just pick a random safe move.
            return random.choice(safe_actions)

# Example usage (requires a running game)
if __name__ == '__main__':
    import time
    game = SnakeGame()
    expert = ExpertSnakePlayer(game, verbose=True)
    screen = None
    try:
        screen = game.render() # Initialize screen
        if screen is None: raise Exception("Pygame init failed for expert test")

        while not game.game_over:
            # Get action from expert
            action = expert.get_action()

            # Perform action in the game
            state, reward, done = game.step(action)

            # Render the game state
            screen = game.render(screen)

            # Optional delay to watch the expert play
            time.sleep(0.1)
            # pygame.time.Clock().tick(10) # Alternative: Use pygame clock

            if done:
                cprint(f"Expert finished game. Final Score: {game.score}", "green")
                time.sleep(2)
                # game.reset() # Optionally reset and run again

    except Exception as e:
        cprint(f"Error during expert player test: {e}", "red")
    finally:
        import pygame
        pygame.quit() 