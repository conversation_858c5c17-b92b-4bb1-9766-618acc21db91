import torch
import torch.nn as nn
import math
from termcolor import cprint

# Assuming config.py defines GRID_SIZE
from config import GRID_SIZE

# --- Model Configuration ---
INPUT_DIM = GRID_SIZE * GRID_SIZE # Flattened grid size
NUM_CLASSES = 4 # Number of possible actions (Up, Down, Left, Right)
EMBED_DIM = 128 # Embedding dimension for input tokens (grid cell values)
NUM_HEADS = 8 # Number of attention heads
NUM_LAYERS = 6 # Number of Transformer encoder layers
DIM_FEEDFORWARD = 512 # Dimension of the feedforward network model in nn.TransformerEncoderLayer
DROPOUT = 0.1
VOCAB_SIZE = 5 # Number of unique cell values (Empty, Head, Body, Food, maybe Padding if needed)

class PositionalEncoding(nn.Module):
    """Injects positional information into the input embeddings."""
    def __init__(self, d_model, dropout=0.1, max_len=5000):
        super(PositionalEncoding, self).__init__()
        self.dropout = nn.Dropout(p=dropout)

        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * (-math.log(10000.0) / d_model))
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        self.register_buffer('pe', pe) # Register as buffer so it's part of state_dict but not parameters

    def forward(self, x):
        """
        Args:
            x: Tensor, shape [seq_len, batch_size, embedding_dim]
        """
        x = x + self.pe[:x.size(0), :]
        return self.dropout(x)

class SnakeTransformer(nn.Module):
    """Transformer Encoder model for classifying snake actions."""
    def __init__(self, input_dim=INPUT_DIM, num_classes=NUM_CLASSES, vocab_size=VOCAB_SIZE,
                 embed_dim=EMBED_DIM, num_heads=NUM_HEADS, num_layers=NUM_LAYERS,
                 dim_feedforward=DIM_FEEDFORWARD, dropout=DROPOUT):
        super(SnakeTransformer, self).__init__()
        cprint("Initializing SnakeTransformer model...", "cyan")

        self.input_dim = input_dim # Should be seq_len = grid_size * grid_size
        self.embed_dim = embed_dim

        # Embedding layer: Maps input token indices (0-4) to dense vectors
        self.embedding = nn.Embedding(vocab_size, embed_dim)
        cprint(f"  Embedding layer: vocab_size={vocab_size}, embed_dim={embed_dim}", "blue")

        # Positional Encoding
        self.pos_encoder = PositionalEncoding(embed_dim, dropout, max_len=input_dim + 1)
        cprint("  Positional Encoding layer added.", "blue")

        # Transformer Encoder Layer
        encoder_layer = nn.TransformerEncoderLayer(d_model=embed_dim, nhead=num_heads,
                                                   dim_feedforward=dim_feedforward,
                                                   dropout=dropout, batch_first=False)
        cprint(f"  Encoder Layer: embed_dim={embed_dim}, heads={num_heads}, ff_dim={dim_feedforward}, dropout={dropout}", "blue")

        # Transformer Encoder
        self.transformer_encoder = nn.TransformerEncoder(encoder_layer, num_layers=num_layers)
        cprint(f"  Transformer Encoder: num_layers={num_layers}", "blue")

        # Output layer: Maps the transformer output to action probabilities
        # We can take the output corresponding to the first token ([CLS] token style, though we don't have one explicitly)
        # Or average the outputs, or use the output of the last token.
        # Let's try averaging the sequence output for simplicity first.
        self.fc_out = nn.Linear(embed_dim, num_classes)
        cprint(f"  Output FC layer: in_features={embed_dim}, out_features={num_classes}", "blue")

        self._init_weights()

    def _init_weights(self):
        """Initializes weights using Xavier initialization."""
        initrange = 0.1
        try:
            self.embedding.weight.data.uniform_(-initrange, initrange)
            self.fc_out.bias.data.zero_()
            self.fc_out.weight.data.uniform_(-initrange, initrange)
            cprint("Model weights initialized.", "yellow")
        except Exception as e:
             cprint(f"Error initializing weights: {e}", "red")

    def forward(self, src):
        """
        Forward pass of the model.

        Args:
            src: Input tensor, shape [batch_size, seq_len].
                 Represents the flattened grid state with integer cell values.

        Returns:
            Output tensor, shape [batch_size, num_classes].
            Logits for each action class.
        """
        try:
            # src shape: [batch_size, seq_len]
            # Embedding expects [batch_size, seq_len] or [seq_len, batch_size]
            # TransformerEncoder expects [seq_len, batch_size, embed_dim] by default (batch_first=False)

            # 1. Embedding
            # Input src expected to be LongTensor for nn.Embedding
            embedded = self.embedding(src.long()) * math.sqrt(self.embed_dim)
            # embedded shape: [batch_size, seq_len, embed_dim]

            # 2. Positional Encoding - needs [seq_len, batch_size, embed_dim]
            embedded = embedded.permute(1, 0, 2) # Permute to [seq_len, batch_size, embed_dim]
            pos_encoded = self.pos_encoder(embedded)
            # pos_encoded shape: [seq_len, batch_size, embed_dim]

            # 3. Transformer Encoder
            # Masking: For this classification task based on the whole sequence,
            # we typically don't need a source mask (src_mask) or key padding mask (src_key_padding_mask)
            # unless we have variable length sequences or padding.
            # Our sequences are fixed length (GRID_SIZE*GRID_SIZE).
            transformer_output = self.transformer_encoder(pos_encoded)
            # transformer_output shape: [seq_len, batch_size, embed_dim]

            # 4. Aggregate output and classify
            # Option 1: Average pooling over the sequence length dimension
            aggregated_output = transformer_output.mean(dim=0) # Average across seq_len
            # aggregated_output shape: [batch_size, embed_dim]

            # Option 2: Use the output of the first token (like [CLS] token)
            # aggregated_output = transformer_output[0, :, :] # Shape: [batch_size, embed_dim]

            # 5. Final Linear Layer
            output_logits = self.fc_out(aggregated_output)
            # output_logits shape: [batch_size, num_classes]

            return output_logits

        except Exception as e:
            cprint(f"Error during forward pass: {e}", "red", attrs=["bold"])
            cprint(f"Input shape: {src.shape if isinstance(src, torch.Tensor) else type(src)}", "red")
            import traceback
            traceback.print_exc()
            # Depending on how this is called, might need to return a dummy tensor
            # of the expected shape to prevent crashes upstream.
            # Create dummy output matching expected shape [batch_size, num_classes]
            # Need batch_size from input src
            batch_size = src.shape[0] if isinstance(src, torch.Tensor) and src.ndim > 1 else 1
            dummy_output = torch.zeros((batch_size, NUM_CLASSES), device=src.device if isinstance(src, torch.Tensor) else torch.device('cpu'))
            return dummy_output


# Example usage (for testing shape)
if __name__ == '__main__':
    BATCH_SIZE = 4
    SEQ_LEN = GRID_SIZE * GRID_SIZE

    # Create dummy input data (batch_size, seq_len)
    # Values should be within the vocab size [0, VOCAB_SIZE-1]
    dummy_input = torch.randint(0, VOCAB_SIZE, (BATCH_SIZE, SEQ_LEN)) 
    cprint(f"Dummy input shape: {dummy_input.shape}", "yellow")

    # Instantiate the model
    model = SnakeTransformer()
    cprint(f"Model Structure:\n{model}", "yellow")

    # Perform a forward pass
    cprint("Performing forward pass...", "yellow")
    try:
        output = model(dummy_input)
        cprint(f"Output shape: {output.shape}", "green") # Expected: [BATCH_SIZE, NUM_CLASSES]
        cprint(f"Output logits (first item):\n{output[0]}", "green")
    except Exception as e:
        cprint(f"Error during example forward pass: {e}", "red") 