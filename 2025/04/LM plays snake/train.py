import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader, random_split
import numpy as np
import pickle
import os
from tqdm import tqdm
from termcolor import cprint

from transformer_model import SnakeTransformer # Import the model definition
from utils import plot_metrics, save_checkpoint, save_best_model, load_checkpoint # Import utility functions

# --- Configuration ---
DATA_FILENAME = "expert_snake_data.pkl"
CHECKPOINT_FILE = "snake_transformer_checkpoint.pth"
BEST_MODEL_FILE = "best_snake_transformer.pth"

# Training Hyperparameters
LEARNING_RATE = 0.0001
BATCH_SIZE = 64
NUM_EPOCHS = 50
VALIDATION_SPLIT = 0.1 # Fraction of data to use for validation
EARLY_STOPPING_PATIENCE = 5 # Stop if validation loss doesn't improve for this many epochs
DEVICE = "cuda" if torch.cuda.is_available() else "cpu"

# Model hyperparameters (should match transformer_model.py or be passed)
# INPUT_DIM, NUM_CLASSES, EMBED_DIM, etc. are defined in transformer_model.py

class SnakeDataset(Dataset):
    """Custom PyTorch Dataset for the snake state-action pairs."""
    def __init__(self, data):
        self.data = data
        cprint(f"Created dataset with {len(data)} samples.", "yellow")

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        state, action = self.data[idx]
        # Convert state (numpy array) and action (int) to tensors
        # State should be LongTensor for embedding layer
        state_tensor = torch.tensor(state, dtype=torch.long)
        # Action should be LongTensor for CrossEntropyLoss
        action_tensor = torch.tensor(action, dtype=torch.long)
        return state_tensor, action_tensor

def calculate_accuracy(outputs, labels):
    """Calculates the accuracy of predictions.""" 
    _, predicted = torch.max(outputs.data, 1)
    total = labels.size(0)
    correct = (predicted == labels).sum().item()
    return correct / total

def train_model():
    cprint(f"Using device: {DEVICE}", "cyan")

    # 1. Load Data
    cprint(f"Loading data from {DATA_FILENAME}...", "yellow")
    try:
        with open(DATA_FILENAME, 'rb') as f:
            dataset_raw = pickle.load(f)
        cprint(f"Successfully loaded {len(dataset_raw)} samples.", "green")
    except FileNotFoundError:
        cprint(f"Error: Data file not found at {DATA_FILENAME}.", "red", attrs=["bold"])
        cprint("Please run generate_data.py first.", "red")
        return
    except Exception as e:
        cprint(f"Error loading data: {e}", "red", attrs=["bold"])
        return

    # 2. Create Dataset and Dataloaders
    dataset = SnakeDataset(dataset_raw)
    dataset_size = len(dataset)
    val_size = int(VALIDATION_SPLIT * dataset_size)
    train_size = dataset_size - val_size

    try:
        train_dataset, val_dataset = random_split(dataset, [train_size, val_size])
        cprint(f"Split data: {len(train_dataset)} train, {len(val_dataset)} validation samples.", "yellow")
    except Exception as e:
        cprint(f"Error splitting dataset: {e}", "red")
        return

    train_loader = DataLoader(train_dataset, batch_size=BATCH_SIZE, shuffle=True, num_workers=4, pin_memory=True if DEVICE=='cuda' else False)
    val_loader = DataLoader(val_dataset, batch_size=BATCH_SIZE, shuffle=False, num_workers=4, pin_memory=True if DEVICE=='cuda' else False)
    cprint(f"Created DataLoaders with batch size {BATCH_SIZE}.", "yellow")

    # 3. Initialize Model, Loss, Optimizer
    model = SnakeTransformer().to(DEVICE) # Uses defaults from transformer_model.py
    criterion = nn.CrossEntropyLoss() # Suitable for classification
    optimizer = optim.Adam(model.parameters(), lr=LEARNING_RATE)

    # --- Variables for tracking progress & best model ---
    best_val_loss = float('inf')
    epochs_no_improve = 0
    start_epoch = 0
    train_losses, val_losses = [], []
    train_accuracies, val_accuracies = [], []

    # 4. Load Checkpoint (if exists)
    cprint("Attempting to load checkpoint...", "yellow")
    checkpoint = load_checkpoint(model, optimizer, CHECKPOINT_FILE)
    if checkpoint:
        start_epoch = checkpoint.get('epoch', 0) + 1 # Start from next epoch
        # best_val_loss might need loading if we stored it, or recalculate based on loaded loss
        cprint(f"Resuming training from epoch {start_epoch}", "green")
        # TODO: Potentially load historical losses/accuracies if saved in checkpoint

    # 5. Training Loop
    cprint("Starting training loop...", "cyan")
    for epoch in range(start_epoch, NUM_EPOCHS):
        model.train() # Set model to training mode
        running_loss = 0.0
        running_accuracy = 0.0
        train_steps = 0

        progress_bar = tqdm(train_loader, desc=f"Epoch {epoch+1}/{NUM_EPOCHS} [Train]")
        try:
            for i, (inputs, labels) in enumerate(progress_bar):
                inputs, labels = inputs.to(DEVICE), labels.to(DEVICE)

                # Zero the parameter gradients
                optimizer.zero_grad()

                # Forward pass
                outputs = model(inputs)

                # Calculate loss
                loss = criterion(outputs, labels)

                # Backward pass and optimize
                loss.backward()
                optimizer.step()

                # Statistics
                running_loss += loss.item()
                accuracy = calculate_accuracy(outputs, labels)
                running_accuracy += accuracy
                train_steps += 1

                # Update progress bar
                progress_bar.set_postfix({'loss': running_loss/train_steps, 'acc': running_accuracy/train_steps})

            epoch_train_loss = running_loss / train_steps
            epoch_train_acc = running_accuracy / train_steps
            train_losses.append(epoch_train_loss)
            train_accuracies.append(epoch_train_acc)
            cprint(f"\nEpoch {epoch+1} Train Loss: {epoch_train_loss:.4f}, Train Acc: {epoch_train_acc:.4f}", "green")

        except Exception as e:
            cprint(f"\nError during training epoch {epoch+1}: {e}", "red", attrs=["bold"])
            import traceback
            traceback.print_exc()
            cprint("Trying to save checkpoint before stopping...", "yellow")
            save_checkpoint(model, optimizer, epoch, running_loss / train_steps if train_steps > 0 else 0, CHECKPOINT_FILE)
            break # Stop training if an epoch fails

        # --- Validation Step ---
        model.eval() # Set model to evaluation mode
        val_loss = 0.0
        val_accuracy = 0.0
        val_steps = 0
        with torch.no_grad(): # No need to track gradients during validation
            progress_bar_val = tqdm(val_loader, desc=f"Epoch {epoch+1}/{NUM_EPOCHS} [Val]")
            try:
                for inputs, labels in progress_bar_val:
                    inputs, labels = inputs.to(DEVICE), labels.to(DEVICE)
                    outputs = model(inputs)
                    loss = criterion(outputs, labels)
                    val_loss += loss.item()
                    accuracy = calculate_accuracy(outputs, labels)
                    val_accuracy += accuracy
                    val_steps += 1
                    progress_bar_val.set_postfix({'val_loss': val_loss/val_steps, 'val_acc': val_accuracy/val_steps})

            except Exception as e:
                 cprint(f"\nError during validation epoch {epoch+1}: {e}", "red", attrs=["bold"])
                 # Decide if you want to stop training or just skip validation for this epoch
                 cprint("Skipping validation for this epoch due to error.", "red")
                 # Add placeholder values or handle as needed
                 val_losses.append(float('nan'))
                 val_accuracies.append(float('nan'))
                 continue # Continue to next epoch

        epoch_val_loss = val_loss / val_steps
        epoch_val_acc = val_accuracy / val_steps
        val_losses.append(epoch_val_loss)
        val_accuracies.append(epoch_val_acc)
        cprint(f"Epoch {epoch+1} Val Loss: {epoch_val_loss:.4f}, Val Acc: {epoch_val_acc:.4f}", "magenta")

        # --- Plotting --- (Do this every epoch to see live updates)
        plot_metrics(train_losses, val_losses, train_accuracies, val_accuracies, epoch)

        # --- Save Checkpoint --- (Save every epoch or N epochs)
        save_checkpoint(model, optimizer, epoch, epoch_val_loss, CHECKPOINT_FILE)

        # --- Save Best Model & Early Stopping --- 
        if epoch_val_loss < best_val_loss:
            cprint(f"Validation loss improved ({best_val_loss:.4f} -> {epoch_val_loss:.4f}). Saving best model...", "green")
            best_val_loss = epoch_val_loss
            save_best_model(model, BEST_MODEL_FILE) # Save the best model state
            epochs_no_improve = 0
        else:
            epochs_no_improve += 1
            cprint(f"Validation loss did not improve for {epochs_no_improve} epoch(s). Best: {best_val_loss:.4f}", "yellow")
            if epochs_no_improve >= EARLY_STOPPING_PATIENCE:
                cprint(f"Early stopping triggered after {EARLY_STOPPING_PATIENCE} epochs without improvement.", "red")
                break # Exit training loop

    cprint("Training finished.", "cyan")

if __name__ == "__main__":
    train_model() 