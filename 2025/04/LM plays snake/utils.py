import matplotlib.pyplot as plt
import torch
import os
from termcolor import cprint

# --- Configuration ---
PLOT_FILENAME = "training_progress.png"
BEST_MODEL_FILENAME = "best_snake_transformer.pth"
CHECKPOINT_DIR = "checkpoints"

def plot_metrics(train_losses, val_losses, train_accuracies, val_accuracies, epoch):
    """Plots training and validation loss and accuracy and saves the plot."""
    try:
        plt.style.use('dark_background') # Use dark mode style
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))
        fig.suptitle(f'Training Progress - Epoch {epoch + 1}', color='white')

        # Loss Plot
        ax1.plot(train_losses, label='Train Loss', color='#88c0d0') # Blue
        ax1.plot(val_losses, label='Validation Loss', color='#ebcb8b') # Yellow
        ax1.set_xlabel('Epoch', color='#d8dee9')
        ax1.set_ylabel('Loss', color='#d8dee9')
        ax1.set_title('Loss over Epochs', color='white')
        ax1.legend()
        ax1.grid(True, linestyle='--', alpha=0.6)
        ax1.tick_params(colors='#d8dee9')
        for spine in ax1.spines.values():
            spine.set_edgecolor('#4c566a')

        # Accuracy Plot
        ax2.plot(train_accuracies, label='Train Accuracy', color='#a3be8c') # Green
        ax2.plot(val_accuracies, label='Validation Accuracy', color='#b48ead') # Purple
        ax2.set_xlabel('Epoch', color='#d8dee9')
        ax2.set_ylabel('Accuracy', color='#d8dee9')
        ax2.set_title('Accuracy over Epochs', color='white')
        ax2.legend()
        ax2.grid(True, linestyle='--', alpha=0.6)
        ax2.tick_params(colors='#d8dee9')
        for spine in ax2.spines.values():
            spine.set_edgecolor('#4c566a')

        plt.tight_layout(rect=[0, 0.03, 1, 0.95]) # Adjust layout to prevent title overlap
        plt.savefig(PLOT_FILENAME, facecolor='#2e3440')
        cprint(f"Training plot saved to {PLOT_FILENAME}", "magenta")
        plt.close(fig) # Close the figure to free memory

    except Exception as e:
        cprint(f"Error generating plot: {e}", "red")

def save_checkpoint(model, optimizer, epoch, loss, filename="checkpoint.pth"):
    """Saves model checkpoint."""
    if not os.path.exists(CHECKPOINT_DIR):
        try:
            os.makedirs(CHECKPOINT_DIR)
            cprint(f"Created checkpoint directory: {CHECKPOINT_DIR}", "yellow")
        except OSError as e:
            cprint(f"Error creating checkpoint directory {CHECKPOINT_DIR}: {e}", "red")
            return # Don't proceed if dir creation failed

    filepath = os.path.join(CHECKPOINT_DIR, filename)
    try:
        state = {
            'epoch': epoch,
            'state_dict': model.state_dict(),
            'optimizer': optimizer.state_dict(),
            'loss': loss,
        }
        torch.save(state, filepath)
        cprint(f"Checkpoint saved to {filepath}", "green")
    except Exception as e:
        cprint(f"Error saving checkpoint {filepath}: {e}", "red")

def save_best_model(model, filename=BEST_MODEL_FILENAME):
    """Saves the best performing model state dict directly."""
    if not os.path.exists(CHECKPOINT_DIR):
        try:
            os.makedirs(CHECKPOINT_DIR)
            cprint(f"Created checkpoint directory: {CHECKPOINT_DIR}", "yellow")
        except OSError as e:
            cprint(f"Error creating checkpoint directory {CHECKPOINT_DIR}: {e}", "red")
            return

    filepath = os.path.join(CHECKPOINT_DIR, filename)
    try:
        torch.save(model.state_dict(), filepath)
        cprint(f"Best model saved to {filepath}", "green", attrs=["bold"])
    except Exception as e:
        cprint(f"Error saving best model {filepath}: {e}", "red")

def load_checkpoint(model, optimizer, filename="checkpoint.pth"):
    """Loads model checkpoint."""
    filepath = os.path.join(CHECKPOINT_DIR, filename)
    if not os.path.exists(filepath):
        cprint(f"Checkpoint file not found: {filepath}", "yellow")
        return None
    try:
        checkpoint = torch.load(filepath)
        model.load_state_dict(checkpoint['state_dict'])
        if optimizer:
             optimizer.load_state_dict(checkpoint['optimizer'])
        cprint(f"Checkpoint loaded from {filepath}", "green")
        return checkpoint # Return epoch, loss etc.
    except FileNotFoundError:
        cprint(f"Checkpoint file not found at {filepath}. Starting from scratch.", "yellow")
        return None
    except Exception as e:
        cprint(f"Error loading checkpoint {filepath}: {e}", "red")
        return None

def load_model_weights(model, filename=BEST_MODEL_FILENAME):
    """Loads model weights from a state_dict file."""
    filepath = os.path.join(CHECKPOINT_DIR, filename)
    if not os.path.exists(filepath):
        cprint(f"Model weights file not found: {filepath}", "yellow")
        return False
    try:
        device = next(model.parameters()).device # Get device model is on
        state_dict = torch.load(filepath, map_location=device)
        model.load_state_dict(state_dict)
        cprint(f"Model weights loaded from {filepath}", "green")
        return True
    except FileNotFoundError:
        cprint(f"Model weights file not found at {filepath}.", "yellow")
        return False
    except Exception as e:
        cprint(f"Error loading model weights {filepath}: {e}", "red")
        return False

# Example usage (for testing plot)
if __name__ == '__main__':
    cprint("Testing utility functions...", "cyan")
    # Test plot
    train_l = [0.5, 0.4, 0.3, 0.25, 0.2]
    val_l = [0.6, 0.5, 0.45, 0.4, 0.38]
    train_a = [0.7, 0.75, 0.8, 0.82, 0.85]
    val_a = [0.65, 0.7, 0.72, 0.75, 0.78]
    plot_metrics(train_l, val_l, train_a, val_a, epoch=4)
    cprint(f"Plot should be saved as {PLOT_FILENAME}", "cyan")

    # Test saving/loading (requires a dummy model)
    class DummyModel(torch.nn.Module):
        def __init__(self):
            super().__init__()
            self.linear = torch.nn.Linear(10, 2)
        def forward(self, x): return self.linear(x)

    model = DummyModel()
    optimizer = torch.optim.Adam(model.parameters())

    cprint("\nTesting checkpoint save/load...", "cyan")
    save_checkpoint(model, optimizer, epoch=5, loss=0.123, filename="test_checkpoint.pth")
    loaded_checkpoint = load_checkpoint(model, optimizer, filename="test_checkpoint.pth")
    if loaded_checkpoint:
        cprint(f"Loaded checkpoint epoch: {loaded_checkpoint.get('epoch', 'N/A')}", "cyan")

    cprint("\nTesting best model save/load...", "cyan")
    save_best_model(model, filename="test_best_model.pth")
    model_loaded = DummyModel() # Create new instance
    load_model_weights(model_loaded, filename="test_best_model.pth")

    # Clean up test files
    try:
        if os.path.exists(os.path.join(CHECKPOINT_DIR, "test_checkpoint.pth")):
            os.remove(os.path.join(CHECKPOINT_DIR, "test_checkpoint.pth"))
        if os.path.exists(os.path.join(CHECKPOINT_DIR, "test_best_model.pth")):
            os.remove(os.path.join(CHECKPOINT_DIR, "test_best_model.pth"))
        # Only remove dir if it was created AND empty (or only contained test files)
        if os.path.exists(CHECKPOINT_DIR) and not os.listdir(CHECKPOINT_DIR):
            os.rmdir(CHECKPOINT_DIR)
        cprint("Cleaned up test files.", "yellow")
    except Exception as e:
        cprint(f"Error during cleanup: {e}", "red") 