import numpy as np
import pickle
import os
from tqdm import tqdm
from termcolor import cprint

from snake_game import <PERSON><PERSON><PERSON>
from expert_snake_player import ExpertSnakePlayer

# --- Configuration ---
NUM_GAMES = 1000
OUTPUT_FILENAME = "expert_snake_data.pkl"
# Map actions (dx, dy) to integer labels for classification
ACTION_MAP = {
    (0, -1): 0, # UP
    (0, 1): 1,  # DOWN
    (-1, 0): 2, # LEFT
    (1, 0): 3   # RIGHT
}
REVERSE_ACTION_MAP = {v: k for k, v in ACTION_MAP.items()}

def generate_data():
    cprint(f"Starting data generation for {NUM_GAMES} games...", "cyan")
    game = SnakeGame()
    # Instantiate expert with verbose=False
    expert = ExpertSnakePlayer(game, verbose=False)
    dataset = [] # List to store (state, action_label) pairs
    game_lengths = [] # Store lengths for summary

    try:
        # Use tqdm directly on the range, disable default bar if too noisy
        for i in tqdm(range(NUM_GAMES), desc="Generating Games", unit="game"):
            # Removed the per-game start print
            # cprint(f"\n--- Starting Game {i+1}/{NUM_GAMES} ---", "yellow")
            game.reset()
            state = game.get_state()
            game_steps = 0
            action_label = -1 # Initialize action_label

            while not game.game_over:
                # Get the expert's action for the current state
                action_coords = expert.get_action()

                # Get the integer label for this action
                if action_coords in ACTION_MAP:
                    action_label = ACTION_MAP[action_coords]
                else:
                    # Only print warning if unexpected action occurs
                    cprint(f"Warning: Expert returned unexpected action {action_coords} in game {i+1}, skipping step data.", "red")
                    # We still need to step the game, maybe use current direction?
                    # For simplicity, let's just use the potentially bad action_coords
                    # No data is recorded for this step though.
                    action_label = -1 # Indicate invalid action for recording
                    # pass # Fall through to game.step with the raw action_coords

                # Record the state and the chosen action label *before* stepping
                if action_label != -1: # Only record if action was valid
                    current_state_flattened = state.flatten()
                    dataset.append((current_state_flattened, action_label))
                else:
                    # Reset action label if it was invalid
                    action_label = -1

                # Take the step in the game
                next_state, reward, done = game.step(action_coords)
                state = next_state # Update state for the next iteration
                game_steps += 1

                # Optional: Add a maximum step limit per game
                # ... (limit logic if needed)

            # Store final length and print summary for the completed game
            final_length = len(game.snake)
            game_lengths.append(final_length)
            # Use a less verbose print, perhaps update tqdm description or use simple print
            # tqdm.write(f"Game {i+1}/{NUM_GAMES} finished. Final Length: {final_length}")
            # Or just print directly:
            cprint(f"Game {i+1}/{NUM_GAMES} finished. Final Length: {final_length}", "blue")

    except KeyboardInterrupt:
        cprint("\nData generation interrupted by user.", "red")
    except Exception as e:
        cprint(f"\nAn error occurred during data generation: {e}", "red", attrs=["bold"])
        import traceback
        traceback.print_exc()
    finally:
        # --- Summary Statistics ---
        if game_lengths:
            avg_len = np.mean(game_lengths)
            max_len = np.max(game_lengths)
            min_len = np.min(game_lengths)
            cprint(f"\n--- Data Generation Summary ---", "yellow")
            cprint(f"Games Played: {len(game_lengths)}", "yellow")
            cprint(f"Average Snake Length: {avg_len:.2f}", "yellow")
            cprint(f"Max Snake Length: {max_len}", "yellow")
            cprint(f"Min Snake Length: {min_len}", "yellow")

        # Save the collected data
        if dataset:
            cprint(f"Collected {len(dataset)} state-action pairs.", "green")
            # Use try-with-resources for file saving
            try:
                with open(OUTPUT_FILENAME, 'wb') as f:
                    pickle.dump(dataset, f)
                cprint(f"Dataset saved successfully to {OUTPUT_FILENAME}", "green")
            except IOError as e:
                cprint(f"Error saving dataset to {OUTPUT_FILENAME}: {e}", "red")
        else:
            cprint("No data collected to save.", "yellow")

if __name__ == "__main__":
    # Ensure the output directory exists if specified
    # if '/' in OUTPUT_FILENAME:
    #     os.makedirs(os.path.dirname(OUTPUT_FILENAME), exist_ok=True)
    generate_data() 