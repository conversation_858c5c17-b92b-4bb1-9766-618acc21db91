import torch
import pygame
import time
import numpy as np
from termcolor import cprint

from snake_game import <PERSON><PERSON><PERSON>
from transformer_model import <PERSON><PERSON><PERSON>sformer # Make sure model hyperparams are consistent
from utils import load_model_weights, BEST_MODEL_FILENAME
from config import GRID_SIZE # Used for flattening

# --- Configuration ---
MODEL_WEIGHTS_PATH = BEST_MODEL_FILENAME # From utils.py
DEVICE = "cuda" if torch.cuda.is_available() else "cpu"
FRAME_DELAY = 0.1 # Seconds between frames (adjust for speed)

# Action mapping (ensure this matches generate_data.py)
ACTION_MAP = {
    (0, -1): 0, # UP
    (0, 1): 1,  # DOWN
    (-1, 0): 2, # LEFT
    (1, 0): 3   # RIGHT
}
REVERSE_ACTION_MAP = {v: k for k, v in ACTION_MAP.items()}

def play_game(model_weights_path):
    cprint(f"Loading model from: {model_weights_path}", "yellow")
    cprint(f"Using device: {DEVICE}", "cyan")

    # 1. Initialize Model
    try:
        model = SnakeTransformer().to(DEVICE)
        if not load_model_weights(model, filename=model_weights_path):
            cprint(f"Error: Could not load model weights from {model_weights_path}.", "red", attrs=["bold"])
            cprint("Ensure the model was trained and the file exists in the checkpoints directory.", "red")
            return
        model.eval() # Set model to evaluation mode
        cprint("Model loaded successfully.", "green")
    except Exception as e:
        cprint(f"Error initializing or loading the model: {e}", "red", attrs=["bold"])
        return

    # 2. Initialize Game and Pygame
    game = SnakeGame()
    screen = None
    running = True
    clock = pygame.time.Clock() # Alternative way to control speed

    try:
        screen = game.render() # Initialize Pygame window
        if screen is None:
            raise Exception("Pygame initialization failed.")

        cprint("Starting game loop. Press Q or close window to exit.", "cyan")
        while running:
            # Handle Pygame events
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    running = False
                if event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_q:
                         running = False

            if not running: break

            # --- Model Decision Making ---
            # Get current state
            state_grid = game.get_state()

            # Prepare state for model
            state_flat = state_grid.flatten()
            # Ensure input is LongTensor for embedding layer
            state_tensor = torch.tensor(state_flat, dtype=torch.long).unsqueeze(0).to(DEVICE)

            # Get action prediction
            with torch.no_grad():
                output_logits = model(state_tensor)
                # Get the action with the highest probability
                predicted_action_idx = torch.argmax(output_logits, dim=1).item()

            # Map action index back to coordinates
            if predicted_action_idx in REVERSE_ACTION_MAP:
                 action_coords = REVERSE_ACTION_MAP[predicted_action_idx]
            else:
                 cprint(f"Warning: Model predicted invalid action index {predicted_action_idx}. Using current direction.", "red")
                 action_coords = game.direction # Fallback

            # --- Step Game --- 
            next_state, reward, done = game.step(action_coords)

            # --- Render --- 
            screen = game.render(screen)

            # --- Handle Game Over --- 
            if done:
                cprint(f"Game Over! Final Score: {game.score}, Final Length: {len(game.snake)}", "blue")
                time.sleep(2) # Pause before resetting
                cprint("Resetting game...", "yellow")
                game.reset()
                screen = game.render(screen) # Render the reset state

            # --- Control Speed --- 
            # time.sleep(FRAME_DELAY)
            clock.tick(1.0 / FRAME_DELAY if FRAME_DELAY > 0 else 60) # Use clock tick

    except KeyboardInterrupt:
        cprint("\nGame interrupted by user.", "red")
        running = False
    except Exception as e:
        cprint(f"\nAn error occurred during the game loop: {e}", "red", attrs=["bold"])
        import traceback
        traceback.print_exc()
    finally:
        cprint("Quitting Pygame...", "yellow")
        pygame.quit()

if __name__ == "__main__":
    # Make sure to use the correct path relative to the checkpoints directory
    play_game(BEST_MODEL_FILENAME) 