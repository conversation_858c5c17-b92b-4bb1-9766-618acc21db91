# main.py
import os
from fastapi import FastAPI, Request, Form, HTTPException
from fastapi.responses import H<PERSON>LR<PERSON>ponse
# from fastapi.staticfiles import StaticFiles # Keep commented if not used
from fastapi.templating import Jinja2Templates
# Use instructor which simplifies structured output with Pydantic
# Note: Requires 'instructor' library: pip install instructor
import instructor
from openai import OpenAI, AsyncOpenAI
from pydantic import BaseModel, <PERSON>
from typing import List, <PERSON>ple
import json
from dotenv import load_dotenv
from termcolor import cprint
import asyncio
import random

# Load environment variables
load_dotenv()

# --- Configuration ---
API_KEY = os.getenv("OPENAI_API_KEY")
API_BASE = os.getenv("OPENAI_API_BASE", None)
MODEL_NAME = os.getenv("MODEL_NAME", "o4-mini")

if not API_KEY:
    cprint("Error: OPENAI_API_KEY not found in environment variables.", "red")
    exit(1)

# --- Pydantic Model for Structured Output ---
class IdeaList(BaseModel):
    """Represents a list of generated ideas."""
    ideas: List[str] = Field(..., description="A list of 100 distinct and useful ideas related to the user's topic.")

# --- Pydantic Model for Combined Idea ---
class NewOriginalIdea(BaseModel):
    """Represents a single, novel, original idea generated from two source ideas."""
    new_idea: str = Field(..., description="A completely novel and original idea inspired by the two provided source ideas, not just a simple combination.")

# --- FastAPI Setup ---
app = FastAPI()
templates = Jinja2Templates(directory="templates")

# --- OpenAI Client Setup ---
# Patch the client with instructor for structured output
# Use AsyncOpenAI for FastAPI's async context
aclient = instructor.patch(AsyncOpenAI(
    api_key=API_KEY,
    base_url=API_BASE,
))

cprint("FastAPI application starting...", "green")

# --- Routes ---
@app.get("/", response_class=HTMLResponse)
async def read_root(request: Request):
    """Serves the main HTML page."""
    cprint("Serving index page.", "cyan")
    # Pass initial default for num_ideas if needed
    return templates.TemplateResponse("index.html", {"request": request, "ideas": None, "topic": None, "error": None, "num_ideas_generated": None})

@app.post("/generate", response_class=HTMLResponse)
async def generate_ideas(request: Request, topic: str = Form(...), num_ideas: int = Form(50)): # Added num_ideas parameter
    """Handles form submission, calls AI model using structured output, and returns results."""
    cprint(f"Received topic: {topic}, requesting {num_ideas} ideas", "cyan")

    # Update Pydantic model description dynamically (optional but helpful for LLM)
    class DynamicIdeaList(BaseModel):
        """Represents a list of generated ideas."""
        ideas: List[str] = Field(..., description=f"A list of {num_ideas} distinct and useful ideas related to the user's topic: '{topic}'.")
    
    # Patch the client with the dynamic model for this request
    dynamic_aclient = instructor.patch(AsyncOpenAI(
        api_key=API_KEY,
        base_url=API_BASE,
    ), mode=instructor.Mode.MD_JSON) # Use MD_JSON mode for dynamic models if needed

    # Prompt updated to request specific number of ideas
    prompt = f"Generate {num_ideas} distinct and useful ideas related to the topic: \"{topic}\"."

    ideas_list = []
    error_message = None

    try:
        cprint(f"Calling model '{MODEL_NAME}' for {num_ideas} ideas...", "yellow")
        # Use the dynamic client and model
        response_model: DynamicIdeaList = await dynamic_aclient.chat.completions.create(
            model=MODEL_NAME,
            response_model=DynamicIdeaList, # Use the dynamic Pydantic model
            messages=[
                {"role": "system", "content": "You are an expert idea generation assistant."},
                {"role": "user", "content": prompt}
            ],
        )
        cprint("Received and parsed structured response from model.", "green")

        if response_model and response_model.ideas:
            ideas_list = response_model.ideas
            # Trim if model gives more than requested (sometimes happens)
            if len(ideas_list) > num_ideas:
                cprint(f"Model generated {len(ideas_list)} ideas, trimming to requested {num_ideas}.", "blue")
                ideas_list = ideas_list[:num_ideas]
            cprint(f"Successfully processed {len(ideas_list)} ideas.", "green")
        else:
            cprint("Model returned a response, but it did not contain ideas or failed Pydantic validation.", "red")
            error_message = "The AI model returned an unexpected structure. Please try again."

    except Exception as e:
        cprint(f"Error during generation or parsing: {e}", "red")
        error_message = f"An error occurred: {e}"

    # Render the template, passing back the actual number generated
    return templates.TemplateResponse("index.html", {
        "request": request,
        "ideas": ideas_list,
        "error": error_message,
        "topic": topic,
        "num_ideas_generated": len(ideas_list) if ideas_list else 0 # Pass actual count
    })

@app.post("/combine", response_model=NewOriginalIdea)
async def combine_ideas(ideas: Tuple[str, str]):
    """
    Takes two ideas and generates a novel, original idea inspired by them.
    """
    idea1, idea2 = ideas
    cprint(f"Received ideas to combine: '{idea1}' AND '{idea2}'...", "magenta")

    # Carefully crafted prompt emphasizing originality and inspiration
    prompt = f"""
    You are an exceptionally creative brainstorming assistant.
    Your task is to generate a *completely novel and original* idea inspired by the following two concepts, but *not* a simple merge or average of them.
    Think outside the box, find tangential connections, or use them as a springboard for something entirely new.

    Source Idea 1: "{idea1}"
    Source Idea 2: "{idea2}"

    Generate one single, compelling, and original new idea based on the inspiration from these two.
    **The generated idea must be concise, ideally under 20 words.** 
    """

    try:
        cprint(f"Calling model '{MODEL_NAME}' for combination...", "yellow")
        # Use the patched client and specify the response_model
        response: NewOriginalIdea = await aclient.chat.completions.create(
            model=MODEL_NAME,
            response_model=NewOriginalIdea, # Expecting the new idea structure
            messages=[
                {"role": "system", "content": "You are an expert creative synthesizer focused on concise outputs."},
                {"role": "user", "content": prompt}
            ],
        )
        cprint(f"Generated novel idea: {response.new_idea}", "green")
        return response

    except Exception as e:
        cprint(f"Error during idea combination: {e}", "red")
        # Re-raise or handle as needed, maybe return an error response
        # For now, let's return a placeholder or re-raise
        # Consider FastAPI HTTPExceptions for better error handling
        raise HTTPException(status_code=500, detail=f"Failed to generate combined idea: {e}")

if __name__ == "__main__":
    import uvicorn
    cprint("Running with uvicorn (main:app)... Access at http://0.0.0.0:8520", "yellow")
    uvicorn.run("main:app", host="0.0.0.0", port=8520, reload=True) 