# Ideation Machine

A beautiful, modern web app for rapid idea generation, combination, and curation using AI (OpenAI-compatible API). Built with FastAPI, TailwindCSS, DaisyUI, and Anime.js.

---

## Features

- **Generate Ideas:** Enter a topic and the number of ideas you want (10–100). The app uses an AI model to generate a list of creative, useful ideas.
- **Quick Search:** Instantly filter ideas as you type.
- **Iterative Combination:** Combine two random ideas (as many times as you want) to generate new, original ideas. Each new idea is inspired by two source ideas, not just a simple merge.
- **Traceability:** Hover the info icon on a new idea to see which two ideas inspired it.
- **Save Favorites:** Click the heart checkbox on any idea to save it to your personal, collapsible left panel. Saved ideas persist in your browser (localStorage).
- **Modern UI:** Pastel/dark slate theme, glassmorphism, beautiful animations, and responsive design.

---

## Setup & Installation

1. **Clone the repository:**
   ```bash
   git clone <your-repo-url>
   cd ideation-machine
   ```

2. **Install Python dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up environment variables:**
   - Copy `.env.example` to `.env` and fill in your OpenAI-compatible API key and (optionally) base URL.

4. **Run the app:**
   ```bash
   uvicorn main:app --reload
   ```
   - Visit [http://localhost:8000](http://localhost:8000) in your browser.

---

## Usage Guide

### 1. Generate Ideas
- Enter a topic (e.g., "Sustainable energy solutions").
- Choose how many ideas to generate (10–100).
- Click **Generate Initial Ideas**.
- Ideas will appear in a beautiful grid, each with a save checkbox and (for new ideas) an info icon.

### 2. Search & Save
- Use the search box to filter ideas instantly.
- Click the heart checkbox on any idea to save it to your left panel.
- Open the left panel with the ☰ button (top left) to view/remove saved ideas. Saved ideas persist across reloads.

### 3. Combine & Ideate
- Set the number of iterations (how many new ideas to generate).
- Click **Combine & Ideate**. The app will:
  - Randomly select two ideas from the current grid.
  - Ask the AI to generate a new, original idea inspired by those two (max 20 words).
  - Add each new idea to the top of the grid, with a distinct blue dashed border and info icon showing its sources.
  - You can save these new ideas as well!
- You can repeat this process as many times as you like. Newly generated ideas are included in future combinations.

### 4. Trace Idea Origins
- Hover the info icon ("i") on any new idea to see which two ideas inspired it. The tooltip will show both source ideas, even for long text.

---

## Tech Stack
- **Backend:** FastAPI, Python, OpenAI-compatible API (e.g., OpenAI, OctoAI)
- **Frontend:** Jinja2 templates, TailwindCSS, DaisyUI, Anime.js
- **Persistence:** Browser localStorage (for saved ideas)
- **Other:** Termcolor (for colored terminal logs), Pydantic (for structured outputs)

---

## Customization
- **Model:** Set your model name and API key in `.env`.
- **Styling:** Tweak colors, gradients, and card styles in `templates/index.html`.
- **API:** Works with any OpenAI-compatible API that supports structured outputs.

---

## Troubleshooting
- If you see errors about missing API keys, check your `.env` file.
- If the UI looks off, try a hard refresh (Ctrl+Shift+R).
- For localStorage issues, clear your browser cache.

---

## License
MIT (or your preferred license)

---

## Credits
- Built with ❤️ using FastAPI, TailwindCSS, DaisyUI, and OpenAI-compatible models. 