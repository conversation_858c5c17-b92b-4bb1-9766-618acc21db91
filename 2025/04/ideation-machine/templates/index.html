<!DOCTYPE html>
<html lang="en" data-theme="fantasy">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ideation Machine</title>
    <!-- Tailwind CSS & DaisyUI -->
    <link href="https://cdn.jsdelivr.net/npm/daisyui@4.11.1/dist/full.min.css" rel="stylesheet" type="text/css" />
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Anime.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js"></script>
    <style>
        /* Custom slate purple gradient background */
        body {
            background: linear-gradient(135deg, #50486b, #473d5f, #3a334e);
            background-attachment: fixed;
            min-height: 100vh;
            color: #f0edf8;
        }
        
        /* Main container with glass effect */
        .glass-container {
            background: rgba(255, 255, 255, 0.13);
            backdrop-filter: blur(12px);
            border-radius: 1rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 20, 0.2);
        }
        
        /* Define pastel background colors with good text contrast */
        .pastel-bg-1 { background-color: rgba(253, 230, 243, 0.85); color: #3d3448; }
        .pastel-bg-2 { background-color: rgba(230, 245, 253, 0.85); color: #3d3448; }
        .pastel-bg-3 { background-color: rgba(230, 253, 238, 0.85); color: #3d3448; }
        .pastel-bg-4 { background-color: rgba(255, 251, 235, 0.85); color: #3d3448; }
        .pastel-bg-5 { background-color: rgba(245, 230, 253, 0.85); color: #3d3448; }
        .pastel-bg-6 { background-color: rgba(255, 235, 230, 0.85); color: #3d3448; }

        /* Custom transition for smoother appearance */
        .idea-card {
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.3s ease;
            border-radius: 0.75rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: visible;
            border: 1px solid rgba(255, 255, 255, 0.5);
        }
        
        .idea-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px rgba(0, 0, 0, 0.15);
        }
        
        /* Title text with gradient */
        .gradient-text {
            background: linear-gradient(45deg, #d3c0ff, #f0e6ff);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
        }
        
        /* Custom button styling */
        .custom-btn {
            background: linear-gradient(45deg, #a899df, #c4b8f0);
            border: none;
            color: #2d2841;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            font-weight: 600;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        
        .custom-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 10px rgba(0, 0, 0, 0.15);
            filter: brightness(1.05);
        }
        
        .custom-btn:disabled {
            opacity: 0.7;
            cursor: not-allowed;
        }
        
        /* Pulse animation for loading */
        @keyframes pulse {
            0% { opacity: 0.6; }
            50% { opacity: 1; }
            100% { opacity: 0.6; }
        }
        
        .loading-pulse {
            animation: pulse 1.5s infinite ease-in-out;
        }

        /* Custom text for dark background */
        .light-text {
            color: rgba(255, 255, 255, 0.85);
        }
        
        /* Style for newly generated idea cards */
        .new-idea-card {
            /* Thicker, dashed, dark blue border */
            border: 3px dashed #2563eb; /* Dark Blue (Tailwind blue-600) */
            box-shadow: 0 4px 8px rgba(37, 99, 235, 0.2); /* Shadow matching blue */
            animation: fadeInGlowBlue 1.5s ease-out; /* Renamed animation */
            position: relative; /* For positioning the info icon */
        }

        /* Info icon styling */
        .info-icon {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background-color: rgba(37, 99, 235, 0.8);
            color: white;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: help;
            transition: all 0.2s ease;
        }
        
        .info-icon:hover {
            background-color: rgba(37, 99, 235, 1);
            transform: scale(1.1);
        }
        
        /* Tooltip styling improvements */
        .info-tooltip {
            position: absolute;
            bottom: 38px;
            right: 0;
            width: 220px;
            padding: 12px 14px;
            background-color: rgba(15, 23, 42, 0.97);
            color: white;
            border-radius: 8px;
            font-size: 13px;
            line-height: 1.5;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s cubic-bezier(.4,2,.6,1);
            transform: translateY(10px);
            z-index: 9999;
            pointer-events: none;
            box-shadow: 0 6px 24px rgba(30,41,59,0.18);
            white-space: normal;
            word-break: break-word;
            display: flex;
            flex-direction: column;
            gap: 6px;
        }
        .info-icon:hover + .info-tooltip {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }
        .info-tooltip::after {
            content: '';
            position: absolute;
            left: 85%;
            top: 100%;
            margin-left: -8px;
            border-width: 8px;
            border-style: solid;
            border-color: rgba(15, 23, 42, 0.97) transparent transparent transparent;
        }
        .source-idea {
            padding: 6px 8px;
            background-color: rgba(255, 255, 255, 0.10);
            border-radius: 4px;
            display: block;
            overflow-wrap: break-word;
            font-size: 12px;
            margin-bottom: 0;
        }

        /* Updated glow animation with blue */
        @keyframes fadeInGlowBlue {
            from {
                opacity: 0;
                box-shadow: 0 0 5px rgba(37, 99, 235, 0.1);
            }
            to {
                opacity: 1;
                box-shadow: 0 4px 8px rgba(37, 99, 235, 0.2);
            }
        }

        /* Style for the ideation controls section */
        .ideation-controls {
            border: 1px solid rgba(255, 255, 255, 0.2);
            background: rgba(255, 255, 255, 0.08);
            padding: 1rem;
            border-radius: 0.75rem;
            margin-top: 1rem;
            margin-bottom: 1.5rem;
        }

        /* Collapsible left panel styles */
        .left-panel {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: 320px;
            max-width: 90vw;
            background: rgba(30, 41, 59, 0.97);
            color: #fff;
            box-shadow: 2px 0 16px rgba(30,41,59,0.15);
            z-index: 100;
            transform: translateX(-100%);
            transition: transform 0.3s cubic-bezier(.4,2,.6,1);
            display: flex;
            flex-direction: column;
        }
        .left-panel.open {
            transform: translateX(0);
        }
        .left-panel-header {
            padding: 1.2rem 1.5rem 1rem 1.5rem;
            font-size: 1.3rem;
            font-weight: 700;
            letter-spacing: 0.02em;
            border-bottom: 1px solid rgba(255,255,255,0.08);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .left-panel-close {
            background: none;
            border: none;
            color: #fff;
            font-size: 1.5rem;
            cursor: pointer;
            opacity: 0.7;
            transition: opacity 0.2s;
        }
        .left-panel-close:hover {
            opacity: 1;
        }
        .saved-ideas-list {
            flex: 1;
            overflow-y: auto;
            padding: 1rem 1.5rem;
        }
        .saved-idea {
            background: rgba(255,255,255,0.08);
            border-radius: 0.5rem;
            margin-bottom: 1rem;
            padding: 0.75rem 1rem;
            font-size: 1rem;
            position: relative;
            box-shadow: 0 2px 8px rgba(30,41,59,0.08);
            display: flex;
            align-items: flex-start;
            gap: 0.5rem;
        }
        .saved-idea-remove {
            background: none;
            border: none;
            color: #f87171;
            font-size: 1.2rem;
            cursor: pointer;
            margin-left: auto;
            opacity: 0.7;
            transition: opacity 0.2s;
        }
        .saved-idea-remove:hover {
            opacity: 1;
        }
        .left-panel-empty {
            color: #cbd5e1;
            text-align: center;
            margin-top: 2rem;
            font-size: 1rem;
        }
        .left-panel-toggle {
            position: fixed;
            top: 1.5rem;
            left: 1.5rem;
            z-index: 110;
            background: linear-gradient(45deg, #a899df, #2563eb);
            color: #fff;
            border: none;
            border-radius: 50%;
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.7rem;
            box-shadow: 0 2px 8px rgba(30,41,59,0.12);
            cursor: pointer;
            transition: background 0.2s;
        }
        .left-panel-toggle:hover {
            background: linear-gradient(45deg, #2563eb, #a899df);
        }
        @media (max-width: 600px) {
            .left-panel { width: 95vw; }
            .left-panel-toggle { left: 0.5rem; top: 0.5rem; }
        }

        /* Beautiful checkbox for idea cards */
        .save-checkbox {
            appearance: none;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            border: 2px solid #2563eb;
            background: #fff;
            margin-right: 0.5rem;
            cursor: pointer;
            position: absolute;
            top: 12px;
            left: 12px;
            z-index: 2;
            transition: border-color 0.2s, box-shadow 0.2s;
            box-shadow: 0 2px 6px rgba(37,99,235,0.08);
        }
        .save-checkbox:checked {
            background: #2563eb;
            border-color: #2563eb;
        }
        .save-checkbox:checked::after {
            content: '\2764'; /* Unicode heart */
            color: #fff;
            font-size: 1.1rem;
            position: absolute;
            left: 3px;
            top: 1px;
        }
        .save-checkbox::after {
            content: '';
            position: absolute;
        }
        .idea-card { position: relative; overflow: visible; }
    </style>
</head>
<body class="p-4 md:p-8">
    <!-- Collapsible Left Panel -->
    <button class="left-panel-toggle" id="leftPanelToggle" title="Show Saved Ideas">
        <span>&#9776;</span>
    </button>
    <aside class="left-panel" id="leftPanel">
        <div class="left-panel-header">
            Saved Ideas
            <button class="left-panel-close" id="leftPanelClose" title="Close">&times;</button>
        </div>
        <div class="saved-ideas-list" id="savedIdeasList">
            <!-- Saved ideas will be rendered here -->
        </div>
    </aside>
    <div class="container mx-auto max-w-4xl glass-container p-6 md:p-10">
        <header class="text-center mb-10">
            <h1 class="text-3xl md:text-5xl font-bold mb-3 gradient-text">
                ✨ Ideation Machine
            </h1>
            <p class="light-text italic">Generate unique ideas on any topic</p>
        </header>

        <!-- Input Form -->
        <form action="/generate" method="post" class="mb-10">
            <div class="flex flex-wrap items-end gap-4 mb-6">
                <div class="flex-grow">
                    <label class="label" for="topic-input">
                        <span class="label-text text-lg font-medium light-text">What would you like ideas about?</span>
                    </label>
                    <input type="text" id="topic-input" name="topic" 
                           placeholder="e.g., Sustainable energy solutions, Sci-fi story concepts..."
                           class="input p-4 bg-white bg-opacity-20 border border-indigo-200 border-opacity-30 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-300 w-full text-white placeholder-indigo-200 placeholder-opacity-60"
                           required
                           value="{{ topic if topic else '' }}">
                </div>
                <div class="flex-shrink-0">
                    <label class="label" for="num-ideas-input">
                        <span class="label-text text-sm font-medium light-text"># Ideas:</span>
                    </label>
                    <input type="number" id="num-ideas-input" name="num_ideas" value="50" min="10" max="100" step="10"
                           class="input p-4 w-24 bg-white bg-opacity-20 border border-indigo-200 border-opacity-30 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-300 text-white text-center">
                </div>
            </div>
            <button type="submit" class="custom-btn w-full flex justify-center items-center space-x-2">
                <span>Generate Initial Ideas</span>
                <span class="loading loading-dots loading-sm hidden" id="loading-spinner"></span>
            </button>
        </form>

        <!-- Results Area -->
        <div id="results-area">
            {% if error %}
            <div role="alert" class="alert bg-opacity-20 bg-red-100 border-l-4 border-red-400 text-red-100 p-4 mb-6">
                <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                <span>{{ error }}</span>
            </div>
            {% endif %}

            {% if ideas %}
            <div class="flex flex-col sm:flex-row justify-between items-center mb-4">
                <h2 class="text-2xl font-semibold gradient-text inline-block mb-3 sm:mb-0">
                   <span id="idea-count">{{ ideas|length }}</span> Ideas for "{{ topic }}"
                </h2>
                <!-- Quick Search Input -->
                <div class="w-full sm:w-auto">
                    <input type="text" id="quick-search" placeholder="🔎 Filter ideas..." 
                           class="input input-sm p-3 bg-white bg-opacity-20 border border-indigo-200 border-opacity-30 rounded-lg focus:outline-none focus:ring-1 focus:ring-indigo-300 w-full sm:w-64 text-sm text-white placeholder-indigo-200 placeholder-opacity-60">
                </div>
            </div>
            
            <!-- Ideation Controls -->
            <div class="ideation-controls flex flex-wrap items-center gap-4">
                <div class="flex items-center gap-2">
                    <label for="iterations-input" class="light-text text-sm whitespace-nowrap">Iterations:</label>
                    <input type="number" id="iterations-input" value="3" min="1" max="10"
                        class="input input-sm p-2 bg-white bg-opacity-20 border border-indigo-200 border-opacity-30 rounded-lg focus:outline-none focus:ring-1 focus:ring-indigo-300 text-white w-16 text-center">
                </div>
                <button id="combine-ideas-btn" class="custom-btn py-2 px-4 text-sm flex items-center gap-2">
                    <span>✨ Combine & Ideate</span>
                    <span class="loading loading-dots loading-xs hidden" id="combine-spinner"></span>
                </button>
                <p id="combine-status" class="text-xs light-text italic w-full mt-2"></p>
            </div>
            
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-5" id="ideas-grid">
                {% set num_colors = 6 %}
                {% for idea in ideas %}
                {% set color_index = (loop.index0 % num_colors) + 1 %}
                <div class="idea-card pastel-bg-{{ color_index }}" data-idea="{{ idea|e }}">
                    <input type="checkbox" class="save-checkbox" title="Save idea">
                    <div class="p-4">
                        <p class="text-sm md:text-base idea-text">{{ idea }}</p>
                    </div>
                </div>
                {% endfor %}
            </div>
            <p id="no-results-message" class="text-center light-text mt-6 hidden">No ideas match your filter.</p>
            {% elif request.method == 'POST' and not error %}
            <div role="alert" class="alert bg-opacity-20 bg-amber-100 border-l-4 border-amber-400 text-amber-100 p-4">
                <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" /></svg>
                <span>No ideas were returned. The model might need a clearer topic or didn't provide the expected output format.</span>
            </div>
            {% endif %}
        </div>
    </div>

    <footer class="text-center mt-8 mb-4 text-indigo-200 text-sm">
        <p>Made with ❤️ and AI</p>
    </footer>

    <script>
        const form = document.querySelector('form');
        const spinner = document.getElementById('loading-spinner');
        const searchInput = document.getElementById('quick-search');
        const ideasGrid = document.getElementById('ideas-grid');
        const ideaCards = ideasGrid ? ideasGrid.querySelectorAll('.idea-card') : [];
        const ideaCountSpan = document.getElementById('idea-count');
        const noResultsMessage = document.getElementById('no-results-message');
        
        // New Combine controls
        const combineBtn = document.getElementById('combine-ideas-btn');
        const iterationsInput = document.getElementById('iterations-input');
        const combineSpinner = document.getElementById('combine-spinner');
        const combineStatus = document.getElementById('combine-status');
        let currentIdeas = []; // Array to hold idea text

        // Form Submission Logic
        form.addEventListener('submit', () => {
            spinner.classList.remove('hidden');
            spinner.classList.add('loading-pulse');
            const button = form.querySelector('button[type="submit"]');
            button.disabled = true;
            button.classList.add('opacity-70');
            button.innerHTML = '<span>Generating ideas...</span> <span class="loading loading-dots loading-md"></span>';
        });

        // Quick Search Filter Logic
        if (searchInput && ideaCards.length > 0) {
            searchInput.addEventListener('input', () => {
                const searchTerm = searchInput.value.toLowerCase().trim();
                let visibleCount = 0;

                ideaCards.forEach(card => {
                    const ideaTextElement = card.querySelector('.idea-text');
                    const ideaText = ideaTextElement ? ideaTextElement.textContent.toLowerCase() : '' ;
                    
                    if (ideaText.includes(searchTerm)) {
                        card.style.display = ''; // Show card
                        visibleCount++;
                    } else {
                        card.style.display = 'none'; // Hide card
                    }
                });

                // Update visible count
                if (ideaCountSpan) {
                    ideaCountSpan.textContent = visibleCount;
                }
                
                // Show/hide 'no results' message
                if (noResultsMessage) {
                    noResultsMessage.style.display = visibleCount === 0 ? 'block' : 'none';
                }
            });
        }
        
        // --- Helper Functions for Combining Ideas ---
        function updateCurrentIdeas() {
            currentIdeas = [];
            const cards = ideasGrid.querySelectorAll('.idea-card');
            cards.forEach(card => {
                // Get idea from data attribute if possible, otherwise from text
                const ideaText = card.dataset.idea || card.querySelector('.idea-text')?.textContent || '';
                if (ideaText) {
                    currentIdeas.push(ideaText.trim());
                }
            });
            console.log("Updated currentIdeas count:", currentIdeas.length);
        }

        function createIdeaCard(ideaText, isNew = false, sourceIdea1 = null, sourceIdea2 = null) {
            const numColors = 6;
            // Assign color based on total number of cards for variety
            const totalCards = ideasGrid.children.length;
            const colorIndex = (totalCards % numColors) + 1;

            const card = document.createElement('div');
            card.classList.add('idea-card', `pastel-bg-${colorIndex}`);
            if (isNew) {
                card.classList.add('new-idea-card'); // Add distinction
                // Only add info icon and tooltip if source ideas are available
                if (sourceIdea1 && sourceIdea2) {
                    card.dataset.sourceIdea1 = sourceIdea1;
                    card.dataset.sourceIdea2 = sourceIdea2;
                    // Create info icon
                    const infoIcon = document.createElement('div');
                    infoIcon.classList.add('info-icon');
                    infoIcon.innerText = 'i';
                    // Create tooltip
                    const tooltip = document.createElement('div');
                    tooltip.classList.add('info-tooltip');
                    tooltip.innerHTML = `
                        <strong>Generated from:</strong>
                        <span class="source-idea">${sourceIdea1}</span>
                        <span class="source-idea">${sourceIdea2}</span>
                    `;
                    card.appendChild(infoIcon);
                    card.appendChild(tooltip);
                }
            }
            card.dataset.idea = ideaText; // Store idea text in data attribute

            // --- Add the save checkbox ---
            const checkbox = document.createElement('input');
            checkbox.type = 'checkbox';
            checkbox.className = 'save-checkbox';
            checkbox.title = 'Save idea';
            card.appendChild(checkbox);

            const cardBody = document.createElement('div');
            cardBody.classList.add('p-4');

            const paragraph = document.createElement('p');
            paragraph.classList.add('text-sm', 'md:text-base', 'idea-text');
            paragraph.textContent = ideaText;

            cardBody.appendChild(paragraph);
            card.appendChild(cardBody);
            return card;
        }

        async function combineAndGenerate() {
            if (!combineBtn || !iterationsInput || !ideasGrid) return;
            
            // First update the current ideas array from the DOM
            updateCurrentIdeas();
            
            if (currentIdeas.length < 2) {
                combineStatus.textContent = "Need at least 2 ideas to combine.";
                return;
            }

            const iterations = parseInt(iterationsInput.value, 10);
            if (isNaN(iterations) || iterations < 1) {
                combineStatus.textContent = "Please enter a valid number of iterations.";
                return;
            }

            // Disable button and show spinner
            combineBtn.disabled = true;
            combineBtn.classList.add('opacity-70');
            combineSpinner.classList.remove('hidden');
            combineStatus.textContent = `Starting ${iterations} iterations...`;

            let successfulIterations = 0;

            for (let i = 0; i < iterations; i++) {
                combineStatus.textContent = `Generating idea ${i + 1} of ${iterations}...`;

                // Ensure we have enough unique ideas if possible
                if (currentIdeas.length < 2) {
                    combineStatus.textContent = `Not enough ideas left for iteration ${i + 1}.`;
                    break;
                }

                // Select two different random ideas
                let index1 = Math.floor(Math.random() * currentIdeas.length);
                let index2 = Math.floor(Math.random() * currentIdeas.length);
                // Ensure indices are different
                while (index1 === index2) {
                    index2 = Math.floor(Math.random() * currentIdeas.length);
                }

                const idea1 = currentIdeas[index1];
                const idea2 = currentIdeas[index2];

                try {
                    const response = await fetch('/combine', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/json'
                        },
                        body: JSON.stringify([idea1, idea2]) // Send as JSON array/tuple
                    });

                    if (!response.ok) {
                        const errorData = await response.json();
                        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
                    }

                    const result = await response.json(); // { new_idea: "..." }

                    if (result.new_idea) {
                        const newIdeaText = result.new_idea;
                        // Add to the start of the JS array
                        currentIdeas.unshift(newIdeaText);
                        // Create and prepend the new card element with source ideas
                        const newCard = createIdeaCard(newIdeaText, true, idea1, idea2); // Pass source ideas
                        ideasGrid.prepend(newCard);
                        // Animate the new card
                        anime({
                            targets: newCard,
                            opacity: [0, 1],
                            translateY: [-20, 0], // Animate from top
                            duration: 600,
                            easing: 'easeOutQuad'
                        });
                        successfulIterations++;
                    } else {
                        console.error("API response did not contain 'new_idea'.", result);
                        combineStatus.textContent = `Error in iteration ${i + 1}: Invalid response format.`;
                    }
                } catch (error) {
                    console.error(`Error during combination iteration ${i + 1}:`, error);
                    combineStatus.textContent = `Error in iteration ${i + 1}: ${error.message}`;
                    // Optionally break the loop on error, or continue
                    // break;
                }
                // Small delay between iterations to avoid overwhelming the API/browser
                await new Promise(resolve => setTimeout(resolve, 200));
            }

            // Re-enable button and hide spinner
            combineBtn.disabled = false;
            combineBtn.classList.remove('opacity-70');
            combineSpinner.classList.add('hidden');
            combineStatus.textContent = `Finished ${successfulIterations} generations.`;

            // Update the main idea count display
            if (ideaCountSpan) {
                ideaCountSpan.textContent = currentIdeas.length;
            }
            
            // Update the search filter to include new cards
            if (searchInput && searchInput.value) {
                // Re-trigger filtering to include new cards
                searchInput.dispatchEvent(new Event('input'));
            }
        }

        // Attach event listener to Combine button
        if (combineBtn) {
            combineBtn.addEventListener('click', combineAndGenerate);
        }

        // Animation Logic
        document.addEventListener('DOMContentLoaded', () => {
            // Initial population of currentIdeas if grid exists
            if (ideasGrid) {
                updateCurrentIdeas();
            }
            
            if (ideaCards.length > 0) {
                anime({
                    targets: '#ideas-grid .idea-card', // Target cards within the grid
                    opacity: [0, 1],
                    translateY: [20, 0],
                    delay: anime.stagger(50),
                    duration: 600,
                    easing: 'easeOutQuad'
                });
            }
            
            // Background Animation with slate purples
            anime({
                targets: 'body',
                background: [
                    'linear-gradient(135deg, #50486b, #473d5f, #3a334e)',
                    'linear-gradient(135deg, #473d5f, #3a334e, #564f73)',
                    'linear-gradient(135deg, #3a334e, #564f73, #473d5f)',
                    'linear-gradient(135deg, #564f73, #473d5f, #3a334e)'
                ],
                duration: 20000,
                easing: 'easeInOutQuad',
                loop: true,
                direction: 'alternate'
            });
        });

        // --- Saved Ideas Panel Logic ---
        const leftPanel = document.getElementById('leftPanel');
        const leftPanelToggle = document.getElementById('leftPanelToggle');
        const leftPanelClose = document.getElementById('leftPanelClose');
        const savedIdeasList = document.getElementById('savedIdeasList');
        const SAVED_IDEAS_KEY = 'ideation_machine_saved_ideas_v1';

        function getSavedIdeas() {
            try {
                return JSON.parse(localStorage.getItem(SAVED_IDEAS_KEY)) || [];
            } catch { return []; }
        }
        function setSavedIdeas(ideas) {
            localStorage.setItem(SAVED_IDEAS_KEY, JSON.stringify(ideas));
        }
        function renderSavedIdeas() {
            const ideas = getSavedIdeas();
            savedIdeasList.innerHTML = '';
            if (!ideas.length) {
                savedIdeasList.innerHTML = '<div class="left-panel-empty">No ideas saved yet.<br>Click the heart on any idea to save it here!</div>';
                return;
            }
            ideas.forEach((idea, idx) => {
                const div = document.createElement('div');
                div.className = 'saved-idea';
                div.innerHTML = `<span>${idea}</span><button class="saved-idea-remove" title="Remove">&times;</button>`;
                div.querySelector('.saved-idea-remove').onclick = () => {
                    const updated = getSavedIdeas().filter((_, i) => i !== idx);
                    setSavedIdeas(updated);
                    renderSavedIdeas();
                    updateCheckboxes();
                };
                savedIdeasList.appendChild(div);
            });
        }
        function updateCheckboxes() {
            const saved = getSavedIdeas();
            document.querySelectorAll('.idea-card').forEach(card => {
                const idea = card.dataset.idea;
                const checkbox = card.querySelector('.save-checkbox');
                if (!checkbox) return;
                checkbox.checked = saved.includes(idea);
            });
        }
        function setupCheckboxes() {
            document.querySelectorAll('.idea-card').forEach(card => {
                const idea = card.dataset.idea;
                const checkbox = card.querySelector('.save-checkbox');
                if (!checkbox) return;
                checkbox.onchange = () => {
                    let saved = getSavedIdeas();
                    if (checkbox.checked) {
                        if (!saved.includes(idea)) saved.push(idea);
                    } else {
                        saved = saved.filter(i => i !== idea);
                    }
                    setSavedIdeas(saved);
                    renderSavedIdeas();
                };
            });
        }
        leftPanelToggle.onclick = () => {
            leftPanel.classList.add('open');
        };
        leftPanelClose.onclick = () => {
            leftPanel.classList.remove('open');
        };
        // Initial render
        renderSavedIdeas();
        // Setup checkboxes after DOM loaded and after new ideas are added
        document.addEventListener('DOMContentLoaded', () => {
            setupCheckboxes();
            updateCheckboxes();
        });
        // Also call after new ideas are generated
        const observer = new MutationObserver(() => {
            setupCheckboxes();
            updateCheckboxes();
        });
        observer.observe(document.getElementById('ideas-grid'), { childList: true, subtree: true });
    </script>
</body>
</html> 