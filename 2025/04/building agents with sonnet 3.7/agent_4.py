"""
Agent v4: Final version incorporating all features.

This version adds:
- Real web search via Exa API.
- A CreatePlanTool.
- A FileSystemTool.
- Improved UI and error handling.
"""
import anthropic
import os
import sys
import json
import math
import sqlite3 
import uuid      
import datetime  
import pathlib # New import for FileSystemTool
import re      # New import (though potentially optional for final impl)
from typing import List, Dict, Any, Optional
from dotenv import load_dotenv
from termcolor import colored

# Load environment variables from .env file
load_dotenv()

# --- Tool Definition --- 

class BaseTool:
    """Base class for all tools."""
    name: str = "base_tool"
    description: str = "Base tool class."
    input_schema: Dict[str, Any] = {}
    
    def execute(self, **kwargs) -> Dict[str, Any]:
        raise NotImplementedError("Tool must implement execute method")
    
    def get_definition(self) -> Dict[str, Any]:
        return {
            "name": self.name,
            "description": self.description,
            "input_schema": self.input_schema
        }

class SearchWebTool(BaseTool):
    """Tool for searching the web (uses Exa if available)."""
    name = "search_web"
    description = "Search the web for current information. Use this tool when you need up-to-date information that might not be in your training data. Provide a specific search query."
    input_schema = {
        "type": "object",
        "properties": {
            "query": { "type": "string", "description": "The search query" },
            "num_results": { "type": "integer", "description": "Number of results (default 3, max 10)", "default": 3}
        },
        "required": ["query"]
    }
    
    def __init__(self, agent):
        self.agent = agent # Tool needs access to agent's Exa client

    def execute(self, query: str, num_results: int = 3) -> Dict[str, Any]:
        """Execute web search using Exa if available, otherwise simulate."""
        num_results = min(num_results, 10)
        
        if self.agent.use_exa:
            try:
                print(colored(f"🔍 Performing Exa search for: {query} ({num_results} results)", "magenta"))
                search_result = self.agent._search_with_exa(query, num_results)
                # print(search_result)
                return {"status": "success", "content": search_result}
            except Exception as e:
                print(colored(f"✗ Exa search failed: {e}. Falling back to simulation.", "red"))
                # Fallback or return error
                return {"status": "error", "content": f"Exa search failed: {str(e)}"}
        else:
            # Simulate if Exa not available
            print(colored(f"🔍 Simulating web search for: {query}", "magenta"))
            results = f"Simulated search results for: '{query}'\n- Result 1\n- Result 2"
            return {"status": "success", "content": results}

class CalculateTool(BaseTool):
    """Tool for performing calculations."""
    name = "calculate"
    description = "Perform a calculation. Use this for arithmetic or math expressions."
    input_schema = { "type": "object", "properties": { "expression": {"type": "string"} }, "required": ["expression"]}
    
    def execute(self, expression: str) -> Dict[str, Any]:
        """Safely evaluate a mathematical expression."""
        try:
            print(colored(f"🧮 Calculating expression: {expression}", "cyan"))
            allowed_chars = "0123456789+-*/(). %"
            # A potential improvement: use a more robust math parsing library 
            sanitized_expression = "".join(c for c in expression if c in allowed_chars)
            sanitized_expression = sanitized_expression.replace('%', '/100') # Handle percentage
            if not sanitized_expression: raise ValueError("Invalid expression")
            # Use a limited eval environment
            result = eval(sanitized_expression, {"__builtins__": {}}, {"math": math})
            return {"status": "success", "content": f"Result: {result}"}
        except Exception as calc_error:
            print(colored(f"✗ Calculation error: {calc_error}", "red"))
            return {"status": "error", "content": f"Calculation error: {calc_error}"}

class CreatePlanTool(BaseTool):
    """Tool for creating step-by-step plans using an LLM call."""
    name = "create_plan"
    description = "Create a detailed step-by-step plan to accomplish a task. Use this to break down complex problems. Specify the task and any constraints."
    input_schema = {
        "type": "object",
        "properties": {
            "task": { "type": "string", "description": "The task requiring a plan" },
            "constraints": { "type": "string", "description": "Optional constraints or considerations" }
        },
        "required": ["task"]
    }

    def __init__(self, agent):
        self.agent = agent # Tool needs access to agent's client for LLM call

    def execute(self, task: str, constraints: str = "None") -> Dict[str, Any]:
        """Generate a plan by calling the LLM again with a specific prompt."""
        print(colored(f"📝 Generating plan for task: {task} (Constraints: {constraints})", "green"))
        try:
            # Use the agent's LLM client to generate a plan
            plan_system_prompt = "You are an expert planner. Create a concise, step-by-step plan for the given task, considering the constraints. Format as a numbered list."
            plan_query = f"Task: {task}\nConstraints: {constraints}"
            
            # Note: This uses the main agent's client. Consider potential context mixing issues.
            response = self.agent.client.messages.create(
                model=self.agent.model_name, # Use the same model as the agent
                max_tokens=500,
                system=plan_system_prompt,
                messages=[{"role": "user", "content": plan_query}]
            )
            
            if response.content and response.content[0].type == 'text':
                plan = response.content[0].text
                self.agent.current_plan = plan # Store the plan in the agent state
                return {"status": "success", "content": plan}
            else:
                raise Exception("No text content received from planning model.")

        except Exception as e:
            print(colored(f"✗ Error generating plan: {e}", "red"))
            # Fallback to a basic plan
            fallback_plan = f"Basic Plan for: {task}\n1. Define goals\n2. Gather resources\n3. Execute steps\n4. Review outcome."
            self.agent.current_plan = fallback_plan 
            return {"status": "error", "content": f"Failed to generate detailed plan: {e}. Basic plan: {fallback_plan}"}

class FileSystemTool(BaseTool):
    """Tool for interacting with the file system (read, write, list, exists)."""
    name = "filesystem"
    description = "Perform file operations: read, write, list directory contents, or check if a path exists. Paths are relative to the agent's working directory."
    input_schema = {
        "type": "object",
        "properties": {
            "operation": {
                "type": "string",
                "enum": ["read", "write", "list", "exists"],
                "description": "Operation: read, write, list, exists"
            },
            "path": {
                "type": "string",
                "description": "File or directory path"
            },
            "content": {
                "type": "string",
                "description": "Content for write operation (optional)"
            }
        },
        "required": ["operation", "path"]
    }

    def execute(self, operation: str, path: str, content: Optional[str] = None) -> Dict[str, Any]:
        """Execute file system operation safely."""
        print(colored(f"📁 Filesystem operation: {operation} on path: {path}", "blue"))
        try:
            # Resolve path relative to current working directory
            base_path = pathlib.Path.cwd()
            target_path = base_path.joinpath(path).resolve()

            # Security Check: Ensure target path is within the base path
            if not str(target_path).startswith(str(base_path)):
                return {"status": "error", "content": f"Security Error: Path access denied outside of working directory ({base_path})."}

            if operation == "read":
                if not target_path.is_file():
                    return {"status": "error", "content": f"Error: Path is not a file or does not exist: {path}"}
                file_content = target_path.read_text(encoding='utf-8')
                # Optional: Truncate long content
                max_len = 2000
                if len(file_content) > max_len:
                    file_content = file_content[:max_len] + "... [truncated]"
                return {"status": "success", "content": file_content}

            elif operation == "write":
                if content is None:
                    return {"status": "error", "content": "Error: Content required for write operation."}
                # Create parent directories if they don't exist
                target_path.parent.mkdir(parents=True, exist_ok=True)
                target_path.write_text(content, encoding='utf-8')
                return {"status": "success", "content": f"Successfully wrote {len(content)} characters to {path}"}

            elif operation == "list":
                if not target_path.is_dir():
                    return {"status": "error", "content": f"Error: Path is not a directory or does not exist: {path}"}
                files = [f.name for f in target_path.iterdir()]
                return {"status": "success", "content": f"Directory listing for {path}:\n" + "\n".join(files)}

            elif operation == "exists":
                exists = target_path.exists()
                return {"status": "success", "content": f"Path '{path}' {'exists' if exists else 'does not exist'}."}
            
            else: # Should not happen due to enum
                 return {"status": "error", "content": f"Unknown filesystem operation: {operation}"}

        except Exception as e:
            print(colored(f"✗ File system error ({operation} on {path}): {e}", "red"))
            return {"status": "error", "content": f"File system error: {str(e)}"}


# --- Memory Manager (from v3) --- 

class MemoryManager:
    """Manages persistent conversation memory using SQLite."""
    
    def __init__(self, db_path="./athena_memory.db"):
        """Initialize the memory manager and database connection."""
        self.db_path = db_path
        self.conn = None
        try:
            self.conn = sqlite3.connect(self.db_path)
            self._initialize_db()
        except sqlite3.Error as e:
            print(colored(f"Database Error: {e}", "red"))
            sys.exit(1)
        
    def _initialize_db(self):
        """Initialize the database schema if it doesn't exist."""
        try:
            with self.conn:
                # Conversations table
                self.conn.execute('''
                    CREATE TABLE IF NOT EXISTS conversations (
                        id TEXT PRIMARY KEY,
                        created_at TIMESTAMP,
                        title TEXT,
                        last_updated TIMESTAMP
                    )
                ''')
                # Messages table
                self.conn.execute('''
                    CREATE TABLE IF NOT EXISTS messages (
                        id TEXT PRIMARY KEY,
                        conversation_id TEXT,
                        timestamp TIMESTAMP,
                        role TEXT,
                        content TEXT,
                        FOREIGN KEY (conversation_id) REFERENCES conversations(id)
                    )
                ''')
        except sqlite3.Error as e:
            print(colored(f"Database schema initialization error: {e}", "red"))
            # Optionally re-raise or handle more gracefully
            raise 
            
    def create_conversation(self, title="New Conversation") -> Optional[str]:
        """Create a new conversation and return its ID."""
        conversation_id = str(uuid.uuid4())
        timestamp = datetime.datetime.now().isoformat()
        try:
            with self.conn:
                self.conn.execute(
                    'INSERT INTO conversations (id, created_at, title, last_updated) VALUES (?, ?, ?, ?)',
                    (conversation_id, timestamp, title, timestamp)
                )
            print(colored(f"💾 Created new conversation: {title} (ID: {conversation_id[:8]}...)", "cyan"))
            return conversation_id
        except sqlite3.Error as e:
            print(colored(f"Error creating conversation: {e}", "red"))
            return None
    
    def add_message(self, conversation_id, role, content) -> Optional[str]:
        """Add a message to the specified conversation."""
        message_id = str(uuid.uuid4())
        timestamp = datetime.datetime.now().isoformat()
        
        # If content is not a string (e.g., tool use/result), convert to JSON
        if not isinstance(content, str):
            try:
                content_str = json.dumps(content)
            except TypeError as e:
                 print(colored(f"Warning: Could not serialize message content to JSON: {e}", "yellow"))
                 content_str = str(content) # Fallback to string representation
        else:
            content_str = content
            
        try:
            with self.conn:
                # Update the conversation's last_updated timestamp
                self.conn.execute(
                    'UPDATE conversations SET last_updated = ? WHERE id = ?',
                    (timestamp, conversation_id)
                )
                # Insert the new message
                self.conn.execute(
                    'INSERT INTO messages (id, conversation_id, timestamp, role, content) VALUES (?, ?, ?, ?, ?)',
                    (message_id, conversation_id, timestamp, role, content_str)
                )
            return message_id
        except sqlite3.Error as e:
            print(colored(f"Error adding message to DB: {e}", "red"))
            return None
    
    def get_conversation(self, conversation_id) -> List[Dict[str, Any]]:
        """Get all messages in a conversation, ordered by timestamp."""
        messages = []
        try:
            cursor = self.conn.execute(
                'SELECT role, content FROM messages WHERE conversation_id = ? ORDER BY timestamp',
                (conversation_id,)
            )
            
            for row in cursor:
                role, content_str = row
                # Try to parse JSON if it looks like JSON
                if isinstance(content_str, str) and content_str.strip().startswith(('{', '[')):
                    try:
                        content = json.loads(content_str)
                    except json.JSONDecodeError:
                        content = content_str # Keep as string if parsing fails
                else:
                    content = content_str
                        
                messages.append({'role': role, 'content': content})
            return messages
        except sqlite3.Error as e:
            print(colored(f"Error retrieving conversation {conversation_id}: {e}", "red"))
            return [] # Return empty list on error
    
    def list_conversations(self, limit=10) -> List[Dict[str, Any]]:
        """List recent conversations."""
        conversations = []
        try:
            self.conn.row_factory = sqlite3.Row # Access columns by name
            cursor = self.conn.execute(
                'SELECT id, title, created_at, last_updated FROM conversations ORDER BY last_updated DESC LIMIT ?',
                (limit,)
            )
            conversations = [dict(row) for row in cursor.fetchall()]
            self.conn.row_factory = None # Reset row factory
            return conversations
        except sqlite3.Error as e:
             print(colored(f"Error listing conversations: {e}", "red"))
             return []
    
    def delete_conversation(self, conversation_id) -> bool:
        """Delete a conversation and all its messages."""
        # This method is not fully implemented or exposed via commands in this version
        # Placeholder implementation:
        print(colored(f"Deletion for {conversation_id} not fully implemented.", "grey"))
        return False 
        # Example full implementation:
        # try:
        #     with self.conn:
        #         self.conn.execute('DELETE FROM messages WHERE conversation_id = ?', (conversation_id,))
        #         result = self.conn.execute('DELETE FROM conversations WHERE id = ?', (conversation_id,))
        #     if result.rowcount > 0: print(colored(f"🗑️ Deleted convo {conversation_id[:8]}","yellow")); return True
        #     else: print(colored(f"Convo ID not found: {conversation_id[:8]}","yellow")); return False
        # except sqlite3.Error as e: print(colored(f"Err deleting convo: {e}","red")); return False
    
    def close(self):
        """Close the database connection."""
        if self.conn:
            try:
                self.conn.close()
                self.conn = None
                print(colored("Database connection closed.", "grey"))
            except sqlite3.Error as e:
                 print(colored(f"Error closing database connection: {e}", "red"))

# --- Agent Class (Full Features) --- 

class Agent:
    """Agent v4: Full feature set including Exa, planning, filesystem."""
    def __init__(self, system_prompt: str = None, conversation_id: str = None):
        """Initializes the agent, memory, tools, and Exa."""
        # --- Core Initialization ---
        self.api_key = os.getenv("ANTHROPIC_API_KEY")
        if not self.api_key: sys.exit(colored("Error: ANTHROPIC_API_KEY not found.", "red"))
        try: self.client = anthropic.Anthropic()
        except Exception as e: sys.exit(colored(f"Error initializing Anthropic client: {e}", "red"))
        self.model_name = "claude-3-5-sonnet-20240620"
        
        default_system_prompt = (
            "You are Athena, an advanced AI agent. Use tools proactively to answer questions accurately."
            " Provide details from web searches. Format plans clearly. Interact with files as requested."
        )
        self.system_prompt = system_prompt if system_prompt else default_system_prompt
        self.current_plan = None # For CreatePlanTool

        # --- Exa Initialization ---
        self.exa_api_key = os.getenv("EXA_API_KEY")
        self.use_exa = False
        self.exa_client = None
        if self.exa_api_key:
            try:
                import exa_py
                self.exa_client = exa_py.Exa(self.exa_api_key)
                self.use_exa = True
                print(colored("✓ Exa client initialized. Real web search enabled.", "green"))
            except ImportError:
                print(colored("⚠ exa_py not installed. Install with `pip install exa_py`. Falling back to simulated search.", "yellow"))
            except Exception as e:
                print(colored(f"✗ Error initializing Exa: {e}. Falling back to simulated search.", "red"))
        else:
             print(colored("⚠ EXA_API_KEY not found. Using simulated web search.", "yellow"))

        # --- Memory Initialization ---
        self.memory = MemoryManager()
        self.history = []
        if conversation_id:
            # Try to load existing conversation
            self.history = self.memory.get_conversation(conversation_id)
            if self.history: self.conversation_id = conversation_id; print(colored(f"📚 Loaded {len(self.history)} msgs from convo {conversation_id[:8]}...", "cyan"))
            else: print(colored(f"⚠ Convo {conversation_id} not found/empty. Starting new.", "yellow")); self.conversation_id = self.memory.create_conversation() 
        else:
            self.conversation_id = self.memory.create_conversation()
        if not self.conversation_id: sys.exit("Fatal: Could not create/load conversation.")

        # --- Tool Initialization ---
        self._initialize_tools()
        print(colored(f"✓ Agent v4 initialized. Convo ID: {self.conversation_id[:8]}... Tools: {list(self.tool_map.keys())}", "green"))

    def __del__(self):
        self.cleanup()

    def cleanup(self):
        if hasattr(self, 'memory') and self.memory: self.memory.close(); self.memory = None

    def _initialize_tools(self) -> None:
        """Initialize all tools, including Plan and Filesystem."""
        tool_instances = [
            SearchWebTool(self), # Pass agent for Exa client access
            CalculateTool(),
            CreatePlanTool(self), # Pass agent for LLM client access
            FileSystemTool()      # Standalone
        ]
        self.tool_map = {tool.name: tool for tool in tool_instances}
        
        # Enhance search description if Exa is active
        search_def = SearchWebTool(self).get_definition()
        if self.use_exa:
            search_def['description'] += " Provides detailed results and content summaries via Exa API."
        
        self.tools = [tool.get_definition() for tool in tool_instances if tool.name != 'search_web']
        self.tools.insert(0, search_def) # Put potentially modified search tool def first

    def _search_with_exa(self, query: str, num_results: int = 3) -> str:
        """Performs search using Exa and formats results."""
        if not self.use_exa or not self.exa_client: return "Exa client not available."
        try:
            # Use search_and_contents for richer results
            search_response = self.exa_client.search_and_contents(
                query, num_results=num_results, use_autoprompt=True, text=True, highlights=True
            )
            formatted = f"Exa Search Results for '{query}':\n\n"
            for r in search_response.results:
                formatted += f"Title: {r.title}\nURL: {r.url}\n"
                if r.published_date: formatted += f"Date: {r.published_date}\n"
                if r.highlights: formatted += f"Highlights: {' | '.join(r.highlights[:3])}\n"
                if r.text: formatted += f"Content Snippet: {r.text[:500]}...\n"
                formatted += "---\n"
            return formatted
        except Exception as e:
            print(colored(f"✗ Exa API search error: {e}", "red"))
            # Raise the exception so the calling tool can handle it
            raise

    def _execute_tool(self, tool_name: str, tool_input: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a tool by name (same core logic as v3)."""
        print(colored(f"🔧 Executing: {tool_name} | Input: {json.dumps(tool_input)}", "blue"))
        try:
            if tool_name in self.tool_map: return self.tool_map[tool_name].execute(**tool_input)
            else: return {"status": "error", "content": f"Unknown tool: {tool_name}"}
        except Exception as e: return {"status": "error", "content": f"Err executing {tool_name}: {e}"}
    
    def run(self, user_query: str) -> str:
        """Main run loop, identical logic to v3 but uses the v4 initialized components."""
        print(colored("\n" + "="*50, "cyan"))
        print(colored(f"📩 User ({self.conversation_id[:8]}): {user_query}", "white", attrs=["bold"]))
        print(colored("="*50, "cyan"))

        self.history.append({"role": "user", "content": user_query})
        self.memory.add_message(self.conversation_id, "user", user_query)

        agent_response = ""
        try:
            messages_to_send = list(self.history)
            api_params = {"model": self.model_name, "max_tokens": 2048, "messages": messages_to_send, "system": self.system_prompt, "tools": self.tools}
            
            print(colored("🔄 Thinking...", "yellow"))
            response = self.client.messages.create(**api_params)
            
            if response.stop_reason == "tool_use":
                print(colored("🔧 Tool use required.", "blue"))
                agent_response = self._handle_tool_use(response)
            elif response.content and response.content[0].type == 'text':
                agent_response = response.content[0].text
            else:
                agent_response = "(No response content)"

        except Exception as e:
            print(colored(f"✗ API Error: {e}", "red", attrs=["bold"]))
            agent_response = f"API Error: {str(e)}"
            self.memory.add_message(self.conversation_id, "assistant", agent_response) 
            return agent_response 

        print(colored("\n" + "-"*50, "green"))
        print(colored(f"🤖 Athena ({self.conversation_id[:8]}):", "green", attrs=["bold"]))
        print(colored(agent_response, "white"))
        print(colored("-"*50, "green"))

        self.history.append({"role": "assistant", "content": agent_response})
        self.memory.add_message(self.conversation_id, "assistant", agent_response)
        return agent_response

    def _handle_tool_use(self, response) -> str:
        """Tool handling logic, identical to v3 but uses v4 components."""
        tool_use_block = next((block for block in response.content if block.type == 'tool_use'), None)
        if not tool_use_block: return "Error: Tool use block missing."
        tool_name, tool_input, tool_use_id = tool_use_block.name, tool_use_block.input, tool_use_block.id

        assistant_message = {"role": "assistant", "content": response.content}
        self.history.append(assistant_message)
        self.memory.add_message(self.conversation_id, "assistant", response.content)
        
        tool_result = self._execute_tool(tool_name, tool_input)
        tool_result_content = [{"type": "tool_result", "tool_use_id": tool_use_id, "content": tool_result["content"], "is_error": tool_result["status"] == "error"}]
        
        tool_result_message = {"role": "user", "content": tool_result_content}
        self.history.append(tool_result_message)
        self.memory.add_message(self.conversation_id, "user", tool_result_content)
        
        final_response_text = ""
        try:
            print(colored("🔄 Sending tool results back...", "yellow"))
            messages_to_send = list(self.history) 
            response = self.client.messages.create(model=self.model_name, max_tokens=2048, messages=messages_to_send, system=self.system_prompt, tools=self.tools)
            
            if response.content and response.content[0].type == 'text': final_response_text = response.content[0].text
            else: final_response_text = "(No response after tool use)"
        except Exception as e:
            print(colored(f"✗ Follow-up API Error: {e}", "red"))
            final_response_text = f"Error after tool use: {str(e)}"
            self.memory.add_message(self.conversation_id, "assistant", final_response_text)
            return final_response_text
        
        return final_response_text

    # --- Conversation Management Methods (from v3) --- 
    def clear_history(self) -> None: 
        self.history = []; self.current_plan = None; 
        print(colored("🧹 In-memory history cleared.", "yellow"))
    def list_conversations(self, limit=5): return self.memory.list_conversations(limit)
    def switch_conversation(self, cid): 
        print(colored(f"Switching to convo {cid[:8]}...", "blue")); 
        hist = self.memory.get_conversation(cid); 
        # Check if ID exists even if history is empty (via list_conversations)
        if hist or cid in [c['id'] for c in self.memory.list_conversations(999)]:
            old_id = self.conversation_id; self.conversation_id = cid; self.history = hist; 
            print(colored(f"🔄 Switched from {old_id[:8]} to {cid[:8]}. Loaded {len(hist)} msgs.","cyan")); return True
        else: print(colored(f"✗ Convo {cid} not found.","red")); return False

# --- UI and Command Handling (from v3, updated) --- 

def print_welcome():
    print("\n" + "=" * 60); 
    print(colored("🤖 ATHENA AI AGENT (v4 - Full Features)", "cyan", attrs=["bold"])); 
    print(colored("=" * 60, "cyan"));
    print(colored("Commands: list, switch <id>, new, clear, help, exit", "yellow"))
    print("=" * 60 + "\n")

def print_help():
    print("\n" + "=" * 60); print(colored("📚 AVAILABLE COMMANDS", "green", attrs=["bold"])); print("=" * 60)
    print(f"{colored('• help', 'white', attrs=['bold']):<18} - Show this help")
    print(f"{colored('• list', 'white', attrs=['bold']):<18} - List recent conversations")
    print(f"{colored('• switch <id>', 'white', attrs=['bold']):<18} - Switch to conversation ID")
    print(f"{colored('• new', 'white', attrs=['bold']):<18} - Start a new conversation")
    print(f"{colored('• clear', 'white', attrs=['bold']):<18} - Clear current in-memory history")
    print(f"{colored('• exit / quit', 'white', attrs=['bold']):<18} - End the session")
    print("=" * 60 + "\n")

def handle_command(cmd: str, agent: Agent) -> Optional[bool]:
    # --- Identical to agent_3.py --- 
    cmd=cmd.strip().lower(); parts=cmd.split(' ',1); command=parts[0]; arg=parts[1].strip() if len(parts)>1 else None
    if command in ('exit','quit'): return False
    elif command=='help': print_help()
    elif command=='clear': agent.clear_history()
    elif command=='list': 
        convos=agent.list_conversations(); print("\n"+"-"*60); print(colored("📝 RECENT CONVERSATIONS","magenta",attrs=["bold"]));
        if not convos: print(colored("  No saved convos.","yellow"))
        else: 
            for i,c in enumerate(convos,1): 
                cr=datetime.datetime.fromisoformat(c["created_at"]).strftime("%y-%m-%d %H:%M"); 
                up=datetime.datetime.fromisoformat(c["last_updated"]).strftime("%H:%M"); 
                curr="(current)" if c["id"]==agent.conversation_id else ""; 
                title=c['title'] if c['title'] else "(Untitled)"
                print(f"{i}. {colored(title, 'white', attrs=['bold'])}{colored(curr, 'green')} {colored('(ID: '+c['id'][:8]+'...)' ,'grey')}")
                print(f"   Created: {cr} | Updated: {up}")
        print("-"*60+"\n")
    elif command=='switch': 
        if arg: agent.switch_conversation(arg)
        else: print(colored("Usage: switch <id>","red"))
    elif command=='new': 
        new_id=agent.memory.create_conversation("New Convo"); 
        if new_id: agent.conversation_id=new_id; agent.history=[]; print(colored(f"Started new convo: {new_id[:8]}...","green"))
        else: print(colored("Failed to start new convo.","red"))
    else: return None 
    return True 

# --- Main Execution --- 
def main():
    print_welcome()
    agent = None 
    try:
        agent = Agent()
        # Display Exa status
        if agent.use_exa: print(colored("🌐 Real web search ENABLED (Exa).", "green"))
        else: print(colored("🌐 Web search DISABLED (Simulated only).", "yellow"))
        handle_command("list", agent) 
        while True:
            user_input = input(colored(f"\n💬 You ({agent.conversation_id[:8]}): ", "cyan"))
            if not user_input.strip(): continue
            command_result = handle_command(user_input, agent)
            if command_result is False: break   
            if command_result is True: continue 
            response = agent.run(user_input)
    except KeyboardInterrupt: print(colored("\nExiting agent.", "yellow"))
    except Exception as e: print(colored(f"Unexpected error: {e}", "red"))
    finally:
        print(colored("\n✨ Session ended.", "cyan"))
        if agent: agent.cleanup() 

if __name__ == "__main__":
    main() 