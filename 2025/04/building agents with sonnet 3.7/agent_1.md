# Agent v1: Basic Structure (`agent_1.py`)

This first version establishes the fundamental structure of our AI agent, "Athena".

## Key Components:

1.  **Imports**: Includes essential libraries:
    *   `anthropic`: For interacting with the Claude API.
    *   `os`: To access environment variables (like the API key).
    *   `dotenv`: To load environment variables from a `.env` file.
    *   `termcolor`: For adding color to terminal output, improving readability.
    *   `sys`: For basic system operations like exiting on critical errors.

2.  **Environment Variables**:
    *   `load_dotenv()`: Attempts to load variables from a `.env` file in the same directory. This is crucial for securely managing the `ANTHROPIC_API_KEY`.

3.  **`Agent` Class**:
    *   **`__init__`**:
        *   Retrieves the `ANTHROPIC_API_KEY` from environment variables. Exits if not found.
        *   Initializes the `anthropic.Anthropic` client.
        *   Sets the `model_name` (e.g., `claude-3-5-sonnet-20240620`).
        *   Sets a default `system_prompt`.
        *   Initializes an empty list `self.history` to store the conversation turns *for the current session only*.
        *   Prints a confirmation message.
    *   **`run(user_query)`**:
        *   Takes the user's input string (`user_query`).
        *   Prints the user query using `termcolor`.
        *   Appends the user query to the `self.history` list.
        *   Constructs the `api_params` dictionary for the `messages.create` call, including the model, max tokens, messages (current history), and system prompt.
        *   Calls the Anthropic API (`self.client.messages.create`).
        *   Includes basic error handling for the API call.
        *   Extracts the text response from the API result.
        *   Prints the agent's response using `termcolor`.
        *   Appends the agent's response to `self.history`.
        *   Returns the agent's response string.

4.  **`if __name__ == "__main__":` block**:
    *   This code runs only when the script is executed directly.
    *   Prints a welcome banner.
    *   Creates an instance of the `Agent` class.
    *   Enters a `while True` loop to continuously:
        *   Prompt the user for input using `input()`.
        *   Check if the user typed "exit" to break the loop.
        *   Skip empty input.
        *   Call the `agent.run()` method with the user's input.
    *   Includes `try...except KeyboardInterrupt` to allow graceful exit using Ctrl+C.
    *   Prints a session ended message.

## Functionality:

*   Connects to the Claude API using an API key.
*   Takes user input in a loop.
*   Sends the input along with a minimal conversation history (user message -> assistant response -> user message...) to Claude.
*   Receives and displays Claude's text response.
*   Uses basic colored text for better UI feedback.

## Limitations:

*   **No Tool Use**: Cannot perform actions like web searches, calculations, or file operations.
*   **No Persistent Memory**: Conversation history is lost when the script ends.
*   **Basic History Management**: Simply appends messages; no summarization or context window management.
*   **Minimal Error Handling**: Only catches general exceptions during the API call.
*   **No Commands**: Lacks features like listing/switching conversations, clearing history, etc.

This forms the foundation upon which we will build more advanced features. 