"""
Agent v3: Adds persistent memory using SQLite and conversation management.

This version introduces the MemoryManager class to store conversations
in a database, allowing the agent to retain history across sessions.
It also adds commands for listing and switching conversations.
"""
import anthropic
import os
import sys
import json
import math
import sqlite3 # New import for database
import uuid      # New import for unique IDs
import datetime  # New import for timestamps
from typing import List, Dict, Any, Optional
from dotenv import load_dotenv
from termcolor import colored

# Load environment variables from .env file
load_dotenv()

# --- Tool Definition (from v2) --- 

class BaseTool:
    name: str = "base_tool"
    description: str = "Base tool class."
    input_schema: Dict[str, Any] = {}
    
    def execute(self, **kwargs) -> Dict[str, Any]:
        raise NotImplementedError("Tool must implement execute method")
    
    def get_definition(self) -> Dict[str, Any]:
        return {
            "name": self.name,
            "description": self.description,
            "input_schema": self.input_schema
        }

class SearchWebTool(BaseTool):
    name = "search_web"
    description = "Search the web for current information (simulated)."
    input_schema = { "type": "object", "properties": { "query": {"type": "string"} }, "required": ["query"]}
    
    def execute(self, query: str) -> Dict[str, Any]:
        print(colored(f"🔍 Simulating web search for: {query}", "magenta"))
        results = f"Simulated search results for: '{query}'\n- Result 1\n- Result 2"
        return {"status": "success", "content": results}

class CalculateTool(BaseTool):
    name = "calculate"
    description = "Perform a calculation."
    input_schema = { "type": "object", "properties": { "expression": {"type": "string"} }, "required": ["expression"]}
    
    def execute(self, expression: str) -> Dict[str, Any]:
        try:
            print(colored(f"🧮 Calculating expression: {expression}", "cyan"))
            allowed_chars = "0123456789+-*/(). "
            sanitized_expression = "".join(c for c in expression if c in allowed_chars)
            if not sanitized_expression: raise ValueError("Invalid expression")
            result = eval(sanitized_expression, {"__builtins__": {}}, {"math": math})
            return {"status": "success", "content": f"Result: {result}"}
        except Exception as calc_error:
            print(colored(f"✗ Calculation error: {calc_error}", "red"))
            return {"status": "error", "content": f"Calculation error: {calc_error}"}

# --- Memory Manager --- 

class MemoryManager:
    """Manages persistent conversation memory using SQLite."""
    
    def __init__(self, db_path="./athena_memory.db"):
        """Initialize the memory manager and database connection."""
        self.db_path = db_path
        self.conn = None
        try:
            self.conn = sqlite3.connect(self.db_path)
            self._initialize_db()
        except sqlite3.Error as e:
            print(colored(f"Database Error: {e}", "red"))
            sys.exit(1)
        
    def _initialize_db(self):
        """Initialize the database schema if it doesn't exist."""
        try:
            with self.conn:
                # Conversations table
                self.conn.execute('''
                    CREATE TABLE IF NOT EXISTS conversations (
                        id TEXT PRIMARY KEY,
                        created_at TIMESTAMP,
                        title TEXT,
                        last_updated TIMESTAMP
                    )
                ''')
                # Messages table
                self.conn.execute('''
                    CREATE TABLE IF NOT EXISTS messages (
                        id TEXT PRIMARY KEY,
                        conversation_id TEXT,
                        timestamp TIMESTAMP,
                        role TEXT,
                        content TEXT,
                        FOREIGN KEY (conversation_id) REFERENCES conversations(id)
                    )
                ''')
        except sqlite3.Error as e:
            print(colored(f"Database schema initialization error: {e}", "red"))
            # Optionally re-raise or handle more gracefully
            raise 
            
    def create_conversation(self, title="New Conversation") -> Optional[str]:
        """Create a new conversation and return its ID."""
        conversation_id = str(uuid.uuid4())
        timestamp = datetime.datetime.now().isoformat()
        try:
            with self.conn:
                self.conn.execute(
                    'INSERT INTO conversations (id, created_at, title, last_updated) VALUES (?, ?, ?, ?)',
                    (conversation_id, timestamp, title, timestamp)
                )
            print(colored(f"💾 Created new conversation: {title} (ID: {conversation_id})", "cyan"))
            return conversation_id
        except sqlite3.Error as e:
            print(colored(f"Error creating conversation: {e}", "red"))
            return None
    
    def add_message(self, conversation_id, role, content) -> Optional[str]:
        """Add a message to the specified conversation."""
        message_id = str(uuid.uuid4())
        timestamp = datetime.datetime.now().isoformat()
        
        # If content is not a string (e.g., tool use/result), convert to JSON
        if not isinstance(content, str):
            try:
                content_str = json.dumps(content)
            except TypeError as e:
                 print(colored(f"Warning: Could not serialize message content to JSON: {e}", "yellow"))
                 content_str = str(content) # Fallback to string representation
        else:
            content_str = content
            
        try:
            with self.conn:
                # Update the conversation's last_updated timestamp
                self.conn.execute(
                    'UPDATE conversations SET last_updated = ? WHERE id = ?',
                    (timestamp, conversation_id)
                )
                # Insert the new message
                self.conn.execute(
                    'INSERT INTO messages (id, conversation_id, timestamp, role, content) VALUES (?, ?, ?, ?, ?)',
                    (message_id, conversation_id, timestamp, role, content_str)
                )
            return message_id
        except sqlite3.Error as e:
            print(colored(f"Error adding message to DB: {e}", "red"))
            return None
    
    def get_conversation(self, conversation_id) -> List[Dict[str, Any]]:
        """Get all messages in a conversation, ordered by timestamp."""
        messages = []
        try:
            cursor = self.conn.execute(
                'SELECT role, content FROM messages WHERE conversation_id = ? ORDER BY timestamp',
                (conversation_id,)
            )
            
            for row in cursor:
                role, content_str = row
                # Try to parse JSON if it looks like JSON
                if isinstance(content_str, str) and content_str.strip().startswith(('{', '[')):
                    try:
                        content = json.loads(content_str)
                    except json.JSONDecodeError:
                        content = content_str # Keep as string if parsing fails
                else:
                    content = content_str
                        
                messages.append({'role': role, 'content': content})
            return messages
        except sqlite3.Error as e:
            print(colored(f"Error retrieving conversation {conversation_id}: {e}", "red"))
            return [] # Return empty list on error
    
    def list_conversations(self, limit=10) -> List[Dict[str, Any]]:
        """List recent conversations."""
        conversations = []
        try:
            self.conn.row_factory = sqlite3.Row # Access columns by name
            cursor = self.conn.execute(
                'SELECT id, title, created_at, last_updated FROM conversations ORDER BY last_updated DESC LIMIT ?',
                (limit,)
            )
            conversations = [dict(row) for row in cursor.fetchall()]
            self.conn.row_factory = None # Reset row factory
            return conversations
        except sqlite3.Error as e:
             print(colored(f"Error listing conversations: {e}", "red"))
             return []
    
    def delete_conversation(self, conversation_id) -> bool:
        """Delete a conversation and all its messages."""
        try:
            with self.conn:
                # First delete the messages
                self.conn.execute('DELETE FROM messages WHERE conversation_id = ?', (conversation_id,))
                # Then delete the conversation
                result = self.conn.execute('DELETE FROM conversations WHERE id = ?', (conversation_id,))
            
            if result.rowcount > 0:
                 print(colored(f"🗑️ Deleted conversation: {conversation_id}", "yellow"))
                 return True
            else:
                 print(colored(f"Conversation ID not found for deletion: {conversation_id}", "yellow"))
                 return False
        except sqlite3.Error as e:
             print(colored(f"Error deleting conversation {conversation_id}: {e}", "red"))
             return False
    
    def close(self):
        """Close the database connection."""
        if self.conn:
            try:
                self.conn.close()
                self.conn = None
                print(colored("Database connection closed.", "grey"))
            except sqlite3.Error as e:
                 print(colored(f"Error closing database connection: {e}", "red"))

# --- Agent Class (with Memory) --- 

class Agent:
    """Agent v3: Adds persistent memory and conversation management."""
    def __init__(self, system_prompt: str = None, conversation_id: str = None):
        """Initializes the agent, memory manager, and loads/creates a conversation."""
        self.api_key = os.getenv("ANTHROPIC_API_KEY")
        if not self.api_key: sys.exit(colored("Error: ANTHROPIC_API_KEY not found.", "red"))
            
        try: self.client = anthropic.Anthropic()
        except Exception as e: sys.exit(colored(f"Error initializing Anthropic client: {e}", "red"))
            
        self.model_name = "claude-3-5-sonnet-20240620"
        
        default_system_prompt = (
            "You are Athena, a helpful AI assistant. You can use tools like web search and calculator when needed."
            " Keep track of the conversation history."
        )
        self.system_prompt = system_prompt if system_prompt else default_system_prompt
        
        # Initialize Memory Manager
        self.memory = MemoryManager() 
        
        # Initialize or load conversation
        self.history = []
        if conversation_id:
            print(colored(f"Attempting to load conversation: {conversation_id}", "blue"))
            self.history = self.memory.get_conversation(conversation_id)
            if self.history:
                self.conversation_id = conversation_id
                print(colored(f"📚 Loaded conversation {conversation_id} with {len(self.history)} messages.", "cyan"))
            else:
                 print(colored(f"Warning: Conversation {conversation_id} not found or empty. Starting new.", "yellow"))
                 self.conversation_id = self.memory.create_conversation()
                 if not self.conversation_id: sys.exit("Failed to create initial conversation.")
        else:
            self.conversation_id = self.memory.create_conversation()
            if not self.conversation_id: sys.exit("Failed to create initial conversation.")
        
        # Initialize tools (same as v2)
        self._initialize_tools()
        
        print(colored(f"✓ Agent v3 initialized. Current conversation ID: {self.conversation_id}", "green"))

    # Destructor to ensure cleanup as a fallback
    def __del__(self):
        self.cleanup()

    def cleanup(self):
        """Explicitly clean up resources, especially the DB connection."""
        if hasattr(self, 'memory') and self.memory:
            self.memory.close()
            self.memory = None # Prevent double closing

    def _initialize_tools(self) -> None:
        """Initialize and register available tools (same as v2)."""
        tool_instances = [ SearchWebTool(), CalculateTool() ]
        self.tool_map = {tool.name: tool for tool in tool_instances}
        self.tools = [tool.get_definition() for tool in tool_instances]

    def _execute_tool(self, tool_name: str, tool_input: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a tool by name with given input (same as v2)."""
        print(colored(f"🔧 Executing tool: {tool_name} with input: {json.dumps(tool_input)}", "blue"))
        try:
            if tool_name in self.tool_map:
                return self.tool_map[tool_name].execute(**tool_input)
            else:
                return {"status": "error", "content": f"Unknown tool: {tool_name}"}
        except Exception as e:
            return {"status": "error", "content": f"Error executing {tool_name}: {str(e)}"}
    
    def run(self, user_query: str) -> str:
        """Processes a query, saves interaction to DB, potentially uses tools."""
        print(colored("\n" + "="*50, "cyan"))
        print(colored("📩 User Query: ", "cyan", attrs=["bold"]) + colored(user_query, "white"))
        print(colored("="*50 + "\n", "cyan"))

        # Add user query to in-memory history AND database
        self.history.append({"role": "user", "content": user_query})
        self.memory.add_message(self.conversation_id, "user", user_query)

        agent_response = "" # Initialize agent_response
        try:
            messages_to_send = list(self.history)
            api_params = {
                "model": self.model_name, "max_tokens": 1024,
                "messages": messages_to_send, "system": self.system_prompt,
                "tools": self.tools
            }
            
            print(colored("🔄 Sending request...", "yellow"))
            response = self.client.messages.create(**api_params)
            
            if response.stop_reason == "tool_use":
                print(colored("🔧 Tool use requested", "blue"))
                agent_response = self._handle_tool_use(response)
            elif response.content and response.content[0].type == 'text':
                agent_response = response.content[0].text
            else:
                agent_response = "(No text response received)"

        except Exception as e:
            print(colored(f"✗ API Error: {e}", "red", attrs=["bold"]))
            agent_response = f"Sorry, API error: {str(e)}"
            # Save error response to DB as well
            self.memory.add_message(self.conversation_id, "assistant", agent_response)
            # Note: Error response is NOT added to in-memory self.history here
            # because it wasn't a successful turn. This prevents sending the error
            # back to the API on the next turn. Revisit if different behavior is needed.
            return agent_response # Return error immediately

        # Print successful response
        print(colored("\n" + "-"*50, "green"))
        print(colored("🤖 Athena: ", "green", attrs=["bold"]) + colored(agent_response, "white"))
        print(colored("-"*50 + "\n", "green"))

        # Add successful assistant response to in-memory history AND database
        self.history.append({"role": "assistant", "content": agent_response})
        self.memory.add_message(self.conversation_id, "assistant", agent_response)
        
        return agent_response

    def _handle_tool_use(self, response) -> str:
        """Handles tool use, saves steps to DB, gets final response."""
        tool_use_block = next((block for block in response.content if block.type == 'tool_use'), None)
                
        if not tool_use_block: return "Error: Tool use block not found."

        tool_name, tool_input, tool_use_id = tool_use_block.name, tool_use_block.input, tool_use_block.id

        # Save assistant's tool_use request to history AND DB
        assistant_message = {"role": "assistant", "content": response.content}
        self.history.append(assistant_message)
        self.memory.add_message(self.conversation_id, "assistant", response.content)
        
        # Execute tool
        tool_result = self._execute_tool(tool_name, tool_input)
        
        # Prepare tool_result message content
        tool_result_content = [
            {
                "type": "tool_result",
                "tool_use_id": tool_use_id,
                "content": tool_result["content"],
                "is_error": tool_result["status"] == "error"
            }
        ]
        
        # Save user's tool_result message to history AND DB
        tool_result_message = {"role": "user", "content": tool_result_content}
        self.history.append(tool_result_message)
        self.memory.add_message(self.conversation_id, "user", tool_result_content)
        
        # Make second API call
        final_response_text = ""
        try:
            print(colored("🔄 Sending tool results back...", "yellow"))
            messages_to_send = list(self.history) 
            response = self.client.messages.create(
                model=self.model_name, max_tokens=1024,
                messages=messages_to_send, system=self.system_prompt,
                tools=self.tools
            )
            
            if response.content and response.content[0].type == 'text':
                final_response_text = response.content[0].text
            else:
                 final_response_text = "(No final text response after tool use)"

        except Exception as e:
            print(colored(f"✗ Error in follow-up API call: {e}", "red"))
            final_response_text = f"Sorry, error after tool use: {str(e)}"
            # Save this error response to DB, but not in-memory history
            self.memory.add_message(self.conversation_id, "assistant", final_response_text)
            return final_response_text
        
        # Return the final response text (it will be added to history+DB by the main run method)
        return final_response_text

    # --- New Conversation Management Methods --- 
    def clear_history(self) -> None:
        """Clears the in-memory conversation history (but keeps it in the database)."""
        self.history = []
        print(colored("🧹 In-memory conversation history cleared.", "yellow"))
    
    def list_conversations(self, limit=5) -> List[Dict[str, Any]]:
        """List recent conversations from memory."""
        return self.memory.list_conversations(limit)
    
    def switch_conversation(self, conversation_id) -> bool:
        """Switch to a different conversation by loading its history."""
        print(colored(f"Attempting to switch to conversation: {conversation_id}", "blue"))
        new_history = self.memory.get_conversation(conversation_id)
        if new_history or conversation_id in [c['id'] for c in self.memory.list_conversations(999)]: # Check if ID exists even if empty
            old_id = self.conversation_id
            self.conversation_id = conversation_id
            self.history = new_history
            print(colored(f"🔄 Switched from conversation {old_id} to {conversation_id}", "cyan"))
            print(colored(f"📚 Loaded {len(self.history)} messages.", "cyan"))
            return True
        else:
            print(colored(f"✗ Error: Conversation {conversation_id} not found.", "red"))
            return False

# --- UI and Command Handling --- 

def print_welcome():
    """Print a colorful welcome message and basic instructions."""
    print("\n" + "=" * 60)
    print(colored("🤖 ATHENA AI AGENT (v3 - Persistent Memory)", "cyan", attrs=["bold"]))
    print(colored("=" * 60 + "\n", "cyan", attrs=["bold"]))
    print(colored("Type your query, or use commands:", "yellow"))
    print(colored("  list, switch <id>, new, clear, help, exit", "yellow"))
    print("=" * 60 + "\n")

def print_help():
    """Print detailed help information."""
    print("\n" + "=" * 60)
    print(colored("📚 AVAILABLE COMMANDS", "green", attrs=["bold"]))
    print("=" * 60)
    print(colored("• help", "white", attrs=["bold"]) + "         - Show this help message")
    print(colored("• list", "white", attrs=["bold"]) + "         - List recent conversations")
    print(colored("• switch <id>", "white", attrs=["bold"]) + "  - Switch to conversation with the given ID")
    print(colored("• new", "white", attrs=["bold"]) + "          - Start a new conversation")
    print(colored("• clear", "white", attrs=["bold"]) + "        - Clear the current session's history (in-memory)")
    # print(colored("• delete <id>", "white", attrs=["bold"]) + " - Delete a conversation (coming soon)") # Future command
    print(colored("• exit, quit", "white", attrs=["bold"]) + "   - End the session")
    print("=" * 60 + "\n")

def handle_command(cmd: str, agent: Agent) -> Optional[bool]:
    """Handle special commands. Return False to exit, True if handled, None otherwise."""
    cmd = cmd.strip().lower()
    parts = cmd.split(' ', 1)
    command = parts[0]
    arg = parts[1].strip() if len(parts) > 1 else None

    if command in ('exit', 'quit'):
        return False # Signal to exit main loop
    
    elif command == 'help':
        print_help()
    
    elif command == 'clear':
        agent.clear_history()
        # os.system('cls' if os.name == 'nt' else 'clear') # Optional: clear terminal screen
        # print_welcome() 
    
    elif command == 'list':
        conversations = agent.list_conversations()
        print("\n" + "-" * 60)
        print(colored("📝 RECENT CONVERSATIONS", "magenta", attrs=["bold"]))
        if not conversations:
            print(colored("  No saved conversations found.", "yellow"))
        else:
            for i, conv in enumerate(conversations, 1):
                created = datetime.datetime.fromisoformat(conv["created_at"]).strftime("%Y-%m-%d %H:%M")
                last_upd = datetime.datetime.fromisoformat(conv["last_updated"]).strftime("%Y-%m-%d %H:%M")
                current = " (current)" if conv["id"] == agent.conversation_id else ""
                title = conv['title'] if conv['title'] else "(Untitled)"
                print(f"{i}. {colored(title, 'white', attrs=['bold'])}{colored(current, 'green')}")
                print(f"   ID: {conv['id']}")
                print(f"   Created: {created} | Last Update: {last_upd}")
        print("-" * 60 + "\n")
    
    elif command == 'switch':
        if arg:
            agent.switch_conversation(arg)
        else:
            print(colored("Usage: switch <conversation_id>", "red"))
    
    elif command == 'new':
        new_id = agent.memory.create_conversation("New Conversation")
        if new_id:
            agent.conversation_id = new_id
            agent.history = []
            print(colored(f"Started new conversation: {agent.conversation_id}", "green"))
        else:
             print(colored("Failed to create new conversation.", "red"))
    
    # Example for future delete command
    # elif command == 'delete':
    #     if arg:
    #         if agent.memory.delete_conversation(arg):
    #              # If deleting current, switch to a new one or the latest one
    #              if arg == agent.conversation_id:
    #                   # Simple approach: start new
    #                   agent.conversation_id = agent.memory.create_conversation("New Conversation")
    #                   agent.history = []
    #                   print(colored("Current conversation deleted. Started new one.", "yellow"))
    #         else:
    #              print(colored(f"Failed to delete conversation {arg}", "red"))
    #     else:
    #          print(colored("Usage: delete <conversation_id>", "red"))

    else:
        return None  # Not a recognized command
    
    return True  # Command was handled

# --- Main Execution --- 

def main():
    """Main function to run the agent interaction loop."""
    print_welcome()
    agent = None # Initialize agent to None for finally block
    try:
        agent = Agent()
        
        # List conversations on startup
        handle_command("list", agent) 

        while True:
            user_input = input(colored("\n💬 Your query: ", "cyan"))
            
            if not user_input.strip(): continue # Skip empty input

            command_result = handle_command(user_input, agent)
            
            if command_result is False: break    # Exit command
            if command_result is True: continue # Command handled, loop again
            
            # If not a command, run as a query
            response = agent.run(user_input)
            
    except KeyboardInterrupt:
        print(colored("\n\nExiting agent.", "yellow"))
    except Exception as e:
        print(colored(f"An unexpected error occurred: {e}", "red"))
    finally:
        print(colored("\n✨ Session ended.", "cyan"))
        if agent:
            agent.cleanup() # Ensure DB connection is closed

if __name__ == "__main__":
    main() 