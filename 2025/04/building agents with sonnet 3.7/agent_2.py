"""
Agent v2: Adds basic tool use (web search, calculation).

This version introduces the ability for the agent to use external tools,
specifically a web search tool and a calculation tool.
"""
import anthropic
import os
import sys
import json
import math # New import for calculation
from typing import List, Dict, Any, Optional # New imports for type hinting
from dotenv import load_dotenv
from termcolor import colored

# Load environment variables from .env file
load_dotenv()

# --- Tool Definition --- 

class BaseTool:
    """Base class for all tools."""
    name: str = "base_tool"
    description: str = "Base tool class."
    input_schema: Dict[str, Any] = {}
    
    def execute(self, **kwargs) -> Dict[str, Any]:
        """Execute the tool with the given parameters."""
        raise NotImplementedError("Tool must implement execute method")
    
    def get_definition(self) -> Dict[str, Any]:
        """Get the tool definition for the Anthropic API."""
        return {
            "name": self.name,
            "description": self.description,
            "input_schema": self.input_schema
        }

class SearchWebTool(BaseTool):
    """Tool for searching the web (simulated in this version)."""
    name = "search_web"
    description = "Search the web for current information. Use this tool when you need up-to-date information that might not be in your training data. Provide a specific search query."
    input_schema = {
        "type": "object",
        "properties": {
            "query": {
                "type": "string",
                "description": "The search query"
            },
        },
        "required": ["query"]
    }
    
    def execute(self, query: str) -> Dict[str, Any]:
        """Simulate web search."""
        print(colored(f"🔍 Simulating web search for: {query}", "magenta"))
        # In a real scenario, this would call a search API (like Exa)
        results = f"Simulated search results for: '{query}'\n- Result 1 about {query}\n- Result 2 about {query}"
        return {"status": "success", "content": results}

class CalculateTool(BaseTool):
    """Tool for performing calculations."""
    name = "calculate"
    description = "Perform a calculation. Use this for arithmetic, algebra, and other mathematical operations that require precise computation."
    input_schema = {
        "type": "object",
        "properties": {
            "expression": {
                "type": "string",
                "description": "The mathematical expression to evaluate"
            }
        },
        "required": ["expression"]
    }
    
    def execute(self, expression: str) -> Dict[str, Any]:
        """Safely evaluate a mathematical expression."""
        try:
            # WARNING: eval() can be unsafe if not properly sanitized. 
            # A safer approach might involve using ast.literal_eval or a dedicated math parser.
            print(colored(f"🧮 Calculating expression: {expression}", "cyan"))
            # Basic sanitization: Allow digits, operators, parentheses, dots, and spaces
            allowed_chars = "0123456789+-*/(). "
            sanitized_expression = "".join(c for c in expression if c in allowed_chars)
            if not sanitized_expression:
                 raise ValueError("Expression is empty or invalid after sanitization")
            
            result = eval(sanitized_expression, {"__builtins__": {}}, {"math": math}) # Provide math module
            return {"status": "success", "content": f"Result: {result}"}
        except Exception as calc_error:
            print(colored(f"✗ Calculation error: {calc_error}", "red"))
            return {"status": "error", "content": f"Calculation error: {calc_error}"}

# --- Agent Class --- 

class Agent:
    """Agent v2: Basic structure with tool use."""
    def __init__(self, system_prompt: str = None):
        """Initializes the agent with the Anthropic client and tools."""
        self.api_key = os.getenv("ANTHROPIC_API_KEY")
        if not self.api_key:
            print(colored("Error: ANTHROPIC_API_KEY not found.", "red"))
            sys.exit(1)
            
        try:
            self.client = anthropic.Anthropic()
        except Exception as e:
            print(colored(f"Error initializing Anthropic client: {e}", "red"))
            sys.exit(1)
            
        self.model_name = "claude-3-7-sonnet-20250219"
        
        default_system_prompt = (
            "You are Athena, a helpful AI assistant. You can use tools like web search and calculator when needed."
        )
        self.system_prompt = system_prompt if system_prompt else default_system_prompt
        
        self.history = []
        
        # Initialize tools
        self._initialize_tools()
        
        print(colored(f"✓ Agent v2 initialized with model: {self.model_name} and tools: {list(self.tool_map.keys())}", "green"))

    def _initialize_tools(self) -> None:
        """Initialize and register available tools."""
        # Create tool instances
        tool_instances = [
            SearchWebTool(),
            CalculateTool()
        ]
        
        self.tool_map = {tool.name: tool for tool in tool_instances}
        self.tools = [tool.get_definition() for tool in tool_instances]

    def _execute_tool(self, tool_name: str, tool_input: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a tool by name with given input."""
        print(colored(f"🔧 Executing tool: ", "blue", attrs=["bold"]) + 
              colored(f"{tool_name}", "blue", attrs=["bold", "underline"]) + 
              colored(" with input: ", "blue") + 
              colored(f"{json.dumps(tool_input)}", "blue"))
        
        try:
            if tool_name in self.tool_map:
                tool = self.tool_map[tool_name]
                return tool.execute(**tool_input)
            else:
                print(colored(f"✗ Unknown tool: {tool_name}", "red"))
                return {"status": "error", "content": f"Unknown tool: {tool_name}"}
        except Exception as e:
            print(colored(f"✗ Error executing {tool_name}: {str(e)}", "red"))
            return {"status": "error", "content": f"Error executing {tool_name}: {str(e)}"}
    
    def run(self, user_query: str) -> str:
        """Processes a user query, potentially using tools."""
        print(colored("\n" + "="*50, "cyan"))
        print(colored("📩 User Query: ", "cyan", attrs=["bold"]) + colored(user_query, "white"))
        print(colored("="*50 + "\n", "cyan"))

        self.history.append({"role": "user", "content": user_query})

        try:
            messages_to_send = list(self.history)
            
            # Configure API call - now includes tools parameter
            api_params = {
                "model": self.model_name,
                "max_tokens": 1024,
                "messages": messages_to_send,
                "system": self.system_prompt,
                "tools": self.tools # Pass tool definitions to the API
            }
            
            print(colored("🔄 Sending request to Claude API...", "yellow"))
            response = self.client.messages.create(**api_params)
            
            # Check if the response requires tool use
            if response.stop_reason == "tool_use":
                print(colored("🔧 Claude requested to use a tool", "blue"))
                # Handle the tool use flow and return the final response
                return self._handle_tool_use(response)
            
            # If no tool use, process the text response as before
            if response.content and response.content[0].type == 'text':
                agent_response = response.content[0].text
            else:
                agent_response = "(No text response received)"

        except Exception as e:
            print(colored(f"✗ Error calling Anthropic API: {e}", "red", attrs=["bold"]))
            agent_response = f"Sorry, I encountered an error: {str(e)}"

        print(colored("\n" + "-"*50, "green"))
        print(colored("🤖 Athena: ", "green", attrs=["bold"]) + colored(agent_response, "white"))
        print(colored("-"*50 + "\n", "green"))

        self.history.append({"role": "assistant", "content": agent_response})
        return agent_response

    def _handle_tool_use(self, response) -> str:
        """Handles the multi-step process of executing a tool and getting a final response."""
        # Extract the tool use request from the initial response
        tool_use_block = None
        assistant_content_so_far = [] # Store potential text before tool use
        for block in response.content:
            if block.type == 'text':
                assistant_content_so_far.append(block)
            elif block.type == 'tool_use':
                tool_use_block = block
                # For simplicity in v2, we handle only the first tool use block if multiple are sent
                break 
                
        if not tool_use_block:
            print(colored("✗ Tool use requested, but no tool_use block found.", "red"))
            return "Error: Tool use requested by model, but block not found."

        tool_name = tool_use_block.name
        tool_input = tool_use_block.input
        tool_use_id = tool_use_block.id

        # IMPORTANT: Add the assistant's response containing the tool_use block to history
        # before adding the tool result. This follows the required API message sequence.
        assistant_message = {"role": "assistant", "content": response.content}
        self.history.append(assistant_message)
        
        # Execute the requested tool
        tool_result = self._execute_tool(tool_name, tool_input)
        
        # Construct the tool_result message for the API
        tool_result_message = {
            "role": "user", # Tool results are sent back in a user role message
            "content": [
                {
                    "type": "tool_result",
                    "tool_use_id": tool_use_id,
                    "content": tool_result["content"],
                    # Optionally add "is_error": True if tool execution failed
                    "is_error": tool_result["status"] == "error"
                }
            ]
        }
        
        # Add the tool result to history
        self.history.append(tool_result_message)
        
        # ----- Make the second API call with the tool result ----- 
        try:
            print(colored("🔄 Sending tool results back to Claude API...", "yellow"))
            # Send the updated history (including tool_use and tool_result)
            messages_to_send = list(self.history) 
            
            response = self.client.messages.create(
                model=self.model_name,
                max_tokens=1024,
                messages=messages_to_send,
                system=self.system_prompt,
                tools=self.tools # Important to include tools definition again
            )
            
            # Extract the final response after tool use
            if response.content and response.content[0].type == 'text':
                final_response = response.content[0].text
            else:
                final_response = "(No final text response received after tool use)"

        except Exception as e:
            print(colored(f"✗ Error in follow-up API call after tool use: {e}", "red"))
            final_response = f"Sorry, I encountered an error after using the tool: {str(e)}"
        
        # Print the final response
        print(colored("\n" + "-"*50, "green"))
        print(colored("🤖 Athena (after tool use): ", "green", attrs=["bold"]) + colored(final_response, "white"))
        print(colored("-"*50 + "\n", "green"))
        
        # Add the final response to history
        self.history.append({"role": "assistant", "content": final_response})
        
        return final_response

# --- Main Execution --- 

if __name__ == '__main__':
    print(colored("\n" + "="*60, "cyan", attrs=["bold"]))
    print(colored("🤖 ATHENA AI AGENT (v2 - Basic Tool Use)", "cyan", attrs=["bold"]))
    print(colored("="*60 + "\n", "cyan", attrs=["bold"]))
    print(colored("Type your query (e.g., 'What is 12 * 5?' or 'Search for Claude AI'), or type 'exit' to quit.", "yellow"))
    
    agent = Agent()
    
    try:
        while True:
            user_input = input(colored("\n💬 Your query: ", "cyan"))
            
            if user_input.lower() == 'exit':
                break
            elif not user_input.strip():
                continue
                
            response = agent.run(user_input)
            
    except KeyboardInterrupt:
        print(colored("\n\nExiting agent.", "yellow"))
    except Exception as e:
        print(colored(f"An unexpected error occurred: {e}", "red"))
    finally:
        print(colored("\n✨ Session ended.", "cyan")) 