# Agent v3: Persistent Memory (`agent_3.py`)

```python
"""
Agent v3: Adds persistent memory using SQLite and conversation management.

This version introduces the MemoryManager class to store conversations
in a database, allowing the agent to retain history across sessions.
It also adds commands for listing and switching conversations.
"""
```

This third iteration significantly enhances the agent by adding persistent memory and basic conversation management.

## Key Additions:

1.  **New Imports**:
    *   `sqlite3`: The Python module for interacting with SQLite databases.
    *   `uuid`: To generate unique IDs for conversations and messages.
    *   `datetime`: To timestamp conversations and messages.

2.  **`MemoryManager` Class**:
    *   This is the core of the persistence layer.
    *   **`__init__(db_path)`**:
        *   Takes the path for the SQLite database file (defaults to `./athena_memory.db`).
        *   Establishes a connection (`self.conn`) to the database.
        *   Calls `_initialize_db()` to ensure tables exist.
    *   **`_initialize_db()`**:
        *   Creates the `conversations` and `messages` tables if they don't already exist.
        *   Defines the schema for each table (IDs, timestamps, content, relationships).
    *   **`create_conversation(title)`**:
        *   Generates a unique ID for the new conversation.
        *   Inserts a new record into the `conversations` table with a title and timestamps.
        *   Returns the new conversation ID.
    *   **`add_message(conversation_id, role, content)`**:
        *   Generates a unique ID for the message.
        *   Gets the current timestamp.
        *   **Handles non-string content (like tool results) by converting it to a JSON string before saving.**
        *   Inserts the message details (`conversation_id`, `role`, `content`, `timestamp`) into the `messages` table.
        *   Updates the `last_updated` timestamp on the corresponding `conversations` record.
    *   **`get_conversation(conversation_id)`**:
        *   Retrieves all messages for a given `conversation_id`, ordered by `timestamp`.
        *   Uses `conn.row_factory = sqlite3.Row` for easy access to columns by name.
        *   **Attempts to parse the `content` field as JSON if it looks like a JSON string (starts with `{` or `[`). If parsing fails, it keeps the content as a string.** This handles both regular text messages and stored tool results.
        *   Returns a list of message dictionaries (`{'role': ..., 'content': ...}`).
    *   **`list_conversations(limit)`**:
        *   Retrieves the most recent conversations (ordered by `last_updated`, limited by `limit`).
        *   Returns a list of conversation dictionaries (`{'id': ..., 'title': ..., 'created_at': ...}`).
    *   **`delete_conversation(conversation_id)`**:
        *   Deletes all messages associated with the `conversation_id` from the `messages` table.
        *   Deletes the conversation record itself from the `conversations` table.
    *   **`close()`**:
        *   Closes the database connection (`self.conn.close()`). Essential for releasing the database file.

3.  **`Agent` Class Modifications**:
    *   **`__init__`**:
        *   Accepts an optional `conversation_id` argument.
        *   Initializes `self.memory = MemoryManager()`.
        *   If `conversation_id` is provided:
            *   Sets `self.conversation_id`.
            *   Calls `self.memory.get_conversation()` to load the history into `self.history`.
        *   If no `conversation_id` is provided:
            *   Calls `self.memory.create_conversation()` to start a new one and sets `self.conversation_id`.
    *   **`__del__` (Destructor)**:
        *   Added to attempt closing the memory connection when the agent object is garbage collected (as a fallback).
    *   **`run(user_query)`**:
        *   Calls `self.memory.add_message()` to save the user query to the database immediately after adding it to the in-memory `self.history`.
        *   Calls `self.memory.add_message()` again to save the final assistant response to the database.
    *   **`_handle_tool_use(response)`**:
        *   Calls `self.memory.add_message()` to save the assistant's `tool_use` request to the database.
        *   Calls `self.memory.add_message()` to save the user's `tool_result` message to the database.
        *   Calls `self.memory.add_message()` to save the final assistant response (after tool use) to the database.
    *   **`clear_history()`**:
        *   Now only clears the *in-memory* `self.history`. The conversation remains in the database.
    *   **`list_conversations(limit)`**:
        *   New method that delegates to `self.memory.list_conversations()`.
    *   **`switch_conversation(conversation_id)`**:
        *   New method to change the active conversation.
        *   Sets `self.conversation_id`.
        *   Reloads `self.history` using `self.memory.get_conversation()`.
    *   **`cleanup()`**:
        *   New method to explicitly close the database connection via `self.memory.close()`. **Crucial for proper resource management, especially on Windows.**

4.  **Command Handling (`print_welcome`, `print_help`, `handle_command`, `main`)**:
    *   Introduced dedicated functions for UI elements (`print_welcome`, `print_help`).
    *   Created `handle_command(cmd, agent)` to process special user inputs like `list`, `switch`, `new`, `clear`, `help`, `exit`.
    *   The main loop in `main()` now calls `handle_command` first. If it returns `True`, the command was handled, and the loop continues. If `False`, the loop exits. If `None`, it proceeds to call `agent.run()`.
    *   The `main()` function now includes a `try...finally` block to ensure `agent.cleanup()` is called reliably before the script exits.
    *   The agent initialization in `main()` now lists existing conversations upon startup.

## New Functionality:

*   **Persistence**: Conversations are saved to `athena_memory.db` and are not lost when the script stops.
*   **Conversation Loading**: The agent can resume a previous conversation if started with its ID (though this isn't exposed in the `main` loop yet, `switch` uses it).
*   **JSON Handling**: Memory manager correctly stores and retrieves complex message content (like tool results).
*   **Conversation Management Commands**:
    *   `list`: Shows recent conversations stored in the database.
    *   `switch <id>`: Loads and continues a different conversation.
    *   `new`: Starts a fresh conversation (saved to DB).
    *   `clear`: Clears the *current session's* in-memory history (doesn't delete from DB).
    *   `help`: Displays available commands.
*   **Resource Management**: Explicitly closes the database connection on exit.

## Limitations Compared to Final Version:

*   **No Exa Integration**: Search is still simulated.
*   **Limited Tools**: Still missing `CreatePlanTool` and `FileSystemTool`.
*   **Basic UI**: While functional, could be further refined.

This version represents a major step towards a stateful and more usable agent. 