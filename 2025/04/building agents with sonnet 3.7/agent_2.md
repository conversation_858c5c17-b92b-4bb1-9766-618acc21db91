# Agent v2: Basic Tool Use (`agent_2.py`)

```python
"""
Agent v2: Adds basic tool use (web search, calculation).

This version introduces the ability for the agent to use external tools,
specifically a web search tool and a calculation tool.
"""
```

This second iteration enhances the agent by introducing basic tool use capabilities.

## Key Additions:

1.  **New Imports**:
    *   `typing`: Used for type hinting (e.g., `List`, `Dict`, `Any`, `Optional`).
    *   `math`: Needed for the `CalculateTool` to safely evaluate math expressions.

2.  **`BaseTool` Class**:
    *   Introduced as an abstract base class for all tools.
    *   Defines the required structure: `name`, `description`, `input_schema`.
    *   Includes `get_definition()` to format the tool for the API and an `execute()` method to be implemented by subclasses.

3.  **Tool Implementations (`SearchWebTool`, `CalculateTool`)**:
    *   Inherit from `BaseTool`.
    *   Define their specific `name`, `description`, and `input_schema` (following JSON Schema format).
    *   Implement the `execute()` method:
        *   `SearchWebTool`: Simulates a web search (as Exa integration comes later). Returns placeholder results.
        *   `CalculateTool`: Uses `eval()` to compute mathematical expressions (with a safety warning). Includes basic error handling.

4.  **`Agent` Class Modifications**:
    *   **`__init__`**:
        *   Calls a new `_initialize_tools()` method to set up available tools.
        *   Stores tool instances in `self.tool_map` (a dictionary mapping tool name to instance) for execution.
        *   Stores API-formatted tool definitions in `self.tools` (a list) to be sent to the API.
    *   **`_initialize_tools()`**:
        *   Instantiates `SearchWebTool` and `CalculateTool`.
        *   Populates `self.tool_map` and `self.tools`.
    *   **`_execute_tool(tool_name, tool_input)`**:
        *   New method to handle tool execution.
        *   Looks up the tool in `self.tool_map` using `tool_name`.
        *   Calls the tool's `execute()` method with `tool_input`.
        *   Includes error handling for unknown tools or execution errors.
        *   Returns a dictionary with `status` and `content`.
    *   **`run(user_query)`**:
        *   Adds the `tools=self.tools` parameter to the `client.messages.create` call.
        *   Checks the `response.stop_reason`. If it's `"tool_use"`, it calls a new `_handle_tool_use()` method.
    *   **`_handle_tool_use(response)`**:
        *   New method triggered when the API indicates tool use.
        *   Iterates through `response.content` to find the `tool_use` block(s).
        *   Extracts the `tool_use_id`, `name`, and `input`.
        *   **Crucially, adds the assistant's `tool_use` request to the history *before* executing the tool.** This maintains the correct conversational flow for the API.
        *   Calls `_execute_tool()` with the extracted name and input.
        *   Formats the tool's result into a `tool_result` content block, including the `tool_use_id` and the `content` (or `is_error: true` if execution failed).
        *   **Adds the `tool_result` message (with `role: user`) to the history.**
        *   Calls `client.messages.create` **again**, sending the updated history (including the `tool_use` and `tool_result`) back to the model.
        *   Extracts the final text response from this second API call.
        *   Adds the final response to the history and returns it.

5.  **`if __name__ == "__main__":` block**:
    *   Remains largely the same, but now the agent invoked by `agent.run()` has tool capabilities.

## New Functionality:

*   **Tool Definition**: Defines tools with names, descriptions, and input schemas.
*   **Tool Detection**: The agent can now recognize when a user query might require a tool.
*   **Tool Execution**: The agent can call the corresponding tool code (simulated web search, actual calculation).
*   **Multi-Turn Tool Use**: Handles the required multi-step interaction with the Claude API for tool use (User Query -> Assistant Tool Request -> User Tool Result -> Assistant Final Response).
*   **Basic Error Handling for Tools**: Can report tool execution errors back to the model.

## Limitations Compared to Final Version:

*   **No Persistent Memory**: History is still lost on exit.
*   **Limited Tools**: Only search (simulated) and calculation.
*   **No Exa Integration**: Search is not real.
*   **No Planning Tool**: Cannot create multi-step plans.
*   **No File System Tool**: Cannot interact with files.
*   **Basic UI/Commands**: No advanced commands (`list`, `switch`, etc.).

This version demonstrates the core logic required to integrate tools with the Claude Messages API. 