import anthropic
import os
import sys
import json
from dotenv import load_dotenv
from termcolor import colored

# Load environment variables from .env file
load_dotenv()

class Agent:
    """Agent v1: Basic structure and API connection."""
    def __init__(self, system_prompt: str = None):
        """Initializes the agent with the Anthropic client."""
        # Retrieve API key from environment variables
        self.api_key = os.getenv("ANTHROPIC_API_KEY")
        if not self.api_key:
            print(colored("Error: ANTHROPIC_API_KEY not found. Please set it in your .env file or environment.", "red"))
            sys.exit(1)
            
        # Initialize Anthropic client
        try:
            self.client = anthropic.Anthropic()
        except Exception as e:
            print(colored(f"Error initializing Anthropic client: {e}", "red"))
            sys.exit(1)
            
        # Set model name
        self.model_name = "claude-3-7-sonnet-20250219" 
        
        # Set default system prompt if none provided
        default_system_prompt = (
            "<PERSON> are <PERSON>, a helpful AI assistant. Provide concise and accurate answers."
        )
        self.system_prompt = system_prompt if system_prompt else default_system_prompt
        
        # Initialize conversation history (in-memory only for this version)
        self.history = []
        
        print(colored(f"✓ Agent initialized with model: {self.model_name}", "green"))

    def run(self, user_query: str) -> str:
        """
        Processes a single user query and returns the agent's response.
        
        Args:
            user_query: The input query from the user.

        Returns:
            A string response from the agent.
        """
        print(colored("\n" + "="*50, "cyan"))
        print(colored("📩 User Query: ", "cyan", attrs=["bold"]) + colored(user_query, "white"))
        print(colored("="*50 + "\n", "cyan"))

        # Add user query to the session's history
        self.history.append({"role": "user", "content": user_query})

        try:
            # Prepare messages for the API call
            messages_to_send = list(self.history) # Send the current conversation history
            
            # Configure API parameters
            api_params = {
                "model": self.model_name,
                "max_tokens": 1024,
                "messages": messages_to_send,
                "system": self.system_prompt
            }
            
            # Make the API call
            print(colored("🔄 Sending request to Claude API...", "yellow"))
            response = self.client.messages.create(**api_params)
            
            # Extract the response text
            if response.content and response.content[0].type == 'text':
                agent_response = response.content[0].text
            else:
                agent_response = "(No text response received)"

        except Exception as e:
            print(colored(f"✗ Error calling Anthropic API: {e}", "red", attrs=["bold"]))
            agent_response = f"Sorry, I encountered an error: {str(e)}"

        # Print the agent's response
        print(colored("\n" + "-"*50, "green"))
        print(colored("🤖 Athena: ", "green", attrs=["bold"]) + colored(agent_response, "white"))
        print(colored("-"*50 + "\n", "green"))

        # Add agent's response to the session's history
        self.history.append({"role": "assistant", "content": agent_response})

        return agent_response

if __name__ == '__main__':
    # Print welcome banner
    print(colored("\n" + "="*60, "cyan", attrs=["bold"]))
    print(colored("🤖 ATHENA AI AGENT (v1 - Basic)", "cyan", attrs=["bold"]))
    print(colored("="*60 + "\n", "cyan", attrs=["bold"]))
    print(colored("Type your query below, or type 'exit' to quit.", "yellow"))
    
    # Initialize the agent
    agent = Agent()
    
    # Main interaction loop
    try:
        while True:
            user_input = input(colored("\n💬 Your query: ", "cyan"))
            
            if user_input.lower() == 'exit':
                break
            elif not user_input.strip():
                continue # Skip empty input
                
            # Get response from agent
            response = agent.run(user_input)
            
    except KeyboardInterrupt:
        print(colored("\n\nExiting agent.", "yellow"))
    except Exception as e:
        print(colored(f"An unexpected error occurred: {e}", "red"))
    finally:
        print(colored("\n✨ Session ended.", "cyan")) 