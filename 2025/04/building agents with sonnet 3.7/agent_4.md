# Agent v4: Full Feature Set (`agent_4.py` - Equivalent to `agi_agent.py`)

```python
"""
Agent v4: Final version incorporating all features.

This version adds:
- Real web search via Exa API.
- A CreatePlanTool.
- A FileSystemTool.
- Improved UI and error handling.
- Matches the functionality of the original agi_agent.py script.
"""
```

This final iteration brings the agent to its full functionality, matching the original `agi_agent.py` script developed earlier.

## Key Additions & Changes:

1.  **New Imports**:
    *   `pathlib`: For more robust file path manipulation in the `FileSystemTool`.
    *   `re`: Regular expressions (though not strictly necessary for the final version's implementation, often useful in more complex agents).

2.  **Exa Integration in `SearchWebTool`**:
    *   The `Agent.__init__` now checks for the `EXA_API_KEY` environment variable.
    *   If found, it attempts to `import exa_py` and initialize the `exa_client`.
    *   The `SearchWebTool.execute` method now uses `agent.exa_client.search_and_contents()` to perform *real* web searches if `agent.use_exa` is `True`.
    *   Includes formatting of Exa results (title, URL, highlights, content preview).
    *   Falls back to the simulated search if <PERSON><PERSON> is unavailable.
    *   The `Agent._initialize_tools` method enhances the `search_web` tool's description if Exa is active.
    *   The `Agent` class includes the `_search_with_exa` helper method (originally part of `agi_agent.py`) which is now called by the `SearchWebTool`.

3.  **`CreatePlanTool` Implementation**:
    *   Added as a new tool inheriting from `BaseTool`.
    *   `input_schema` defines `task` (required) and `constraints` (optional).
    *   The `execute` method now calls the Claude API *again* using a dedicated system prompt to generate a more detailed plan based on the provided task and constraints. This demonstrates tool-initiated LLM calls.
    *   Includes fallback to a basic plan if the LLM call fails.
    *   Stores the generated plan in `agent.current_plan`.

4.  **`FileSystemTool` Implementation**:
    *   Added as a new tool inheriting from `BaseTool`.
    *   `input_schema` defines `operation` (enum: `read`, `write`, `list`, `exists`), `path` (required), and `content` (optional, for write).
    *   Uses `pathlib.Path` for safer path operations.
    *   Includes a basic security check to prevent accessing files outside the current working directory.
    *   Implements `read`, `write` (with directory creation), `list`, and `exists` operations.
    *   Provides specific error messages for file not found, not a directory, etc.

5.  **`Agent` Class Refinements**:
    *   **`__init__`**: Incorporates Exa initialization logic.
    *   **`_initialize_tools`**: Now includes `CreatePlanTool` and `FileSystemTool` in the registry.
    *   **`run`**: Minor refinements to logging/printing.
    *   **`_handle_tool_use`**: Logic remains largely the same but now handles the expanded set of tools.
    *   **`clear_history`**: Also clears `self.current_plan`.

6.  **UI and `main` Function Refinements**:
    *   Welcome messages and help text are updated to reflect all available commands and tools.
    *   The `main` function displays Exa status on startup.
    *   Error handling in `main` and `MemoryManager` is slightly more robust.
    *   The command handling logic remains the same structure as v3.

## Final Functionality:

*   All features from previous versions (basic chat, tool use framework, persistent memory, conversation management).
*   **Real Web Search**: Uses the Exa API for up-to-date information retrieval.
*   **Planning**: Can generate step-by-step plans using an LLM call within a tool.
*   **File System Interaction**: Can read, write, list, and check the existence of files/directories within its working directory.
*   **Refined UI**: Clearer status messages and command instructions.
*   **Robustness**: Improved error handling and resource management.

This version represents the complete agent as developed in the prior conversation, ready for use or further extension. 