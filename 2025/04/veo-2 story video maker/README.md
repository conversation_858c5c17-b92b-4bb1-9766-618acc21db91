# Video Story Generator

A Python tool that automatically generates video stories from text concepts using GPT-4o for scene generation and Google's Veo 2.0 model for video creation.

## Features

- ✏️ Generates creative scene descriptions from a simple concept using OpenAI's GPT-4o
- 🎬 Creates high-quality videos for each scene using Google's Veo 2.0 model
- 🔄 Supports parallel processing for faster generation
- 🔁 Built-in retry mechanism for failed video generations
- 📁 Saves individual scene videos and combines them into a complete story
- 📝 Preserves scene descriptions in JSON format

## Requirements

- Python 3.8+
- OpenAI API key
- Google Gemini API key with access to Veo 2.0 model
- FFmpeg installed on your system
- Required Python packages (see `requirements.txt`)

## Installation

1. Clone this repository or download the script
2. Install FFmpeg: [FFmpeg Installation Guide](https://www.hostinger.com/tutorials/how-to-install-ffmpeg)
3. Install the required dependencies:

```bash
pip install -r requirements.txt
```

4. Set up your API keys as environment variables:

```bash
# Windows
set OPENAI_API_KEY=your_openai_api_key
set GEMINI_API_KEY=your_gemini_api_key

# Linux/macOS
export OPENAI_API_KEY=your_openai_api_key
export GEMINI_API_KEY=your_gemini_api_key
```

## Configuration

The script contains several configurable constants at the top:

- `GPT_MODEL`: The OpenAI model to use for scene generation (default: "gpt-4o")
- `VIDEO_MODEL`: The Gemini video generation model (default: "veo-2.0-generate-001")
- `OUTPUT_FOLDER`: Directory where generated videos are saved (default: "generated_videos")
- `NUM_SCENES`: Number of scenes to generate (default: 5)
- `PARALLEL_GENERATION`: Whether to generate videos in parallel (default: True)
- `LENGTH_OF_VIDEO`: Duration in seconds for each generated video (default: 5)
- `RETRIES`: Number of retry attempts for failed generations (default: 3)

## Usage

Run the script:

```bash
python generate_video_story.py
```

You'll be prompted to enter a concept for your video story. The script will:

1. Generate scene descriptions based on your concept
2. Create videos for each scene
3. Combine all videos into a single story video
4. Save everything in the output folder

## Example

```
--- Video Story Generator ---
Generation mode: Parallel
Enter the concept for your video story: A journey through the seasons
Generating 5 scenes for concept: 'A journey through the seasons'...
Successfully generated 5 scenes.
Scenes saved to JSON file: generated_videos/generated_scenes.json
Starting parallel video generation for 5 scenes...
```

## Output Files

- Individual scene videos: `generated_videos/scene_1.mp4`, `scene_2.mp4`, etc.
- Combined video: `generated_videos/combined_story.mp4`
- Scene descriptions: `generated_videos/generated_scenes.json`

## Troubleshooting

- If you encounter API errors, verify your API keys are set correctly
- For video generation failures, try increasing the `RETRIES` value
- If you experience memory issues with parallel generation, set `PARALLEL_GENERATION = False`
- Make sure moviepy can locate your FFmpeg installation
- If you need a specific version of moviepy for compatibility, install version 1.0.3 