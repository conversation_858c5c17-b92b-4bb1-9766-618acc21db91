import os
import time
import json
import concurrent.futures
from termcolor import cprint
try:
    import openai
    from google import genai
    from google.genai import types
    from moviepy.editor import VideoFileClip, concatenate_videoclips # use moviepy==1.0.3 version
except ImportError:
    cprint("Required libraries not found. Please install them using:", "red")
    cprint("pip install -r requirements.txt", "yellow")
    exit(1)

# --- Constants ---
try:
    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
    GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
    if not OPENAI_API_KEY:
        raise ValueError("OPENAI_API_KEY environment variable not set.")
    if not GEMINI_API_KEY:
        raise ValueError("GEMINI_API_KEY environment variable not set.")
except ValueError as e:
    cprint(f"Error: {e}", "red")
    exit(1)

GPT_MODEL = "gpt-4o"
VIDEO_MODEL = "veo-2.0-generate-001" # Assuming Veo model based on context
OUTPUT_FOLDER = "generated_videos"
SCENES_JSON_FILE = "generated_scenes.json"
COMBINED_VIDEO_FILE = "combined_story.mp4"
NUM_SCENES = 1
PARALLEL_GENERATION = False  # Set to False for serial generation
LENGHT_OF_VIDEO = 5
RETRIES = 3
WAIT_BEFORE_RETRY = 5

# --- API Clients ---
try:
    openai_client = openai.OpenAI(api_key=OPENAI_API_KEY)
    genai_client = genai.Client(api_key=GEMINI_API_KEY)
except Exception as e:
    cprint(f"Error initializing API clients: {e}", "red")
    exit(1)

# --- Functions ---

def generate_scenes(concept: str) -> list[str]:
    """Generates scene descriptions using GPT-4o."""
    cprint(f"Generating {NUM_SCENES} scenes for concept: '{concept}'...", "cyan")
    system_prompt = f"""
    You are a creative assistant. Based on the user's concept, generate a JSON object
    containing a list of exactly {NUM_SCENES} distinct, detailed scene descriptions suitable
    for video generation prompts. Each scene description should be concise but vivid,
    focusing on visual elements.
    if the user mentions a style, descrive the scene with that style in mind
    The JSON object should have a single key "scenes" which is a list of strings.
    Example format: {{"scenes": ["Scene 1 description...", "Scene 2 description...", ...]}}
    """
    user_prompt = f"Concept: {concept}"

    try:
        response = openai_client.chat.completions.create(
            model=GPT_MODEL,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt},
            ],
            response_format={"type": "json_object"},
            temperature=0.7,
        )
        scenes_json = json.loads(response.choices[0].message.content)
        scenes = scenes_json.get("scenes", [])
        if not isinstance(scenes, list) or len(scenes) != NUM_SCENES:
             raise ValueError(f"Expected {NUM_SCENES} scenes in a list, but got: {scenes}")
        cprint(f"Successfully generated {len(scenes)} scenes.", "green")
        return scenes
    except (openai.APIError, json.JSONDecodeError, ValueError, Exception) as e:
        cprint(f"Error generating scenes: {e}", "red")
        return []

def generate_single_video(scene_index: int, scene_prompt: str, max_retries=None):
    """Submits a video generation request for a single scene and waits for completion.
    Will retry up to max_retries times if generation fails."""
    # Use the global RETRIES constant if max_retries is not specified
    if max_retries is None:
        max_retries = RETRIES
        
    retry_count = 0
    
    while retry_count < max_retries:
        try:
            if retry_count > 0:
                cprint(f"Retry attempt {retry_count}/{max_retries-1} for Scene {scene_index + 1}...", "yellow")
                cprint(f"  Waiting {WAIT_BEFORE_RETRY} seconds before retry...", "yellow")
                time.sleep(WAIT_BEFORE_RETRY)  # Use the global constant for wait time
            else:
                cprint(f"Submitting video generation for Scene {scene_index + 1}...", "cyan")
                
            cprint(f"  Prompt: {scene_prompt[:100]}...", "blue") # Print truncated prompt

            operation = genai_client.models.generate_videos(
                model=VIDEO_MODEL,
                prompt=scene_prompt,
                config=types.GenerateVideosConfig(
                    person_generation="allow_adult",
                    aspect_ratio="16:9",
                    number_of_videos=1,
                    duration_seconds=LENGHT_OF_VIDEO,
                ),
            )
            cprint(f"  Video generation for Scene {scene_index + 1} submitted (Operation ID: {operation}). Waiting...", "yellow")

            while not operation.done:
                time.sleep(20) # Check every 20 seconds
                cprint(f"  Checking status for Scene {scene_index + 1}...", "magenta")
                operation = genai_client.operations.get(operation)

            if operation.error:
                cprint(f"  Error generating video for Scene {scene_index + 1}: {operation.error}", "red")
                # Operation failed but no exception was raised, continue to retry
                retry_count += 1
                if retry_count >= max_retries:
                    cprint(f"  All {max_retries} attempts failed for Scene {scene_index + 1}. Giving up.", "red")
                    return None, scene_index
                # No need for sleep here as we'll sleep at the beginning of the next iteration
                continue
            
            # If we get here, the operation was successful
            cprint(f"  Video generation for Scene {scene_index + 1} completed successfully.", "green")
            return operation, scene_index
            
        except Exception as e:
            cprint(f"Error during video generation/polling for Scene {scene_index + 1}: {e}", "red")
            retry_count += 1
            if retry_count >= max_retries:
                cprint(f"  All {max_retries} attempts failed for Scene {scene_index + 1}. Giving up.", "red")
                return None, scene_index
            # No need for sleep here as we'll sleep at the beginning of the next iteration
    
    # Should never reach here, but just in case
    return None, scene_index

def save_video(operation, scene_index: int):
    """Saves the video from a completed operation."""
    if not operation or not operation.response or not operation.response.generated_videos:
        cprint(f"Skipping save for Scene {scene_index + 1} due to generation error or missing data.", "yellow")
        return

    try:
        # Ensure output directory exists
        os.makedirs(OUTPUT_FOLDER, exist_ok=True)

        video_data = operation.response.generated_videos[0].video
        file_name = f"scene_{scene_index + 1}.mp4"
        save_path = os.path.join(OUTPUT_FOLDER, file_name)

        cprint(f"Downloading video for Scene {scene_index + 1}...", "cyan")
        # The genai library download/save methods might not need explicit encoding="utf-8"
        # as they handle binary data directly.
        # Let's assume the library handles it correctly. If issues arise, we might need
        # to download raw bytes and write manually with open(..., 'wb').
        genai_client.files.download(file=video_data)
        video_data.save(save_path)

        cprint(f"Successfully saved video for Scene {scene_index + 1} to '{save_path}'.", "green")
    except (types.GoogleAPIError, OSError, Exception) as e:
        cprint(f"Error saving video for Scene {scene_index + 1}: {e}", "red")

def save_scenes_to_json(concept: str, scenes: list[str]):
    """Saves the generated scenes to a JSON file."""
    try:
        data = {
            "concept": concept,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "scenes": scenes
        }
        
        os.makedirs(os.path.dirname(os.path.join(OUTPUT_FOLDER, SCENES_JSON_FILE)) or ".", exist_ok=True)
        json_path = os.path.join(OUTPUT_FOLDER, SCENES_JSON_FILE)
        
        with open(json_path, 'w', encoding="utf-8") as f:
            json.dump(data, f, indent=2)
            
        cprint(f"Scenes saved to JSON file: {json_path}", "green")
    except Exception as e:
        cprint(f"Error saving scenes to JSON: {e}", "red")

def combine_videos(scene_count):
    """Combines all generated videos into a single video file."""
    cprint("Combining all videos into a single clip...", "cyan")
    
    try:
        # Get a list of all scene video files in correct order
        video_files = []
        for i in range(scene_count):
            file_path = os.path.join(OUTPUT_FOLDER, f"scene_{i + 1}.mp4")
            if os.path.exists(file_path):
                video_files.append(file_path)
            else:
                cprint(f"Warning: Scene {i + 1} video file not found.", "yellow")
        
        if not video_files:
            cprint("No videos found to combine.", "red")
            return False
            
        # Load all video clips
        cprint(f"Loading {len(video_files)} video clips...", "blue")
        clips = []
        for file in video_files:
            try:
                clip = VideoFileClip(file)
                clips.append(clip)
                cprint(f"  Loaded: {os.path.basename(file)} - Duration: {clip.duration:.2f}s", "green")
            except Exception as e:
                cprint(f"  Error loading {file}: {e}", "red")
        
        if not clips:
            cprint("No clips could be loaded.", "red")
            return False
            
        # Combine clips
        cprint("Concatenating clips...", "blue")
        final_clip = concatenate_videoclips(clips, method="compose")
        
        # Save combined video
        output_path = os.path.join(OUTPUT_FOLDER, COMBINED_VIDEO_FILE)
        cprint(f"Saving combined video to {output_path}...", "blue")
        final_clip.write_videofile(output_path, codec="libx264", audio_codec="aac")
        
        # Close clips to free resources
        final_clip.close()
        for clip in clips:
            clip.close()
            
        cprint(f"Combined video saved successfully! Total duration: {final_clip.duration:.2f}s", "green")
        return True
    except Exception as e:
        cprint(f"Error combining videos: {e}", "red")
        return False

def main():
    """Main function to orchestrate scene generation and video creation."""
    cprint("--- Video Story Generator ---", "yellow", attrs=["bold"])
    cprint(f"Generation mode: {'Parallel' if PARALLEL_GENERATION else 'Serial'}", "cyan")

    concept = input("Enter the concept for your video story: ")
    if not concept:
        cprint("Error: Concept cannot be empty.", "red")
        return

    scenes = generate_scenes(concept)
    if not scenes:
        cprint("Failed to generate scenes. Exiting.", "red")
        return
        
    # Ensure output directory exists early
    os.makedirs(OUTPUT_FOLDER, exist_ok=True)
    
    # Save scenes to JSON file
    save_scenes_to_json(concept, scenes)

    successful_scenes = []  # Track successfully generated scenes for combining later

    if PARALLEL_GENERATION:
        cprint(f"Starting parallel video generation for {len(scenes)} scenes...", "cyan")
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=NUM_SCENES) as executor:
            # Submit tasks
            future_to_scene = {
                executor.submit(generate_single_video, i, scene): i for i, scene in enumerate(scenes)
            }

            # Process completed tasks and save videos as they complete
            for future in concurrent.futures.as_completed(future_to_scene):
                scene_index = future_to_scene[future]
                try:
                    operation_result, returned_index = future.result()
                    if operation_result:
                        # Save video immediately after generation completes
                        save_video(operation_result, returned_index)
                        successful_scenes.append(returned_index)
                    else:
                        cprint(f"Video generation failed for scene {returned_index + 1}.", "yellow")
                except Exception as exc:
                    cprint(f'Scene {scene_index + 1} generated an exception: {exc}', "red")
    else:
        cprint(f"Starting serial video generation for {len(scenes)} scenes...", "cyan")
        
        # Generate and save videos one by one
        for i, scene in enumerate(scenes):
            try:
                operation_result, returned_index = generate_single_video(i, scene)
                if operation_result:
                    # Save video immediately after generation
                    save_video(operation_result, returned_index)
                    successful_scenes.append(returned_index)
                else:
                    cprint(f"Video generation failed for scene {returned_index + 1}.", "yellow")
            except Exception as exc:
                cprint(f'Scene {i + 1} generated an exception: {exc}', "red")

    cprint("--- Video Story Generation Complete ---", "yellow", attrs=["bold"])
    cprint(f"Generated videos saved in folder: '{OUTPUT_FOLDER}'", "green")
    cprint(f"Scene descriptions saved to: {os.path.join(OUTPUT_FOLDER, SCENES_JSON_FILE)}", "green")
    
    # Combine all videos into a single clip
    cprint("\n--- Combining Videos ---", "yellow", attrs=["bold"])
    if combine_videos(len(scenes)):
        cprint(f"Combined video saved to: {os.path.join(OUTPUT_FOLDER, COMBINED_VIDEO_FILE)}", "green", attrs=["bold"])
    else:
        cprint("Failed to create combined video.", "red")


if __name__ == "__main__":
    main() 