import pygame
import math
import random

# Initialize Pygame
pygame.init()

# Set up the game window
WIDTH = 800
HEIGHT = 600
FONT = pygame.font.Font(None, 36)

screen = pygame.display.set_mode((WIDTH, HEIGHT))
pygame.display.set_caption("Simple Tower Defense Game")

# Colors
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)

class Tower:
    def __init__(self, x, y):
        self.image = pygame.image.load("tower.png").convert_alpha()
        self.image = pygame.transform.scale(self.image, (64, 64))
        self.rect = self.image.get_rect(center=(x, y))
        self.range = 200
        self.damage = 5
        self.attack_speed = 1

    def draw(self):
        screen.blit(self.image, self.rect.topleft)

class Enemy:
    def __init__(self, x, y):
        self.image = pygame.image.load("enemy.png").convert_alpha()
        self.image = pygame.transform.scale(self.image, (64, 64))
        self.rect = self.image.get_rect(center=(x, y))
        self.health = 10
        self.velocity = 2

    def draw(self):
        screen.blit(self.image, self.rect)

class Projectile:
    def __init__(self, x, y, target_x, target_y):
        self.x = x
        self.y = y
        self.target_x = target_x
        self.target_y = target_y
        self.radius = 5
        self.velocity = 10

    def draw(self):
        dx = self.target_x - self.x
        dy = self.target_y - self.y
        distance = math.sqrt(dx**2 + dy**2)
        if distance != 0:
            pygame.draw.line(screen, WHITE, (self.x, self.y), 
                           (self.x + dx/10, self.y + dy/10), width=3)

def main():
    towers = []
    enemies = []

    # Create a few example towers and enemies
    for i in range(3):
        x = random.randint(100, WIDTH - 100)
        y = random.randint(100, HEIGHT - 100)
        towers.append(Tower(x, y))

    game_running = True
    clock = pygame.time.Clock()

    while game_running:
        screen.fill(BLACK)

        # Handle events
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                game_running = False

        # Update and draw tower
        for tower in towers:
            tower.draw()

        # Add your enemy spawning and movement logic here
        # This is a basic version, you can expand based on your needs.

        # Draw projectiles (just an example)
        # You would need to modify this part to include actual projectile functionality

        # Update display
        pygame.display.flip()
        clock.tick(60)

    pygame.quit()

if __name__ == "__main__":
    main()