import pandas as pd
import random
import json
import base64
import os
from google import genai
from google.genai import types
from termcolor import colored
import sys

# --- Configuration ---
CSV_FILE = 'train.csv'
CONTEXT_FILE = 'additional_info.txt'
OUTPUT_JSON = 'gemini_predictions.json'
NUM_EXAMPLES = 50
NUM_PREDICT = 50
GEMINI_MODEL = "gemini-2.5-pro-exp-03-25" # Or your preferred model

# --- Helper Functions ---
def print_step(message):
    """Prints a colored step message."""
    print(colored(f"[STEP] {message}", 'cyan', attrs=['bold']))

def print_info(message):
    """Prints a colored info message."""
    print(colored(f"  -> {message}", 'blue'))

def print_warning(message):
    """Prints a colored warning message."""
    print(colored(f"  [!] {message}", 'yellow'))

def print_error(message):
    """Prints a colored error message and exits."""
    print(colored(f"[ERROR] {message}", 'red', attrs=['bold']))
    sys.exit(1)

def prepare_row_string(row, include_target=True):
    """Formats a DataFrame row into a string representation for the prompt."""
    row_dict = row.to_dict()
    if not include_target:
        row_dict.pop('Transported', None) # Remove target if not needed
    return json.dumps(row_dict)

# --- Main Script ---
def main():
    # --- 1. Read Data and Context ---
    print_step(f"Reading data from {CSV_FILE} and context from {CONTEXT_FILE}...")
    try:
        df = pd.read_csv(CSV_FILE)
        print_info(f"Successfully read {len(df)} rows from {CSV_FILE}.")
    except FileNotFoundError:
        print_error(f"CSV file not found: {CSV_FILE}")
    except Exception as e:
        print_error(f"Error reading CSV file {CSV_FILE}: {e}")

    try:
        with open(CONTEXT_FILE, 'r') as f:
            context_info = f.read()
        print_info(f"Successfully read context from {CONTEXT_FILE}.")
    except FileNotFoundError:
        print_warning(f"Context file not found: {CONTEXT_FILE}. Proceeding without additional context.")
        context_info = "No additional context provided."
    except Exception as e:
        print_warning(f"Error reading context file {CONTEXT_FILE}: {e}. Proceeding without additional context.")
        context_info = "No additional context provided."

    if len(df) < (NUM_EXAMPLES + NUM_PREDICT):
        print_error(f"Not enough rows in {CSV_FILE} (need {NUM_EXAMPLES + NUM_PREDICT}, found {len(df)}).")

    # --- 2. Select Random Rows ---
    print_step("Selecting random rows for examples and prediction...")
    all_indices = list(df.index)
    random.shuffle(all_indices)

    example_indices = all_indices[:NUM_EXAMPLES]
    predict_indices = all_indices[NUM_EXAMPLES : NUM_EXAMPLES + NUM_PREDICT]

    example_df = df.loc[example_indices]
    predict_df = df.loc[predict_indices]

    print_info(f"Selected {len(example_df)} rows for examples.")
    print_info(f"Selected {len(predict_df)} rows for prediction.")

    # --- 3. Prepare Prompt ---
    print_step("Constructing the prompt for Gemini...")

    example_rows_str = "\n".join([prepare_row_string(row, include_target=True) for _, row in example_df.iterrows()])
    predict_rows_str = "\n".join([prepare_row_string(row, include_target=False) for _, row in predict_df.iterrows()])

    prompt = f"""
Context:
You are an AI assistant analyzing passenger data from the Spaceship Titanic disaster. Your task is to predict whether a passenger was transported to an alternate dimension based on their available data.

{context_info}

Instructions:
1. Analyze the provided passenger data.
2. Predict the 'Transported' status (True or False) for each passenger in the prediction list.
3. Return **ONLY** a JSON object containing a single key "predictions". The value should be a list of JSON objects, where each object has:
    - "PassengerId": The ID of the passenger.
    - "Predicted_Transported": Your prediction (True or False).

Example Data (with known 'Transported' status):
{example_rows_str}

Passengers to Predict 'Transported' Status For:
{predict_rows_str}

Output Format (Strict JSON):
{{
  "predictions": [
    {{ "PassengerId": "...", "Predicted_Transported": ... }},
    {{ "PassengerId": "...", "Predicted_Transported": ... }},
    ...
  ]
}}
"""
    print_info("Prompt constructed successfully.")
    # print(prompt) # Uncomment to debug the prompt

    # --- 4. Call Gemini API ---
    print_step(f"Calling the Gemini API (Model: {GEMINI_MODEL})...")
    api_key = os.environ.get("GEMINI_API_KEY")
    if not api_key:
        print_error("GEMINI_API_KEY environment variable not set.")

    # Using the exact structure from api_calls.txt
    try:
        client = genai.Client(api_key=api_key)
        model = GEMINI_MODEL # Using the variable defined earlier
        contents = [
            types.Content(
                role="user",
                parts=[
                    # Replacing INSERT_INPUT_HERE with our constructed prompt
                    types.Part.from_text(text=prompt),
                ],
            ),
        ]
        generate_content_config = types.GenerateContentConfig(
            response_mime_type="text/plain", # Expecting text that should be JSON
        )

        print_info("Sending request to Gemini...")
        full_response_text = ""
        for chunk in client.models.generate_content_stream(
            model=model,
            contents=contents,
            config=generate_content_config,
        ):
            print(colored(chunk.text, 'green'), end="") # Print streaming response
            full_response_text += chunk.text
        print() # Newline after streaming output
        print_info("Received response from Gemini.")

    except Exception as e:
        print_error(f"Error calling Gemini API: {e}")

    # --- 5. Parse Response and Save ---
    print_step("Parsing the Gemini response and saving results...")
    try:
        # Clean potential markdown/formatting issues if Gemini doesn't return pure JSON
        if full_response_text.strip().startswith("```json"):
            full_response_text = full_response_text.strip()[7:-3].strip()
        elif full_response_text.strip().startswith("```"):
             full_response_text = full_response_text.strip()[3:-3].strip()

        result_data = json.loads(full_response_text)

        if "predictions" not in result_data or not isinstance(result_data["predictions"], list):
            print_error(f"Invalid JSON structure received from Gemini. Expected 'predictions' key with a list value. Received: {full_response_text}")

        predictions_list = result_data["predictions"]

        # --- 6. Calculate Accuracy and Prepare Final Output ---
        print_step("Calculating prediction accuracy...")
        output_predictions_detail = []
        correct_predictions = 0
        total_predictions_evaluated = 0
        original_data_map = predict_df.set_index('PassengerId')['Transported'].to_dict()
        predicted_passenger_ids = set()

        for pred in predictions_list:
            # Basic validation of prediction format
            if not isinstance(pred, dict) or "PassengerId" not in pred or "Predicted_Transported" not in pred:
                 print_warning(f"Skipping invalid prediction format: {pred}")
                 continue
            if not isinstance(pred["Predicted_Transported"], bool):
                 print_warning(f"Skipping prediction with non-boolean value for PassengerId {pred['PassengerId']}: {pred['Predicted_Transported']}")
                 continue

            passenger_id = pred["PassengerId"]
            predicted_transported = pred["Predicted_Transported"]
            predicted_passenger_ids.add(passenger_id)

            if passenger_id not in original_data_map:
                print_warning(f"Received prediction for PassengerId '{passenger_id}' which was not in the original request list. Skipping.")
                continue

            original_transported = original_data_map[passenger_id]

            # Ensure original value is boolean if not NaN
            is_correct = None
            if pd.notna(original_transported):
                original_transported_bool = bool(original_transported)
                is_correct = (predicted_transported == original_transported_bool)
                if is_correct:
                    correct_predictions += 1
                total_predictions_evaluated += 1
            else:
                 print_warning(f"Original 'Transported' value for PassengerId {passenger_id} is missing (NaN). Cannot evaluate accuracy for this row.")


            output_predictions_detail.append({
                "PassengerId": passenger_id,
                "Predicted_Transported": predicted_transported,
                "Original_Transported": original_transported_bool if pd.notna(original_transported) else None,
                "Is_Correct": is_correct,
                # "Prediction_Percentage": 100.0 if predicted_transported else 0.0 # Redundant if saving boolean
            })

        # Calculate overall accuracy
        overall_accuracy = (correct_predictions / total_predictions_evaluated * 100) if total_predictions_evaluated > 0 else 0.0
        print_info(f"Correct Predictions: {correct_predictions}")
        print_info(f"Total Predictions Evaluated: {total_predictions_evaluated}")
        print_info(colored(f"Overall Accuracy: {overall_accuracy:.2f}%", 'magenta', attrs=['bold']))

        # Check if all requested passengers were predicted
        missing_ids = set(original_data_map.keys()) - predicted_passenger_ids
        if missing_ids:
            print_warning(f"Gemini did not return predictions for all requested PassengerIds. Missing: {missing_ids}")
        if len(predictions_list) != len(predict_df):
             print_warning(f"Initial number of predictions received ({len(predictions_list)}) does not match number requested ({len(predict_df)}). Some might be invalid or unexpected.")
        if total_predictions_evaluated != len(predict_df.dropna(subset=['Transported'])):
             print_warning(f"Number of predictions evaluated for accuracy ({total_predictions_evaluated}) does not match number requested with non-NaN 'Transported' ({len(predict_df.dropna(subset=['Transported']))}). Check warnings.")


        print_info(f"Successfully processed {len(output_predictions_detail)} predictions.")

        # --- 7. Save Results to JSON ---
        print_step(f"Saving results to {OUTPUT_JSON}...")
        final_output = {
            "overall_accuracy_percent": round(overall_accuracy, 2),
            "predictions": output_predictions_detail
        }

        try:
            with open(OUTPUT_JSON, 'w') as f:
                json.dump(final_output, f, indent=4)
            print_info(f"Results saved to {OUTPUT_JSON}")
        except Exception as e:
            print_error(f"Error saving results to {OUTPUT_JSON}: {e}")

    except json.JSONDecodeError:
        # Escape the literal curly braces in the f-string
        print_error(f"""Failed to decode JSON response from Gemini. Response was:
---
{{full_response_text}}
---""")
    except Exception as e:
        print_error(f"An unexpected error occurred during parsing or saving: {e}")

    print_step("Script finished successfully.")

if __name__ == "__main__":
    main() 