import pandas as pd
import random
import json
import os
import time
import concurrent.futures
from concurrent.futures import ThreadPoolExecutor
import matplotlib.pyplot as plt
from google import genai
from google.genai import types
from termcolor import colored
import sys

# --- Configuration ---
CSV_FILE = 'train.csv'
CONTEXT_FILE = 'additional_info.txt'
RESULTS_JSON = 'gemini_fewshot_accuracy_results.json'
CHART_FILE = 'gemini_fewshot_accuracy_chart.png'
NUM_PREDICTION_ROWS = 50  # Fixed number of rows to predict
EXAMPLE_COUNTS = [10, 50, 100, 500, 1000]  # Different numbers of examples to test
MAX_WORKERS = 5  # Maximum number of parallel threads (adjust based on your API rate limits)
GEMINI_MODEL = "gemini-2.5-pro-exp-03-25"  # Or your preferred model

# --- Helper Functions ---
def print_step(message):
    """Prints a colored step message."""
    print(colored(f"[STEP] {message}", 'cyan', attrs=['bold']))

def print_info(message):
    """Prints a colored info message."""
    print(colored(f"  -> {message}", 'blue'))

def print_warning(message):
    """Prints a colored warning message."""
    print(colored(f"  [!] {message}", 'yellow'))

def print_error(message):
    """Prints a colored error message and exits."""
    print(colored(f"[ERROR] {message}", 'red', attrs=['bold']))
    sys.exit(1)

def prepare_row_string(row, include_target=True):
    """Formats a DataFrame row into a string representation for the prompt."""
    row_dict = row.to_dict()
    if not include_target:
        row_dict.pop('Transported', None)  # Remove target if not needed
    return json.dumps(row_dict)

def run_experiment(num_examples, prediction_df, example_pool_df, context_info, api_key, experiment_id):
    """
    Run a single experiment with a specific number of examples.
    
    Args:
        num_examples: Number of examples to use for few-shot learning
        prediction_df: DataFrame containing rows to predict (fixed across experiments)
        example_pool_df: DataFrame to sample examples from (different from prediction_df)
        context_info: Context information to include in the prompt
        api_key: Gemini API key
        experiment_id: Unique identifier for this experiment
        
    Returns:
        dict: Results containing accuracy and metadata
    """
    try:
        start_time = time.time()
        
        # 1. Select random examples
        if len(example_pool_df) < num_examples:
            print_warning(f"Not enough examples in pool (requested {num_examples}, available {len(example_pool_df)})")
            num_examples = len(example_pool_df)
        
        # Sample without replacement
        example_df = example_pool_df.sample(n=num_examples, random_state=experiment_id)
        
        # 2. Prepare the prompt
        example_rows_str = "\n".join([prepare_row_string(row, include_target=True) 
                                      for _, row in example_df.iterrows()])
        predict_rows_str = "\n".join([prepare_row_string(row, include_target=False) 
                                      for _, row in prediction_df.iterrows()])

        prompt = f"""
Context:
You are an AI assistant analyzing passenger data from the Spaceship Titanic disaster. Your task is to predict whether a passenger was transported to an alternate dimension based on their available data.

{context_info}

Instructions:
1. Analyze the provided passenger data.
2. Predict the 'Transported' status (True or False) for each passenger in the prediction list.
3. Return **ONLY** a JSON object containing a single key "predictions". The value should be a list of JSON objects, where each object has:
    - "PassengerId": The ID of the passenger.
    - "Predicted_Transported": Your prediction (True or False).

Example Data (with known 'Transported' status) - {num_examples} examples:
{example_rows_str}

Passengers to Predict 'Transported' Status For ({len(prediction_df)} passengers):
{predict_rows_str}

Output Format (Strict JSON):
{{
  "predictions": [
    {{ "PassengerId": "...", "Predicted_Transported": ... }},
    {{ "PassengerId": "...", "Predicted_Transported": ... }},
    ...
  ]
}}
"""

        # 3. Call Gemini API
        client = genai.Client(api_key=api_key)
        contents = [
            types.Content(
                role="user",
                parts=[types.Part.from_text(text=prompt)],
            ),
        ]
        generate_content_config = types.GenerateContentConfig(
            response_mime_type="text/plain",
        )

        print_info(f"Experiment {experiment_id}: Sending request for {num_examples} examples...")
        full_response_text = ""
        try:
            for chunk in client.models.generate_content_stream(
                model=GEMINI_MODEL,
                contents=contents,
                config=generate_content_config,
            ):
                full_response_text += chunk.text
            
            # 4. Parse response
            if full_response_text.strip().startswith("```json"):
                full_response_text = full_response_text.strip()[7:-3].strip()
            elif full_response_text.strip().startswith("```"):
                full_response_text = full_response_text.strip()[3:-3].strip()
            
            result_data = json.loads(full_response_text)
            
            if "predictions" not in result_data or not isinstance(result_data["predictions"], list):
                print_warning(f"Experiment {experiment_id}: Invalid JSON structure received from Gemini")
                return {
                    "experiment_id": experiment_id,
                    "num_examples": num_examples,
                    "success": False,
                    "error": "Invalid JSON structure"
                }
            
            # 5. Calculate accuracy
            predictions_list = result_data["predictions"]
            original_data_map = prediction_df.set_index('PassengerId')['Transported'].to_dict()
            
            correct_predictions = 0
            total_predictions_evaluated = 0
            
            for pred in predictions_list:
                if not isinstance(pred, dict) or "PassengerId" not in pred or "Predicted_Transported" not in pred:
                    continue
                
                passenger_id = pred["PassengerId"]
                predicted_transported = pred["Predicted_Transported"]
                
                if passenger_id not in original_data_map:
                    continue
                
                original_transported = original_data_map[passenger_id]
                
                if pd.notna(original_transported):
                    original_transported_bool = bool(original_transported)
                    is_correct = (predicted_transported == original_transported_bool)
                    if is_correct:
                        correct_predictions += 1
                    total_predictions_evaluated += 1
            
            # Calculate accuracy
            accuracy = (correct_predictions / total_predictions_evaluated * 100) if total_predictions_evaluated > 0 else 0.0
            
            end_time = time.time()
            duration = end_time - start_time
            
            print_info(colored(f"Experiment {experiment_id}: {num_examples} examples → Accuracy: {accuracy:.2f}% ({correct_predictions}/{total_predictions_evaluated}) in {duration:.2f}s", 
                              'green' if accuracy > 70 else 'yellow'))
            
            return {
                "experiment_id": experiment_id,
                "num_examples": num_examples,
                "success": True,
                "accuracy": accuracy,
                "correct_predictions": correct_predictions,
                "total_evaluated": total_predictions_evaluated,
                "duration_seconds": duration
            }
            
        except Exception as e:
            print_warning(f"Experiment {experiment_id}: Error during API call or processing: {str(e)}")
            return {
                "experiment_id": experiment_id,
                "num_examples": num_examples,
                "success": False,
                "error": str(e)
            }
            
    except Exception as e:
        print_warning(f"Experiment {experiment_id}: Unexpected error: {str(e)}")
        return {
            "experiment_id": experiment_id,
            "num_examples": num_examples,
            "success": False,
            "error": str(e)
        }

def plot_accuracy_chart(results):
    """Generate and save a line chart of accuracy vs example count."""
    try:
        # Filter successful experiments and sort by num_examples
        valid_results = [r for r in results if r.get("success", False)]
        valid_results.sort(key=lambda x: x["num_examples"])
        
        if not valid_results:
            print_warning("No valid results to plot")
            return False
            
        example_counts = [r["num_examples"] for r in valid_results]
        accuracies = [r["accuracy"] for r in valid_results]
        
        plt.figure(figsize=(10, 6))
        plt.plot(example_counts, accuracies, marker='o', linestyle='-', linewidth=2, markersize=8)
        
        # Add data labels
        for i, (x, y) in enumerate(zip(example_counts, accuracies)):
            plt.annotate(f"{y:.2f}%", 
                         (x, y), 
                         textcoords="offset points",
                         xytext=(0, 10), 
                         ha='center',
                         fontweight='bold')
        
        plt.title('Effect of Few-Shot Example Count on Prediction Accuracy', fontsize=16)
        plt.xlabel('Number of Examples', fontsize=14)
        plt.ylabel('Accuracy (%)', fontsize=14)
        plt.grid(True, linestyle='--', alpha=0.7)
        
        # Set x-axis to use the actual example counts
        plt.xticks(example_counts)
        
        # Y-axis from 0 to 100%
        plt.ylim(0, 100)
        
        plt.tight_layout()
        plt.savefig(CHART_FILE, dpi=300)
        print_info(f"Chart saved to {CHART_FILE}")
        return True
        
    except Exception as e:
        print_warning(f"Error generating chart: {e}")
        return False

# --- Main Script ---
def main():
    # --- 1. Check environment ---
    print_step("Checking environment...")
    api_key = os.environ.get("GEMINI_API_KEY")
    if not api_key:
        print_error("GEMINI_API_KEY environment variable not set.")
    
    # --- 2. Read Data and Context ---
    print_step(f"Reading data from {CSV_FILE} and context from {CONTEXT_FILE}...")
    try:
        df = pd.read_csv(CSV_FILE)
        print_info(f"Successfully read {len(df)} rows from {CSV_FILE}.")
    except FileNotFoundError:
        print_error(f"CSV file not found: {CSV_FILE}")
    except Exception as e:
        print_error(f"Error reading CSV file {CSV_FILE}: {e}")

    try:
        with open(CONTEXT_FILE, 'r') as f:
            context_info = f.read()
        print_info(f"Successfully read context from {CONTEXT_FILE}.")
    except FileNotFoundError:
        print_warning(f"Context file not found: {CONTEXT_FILE}. Proceeding without additional context.")
        context_info = "No additional context provided."
    except Exception as e:
        print_warning(f"Error reading context file {CONTEXT_FILE}: {e}. Proceeding without additional context.")
        context_info = "No additional context provided."
    
    # --- 3. Select fixed prediction rows ---
    print_step(f"Selecting {NUM_PREDICTION_ROWS} fixed rows for prediction...")
    
    # Ensure we have enough rows
    max_examples = max(EXAMPLE_COUNTS)
    if len(df) < (max_examples + NUM_PREDICTION_ROWS):
        print_error(f"Not enough rows in {CSV_FILE}. Need at least {max_examples + NUM_PREDICTION_ROWS}, found {len(df)}.")
    
    # Create a random, reproducible split
    random.seed(42)  # For reproducibility
    all_indices = list(df.index)
    random.shuffle(all_indices)
    
    # First N rows for prediction (fixed across all experiments)
    prediction_indices = all_indices[:NUM_PREDICTION_ROWS]
    prediction_df = df.loc[prediction_indices]
    
    # Remaining rows as the pool for examples
    example_pool_indices = all_indices[NUM_PREDICTION_ROWS:]
    example_pool_df = df.loc[example_pool_indices]
    
    print_info(f"Selected {len(prediction_df)} rows for prediction.")
    print_info(f"Remaining {len(example_pool_df)} rows available for examples.")
    
    # --- 4. Run experiments in parallel ---
    print_step(f"Running experiments with different example counts: {EXAMPLE_COUNTS}")
    
    results = []
    with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
        # Submit all tasks
        future_to_example_count = {
            executor.submit(
                run_experiment, 
                num_examples, 
                prediction_df, 
                example_pool_df, 
                context_info, 
                api_key, 
                idx
            ): num_examples 
            for idx, num_examples in enumerate(EXAMPLE_COUNTS)
        }
        
        # Process results as they complete
        for future in concurrent.futures.as_completed(future_to_example_count):
            example_count = future_to_example_count[future]
            try:
                result = future.result()
                results.append(result)
            except Exception as e:
                print_warning(f"Experiment with {example_count} examples failed: {e}")
                results.append({
                    "num_examples": example_count,
                    "success": False,
                    "error": str(e)
                })
    
    # --- 5. Save results ---
    print_step("Saving and visualizing results...")
    
    # Save raw results
    try:
        with open(RESULTS_JSON, 'w') as f:
            json.dump({
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                "model": GEMINI_MODEL,
                "prediction_rows": NUM_PREDICTION_ROWS,
                "results": results
            }, f, indent=4)
        print_info(f"Results saved to {RESULTS_JSON}")
    except Exception as e:
        print_warning(f"Error saving results to {RESULTS_JSON}: {e}")
    
    # Create visualization
    plot_success = plot_accuracy_chart(results)
    
    # --- 6. Print summary ---
    print_step("Summary of results:")
    
    # Sort by number of examples for consistent display
    results.sort(key=lambda x: x.get("num_examples", 0))
    
    # Table header
    print(colored(f"{'Examples':^10} | {'Accuracy':^10} | {'Correct':^10} | {'Total':^10} | {'Time (s)':^10} | {'Status':^10}", 
                  'white', attrs=['bold']))
    print(colored(f"{'-'*10:^10} | {'-'*10:^10} | {'-'*10:^10} | {'-'*10:^10} | {'-'*10:^10} | {'-'*10:^10}", 
                  'white'))
    
    # Table rows
    for result in results:
        num_examples = result.get("num_examples", "N/A")
        success = result.get("success", False)
        
        if success:
            accuracy = f"{result.get('accuracy', 0):.2f}%"
            correct = str(result.get("correct_predictions", "N/A"))
            total = str(result.get("total_evaluated", "N/A"))
            duration = f"{result.get('duration_seconds', 0):.2f}"
            status = colored("SUCCESS", 'green')
        else:
            accuracy = "N/A"
            correct = "N/A"
            total = "N/A"
            duration = "N/A"
            status = colored("FAILED", 'red')
        
        print(f"{num_examples:^10} | {accuracy:^10} | {correct:^10} | {total:^10} | {duration:^10} | {status:^10}")
    
    print_step("Benchmark completed!")

if __name__ == "__main__":
    main() 