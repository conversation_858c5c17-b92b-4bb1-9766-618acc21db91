import pandas as pd
import random
import json
import os
import time
import anthropic  # Import Anthropic library
from termcolor import colored
import sys

# --- Configuration ---
CSV_FILE = 'train.csv'
CONTEXT_FILE = 'additional_info.txt'
OUTPUT_JSON = 'sonnet_predictions.json' # Changed output filename
NUM_EXAMPLES = 50
NUM_PREDICT = 50
SONNET_MODEL = "claude-3-7-sonnet-20250219" # Sonnet model name
MAX_TOKENS = 20000 # Max tokens for Sonnet response
# Add thinking parameter configuration if desired
THINKING_CONFIG = {
    "type": "enabled",
    "budget_tokens": 16000
}

# --- Helper Functions ---
def print_step(message):
    """Prints a colored step message."""
    print(colored(f"[STEP] {message}", 'cyan', attrs=['bold']))

def print_info(message):
    """Prints a colored info message."""
    print(colored(f"  -> {message}", 'blue'))

def print_warning(message):
    """Prints a colored warning message."""
    print(colored(f"  [!] {message}", 'yellow'))

def print_error(message):
    """Prints a colored error message and exits."""
    print(colored(f"[ERROR] {message}", 'red', attrs=['bold']))
    sys.exit(1)

def prepare_row_string(row, include_target=True):
    """Formats a DataFrame row into a string representation for the prompt."""
    row_dict = row.to_dict()
    if not include_target:
        row_dict.pop('Transported', None) # Remove target if not needed
    return json.dumps(row_dict)

# --- Main Script ---
def main():
    # --- 1. Read Data and Context --- (Same as before)
    print_step(f"Reading data from {CSV_FILE} and context from {CONTEXT_FILE}...")
    try:
        df = pd.read_csv(CSV_FILE)
        print_info(f"Successfully read {len(df)} rows from {CSV_FILE}.")
    except FileNotFoundError:
        print_error(f"CSV file not found: {CSV_FILE}")
    except Exception as e:
        print_error(f"Error reading CSV file {CSV_FILE}: {e}")

    try:
        with open(CONTEXT_FILE, 'r') as f:
            context_info = f.read()
        print_info(f"Successfully read context from {CONTEXT_FILE}.")
    except FileNotFoundError:
        print_warning(f"Context file not found: {CONTEXT_FILE}. Proceeding without additional context.")
        context_info = "No additional context provided."
    except Exception as e:
        print_warning(f"Error reading context file {CONTEXT_FILE}: {e}. Proceeding without additional context.")
        context_info = "No additional context provided."

    if len(df) < (NUM_EXAMPLES + NUM_PREDICT):
        print_error(f"Not enough rows in {CSV_FILE} (need {NUM_EXAMPLES + NUM_PREDICT}, found {len(df)}).")

    # --- 2. Select Random Rows --- (Same as before)
    print_step("Selecting random rows for examples and prediction...")
    all_indices = list(df.index)
    random.seed(42) # Use a fixed seed for reproducibility if needed
    random.shuffle(all_indices)

    example_indices = all_indices[:NUM_EXAMPLES]
    predict_indices = all_indices[NUM_EXAMPLES : NUM_EXAMPLES + NUM_PREDICT]

    example_df = df.loc[example_indices]
    predict_df = df.loc[predict_indices]

    print_info(f"Selected {len(example_df)} rows for examples.")
    print_info(f"Selected {len(predict_df)} rows for prediction.")

    # --- 3. Prepare Prompt --- (Same logic, slightly different formatting for API)
    print_step("Constructing the prompt for Sonnet...")

    example_rows_str = "\n".join([prepare_row_string(row, include_target=True) for _, row in example_df.iterrows()])
    predict_rows_str = "\n".join([prepare_row_string(row, include_target=False) for _, row in predict_df.iterrows()])

    # Construct the user message content - adding instruction for XML tags
    user_prompt_content = f"""
Context:
You are an AI assistant analyzing passenger data from the Spaceship Titanic disaster. Your task is to predict whether a passenger was transported to an alternate dimension based on their available data.

{context_info}

Instructions:
1. Analyze the provided passenger data.
2. Predict the 'Transported' status (True or False) for each passenger in the prediction list.
3. Return **ONLY** the JSON object containing the predictions, enclosed within `<prediction>` tags. Do not include any other text before or after the tags.
   Example: `<prediction>{{"predictions": [...]}}</prediction>`

JSON Structure:
The JSON object should contain a single key "predictions". The value should be a list of JSON objects, where each object has:
    - "PassengerId": The ID of the passenger.
    - "Predicted_Transported": Your prediction (True or False).

Example Data (with known 'Transported' status):
{example_rows_str}

Passengers to Predict 'Transported' Status For:
{predict_rows_str}

Output Format:
<prediction>
{{
  "predictions": [
    {{ "PassengerId": "...", "Predicted_Transported": ... }},
    {{ "PassengerId": "...", "Predicted_Transported": ... }},
    ...
  ]
}}
</prediction>
"""
    print_info("Prompt constructed successfully.")

    # --- 4. Call Sonnet API ---
    print_step(f"Calling the Sonnet API (Model: {SONNET_MODEL})...")
    api_key = os.environ.get("ANTHROPIC_API_KEY") # Check for Anthropic key
    if not api_key:
        print_error("ANTHROPIC_API_KEY environment variable not set.")

    # Using the structure from api_calls.txt Sonnet example
    try:
        client = anthropic.Anthropic(api_key=api_key) # Initialize client

        print_info("Sending request to Sonnet...")
        start_time = time.time()
        response = client.messages.create(
            model=SONNET_MODEL,
            max_tokens=MAX_TOKENS,
            thinking=THINKING_CONFIG, # Include thinking config
            messages=[{
                "role": "user",
                "content": user_prompt_content # Pass the constructed prompt content
            }]
        )
        end_time = time.time()
        duration = end_time - start_time
        print_info(f"Received response from Sonnet in {duration:.2f}s.")

        # Extract the text content - likely the first text block
        full_response_text = ""
        if response.content and isinstance(response.content, list):
            for block in response.content:
                if hasattr(block, 'type') and block.type == 'text':
                    full_response_text = block.text
                    break # Use the first text block found
        
        if not full_response_text:
            print_warning("No text content found in the Sonnet response.")
            # Attempt to show the raw response for debugging
            try:
                print_warning(f"Raw response: {response}")
            except:
                pass 
            print_error("Failed to extract text prediction from Sonnet response.")
            

    except anthropic.APIConnectionError as e:
        print_error(f"Sonnet API Connection Error: {e}")
    except anthropic.RateLimitError as e:
         print_error(f"Sonnet API Rate Limit Error: {e}")
    except anthropic.APIStatusError as e:
         print_error(f"Sonnet API Status Error (Status: {e.status_code}): {e.response}")
    except Exception as e:
        print_error(f"Error calling Sonnet API: {e}")

    # --- 5. Parse Response and Save --- (Modified to extract from tags)
    print_step("Parsing the Sonnet response and saving results...")
    try:
        # Extract content between <prediction> tags
        start_tag = "<prediction>"
        end_tag = "</prediction>"
        start_index = full_response_text.find(start_tag)
        end_index = full_response_text.find(end_tag)

        if start_index != -1 and end_index != -1:
            # Adjust start index to be after the tag itself
            start_index += len(start_tag)
            json_text = full_response_text[start_index:end_index].strip()
            print_info("Extracted text between <prediction> tags.")
        else:
            # Fallback: try cleaning markdown if tags not found (optional, can be removed)
            print_warning("Could not find <prediction> tags. Attempting markdown cleaning...")
            if full_response_text.strip().startswith("```json"):
                json_text = full_response_text.strip()[7:-3].strip()
            elif full_response_text.strip().startswith("```"):
                 json_text = full_response_text.strip()[3:-3].strip()
            else:
                 json_text = full_response_text # Assume raw text if no tags/markdown
            
            if not json_text: # Check if json_text is empty after attempted cleaning
                 print_error(f"Failed to extract JSON content from response. Raw response:\n---\n{full_response_text}\n---")


        result_data = json.loads(json_text)

        if "predictions" not in result_data or not isinstance(result_data["predictions"], list):
            print_error(f"Invalid JSON structure received from Sonnet. Expected 'predictions' key with a list value. Parsed text: {json_text}")

        predictions_list = result_data["predictions"]

        # --- 6. Calculate Accuracy and Prepare Final Output --- (Same as before)
        print_step("Calculating prediction accuracy...")
        output_predictions_detail = []
        correct_predictions = 0
        total_predictions_evaluated = 0
        original_data_map = predict_df.set_index('PassengerId')['Transported'].to_dict()
        predicted_passenger_ids = set()

        for pred in predictions_list:
            # Basic validation of prediction format
            if not isinstance(pred, dict) or "PassengerId" not in pred or "Predicted_Transported" not in pred:
                 print_warning(f"Skipping invalid prediction format: {pred}")
                 continue
            if not isinstance(pred["Predicted_Transported"], bool):
                 print_warning(f"Skipping prediction with non-boolean value for PassengerId {pred['PassengerId']}: {pred['Predicted_Transported']}")
                 continue

            passenger_id = pred["PassengerId"]
            predicted_transported = pred["Predicted_Transported"]
            predicted_passenger_ids.add(passenger_id)

            if passenger_id not in original_data_map:
                print_warning(f"Received prediction for PassengerId '{passenger_id}' which was not in the original request list. Skipping.")
                continue

            original_transported = original_data_map[passenger_id]

            # Ensure original value is boolean if not NaN
            is_correct = None
            if pd.notna(original_transported):
                original_transported_bool = bool(original_transported)
                is_correct = (predicted_transported == original_transported_bool)
                if is_correct:
                    correct_predictions += 1
                total_predictions_evaluated += 1
            else:
                 print_warning(f"Original 'Transported' value for PassengerId {passenger_id} is missing (NaN). Cannot evaluate accuracy for this row.")


            output_predictions_detail.append({
                "PassengerId": passenger_id,
                "Predicted_Transported": predicted_transported,
                "Original_Transported": original_transported_bool if pd.notna(original_transported) else None,
                "Is_Correct": is_correct,
            })

        # Calculate overall accuracy
        overall_accuracy = (correct_predictions / total_predictions_evaluated * 100) if total_predictions_evaluated > 0 else 0.0
        print_info(f"Correct Predictions: {correct_predictions}")
        print_info(f"Total Predictions Evaluated: {total_predictions_evaluated}")
        print_info(colored(f"Overall Accuracy: {overall_accuracy:.2f}%", 'magenta', attrs=['bold']))

        # Check if all requested passengers were predicted
        missing_ids = set(original_data_map.keys()) - predicted_passenger_ids
        if missing_ids:
            print_warning(f"Sonnet did not return predictions for all requested PassengerIds. Missing: {missing_ids}")
        if len(predictions_list) != len(predict_df):
             print_warning(f"Initial number of predictions received ({len(predictions_list)}) does not match number requested ({len(predict_df)}). Some might be invalid or unexpected.")
        if total_predictions_evaluated != len(predict_df.dropna(subset=['Transported'])):
             print_warning(f"Number of predictions evaluated for accuracy ({total_predictions_evaluated}) does not match number requested with non-NaN 'Transported' ({len(predict_df.dropna(subset=['Transported']))}). Check warnings.")


        print_info(f"Successfully processed {len(output_predictions_detail)} predictions.")

        # --- 7. Save Results to JSON --- (Same structure, different filename)
        print_step(f"Saving results to {OUTPUT_JSON}...")
        final_output = {
            "model": SONNET_MODEL,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "overall_accuracy_percent": round(overall_accuracy, 2),
            "predictions": output_predictions_detail
        }

        try:
            with open(OUTPUT_JSON, 'w') as f:
                json.dump(final_output, f, indent=4)
            print_info(f"Results saved to {OUTPUT_JSON}")
        except Exception as e:
            print_error(f"Error saving results to {OUTPUT_JSON}: {e}")

    except json.JSONDecodeError:
        # Use triple quotes for multi-line f-string
        print_error(f"""Failed to decode JSON response from Sonnet. Response was:
---
{full_response_text}
---""")
    except Exception as e:
        print_error(f"An unexpected error occurred during parsing or saving: {e}")

    print_step("Script finished successfully.")

if __name__ == "__main__":
    main() 