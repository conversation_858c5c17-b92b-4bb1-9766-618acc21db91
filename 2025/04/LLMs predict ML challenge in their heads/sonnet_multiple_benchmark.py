import pandas as pd
import random
import json
import os
import time
import concurrent.futures
from concurrent.futures import ThreadPoolExecutor
import matplotlib.pyplot as plt
import anthropic # Changed import
from termcolor import colored
import sys

# --- Configuration ---
CSV_FILE = 'train.csv'
CONTEXT_FILE = 'additional_info.txt'
RESULTS_JSON = 'sonnet_fewshot_accuracy_results.json' # Renamed
CHART_FILE = 'sonnet_fewshot_accuracy_chart.png'     # Renamed
NUM_PREDICTION_ROWS = 50  # Fixed number of rows to predict
EXAMPLE_COUNTS = [10, 50, 100, 500, 1000]  # Different numbers of examples to test
MAX_WORKERS = 5  # Maximum number of parallel threads (adjust based on your API rate limits)
SONNET_MODEL = "claude-3-7-sonnet-20250219" # Changed model
MAX_TOKENS = 20000 # Max tokens for Sonnet response
THINKING_CONFIG = {
    "type": "enabled",
    "budget_tokens": 16000
}

# --- Helper Functions (print_step, print_info, print_warning, print_error, prepare_row_string) remain the same ---
def print_step(message):
    """Prints a colored step message."""
    print(colored(f"[STEP] {message}", 'cyan', attrs=['bold']))

def print_info(message):
    """Prints a colored info message."""
    print(colored(f"  -> {message}", 'blue'))

def print_warning(message):
    """Prints a colored warning message."""
    print(colored(f"  [!] {message}", 'yellow'))

def print_error(message):
    """Prints a colored error message and exits."""
    print(colored(f"[ERROR] {message}", 'red', attrs=['bold']))
    sys.exit(1)

def prepare_row_string(row, include_target=True):
    """Formats a DataFrame row into a string representation for the prompt."""
    row_dict = row.to_dict()
    if not include_target:
        row_dict.pop('Transported', None)  # Remove target if not needed
    return json.dumps(row_dict)
# --- end helpers ---

def run_experiment(num_examples, prediction_df, example_pool_df, context_info, api_key, experiment_id):
    """
    Run a single experiment with a specific number of examples for Sonnet.
    Args are the same as the Gemini version.
    Returns dict with results.
    """
    try:
        start_time = time.time()
        
        # 1. Select random examples (Same as Gemini version)
        if len(example_pool_df) < num_examples:
            print_warning(f"Not enough examples in pool (requested {num_examples}, available {len(example_pool_df)})")
            num_examples = len(example_pool_df)
        example_df = example_pool_df.sample(n=num_examples, random_state=experiment_id)
        
        # 2. Prepare the prompt (Adapted for Sonnet, requesting <prediction> tags)
        example_rows_str = "\n".join([prepare_row_string(row, include_target=True) for _, row in example_df.iterrows()])
        predict_rows_str = "\n".join([prepare_row_string(row, include_target=False) for _, row in prediction_df.iterrows()])

        user_prompt_content = f"""
Context:
You are an AI assistant analyzing passenger data from the Spaceship Titanic disaster. Your task is to predict whether a passenger was transported to an alternate dimension based on their available data.

{context_info}

Instructions:
1. Analyze the provided passenger data.
2. Predict the 'Transported' status (True or False) for each passenger in the prediction list.
3. Return **ONLY** the JSON object containing the predictions, enclosed within `<prediction>` tags. Do not include any other text before or after the tags.
   Example: `<prediction>{{"predictions": [...]}}</prediction>`

JSON Structure:
The JSON object should contain a single key "predictions". The value should be a list of JSON objects, where each object has:
    - "PassengerId": The ID of the passenger.
    - "Predicted_Transported": Your prediction (True or False).

Example Data (with known 'Transported' status) - {num_examples} examples:
{example_rows_str}

Passengers to Predict 'Transported' Status For ({len(prediction_df)} passengers):
{predict_rows_str}

Output Format:
<prediction>
{{
  "predictions": [
    {{ "PassengerId": "...", "Predicted_Transported": ... }},
    {{ "PassengerId": "...", "Predicted_Transported": ... }},
    ...
  ]
}}
</prediction>
"""

        # 3. Call Sonnet API (Adapted for Anthropic client)
        client = anthropic.Anthropic(api_key=api_key)
        print_info(f"Experiment {experiment_id}: Sending request for {num_examples} examples to Sonnet...")
        full_response_text = ""
        try:
            response = client.messages.create(
                model=SONNET_MODEL,
                max_tokens=MAX_TOKENS,
                thinking=THINKING_CONFIG, 
                messages=[{
                    "role": "user",
                    "content": user_prompt_content
                }]
            )
            
            # Extract text content
            if response.content and isinstance(response.content, list):
                for block in response.content:
                    if hasattr(block, 'type') and block.type == 'text':
                        full_response_text = block.text
                        break
            
            if not full_response_text:
                 print_warning(f"Experiment {experiment_id}: No text content found in Sonnet response.")
                 return {
                    "experiment_id": experiment_id,
                    "num_examples": num_examples,
                    "success": False,
                    "error": "No text content found in response"
                 }

            # 4. Parse response (Using tag extraction)
            start_tag = "<prediction>"
            end_tag = "</prediction>"
            start_index = full_response_text.find(start_tag)
            end_index = full_response_text.find(end_tag)

            if start_index != -1 and end_index != -1:
                start_index += len(start_tag)
                json_text = full_response_text[start_index:end_index].strip()
            else:
                print_warning(f"Experiment {experiment_id}: Could not find <prediction> tags. Raw text: {full_response_text[:500]}...") # Log snippet
                # Attempt markdown cleaning as fallback
                if full_response_text.strip().startswith("```json"):
                    json_text = full_response_text.strip()[7:-3].strip()
                elif full_response_text.strip().startswith("```"):
                    json_text = full_response_text.strip()[3:-3].strip()
                else:
                    json_text = full_response_text # Last resort: try raw text
                
                if not json_text: # Check if empty
                    return {
                        "experiment_id": experiment_id,
                        "num_examples": num_examples,
                        "success": False,
                        "error": "Failed to extract JSON content after tag/markdown check."
                     }
            
            result_data = json.loads(json_text)
            
            if "predictions" not in result_data or not isinstance(result_data["predictions"], list):
                print_warning(f"Experiment {experiment_id}: Invalid JSON structure received from Sonnet (Parsed: {json_text[:500]}...)")
                return {
                    "experiment_id": experiment_id,
                    "num_examples": num_examples,
                    "success": False,
                    "error": "Invalid JSON structure"
                }
            
            # 5. Calculate accuracy (Same logic as Gemini version)
            predictions_list = result_data["predictions"]
            original_data_map = prediction_df.set_index('PassengerId')['Transported'].to_dict()
            correct_predictions = 0
            total_predictions_evaluated = 0
            for pred in predictions_list:
                if not isinstance(pred, dict) or "PassengerId" not in pred or "Predicted_Transported" not in pred:
                    continue
                passenger_id = pred["PassengerId"]
                predicted_transported = pred["Predicted_Transported"]
                if passenger_id not in original_data_map:
                    continue
                original_transported = original_data_map[passenger_id]
                if pd.notna(original_transported):
                    original_transported_bool = bool(original_transported)
                    is_correct = (predicted_transported == original_transported_bool)
                    if is_correct:
                        correct_predictions += 1
                    total_predictions_evaluated += 1
            accuracy = (correct_predictions / total_predictions_evaluated * 100) if total_predictions_evaluated > 0 else 0.0
            
            end_time = time.time()
            duration = end_time - start_time
            print_info(colored(f"Experiment {experiment_id}: {num_examples} examples → Accuracy: {accuracy:.2f}% ({correct_predictions}/{total_predictions_evaluated}) in {duration:.2f}s", 'green' if accuracy > 70 else 'yellow'))
            
            return {
                "experiment_id": experiment_id,
                "num_examples": num_examples,
                "success": True,
                "accuracy": accuracy,
                "correct_predictions": correct_predictions,
                "total_evaluated": total_predictions_evaluated,
                "duration_seconds": duration
            }
            
        except json.JSONDecodeError as e:
             print_warning(f"Experiment {experiment_id}: JSON Decode Error for {num_examples} examples: {e}. Text was: {json_text[:500]}...")
             return {"experiment_id": experiment_id, "num_examples": num_examples, "success": False, "error": f"JSONDecodeError: {e}"}
        except anthropic.APIConnectionError as e:
            print_warning(f"Experiment {experiment_id}: Sonnet API Connection Error: {e}")
            return {"experiment_id": experiment_id, "num_examples": num_examples, "success": False, "error": f"APIConnectionError: {e}"}
        except anthropic.RateLimitError as e:
             print_warning(f"Experiment {experiment_id}: Sonnet API Rate Limit Error: {e}")
             return {"experiment_id": experiment_id, "num_examples": num_examples, "success": False, "error": f"RateLimitError: {e}"}
        except anthropic.APIStatusError as e:
             print_warning(f"Experiment {experiment_id}: Sonnet API Status Error (Status: {e.status_code}): {e.response}")
             return {"experiment_id": experiment_id, "num_examples": num_examples, "success": False, "error": f"APIStatusError {e.status_code}"}
        except Exception as e:
            print_warning(f"Experiment {experiment_id}: Error during API call or processing: {str(e)}")
            return {"experiment_id": experiment_id, "num_examples": num_examples, "success": False, "error": str(e)}
            
    except Exception as e:
        # Catch errors during setup (e.g., sampling)
        print_warning(f"Experiment {experiment_id}: Unexpected setup error: {str(e)}")
        return {"experiment_id": experiment_id, "num_examples": num_examples, "success": False, "error": str(e)}

# --- plot_accuracy_chart function remains the same ---
def plot_accuracy_chart(results):
    """Generate and save a line chart of accuracy vs example count."""
    try:
        # Filter successful experiments and sort by num_examples
        valid_results = [r for r in results if r.get("success", False)]
        valid_results.sort(key=lambda x: x["num_examples"])
        
        if not valid_results:
            print_warning("No valid results to plot")
            return False
            
        example_counts = [r["num_examples"] for r in valid_results]
        accuracies = [r["accuracy"] for r in valid_results]
        
        plt.figure(figsize=(10, 6))
        plt.plot(example_counts, accuracies, marker='o', linestyle='-', linewidth=2, markersize=8)
        
        # Add data labels
        for i, (x, y) in enumerate(zip(example_counts, accuracies)):
            plt.annotate(f"{y:.2f}%", 
                         (x, y), 
                         textcoords="offset points",
                         xytext=(0, 10), 
                         ha='center',
                         fontweight='bold')
        
        plt.title('Sonnet: Effect of Few-Shot Example Count on Prediction Accuracy', fontsize=16) # Updated title
        plt.xlabel('Number of Examples', fontsize=14)
        plt.ylabel('Accuracy (%)', fontsize=14)
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.xticks(example_counts)
        plt.ylim(0, 100)
        plt.tight_layout()
        plt.savefig(CHART_FILE, dpi=300)
        print_info(f"Chart saved to {CHART_FILE}")
        return True
        
    except Exception as e:
        print_warning(f"Error generating chart: {e}")
        return False
# --- end plot_accuracy_chart ---

# --- Main Script ---
def main():
    # --- 1. Check environment ---
    print_step("Checking environment...")
    api_key = os.environ.get("ANTHROPIC_API_KEY") # Check for Anthropic key
    if not api_key:
        print_error("ANTHROPIC_API_KEY environment variable not set.")
    
    # --- 2. Read Data and Context (Same as Gemini version) ---
    print_step(f"Reading data from {CSV_FILE} and context from {CONTEXT_FILE}...")
    try:
        df = pd.read_csv(CSV_FILE)
        print_info(f"Successfully read {len(df)} rows from {CSV_FILE}.")
    except FileNotFoundError:
        print_error(f"CSV file not found: {CSV_FILE}")
    except Exception as e:
        print_error(f"Error reading CSV file {CSV_FILE}: {e}")
    try:
        with open(CONTEXT_FILE, 'r') as f:
            context_info = f.read()
        print_info(f"Successfully read context from {CONTEXT_FILE}.")
    except FileNotFoundError:
        print_warning(f"Context file not found: {CONTEXT_FILE}. Proceeding without additional context.")
        context_info = "No additional context provided."
    except Exception as e:
        print_warning(f"Error reading context file {CONTEXT_FILE}: {e}. Proceeding without additional context.")
        context_info = "No additional context provided."
    
    # --- 3. Select fixed prediction rows (Same as Gemini version) ---
    print_step(f"Selecting {NUM_PREDICTION_ROWS} fixed rows for prediction...")
    max_examples = max(EXAMPLE_COUNTS)
    if len(df) < (max_examples + NUM_PREDICTION_ROWS):
        print_error(f"Not enough rows in {CSV_FILE}. Need at least {max_examples + NUM_PREDICTION_ROWS}, found {len(df)}.")
    random.seed(42)
    all_indices = list(df.index)
    random.shuffle(all_indices)
    prediction_indices = all_indices[:NUM_PREDICTION_ROWS]
    prediction_df = df.loc[prediction_indices]
    example_pool_indices = all_indices[NUM_PREDICTION_ROWS:]
    example_pool_df = df.loc[example_pool_indices]
    print_info(f"Selected {len(prediction_df)} rows for prediction.")
    print_info(f"Remaining {len(example_pool_df)} rows available for examples.")
    
    # --- 4. Run experiments in parallel (Same logic, calls adapted run_experiment) ---
    print_step(f"Running experiments with different example counts for Sonnet: {EXAMPLE_COUNTS}")
    results = []
    with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
        future_to_example_count = {
            executor.submit(
                run_experiment, 
                num_examples, 
                prediction_df, 
                example_pool_df, 
                context_info, 
                api_key, 
                idx
            ): num_examples 
            for idx, num_examples in enumerate(EXAMPLE_COUNTS)
        }
        for future in concurrent.futures.as_completed(future_to_example_count):
            example_count = future_to_example_count[future]
            try:
                result = future.result()
                results.append(result)
            except Exception as e:
                print_warning(f"Experiment with {example_count} examples failed: {e}")
                results.append({"num_examples": example_count, "success": False, "error": str(e)})
    
    # --- 5. Save results (Adapted filename and model name) ---
    print_step("Saving and visualizing Sonnet results...")
    try:
        with open(RESULTS_JSON, 'w') as f:
            json.dump({
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                "model": SONNET_MODEL,
                "prediction_rows": NUM_PREDICTION_ROWS,
                "results": results
            }, f, indent=4)
        print_info(f"Results saved to {RESULTS_JSON}")
    except Exception as e:
        print_warning(f"Error saving results to {RESULTS_JSON}: {e}")
    
    # --- 6. Create visualization & Print summary (Same logic) ---
    plot_success = plot_accuracy_chart(results)
    print_step("Summary of Sonnet results:")
    results.sort(key=lambda x: x.get("num_examples", 0))
    print(colored(f"{'Examples':^10} | {'Accuracy':^10} | {'Correct':^10} | {'Total':^10} | {'Time (s)':^10} | {'Status':^10}", 'white', attrs=['bold']))
    print(colored(f"{'-'*10:^10} | {'-'*10:^10} | {'-'*10:^10} | {'-'*10:^10} | {'-'*10:^10} | {'-'*10:^10}", 'white'))
    for result in results:
        num_examples = result.get("num_examples", "N/A")
        success = result.get("success", False)
        if success:
            accuracy = f"{result.get('accuracy', 0):.2f}%"
            correct = str(result.get("correct_predictions", "N/A"))
            total = str(result.get("total_evaluated", "N/A"))
            duration = f"{result.get('duration_seconds', 0):.2f}"
            status = colored("SUCCESS", 'green')
        else:
            accuracy = "N/A"
            correct = "N/A"
            total = "N/A"
            duration = "N/A"
            status = colored("FAILED", 'red')
        print(f"{num_examples:^10} | {accuracy:^10} | {correct:^10} | {total:^10} | {duration:^10} | {status:^10}")
    
    print_step("Sonnet benchmark completed!")

if __name__ == "__main__":
    main() 