import pygame
import sys
import random

# Initialize Pygame
pygame.init()

# Screen settings
CELL_SIZE = 24
GRID_WIDTH = 28
GRID_HEIGHT = 31
WIDTH = GRID_WIDTH * CELL_SIZE
HEIGHT = GRID_HEIGHT * CELL_SIZE
screen = pygame.display.set_mode((WIDTH, HEIGHT))
pygame.display.set_caption("Pacman Clone")

# Colors
BLACK = (0, 0, 0)
BLUE = (0, 0, 200)
YELLOW = (255, 255, 0)
WHITE = (255, 255, 255)
RED = (255, 0, 0)
PINK = (255, 182, 193)
CYAN = (0, 255, 255)
ORANGE = (255, 165, 0)

clock = pygame.time.Clock()

# Maze layout: 0 = wall, 1 = pellet, 2 = empty space (already eaten pellet)
MAZE_LAYOUT = [
    "0000000000000000000000000000",
    "0111111110111111111011111110",
    "0100000010100000010100000010",
    "0122222010122222010122222010",
    "0100000010100000010100000010",
    "0111111110111111111011111110",
    "0100000010100000010100000010",
    "0111111111111111111111111110",
    "0000000010000000100000000000",
    "0000000010000000100000000000",
    "0000000010000000100000000000",
    "0111111111111111111111111110",
    "0100000010100000010100000010",
    "0122222010122222010122222010",
    "0111111110111111111011111110",
    "0000000000000000000000000000",
    "0111111110111111111011111110",
    "0100000010100000010100000010",
    "0122222010122222010122222010",
    "0111111110111111111011111110",
    "0100000010100000010100000010",
    "0111111111111111111111111110",
    "0000000000000000000000000000",
    "0111111110111111111011111110",
    "0100000010100000010100000010",
    "0122222010122222010122222010",
    "0100000010100000010100000010",
    "0111111110111111111011111110",
    "0000000000000000000000000000",
    "0000000000000000000000000000",
    "0000000000000000000000000000"
]

def create_maze():
    maze = []
    for row in MAZE_LAYOUT:
        maze.append([int(char) for char in row])
    return maze

maze = create_maze()

class Player:
    def __init__(self, x, y):
        self.x = x
        self.y = y
        self.direction = (0, 0)
        self.next_direction = (0, 0)
        self.speed = 2
        self.radius = CELL_SIZE // 2 - 2

    def set_direction(self, dx, dy):
        self.next_direction = (dx, dy)

    def update(self):
        # Check if next_direction is valid to move into
        nx, ny = self.x + self.next_direction[0], self.y + self.next_direction[1]
        if is_valid_position(nx, ny):
            self.direction = self.next_direction

        # Move in current direction if possible
        nx, ny = self.x + self.direction[0], self.y + self.direction[1]
        if is_valid_position(nx, ny):
            self.x = nx
            self.y = ny

        # Eat pellet
        grid_x = int((self.x + CELL_SIZE //2) // CELL_SIZE)
        grid_y = int((self.y + CELL_SIZE //2) // CELL_SIZE)
        if maze[grid_y][grid_x] == 1:
            maze[grid_y][grid_x] = 2

    def draw(self, surface):
        pygame.draw.circle(surface, YELLOW, (int(self.x)+CELL_SIZE//2, int(self.y)+CELL_SIZE//2), self.radius)

class Ghost:
    def __init__(self, x, y, color):
        self.x = x
        self.y = y
        self.color = color
        self.direction = random.choice([(CELL_SIZE//6,0), (-CELL_SIZE//6,0), (0,CELL_SIZE//6), (0,-CELL_SIZE//6)])

    def update(self):
        nx, ny = self.x + self.direction[0], self.y + self.direction[1]
        if is_valid_position(nx, ny):
            self.x = nx
            self.y = ny
            # Randomly change direction at intersections
            if random.random() < 0.05:
                self.change_direction()
        else:
            self.change_direction()

    def change_direction(self):
        directions = [(CELL_SIZE//6,0), (-CELL_SIZE//6,0), (0,CELL_SIZE//6), (0,-CELL_SIZE//6)]
        random.shuffle(directions)
        for dir in directions:
            nx, ny = self.x + dir[0], self.y + dir[1]
            if is_valid_position(nx, ny):
                self.direction = dir
                return

    def draw(self, surface):
        pygame.draw.circle(surface, self.color, (int(self.x)+CELL_SIZE//2, int(self.y)+CELL_SIZE//2), CELL_SIZE//2 - 2)

def is_valid_position(x, y):
    grid_x = int((x + CELL_SIZE //2) // CELL_SIZE)
    grid_y = int((y + CELL_SIZE //2) // CELL_SIZE)
    if 0 <= grid_y < GRID_HEIGHT and 0 <= grid_x < GRID_WIDTH:
        return maze[grid_y][grid_x] != 0
    return False

def draw_maze(surface):
    for y in range(GRID_HEIGHT):
        for x in range(GRID_WIDTH):
            rect = pygame.Rect(x*CELL_SIZE, y*CELL_SIZE, CELL_SIZE, CELL_SIZE)
            if maze[y][x] == 0:
                pygame.draw.rect(surface, BLUE, rect)
            elif maze[y][x] == 1:
                pygame.draw.circle(surface, WHITE, rect.center, 3)

def main():
    player = Player(14*CELL_SIZE, 23*CELL_SIZE)

    ghosts = [
        Ghost(13*CELL_SIZE, 11*CELL_SIZE, RED),
        Ghost(14*CELL_SIZE, 11*CELL_SIZE, PINK),
        Ghost(13*CELL_SIZE, 12*CELL_SIZE, CYAN),
        Ghost(14*CELL_SIZE, 12*CELL_SIZE, ORANGE),
    ]

    score = 0
    font = pygame.font.SysFont("Arial", 20)

    running = True
    while running:
        clock.tick(60)
        screen.fill(BLACK)
        draw_maze(screen)

        # Event handling
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running=False
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_LEFT:
                    player.set_direction(-player.speed, 0)
                elif event.key == pygame.K_RIGHT:
                    player.set_direction(player.speed, 0)
                elif event.key == pygame.K_UP:
                    player.set_direction(0, -player.speed)
                elif event.key == pygame.K_DOWN:
                    player.set_direction(0, player.speed)

        # Update Player
        player.update()

        # Ghosts AI
        for ghost in ghosts:
            ghost.update()
            # Check collision with player
            dist = ((player.x - ghost.x)**2 + (player.y - ghost.y)**2 )**0.5
            if dist < CELL_SIZE // 2:
                # Game over
                running = False

        # Calculate score
        score = sum(row.count(2) for row in maze)

        # Draw everything
        player.draw(screen)
        for ghost in ghosts:
            ghost.draw(screen)
        score_text = font.render(f"Score: {score}", True, WHITE)
        screen.blit(score_text, (10, HEIGHT - 30))

        pygame.display.flip()

    pygame.quit()
    sys.exit()

if __name__ == "__main__":
    main()