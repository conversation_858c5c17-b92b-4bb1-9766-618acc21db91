from openai import OpenAI
import os
import re

client = OpenAI(
  base_url="https://openrouter.ai/api/v1",
  api_key=os.getenv("OPENROUTER_API_KEY"),
)

completion = client.chat.completions.create(
  extra_body={},
  model="openrouter/quasar-alpha",
  messages=[
    {
      "role": "system",
      "content": [
        {
          "type": "text",
          "text": "You are a Python developer. Return the full Python code "
        }
      ],
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "Create a fully featured pacman game without using any external assets using pygame. Make a plan first. Then return the full Python code "
        }
      ]
    }
  ]
)

python_code = completion.choices[0].message.content
print(python_code)

# Save the content to a Python file
try:
    # Use regex to find the code block, allowing for optional whitespace
    match = re.search(r"```python\s*\n(.*?)\s*```", python_code, re.DOTALL)
    if match:
        extracted_code = match.group(1).strip()
        with open("output.py", "w", encoding="utf-8") as f:
            f.write(extracted_code)
        print("Python code extracted and saved to output.py")
    else:
        print("Warning: Could not find the expected ```python ... ``` block in the output.")
        # Optional: Save the raw output anyway or handle the error differently
        # with open("output_raw.txt", "w", encoding="utf-8") as f:
        #     f.write(python_code)
        # print("Raw output saved to output_raw.txt")

except Exception as e:
    print(f"Error processing or saving file: {e}")