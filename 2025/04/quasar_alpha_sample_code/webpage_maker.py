from openai import OpenAI
import os

client = OpenAI(
  base_url="https://openrouter.ai/api/v1",
  api_key=os.getenv("OPENROUTER_API_KEY"),
)

completion = client.chat.completions.create(
  extra_body={},
  model="openrouter/quasar-alpha",
  messages=[
    {
      "role": "system",
      "content": [
        {
          "type": "text",
          "text": "You are a web developer. return the full html code in between <website> and </website> tags."
        }
      ],
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "Create the most beautiful website in the world. but first make a plan and deisng it in your head. then return the full html code in between <website> and </website> tags."
        }
      ]
    }
  ]
)

website_content = completion.choices[0].message.content
print(website_content)

# Save the content to an HTML file
try:
    with open("output.html", "w", encoding="utf-8") as f:
        # Basic check to ensure we have the expected tags, though the LLM should provide them
        if website_content.strip().startswith("<website>") and website_content.strip().endswith("</website>"):
             # Extract content between tags if needed, or save as is
             # For now, saving the whole string including the tags as requested
             f.write(website_content)
        else:
            # Fallback if tags are missing - wrap the content
            print("Warning: LLM output did not contain the expected <website> tags. Wrapping content.")
            f.write(f"<website>\n{website_content}\n</website>")
    print("Website content saved to output.html")
except Exception as e:
    print(f"Error saving file: {e}")