# interactive_code_refiner.py
from itertools import tee
import os
import re
from openai import OpenAI

# --- Configuration ---
OUTPUT_FILENAME = "final_code.py"
MODEL_NAME = "openrouter/quasar-alpha" # Or your preferred model
API_KEY = os.getenv("OPENROUTER_API_KEY")
BASE_URL = "https://openrouter.ai/api/v1"
# --- End Configuration ---

if not API_KEY:
    print("Error: OPENROUTER_API_KEY environment variable not set.")
    exit(1)

client = OpenAI(
    base_url=BASE_URL,
    api_key=API_KEY,
)

def call_llm(messages):
    """Sends messages to the LLM and attempts to extract Python code."""
    print("\n🤖 Calling LLM...")
    try:
        completion = client.chat.completions.create(
            model=MODEL_NAME,
            messages=messages
        )
        response_content = completion.choices[0].message.content
        print("✅ LLM Response Received.")
        # print("\n--- Raw LLM Response ---\n", response_content, "\n--- End Raw Response ---") # Uncomment for debugging

        # Extract code using regex (allowing for whitespace variations)
        match = re.search(r"```python\s*\n(.*?)\s*```", response_content, re.DOTALL)
        if match:
            extracted_code = match.group(1).strip()
            return extracted_code
        else:
            print("⚠️ Warning: Could not find ```python ... ``` block in the response.")
            # Fallback: maybe the whole response is code? Or return raw?
            # For now, let's return the raw response if no block found,
            # assuming it might be just the code without markers in some cases.
            # A more robust solution might try other checks.
            print("ℹ️ Attempting to use the full response as code.")
            return response_content.strip()

    except Exception as e:
        print(f"❌ Error calling LLM or processing response: {e}")
        return None

def save_code_to_file(code, filename):
    """Saves the provided code to the specified filename."""
    try:
        with open(filename, "w", encoding="utf-8") as f:
            f.write(code)
        print(f"✅ Code automatically saved to {filename}")
        return True
    except Exception as e:
        print(f"❌ Error saving code to {filename}: {e}")
        return False

def main():
    print("--- Interactive Code Refiner ---")

    # 1. Get Initial Idea
    initial_idea = input("Enter your initial coding idea: ")
    if not initial_idea:
        print("No idea provided. Exiting.")
        return

    # 2. Initial Code Generation
    system_prompt = {
        "role": "system",
        "content": f"You are a Python programming assistant. Generate complete, runnable Python code based on the user's request. Always wrap the final Python code within ```python\\n...\\n```."
    }
    initial_user_prompt = {
        "role": "user",
        "content": f"Generate Python code for the following idea: {initial_idea}"
    }

    messages = [system_prompt, initial_user_prompt]
    current_code = call_llm(messages)

    if current_code is None:
        print("Initial code generation failed. Exiting.")
        return
    else:
        # Save initial code automatically
        save_code_to_file(current_code, OUTPUT_FILENAME)

    # 3. Feedback Loop
    while True:
        print("\n--- Current Code ({}) ---".format(OUTPUT_FILENAME))
        print(current_code)
        print("---------------------" + "-" * len(OUTPUT_FILENAME))

        feedback = input("Enter your feedback (or type 'save' to finish): ")

        if feedback.lower() == 'save':
            print("\nFinal version already saved. Exiting.")
            break
        if not feedback:
            print("No feedback provided. Asking again.")
            continue

        # Prepare messages for refinement
        refinement_user_prompt = {
            "role": "user",
            "content": f"""Refine the following Python code based on the feedback below. Return the *complete, updated* Python code, wrapped in ```python\\n...\\n```.

Feedback:
{feedback}

Previous Code:
```python
{current_code}
```"""
        }
        messages_for_refinement = [system_prompt, refinement_user_prompt]

        refined_code = call_llm(messages_for_refinement)

        if refined_code is not None:
            current_code = refined_code
            # Save refined code automatically
            save_code_to_file(current_code, OUTPUT_FILENAME)
        else:
            print("❌ Code refinement failed. Keeping the previous version.")

    # 4. Final Confirmation (optional, as it's saved iteratively)
    print(f"\nExiting. The latest code is saved in {OUTPUT_FILENAME}")

if __name__ == "__main__":
    main()
