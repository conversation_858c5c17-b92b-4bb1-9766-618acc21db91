# Code Generation Scripts using OpenRouter

This workspace contains several Python scripts demonstrating different ways to interact with an LLM (via OpenRouter) for code and content generation.

## Scripts

### `basic.py`

* **Purpose:** A very basic example demonstrating how to send a text prompt and an image URL to a multimodal LLM (like `openrouter/quasar-alpha`) and print the response.
* **Functionality:**
  * Initializes the OpenAI client configured for OpenRouter.
  * Sends a hardcoded question ("What is in this image?") along with a specific image URL.
  * Prints the LLM's response to the console.
* **Usage:** Run directly. Requires `OPENROUTER_API_KEY` environment variable.

### `webpage_maker.py`

* **Purpose:** Generates a single HTML webpage based on a prompt (originally derived from `basic.py`, but the image part might have been replaced by a general prompt in later edits).
* **Functionality:**
  * Sends a system prompt instructing the LLM to act as a web developer and return HTML within `<website>` tags.
  * Sends a user prompt asking for a website design.
  * Prints the raw LLM response.
  * Saves the content to `output.html`. It attempts to save only the content within `<website>` tags but includes fallback logic.
* **Usage:** Run directly. Requires `OPENROUTER_API_KEY` environment variable. Outputs `output.html`.

### `python_code_maker_simple.py`

* **Purpose:** Generates Python code based on a user prompt in a single request.
* **Functionality:**
  * Sends a system prompt instructing the LLM to act as a Python developer.
  * Sends a user prompt requesting Python code generation (e.g., a Pac-Man game).
  * Instructs the LLM to return the code within ``python ... `` markdown blocks.
  * Prints the raw LLM response.
  * Uses regex to extract the code from the ``python ... `` block.
  * Saves the extracted code to `output.py`.
* **Usage:** Run directly. Requires `OPENROUTER_API_KEY` environment variable. Outputs `output.py`.

### `iterative_coder.py`

* **Purpose:** Provides an interactive command-line interface for generating and iteratively refining Python code with an LLM.
* **Functionality:**
  * Prompts the user for an initial coding idea.
  * Calls the LLM to generate the first version of the code.
  * Enters a loop:
    * Displays the current code.
    * Saves the current code automatically to `final_code.py`.
    * Prompts the user for feedback (or 'save' to exit).
    * If feedback is given, sends the current code and the feedback to the LLM for refinement.
    * Updates the current code with the refined version.
  * Ensures code is extracted from ``python ... `` blocks.
* **Usage:** Run interactively (`python iterative_coder.py`). Requires `OPENROUTER_API_KEY` environment variable. Outputs and continuously updates `final_code.py`.

### `final_code.py`

* **Purpose:** This is **not** a script to be run directly. It is the **output file** generated and updated by `iterative_coder.py`.
* **Content:** Contains the final version of the Python code after the user finishes the interactive refinement process using `iterative_coder.py`. The example content shows a Pygame Pac-Man implementation.

## Setup

1. Ensure you have Python installed.
2. Install the required library: `pip install openai`
3. Set the `OPENROUTER_API_KEY` environment variable with your key.
