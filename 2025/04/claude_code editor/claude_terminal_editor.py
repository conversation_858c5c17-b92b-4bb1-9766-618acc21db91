import os
import anthropic
import json
from termcolor import colored, cprint
import sys
import traceback

class ClaudeTerminalEditor:
    def __init__(self, api_key=None):
        self.api_key = api_key or os.environ.get("ANTHROPIC_API_KEY")
        if not self.api_key:
            cprint("Error: No ANTHROPIC_API_KEY found in environment variables", "red")
            sys.exit(1)
        
        self.client = anthropic.Anthropic(api_key=self.api_key)
        self.model = "claude-3-7-sonnet-20250219"
        self.messages = []
        self.file_backups = {}  # Store file backups for undo operations
        self.max_tool_calls = 15    
        self.system_message = """
        all files are in the working directory
        """
    
    def backup_file(self, file_path):
        """Create a backup of a file before editing."""
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                self.file_backups[file_path] = content
                return True
            except Exception as e:
                cprint(f"Warning: Couldn't backup file {file_path}: {str(e)}", "yellow")
                return False
        return False
    
    def handle_view(self, path, view_range=None):
        """Handle view command to read file or directory contents."""
        try:
            if not os.path.exists(path):
                return f"Error: Path not found: {path}", True
            
            if os.path.isdir(path):
                # List directory contents
                files = os.listdir(path)
                return "\n".join(files), False
            else:
                # Read file contents
                with open(path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if view_range:
                    lines = content.splitlines()
                    start = max(0, view_range[0] - 1)  # Convert to 0-indexed
                    end = view_range[1] if view_range[1] != -1 else len(lines)
                    content = "\n".join([f"{i+1}: {line}" for i, line in enumerate(lines[start:end])])
                else:
                    # Add line numbers to full content
                    content = "\n".join([f"{i+1}: {line}" for i, line in enumerate(content.splitlines())])
                
                return content, False
        except Exception as e:
            return f"Error: {str(e)}", True
    
    def handle_str_replace(self, path, old_str, new_str):
        """Handle str_replace command to replace text in a file."""
        try:
            if not os.path.exists(path):
                return f"Error: File not found: {path}", True
            
            with open(path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Count occurrences to ensure exactly one match
            count = content.count(old_str)
            if count == 0:
                return "Error: No match found for replacement. Please check your text and try again.", True
            elif count > 1:
                return f"Error: Found {count} matches for replacement text. Please provide more context to make a unique match.", True
            
            # Backup the file before modifying
            self.backup_file(path)
            
            # Replace the text
            new_content = content.replace(old_str, new_str)
            with open(path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            return "Successfully replaced text at exactly one location.", False
        except Exception as e:
            return f"Error: {str(e)}", True
    
    def handle_create(self, path, file_text):
        """Handle create command to create a new file with content."""
        try:
            # Check if file already exists
            if os.path.exists(path):
                return f"Error: File already exists: {path}", True
            
            # Create directories if they don't exist
            dir_path = os.path.dirname(path)
            if dir_path and not os.path.exists(dir_path):
                os.makedirs(dir_path)
            
            # Create the file with content
            with open(path, 'w', encoding='utf-8') as f:
                f.write(file_text)
            
            return f"Successfully created file: {path}", False
        except Exception as e:
            return f"Error: {str(e)}", True
    
    def handle_insert(self, path, insert_line, new_str):
        """Handle insert command to insert text at a specific line."""
        try:
            if not os.path.exists(path):
                return f"Error: File not found: {path}", True
            
            with open(path, 'r', encoding='utf-8') as f:
                lines = f.read().splitlines()
            
            # Backup the file before modifying
            self.backup_file(path)
            
            # Insert text after the specified line
            if insert_line == 0:
                lines.insert(0, new_str)
            elif insert_line > len(lines):
                return f"Error: Insert line {insert_line} exceeds file length of {len(lines)}", True
            else:
                lines.insert(insert_line, new_str)
            
            # Write the modified content back to the file
            with open(path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(lines))
            
            return f"Successfully inserted text at line {insert_line}", False
        except Exception as e:
            return f"Error: {str(e)}", True
    
    def handle_undo_edit(self, path):
        """Handle undo_edit command to restore file from backup."""
        try:
            if path not in self.file_backups:
                return f"Error: No backup found for file: {path}", True
            
            # Restore from backup
            with open(path, 'w', encoding='utf-8') as f:
                f.write(self.file_backups[path])
            
            return f"Successfully reverted last edit to {path}", False
        except Exception as e:
            return f"Error: {str(e)}", True
    
    def handle_editor_tool(self, tool_call):
        """Handle Claude's text editor tool requests."""
        cprint(f"-- Handling Tool Call ---", "cyan")
        cprint(f"Tool Name: {tool_call.name}", "magenta")
        cprint(f"Tool Input: {tool_call.input}", "magenta")
        
        try:
            # Fix for ToolUseBlock object - use attribute access instead of dictionary access
            input_params = tool_call.input # input_params is actually a dict here based on debug logs
            # Use .get() for dictionary access, providing default values
            command = input_params.get('command', '<COMMAND NOT FOUND>') 
            path = input_params.get('path', '<PATH NOT FOUND>')
            
            cprint(f"  Command: {command}", "blue")
            cprint(f"  Path: {path}", "blue")

            # --- CWD Safety Check --- 
            try:
                # Get absolute path of CWD
                cwd_abs = os.path.abspath(os.getcwd())
                # Resolve the requested path relative to CWD
                target_abs = os.path.abspath(os.path.join(os.getcwd(), path))
                
                cprint(f"  CWD Absolute: {cwd_abs}", "grey")
                cprint(f"  Target Absolute: {target_abs}", "grey")

                # Check if target path is within the CWD
                if not target_abs.startswith(cwd_abs):
                    error_msg = f"Error: Path is outside the current working directory: {path}"
                    cprint(error_msg, "red")
                    return error_msg, True
            except Exception as e:
                 error_msg = f"Error resolving/checking path: {str(e)}"
                 cprint(error_msg, "red")
                 return error_msg, True
            # --- End CWD Safety Check --- 

            # Validate path to prevent directory traversal attacks (redundant but good practice)
            path = os.path.normpath(path)
            
            if command == 'view':
                view_range = input_params.get('view_range') # Use .get() here too
                cprint(f"View Range: {view_range}", "blue")
                result, is_error = self.handle_view(path, view_range)
            
            elif command == 'str_replace':
                old_str = input_params.get('old_str', '') # Use .get()
                new_str = input_params.get('new_str', '') # Use .get()
                cprint(f"  Old String: '{old_str}'", "blue")
                cprint(f"  New String: '{new_str}'", "blue")
                result, is_error = self.handle_str_replace(path, old_str, new_str)
            
            elif command == 'create':
                file_text = input_params.get('file_text', '') # Use .get()
                cprint(f"  File Text (length): {len(file_text)}", "blue")
                result, is_error = self.handle_create(path, file_text)
            
            elif command == 'insert':
                insert_line = input_params.get('insert_line', 0) # Use .get()
                new_str = input_params.get('new_str', '') # Use .get()
                cprint(f"  Insert Line: {insert_line}", "blue")
                cprint(f"  New String: '{new_str}'", "blue")
                result, is_error = self.handle_insert(path, insert_line, new_str)
            
            elif command == 'undo_edit':
                result, is_error = self.handle_undo_edit(path)
            
            else:
                result = f"Error: Unknown command: {command}"
                is_error = True
            
            cprint(f"  Tool Result: '{result[:100]}...' (is_error={is_error})", "magenta")
            cprint(f"--- Tool Handling Finished ---", "cyan")
            return result, is_error
        except Exception as e:
            error_msg = f"Error handling tool: {str(e)}"
            cprint(error_msg, "red")
            traceback.print_exc()
            cprint(f"--- Tool Handling Finished (with exception) ---", "cyan")
            return error_msg, True
    
    def chat(self):
        """Start interactive terminal chat with Claude."""
        cprint("Claude Terminal Editor - Type 'exit' to quit", "cyan", attrs=["bold"])
        cprint("Type your message and press Enter. Claude may ask to edit your files.", "cyan")
        
        while True:
            # Get user input
            cprint("\nYou: ", "green", end="")
            user_input = input()
            
            if user_input.lower() in ["exit", "quit"]:
                cprint("Goodbye!", "cyan")
                break
            
            # Add the user message to the conversation
            self.messages.append({"role": "user", "content": user_input})
            
            # --- Start Looping Logic --- 
            call_count = 0
            while call_count < self.max_tool_calls:
                call_count += 1
                cprint(f"--- Sending Request to Claude (Turn {call_count}) ---", "yellow")
                
                try:
                    response = self.client.messages.create(
                        model=self.model,
                        max_tokens=4096,
                        system=self.system_message,
                        tools=[
                            {
                                "type": "text_editor_20250124",
                                "name": "str_replace_editor"
                            }
                        ],
                        messages=self.messages
                    )
                except Exception as e:
                    cprint(f"API Error: {str(e)}", "red")
                    traceback.print_exc()
                    # Attempt recovery by removing last assistant message if it exists
                    if self.messages and self.messages[-1]["role"] == "assistant":
                       cprint("Attempting recovery: Removing last potentially problematic assistant message.", "yellow")
                       self.messages.pop() 
                    break # Exit the inner loop on API error
                
                cprint(f"--- Received Response (Turn {call_count}) ---", "yellow")
                # Summarize response content
                assistant_turn_content = []
                tool_calls_in_response = []
                for block in response.content:
                    assistant_turn_content.append(block) # Store raw block for history
                    if block.type == 'text':
                        cprint(f"  Claude Sent Text (length: {len(block.text)})", "grey")
                    elif block.type == 'tool_use':
                        tool_calls_in_response.append(block)
                        tool_input = block.input if hasattr(block, 'input') else {}
                        tool_command = tool_input.get('command', '?')
                        tool_path = tool_input.get('path', '?')
                        cprint(f"  Claude Requested Tool: {block.name}, Command: {tool_command}, Path: {tool_path}", "grey")
                    else:
                        cprint(f"  Claude Sent Unknown Block Type: {block.type}", "grey")

                # Add Claude's full response message to history
                self.messages.append({"role": "assistant", "content": assistant_turn_content})
                
                # --- Process Tool Calls --- 
                if not tool_calls_in_response:
                    # No tools used, print final text and break the loop
                    for block in assistant_turn_content:
                         if block.type == "text":
                            cprint("Claude: ", "blue", attrs=["bold"], end="")
                            print(block.text)
                    break # Exit the while loop, turn is over
                else:
                    # Tools were used, prepare results for the next iteration
                    tool_results_for_next_call = []
                    for tool_call_block in tool_calls_in_response:
                        tool_use_id = tool_call_block.id
                        tool_name = tool_call_block.name
                        cprint(f"Claude is using the {tool_name} tool...", "yellow")
                        
                        # Execute the tool
                        result, is_error = self.handle_editor_tool(tool_call_block)
                        
                        # Prepare the tool_result block
                        tool_result_content = {
                            "type": "tool_result",
                            "tool_use_id": tool_use_id,
                            "content": result
                        }
                        if is_error:
                            tool_result_content["is_error"] = True
                        tool_results_for_next_call.append(tool_result_content)
                    
                    # Add the user message containing tool results before the next loop iteration
                    self.messages.append({"role": "user", "content": tool_results_for_next_call})
                    # Loop continues...

            if call_count >= self.max_tool_calls:
                 cprint("Reached maximum tool call limit for this turn.", "red")

            # --- End Looping Logic ---
            
            # Catch general exceptions outside the loop
            # except Exception as e:
            #    cprint(f"Error in chat loop: {str(e)}", "red")
            #    traceback.print_exc()
            #    # Attempt to remove the last potentially problematic messages to allow recovery
            #    if len(self.messages) > 0 and self.messages[-1]["role"] == "user":
            #        self.messages.pop() # Remove user input
            #    if len(self.messages) > 0 and self.messages[-1]["role"] == "assistant":
            #        self.messages.pop() # Remove potentially incomplete assistant response

def main():
    # Simple command line arg handling without argparse
    api_key = None
    if len(sys.argv) > 1 and sys.argv[1].startswith('--api-key='):
        api_key = sys.argv[1].split('=')[1]
    
    editor = ClaudeTerminalEditor(api_key=api_key)
    editor.chat()

if __name__ == "__main__":
    main() 