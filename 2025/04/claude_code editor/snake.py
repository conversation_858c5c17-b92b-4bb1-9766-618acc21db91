import pygame
import random
import sys
from termcolor import colored, cprint

### I HAVEN'T FULLY TESTED THIS, SO KEEP AN EYE ON IT. USE AT YOUR OWN RISK. ###
### IT HAS SAFEGUARDS TO NOT MODIFY OUTSIDE OF CURRENT DIRECTORY ### STILL BE CAREFUL ###

# Initialize pygame
pygame.init()

# Game constants
WIDTH, HEIGHT = 600, 400
GRID_SIZE = 20
GRID_WIDTH = WIDTH // GRID_SIZE
GRID_HEIGHT = HEIGHT // GRID_SIZE
INITIAL_FPS = 10  # Initial game speed

# Colors
BLACK = (0, 0, 0)
GREEN = (0, 255, 0)
RED = (255, 0, 0)
WHITE = (255, 255, 255)

# Create the screen
screen = pygame.display.set_mode((WIDTH, HEIGHT))
pygame.display.set_caption("Snake Game with Python")
clock = pygame.time.Clock()

# Snake class
class Snake:
    def __init__(self):
        self.positions = [(GRID_WIDTH // 2, GRID_HEIGHT // 2)]
        self.length = 1
        self.direction = (1, 0)  # Start moving right
        self.score = 0
        self.is_alive = True

    def get_head_position(self):
        return self.positions[0]

    def update(self):
        head = self.get_head_position()
        x, y = self.direction
        
        # Two options here:
        # Option 1: Use wrapping (snake comes out the other side)
        new_x = (head[0] + x) % GRID_WIDTH  
        new_y = (head[1] + y) % GRID_HEIGHT
        
        # Check if snake hits itself
        if (new_x, new_y) in self.positions[1:]: 
            self.is_alive = False
            
        # Option 2: Use wall collision (uncomment these if you want the snake to die when hitting walls)
        # new_x = head[0] + x
        # new_y = head[1] + y
        # if new_x < 0 or new_x >= GRID_WIDTH or new_y < 0 or new_y >= GRID_HEIGHT:
        #     self.is_alive = False
            
        new_head = (new_x, new_y)
        self.positions.insert(0, new_head)
        
        # Remove tail if snake didn't eat food
        if len(self.positions) > self.length:
            self.positions.pop()  

    def render(self):
        for position in self.positions:
            rect = pygame.Rect(position[0] * GRID_SIZE, position[1] * GRID_SIZE, GRID_SIZE, GRID_SIZE)
            pygame.draw.rect(screen, GREEN, rect)
            pygame.draw.rect(screen, WHITE, rect, 1)

    def change_direction(self, direction):
        # Prevent 180-degree turns
        if (direction[0] * -1, direction[1] * -1) == self.direction:
            return  
        self.direction = direction

# Food class
class Food:
    def __init__(self):
        self.position = (0, 0)
        self.randomize_position()

    def randomize_position(self, snake_positions=None):
        # Create a list of all possible positions
        all_positions = [(x, y) for x in range(GRID_WIDTH) for y in range(GRID_HEIGHT)]
        
        # Remove positions occupied by the snake
        if snake_positions:
            available_positions = [pos for pos in all_positions if pos not in snake_positions]
            if available_positions:  # Make sure there are available positions
                self.position = random.choice(available_positions)
            else:
                # Fallback if no positions are available (unlikely)
                self.position = (random.randint(0, GRID_WIDTH - 1), random.randint(0, GRID_HEIGHT - 1))
        else:
            self.position = (random.randint(0, GRID_WIDTH - 1), random.randint(0, GRID_HEIGHT - 1))

    def render(self):
        rect = pygame.Rect(self.position[0] * GRID_SIZE, self.position[1] * GRID_SIZE, GRID_SIZE, GRID_SIZE)
        pygame.draw.rect(screen, RED, rect)
        pygame.draw.rect(screen, WHITE, rect, 1)

# Game functions
def display_score(score):
    font = pygame.font.SysFont('Arial', 20)
    text = font.render(f'Score: {score}', True, WHITE)
    screen.blit(text, (5, 5))

def game_over(score):
    font = pygame.font.SysFont('Arial', 36)
    text = font.render(f'GAME OVER! Score: {score}', True, WHITE)
    text_rect = text.get_rect(center=(WIDTH//2, HEIGHT//2))
    screen.blit(text, text_rect)
    
    restart_font = pygame.font.SysFont('Arial', 24)
    restart_text = restart_font.render('Press R to Restart or Q to Quit', True, WHITE)
    restart_rect = restart_text.get_rect(center=(WIDTH//2, HEIGHT//2 + 50))
    screen.blit(restart_text, restart_rect)
    
    pygame.display.update()
    
    waiting = True
    while waiting:
        clock.tick(10)  # Control the event checking rate
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                pygame.quit()
                sys.exit()
            if event.type == pygame.KEYDOWN:
                if event.key == pygame.K_r:
                    waiting = False
                if event.key == pygame.K_q:
                    pygame.quit()
                    sys.exit()

def main():
    snake = Snake()
    food = Food()
    food.randomize_position(snake.positions)  # Make sure food doesn't spawn on snake
    fps = INITIAL_FPS  # Initialize fps as a variable that can be changed
    paused = False
    
    # Main game loop
    running = True
    while running:
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running = False
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_UP:
                    snake.change_direction((0, -1))
                elif event.key == pygame.K_DOWN:
                    snake.change_direction((0, 1))
                elif event.key == pygame.K_LEFT:
                    snake.change_direction((-1, 0))
                elif event.key == pygame.K_RIGHT:
                    snake.change_direction((1, 0))
                elif event.key == pygame.K_p:  # Added pause functionality
                    paused = not paused
        
        # Update snake if not paused
        if snake.is_alive and not paused:
            snake.update()
            
            # Check if snake ate food
            if snake.get_head_position() == food.position:
                snake.length += 1
                snake.score += 5  
                food.randomize_position(snake.positions)
                
                # Increase speed as score increases
                if snake.score % 50 == 0:  
                    fps += 1  
        # Draw everything
        screen.fill(BLACK)
        snake.render()
        food.render()
        display_score(snake.score)
        
        # Display pause message if game is paused
        if paused:
            pause_font = pygame.font.SysFont('Arial', 32)
            pause_text = pause_font.render('PAUSED - Press P to Resume', True, WHITE)
            pause_rect = pause_text.get_rect(center=(WIDTH//2, HEIGHT//2))
            screen.blit(pause_text, pause_rect)
        
        # Check game over
        if not snake.is_alive:
            game_over(snake.score)
            snake = Snake()
            food = Food()
            fps = INITIAL_FPS
        
        pygame.display.update()
        clock.tick(fps)  # Control game speed

    pygame.quit()
    sys.exit()

if __name__ == "__main__":
    # Print colorful welcome message
    cprint("Welcome to Snake Game with Python!", "green", attrs=["bold"])
    cprint("Use arrow keys to control the snake.", "yellow")
    cprint("Eat the red food to grow and increase your score!", "red")
    cprint("Press R to restart or Q to quit when game is over.", "cyan")
    main()
