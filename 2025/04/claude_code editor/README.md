# Claude Terminal Editor

> **⚠️ Important Warning ⚠️**
>
> This script allows an AI (<PERSON>) to potentially view, create, modify, and delete files on your local system based on your conversation.
> While safety features like Current Working Directory (CWD) restrictions are in place, **exercise caution.**
> Do not run this script in directories containing sensitive information.
> You are responsible for any changes made to your files.

A command-line interface for interacting with <PERSON>, featuring built-in text editor capabilities. This tool allows for seamless conversation with <PERSON> while enabling it to view and modify files on your system.

## Features

- Interactive terminal-based chat with Claude 3.7 Sonnet
- Handles multiple tool calls per user turn, allowing <PERSON> to perform sequential actions.
- Colorful terminal output using termcolor
- Full support for <PERSON>'s text editor functionality:
  - View files and directories
  - Replace text in files
  - Create new files
  - Insert text at specific line numbers
  - Undo file modifications
- Configurable maximum tool calls per turn (default: 15) to prevent infinite loops.
- **Security**: Restricts file operations to the current working directory (CWD) by default.
- Automatic file backups before modifications
- Clear error handling and reporting

## Requirements

- Python 3.7 or higher
- `anthropic` Python package
- `termcolor` Python package

## Installation

1. Install the required packages:

```bash
pip install anthropic termcolor
```

2. Set your Anthropic API key as an environment variable:

```bash
# For Windows
set ANTHROPIC_API_KEY=your_api_key_here

# For macOS/Linux
export ANTHROPIC_API_KEY=your_api_key_here
```

## Usage

Run the script from the command line:

```bash
python claude_terminal_editor.py
```

Or with an API key directly:

```bash
python claude_terminal_editor.py --api-key=your_api_key_here
```

Once running:

- Type your messages and press Enter to communicate with Claude
- Claude may ask to view or edit files on your system
- Type 'exit' or 'quit' to end the session

## How It Works

The Claude Terminal Editor creates a chat interface where:

1. You send messages to Claude
2. Claude responds and may request to use its text editor capabilities
3. The application handles these requests, performing file operations as needed
4. File modification results are sent back to Claude
5. The conversation continues with Claude having access to the file information

## Security Considerations

- **CWD Restriction**: By default, Claude can only access files within the directory the script is run from.
- Files are backed up before modifications
- Path validation helps prevent directory traversal attacks
- Error handling prevents many common file operation issues

## Supported Editor Commands

| Command     | Description                           |
| ----------- | ------------------------------------- |
| view        | Read file or directory contents       |
| str_replace | Replace text in a file                |
| create      | Create a new file with content        |
| insert      | Insert text at a specific line number |
| undo_edit   | Revert the last edit to a file        |

## Example Conversation

```
You: Can you create a simple "Hello World" Python script for me?

Claude: I'd be happy to create a simple "Hello World" Python script for you. I'll make a new file called hello_world.py.

Claude is using the str_replace_editor tool...

Claude: I've created a file called hello_world.py with a simple "Hello World" program. It includes:

1. A main function that prints "Hello, World!"
2. A standard Python idiom to make the script executable

You can run it by typing: python hello_world.py

If you want me to modify it or add more features, just let me know!
```

## License

MIT
