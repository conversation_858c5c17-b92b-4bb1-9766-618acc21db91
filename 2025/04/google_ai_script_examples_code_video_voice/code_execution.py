import os
import sys
from termcolor import colored
from google import genai
from google.genai import types
from IPython.display import display, Markdown, HTML, Image

# CONSTANTS
MODEL_NAME = 'gemini-2.0-flash'
API_KEY = os.getenv("GEMINI_API_KEY")
MAX_RETRIES = 3

def print_status(message, color='green'):
    """Print colored status messages to console."""
    print(colored(f"[STATUS] {message}", color))

def display_code_execution_result(response):
    """Display the code execution result in a human-readable format."""
    try:
        for part in response.candidates[0].content.parts:
            if part.text is not None:
                print(colored("TEXT OUTPUT:", 'cyan'))
                print(part.text)
                print()
            
            if hasattr(part, 'executable_code') and part.executable_code is not None:
                print(colored("EXECUTABLE CODE:", 'yellow'))
                print(part.executable_code.code)
                print()
            
            if hasattr(part, 'code_execution_result') and part.code_execution_result is not None:
                print(colored("CODE EXECUTION RESULT:", 'green'))
                print(part.code_execution_result.output)
                print()
            
            if hasattr(part, 'inline_data') and part.inline_data is not None:
                print(colored("INLINE DATA AVAILABLE (not displayed in terminal)", 'magenta'))
            
            print(colored("-" * 50, 'blue'))
    except Exception as e:
        print_status(f"Error displaying results: {str(e)}", 'red')

def initialize_client():
    """Initialize and return the Gemini API client."""
    try:
        if not API_KEY:
            print_status("GEMINI_API_KEY environment variable not set", 'red')
            print_status("Please set your API key with: export GEMINI_API_KEY='your_key_here'", 'yellow')
            sys.exit(1)
        
        print_status("Initializing Gemini API client...")
        client = genai.Client(api_key=API_KEY)
        print_status("Client initialized successfully")
        return client
    except Exception as e:
        print_status(f"Failed to initialize client: {str(e)}", 'red')
        sys.exit(1)

def run_interactive_chat(client):
    """Run an interactive chat session with the Gemini model."""
    try:
        print_status("Starting interactive chat with Gemini...", 'blue')
        print_status("Type 'exit', 'quit', or 'bye' to end the conversation", 'yellow')
        print_status("Your messages will be processed with code execution enabled", 'cyan')
        
        # Create a chat session
        chat = client.chats.create(
            model=MODEL_NAME,
            config=types.GenerateContentConfig(
                tools=[types.Tool(
                    code_execution=types.ToolCodeExecution
                )]
            )
        )
        
        print(colored("\n" + "="*50, 'blue'))
        print(colored("🤖 GEMINI CHAT WITH CODE EXECUTION", 'cyan', attrs=['bold']))
        print(colored("="*50 + "\n", 'blue'))
        
        while True:
            # Get user input
            user_input = input(colored("You: ", 'green', attrs=['bold']))
            
            # Check if user wants to exit
            if user_input.lower() in ['exit', 'quit', 'bye']:
                print_status("Ending chat session", 'yellow')
                break
            
            try:
                # Print processing message
                print(colored("Processing...", 'yellow'))
                
                # Send message to model
                response = chat.send_message(user_input)
                
                # Display the result
                print(colored("Gemini: ", 'cyan', attrs=['bold']))
                display_code_execution_result(response)
                
            except Exception as e:
                print_status(f"Error: {str(e)}", 'red')
                print_status("Continuing chat session...", 'yellow')
    
    except Exception as e:
        print_status(f"Error in interactive chat: {str(e)}", 'red')

def main():
    """Main function to run the interactive chat."""
    print_status("Starting Gemini API Chat with Code Execution", 'blue')
    
    try:
        client = initialize_client()
        run_interactive_chat(client)
    except KeyboardInterrupt:
        print_status("\nOperation interrupted by user.", 'yellow')
    except Exception as e:
        print_status(f"Error in main function: {str(e)}", 'red')

if __name__ == "__main__":
    main()

