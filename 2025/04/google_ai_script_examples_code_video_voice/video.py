import time
from google import genai
from google.genai import types
import os

client = genai.Client(api_key="AIzaSyA7eaDvusussmwXjF_cdcY71qoe1v5zp-s")  # read API key from GOOGLE_API_KEY

operation = client.models.generate_videos(
    model="veo-2.0-generate-001",
    prompt="zoom in to a glass of water where red and blue ink is mixing together",
    config=types.GenerateVideosConfig(
        person_generation="dont_allow",  # "dont_allow" or "allow_adult"
        aspect_ratio="16:9",  # "16:9" or "9:16"
    ),
)

while not operation.done:
    time.sleep(20)
    print("Waiting for operation to complete...")
    operation = client.operations.get(operation)

for n, generated_video in enumerate(operation.response.generated_videos):
    client.files.download(file=generated_video.video)
    generated_video.video.save(f"video{n}.mp4")  # save the video
