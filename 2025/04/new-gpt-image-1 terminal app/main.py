import os
import openai
import base64
import io
from termcolor import colored
import datetime

# --- Constants ---
API_KEY = os.getenv("OPENAI_API_KEY")
if not API_KEY:
    print(colored("Error: OPENAI_API_KEY environment variable not set.", "red"))
    exit()

client = openai.OpenAI(api_key=API_KEY)
GENERATION_MODEL = "gpt-image-1" # Using the model name from the user example
EDIT_MODEL = "gpt-image-1"      # Using the model name from the user example
GENERATED_IMAGES_DIR = "generated_images"
EDIT_IMAGES_DIR = "images_to_be_edited"
QUALITY = "low"

# --- Helper Functions ---
def create_directories():
    """Creates necessary directories if they don't exist."""
    try:
        os.makedirs(GENERATED_IMAGES_DIR, exist_ok=True)
        print(colored(f"Directory '{GENERATED_IMAGES_DIR}' ensured.", "cyan"))
        os.makedirs(EDIT_IMAGES_DIR, exist_ok=True)
        print(colored(f"Directory '{EDIT_IMAGES_DIR}' ensured.", "cyan"))
    except OSError as e:
        print(colored(f"Error creating directories: {e}", "red"))
        exit()

def save_image(b64_json, filename_prefix="generated"):
    """Decodes base64 image data and saves it to a file."""
    try:
        print(colored("Decoding image data...", "yellow"))
        image_bytes = base64.b64decode(b64_json)
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{filename_prefix}_{timestamp}.png"
        filepath = os.path.join(GENERATED_IMAGES_DIR, filename)
        with open(filepath, "wb") as f: # Use "wb" for binary writing
            f.write(image_bytes)
        print(colored(f"Image successfully saved to: {filepath}", "green"))
    except (base64.binascii.Error, IOError, OSError) as e:
        print(colored(f"Error saving image: {e}", "red"))

# --- Core Functions ---
def generate_image_mode():
    """Handles the image generation mode."""
    print(colored("\n--- Image Generation Mode ---", "blue"))
    try:
        prompt = input(colored("Enter the prompt for the image: ", "yellow"))
        if not prompt:
            print(colored("Prompt cannot be empty.", "red"))
            return

        print(colored(f"Generating image with prompt: '{prompt}' using model '{GENERATION_MODEL}'...", "cyan"))

        result = client.images.generate(
            model=GENERATION_MODEL,
            prompt=prompt,
            quality=QUALITY,
        )

        # Check if data is present and contains b64_json
        if result.data and result.data[0].b64_json:
            print(colored("Image generated successfully.", "green"))
            save_image(result.data[0].b64_json, filename_prefix="generated")
        else:
            print(colored("Failed to generate image. No data received.", "red"))
            # Optional: print the raw result for debugging
            # print(result)

    except openai.APIError as e:
        print(colored(f"OpenAI API Error: {e}", "red"))
    except Exception as e:
        print(colored(f"An unexpected error occurred during generation: {e}", "red"))


def edit_image_mode():
    """Handles the image editing mode."""
    print(colored("\n--- Image Editing Mode ---", "blue"))
    try:
        print(colored(f"Looking for images in '{EDIT_IMAGES_DIR}'...", "cyan"))
        # Allow both png and jpeg/jpg files
        allowed_extensions = ('.png', '.jpeg', '.jpg')
        available_images = [f for f in os.listdir(EDIT_IMAGES_DIR) 
                            if f.lower().endswith(allowed_extensions)]

        if not available_images:
            print(colored(f"No PNG or JPEG images found in '{EDIT_IMAGES_DIR}'. Please add images to edit.", "red"))
            return

        print(colored("Available images:", "green"))
        for i, img_name in enumerate(available_images):
            print(f"  {i + 1}: {img_name}")

        while True:
            selection = input(colored(f"Select image numbers to use (comma-separated, e.g., 1,3): ", "yellow"))
            try:
                selected_indices = [int(s.strip()) - 1 for s in selection.split(',') if s.strip()]
                if not selected_indices:
                    raise ValueError("No selection made.")
                if any(idx < 0 or idx >= len(available_images) for idx in selected_indices):
                    raise ValueError("Invalid index selected.")
                break # Valid selection
            except ValueError as e:
                print(colored(f"Invalid input: {e}. Please enter valid numbers corresponding to the images listed.", "red"))

        selected_image_paths = [os.path.join(EDIT_IMAGES_DIR, available_images[idx]) for idx in selected_indices]
        print(colored(f"Selected images: {[os.path.basename(p) for p in selected_image_paths]}", "cyan"))

        prompt = input(colored("Enter the prompt for editing/combining the images: ", "yellow"))
        if not prompt:
            print(colored("Prompt cannot be empty.", "red"))
            return

        print(colored(f"Editing/Combining images with prompt: '{prompt}' using model '{EDIT_MODEL}'...", "cyan"))

        image_file_objects = []
        try:
            # Open all selected files in binary read mode
            for path in selected_image_paths:
                image_file_objects.append(open(path, "rb"))

            # Call the edit endpoint - Assuming it accepts a list of file objects based on JS example
            result = client.images.edit(
                model=EDIT_MODEL,
                image=image_file_objects, # Pass the list of file objects
                prompt=prompt,
            )

             # Check if data is present and contains b64_json
            if result.data and result.data[0].b64_json:
                print(colored("Image edited/combined successfully.", "green"))
                save_image(result.data[0].b64_json, filename_prefix="edited")
            else:
                print(colored("Failed to edit/combine image. No data received.", "red"))
                # Optional: print the raw result for debugging
                # print(result)

        finally:
            # Ensure all opened files are closed
            for f in image_file_objects:
                if not f.closed:
                    f.close()
            print(colored("Image file resources released.", "cyan"))


    except FileNotFoundError:
         print(colored(f"Error: Directory '{EDIT_IMAGES_DIR}' not found.", "red"))
    except openai.APIError as e:
        print(colored(f"OpenAI API Error: {e}", "red"))
    except Exception as e:
        print(colored(f"An unexpected error occurred during editing: {e}", "red"))


# --- Main Application Logic ---
def main():
    """Main function to run the application."""
    create_directories()
    print(colored("Welcome to the OpenAI Image Tool!", "magenta"))

    while True:
        print("\nChoose a mode:")
        print("  1: Generate Image")
        print("  2: Edit/Combine Images")
        print("  q: Quit")

        choice = input(colored("Enter your choice: ", "yellow")).strip().lower()

        if choice == '1':
            generate_image_mode()
        elif choice == '2':
            edit_image_mode()
        elif choice == 'q':
            print(colored("Exiting application. Goodbye!", "magenta"))
            break
        else:
            print(colored("Invalid choice. Please enter 1, 2, or q.", "red"))

if __name__ == "__main__":
    main() 