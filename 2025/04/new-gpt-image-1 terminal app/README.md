# OpenAI Image Generation and Editing Tool

This Python script provides a command-line interface to interact with the OpenAI Images API (using the `gpt-image-1` model) for two main purposes:

1.  **Generate Images:** Create new images based on textual prompts.
2.  **Edit/Combine Images:** Modify or combine existing images based on a textual prompt and reference images.

## Features

*   Simple terminal-based menu for interaction.
*   Generate images from text prompts.
*   Select multiple local images (`.png`, `.jpeg`, `.jpg`) to use as references for editing/combination.
*   Saves generated and edited images to a dedicated directory (`generated_images/`) with timestamps.
*   Uses `termcolor` for colored terminal output.
*   Requires OpenAI API key set as an environment variable (`OPENAI_API_KEY`).

## Prerequisites

*   Python 3.x
*   `pip` (Python package installer)
*   An OpenAI API Key

## Setup

1.  **Clone or Download:** Get the project files onto your local machine.
2.  **Navigate to Directory:** Open your terminal or command prompt and change into the project directory:
    ```bash
    cd path/to/gpt-image-API
    ```
3.  **Install Dependencies:** Install the required Python packages:
    ```bash
    pip install -r requirements.txt
    ```
4.  **Set Environment Variable:** You need to set your OpenAI API key as an environment variable named `OPENAI_API_KEY`. How you do this depends on your operating system:

    *   **Windows (Command Prompt - Current Session):**
        ```bash
        set OPENAI_API_KEY=your_api_key_here
        ```
    *   **Windows (PowerShell - Current Session):**
        ```powershell
        $env:OPENAI_API_KEY="your_api_key_here"
        ```
    *   **macOS/Linux (Current Session):**
        ```bash
        export OPENAI_API_KEY=your_api_key_here
        ```
    *   *Note: For a persistent setting, search for how to set environment variables permanently on your specific OS.*

## Usage

1.  **Run the Script:** Execute the main script from your terminal:
    ```bash
    python main.py
    ```
2.  **Choose a Mode:**
    *   Enter `1` for **Image Generation Mode**.
    *   Enter `2` for **Image Editing/Combination Mode**.
    *   Enter `q` to **Quit**.

### Image Generation Mode

*   You will be prompted to enter a text description for the image you want to generate.
*   The script will call the OpenAI API.
*   If successful, the generated image will be saved as a `.png` file in the `generated_images/` directory, prefixed with `generated_` and a timestamp.

### Image Editing/Combination Mode

*   **Place Images:** Before selecting this mode, place the `.png`, `.jpeg`, or `.jpg` image files you want to use as references into the `images_to_be_edited/` directory.
*   The script will list the available images in that directory with numbers.
*   You will be prompted to select the image numbers you want to use (comma-separated, e.g., `1,3`).
*   You will then be prompted to enter a text description explaining how to edit or combine the selected images.
*   The script will call the OpenAI API using the selected images and the prompt.
*   If successful, the resulting image will be saved as a `.png` file in the `generated_images/` directory, prefixed with `edited_` and a timestamp.

## File Structure

```
gpt-image API/
├── generated_images/       # Output directory for generated/edited images
├── images_to_be_edited/    # Input directory for images to edit/combine
├── main.py                 # The main application script
├── requirements.txt        # Python package dependencies
└── README.md               # This file
``` 