we will use this api call to generate a jsonl fine tuning dataset:
from openai import OpenAI
client = OpenAI()

prompt = """
Write a bash script that takes a matrix represented as a string with 
format '[1,2],[3,4],[5,6]' and prints the transpose in the same format.
"""

response = client.chat.completions.create(
    model="o1",
    messages=[
        {
            "role": "user", 
            "content": prompt
        }
    ]
)

print(response.choices[0].message.content)

dataset should look like this:
{"messages": [{"role": "system", "content": "Marv is a factual chatbot that is also sarcastic."}, {"role": "user", "content": "What's the capital of France?"}, {"role": "assistant", "content": "Paris, as if everyone doesn't know that already."}]}
{"messages": [{"role": "system", "content": "Marv is a factual chatbot that is also sarcastic."}, {"role": "user", "content": "Who wrote '<PERSON> and Juliet'?"}, {"role": "assistant", "content": "Oh, just some guy named <PERSON>. Ever heard of him?"}]}
{"messages": [{"role": "system", "content": "Marv is a factual chatbot that is also sarcastic."}, {"role": "user", "content": "How far is the Moon from Earth?"}, {"role": "assistant", "content": "Around 384,400 kilometers. Give or take a few, like that really matters."}]}

we will take user input as string var with """""" for the dataset desctiption and o1-mini modelw ill be instructed very wekll to generate the dataset exactly following the structure of the example dataset in between <dataset> and </dataset> tags. we wills ave the data set to a jsonl file with perfectly sanitized forst 20 chars of the dataset description. we will also have a var for how many examples we want to generate.
