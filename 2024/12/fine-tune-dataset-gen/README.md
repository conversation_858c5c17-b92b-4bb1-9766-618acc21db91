# Fine-tuning Dataset Generator

Generate custom fine-tuning datasets in JSONL format where you can control exactly how the assistant should respond. Simply set your description of how responses should be structured, and the script will generate a perfect JSONL dataset ready for fine-tuning.

## Features

✨ **Customizable Response Patterns**

- Define exactly how assistant responses should be structured
- Control sentence count, formatting, and content patterns

🔄 **Two-Stage Generation Process**

- Initial generation with o1-preview for creative, accurate responses
- Secondary processing with gpt-4o for perfect JSONL formatting

## ❤️ Support & Get 400+ AI Projects

This is one of 400+ fascinating projects in my collection! [Support me on Patreon](https://www.patreon.com/c/echohive42/membership) to get:

- 🎯 Access to 400+ AI projects (and growing daily!)
  - Including advanced projects like [2 Agent Real-time voice template with turn taking](https://www.patreon.com/posts/2-agent-real-you-118330397)
- 📥 Full source code & detailed explanations
- 📚 1000x Cursor Course
- 🎓 Live coding sessions & AMAs
- 💬 1-on-1 consultations (higher tiers)
- 🎁 Exclusive discounts on AI tools & platforms (up to $180 value)

## Quick Start

```bash
# Set your OpenAI API key
set OPENAI_API_KEY=your_api_key  # Windows
export OPENAI_API_KEY=your_api_key  # Linux/Mac

# Install requirements
pip install -r requirements.txt

# Edit the DATASET_DESC and N_EXAMPLES variables in generate_dataset.py
# Run the script
python generate_dataset.py
```

## How It Works

The script uses a two-step process to generate high-quality training data:

1. **Generation (o1-preview)**: Generates user-assistant conversation pairs where assistant responses follow your specified format/rules
2. **Formatting (gpt-4o)**: Ensures perfect JSONL formatting while preserving the exact content

## Configuration Variables

At the top of `generate_dataset.py`, you can configure:

- `DATASET_DESC`: Description of how assistant responses should be structured
- `N_EXAMPLES`: Number of examples to generate
- `DATASET_SYSTEM_MESSAGE`: System message to be included in each example
- `MODEL`: Model for initial generation (default: "o1-preview")
- `VALIDATION_MODEL`: Model for JSON formatting (default: "gpt-4o")

## Output Files

The script generates two files:

- `*_raw.txt`: Raw conversation pairs from the generation step
- `*.jsonl`: Final formatted dataset ready for fine-tuning

## Example Usage

```python
# Example configuration
DATASET_DESC = """model responses each should be 5 sentences, each in a new line and the first character of each sentence should spell 'hello'"""
N_EXAMPLES = 10
DATASET_SYSTEM_MESSAGE = """You are a helpful assistant"""
```

This will generate a dataset where each assistant response:

- Contains exactly 5 sentences
- Each sentence starts on a new line
- First characters spell "hello"

## Output Format

The generated JSONL file will contain examples in this format:

```json
{"messages": [
    {"role": "system", "content": "You are a helpful assistant"},
    {"role": "user", "content": "Can you write me a creative message?"},
    {"role": "assistant", "content": "Here's a special message for you.\nEveryone deserves kindness.\nLife is full of surprises.\nLet's make today amazing.\nOpportunities await you."}
]}
```

## Error Handling

The script includes comprehensive error handling:

- Saves intermediate results for debugging
- Provides colored console output for progress tracking
- Gracefully handles API errors and formatting issues

## Requirements

- Python 3.7+
- OpenAI API key with access to o1-preview and gpt-4o models
- Required packages in requirements.txt:
  - openai
  - termcolor

## Notes

- Make sure your OpenAI API key has access to the required models
- The script uses environment variables for API key management
- All file operations use UTF-8 encoding
- The script includes detailed logging for debugging
