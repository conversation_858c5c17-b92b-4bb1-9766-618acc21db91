import os
from openai import AsyncOpenAI
import asyncio
import json
from termcolor import colored
import re

# Major variables
MODEL = "o1-preview"
VALIDATION_MODEL = "gpt-4o"
DATASET_DESC = """model responses each should be 5 sentences, each in a new line and the first character of each sentence should spell 'hello'"""
N_EXAMPLES = 10
DATASET_SYSTEM_MESSAGE = """You are a helpful assistant"""

def save_raw_response(content: str, desc: str) -> None:
    try:
        filename = re.sub(r'[^a-zA-Z0-9]', '_', desc[:20]) + '_raw.txt'
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(content)
        print(colored(f"Raw response saved to {filename}", "green"))
    except Exception as e:
        print(colored(f"Error saving raw response: {str(e)}", "red"))

async def process_and_format_json(client: AsyncOpenAI, raw_content: str, n: int) -> list:
    try:
        print(colored("Raw content to process:", "cyan"))
        print(raw_content)
        
        response = await client.chat.completions.create(
            model=VALIDATION_MODEL,
            response_format={"type": "json_object"},
            messages=[
                {
                    "role": "system",
                    "content": """You are a JSON formatter. Extract user and assistant messages from the provided examples and format them into perfect JSONL training data. Each example starts with 'USER:' and is followed by 'ASSISTANT:'. Format these exactly as they appear."""
                },
                {
                    "role": "user",
                    "content": f"""Format these conversation examples into JSONL where each example has this structure:
{{"messages": [
    {{"role": "system", "content": "{DATASET_SYSTEM_MESSAGE}"}},
    {{"role": "user", "content": "EXACT_USER_MESSAGE_WITHOUT_USER_PREFIX"}},
    {{"role": "assistant", "content": "EXACT_ASSISTANT_MESSAGE_WITHOUT_ASSISTANT_PREFIX"}}
]}}

The examples to format are:

{raw_content}

Return a JSON object with an 'examples' array containing all formatted examples."""
                }
            ]
        )
        
        validated_data = json.loads(response.choices[0].message.content)
        formatted_examples = validated_data.get("examples", [])
        
        print(colored(f"\nFormatted {len(formatted_examples)} examples:", "cyan"))
        print(json.dumps(formatted_examples, indent=2))
        
        return formatted_examples
    except Exception as e:
        print(colored(f"Error in JSON processing: {str(e)}", "red"))
        return []

async def generate_dataset(desc: str, n: int) -> list:
    try:
        print(colored("Initializing OpenAI client...", "cyan"))
        client = AsyncOpenAI(api_key=os.getenv("OPENAI_API_KEY"))
        
        print(colored(f"Starting dataset generation for {n} examples...", "yellow"))
        
        try:
            print(colored("Making API call to generate examples...", "blue"))
            response = await client.chat.completions.create(
                model=MODEL,
                messages=[
                    {
                        "role": "user",
                        "content": f"""You are a helpful assistant that generates conversation examples. For each example:
1. Generate a user message asking for help with something
2. Generate an assistant response that MUST follow this description: {desc}

Format each example exactly like this (with the prefixes):
USER: <user message>
ASSISTANT: <assistant response that follows the description>

Generate exactly {n} different examples. Separate each example with two newlines."""
                    },
                    {
                        "role": "user",
                        "content": "Generate the conversation examples now."
                    }
                ]
            )
            
            # Save raw response
            raw_content = response.choices[0].message.content
            save_raw_response(raw_content, desc)
            
            print(colored("\nProcessing raw content with gpt-4o...", "cyan"))
            dataset = await process_and_format_json(client, raw_content, n)
            
            if len(dataset) < n:
                print(colored(f"Warning: Only {len(dataset)} valid examples were generated out of {n} requested", "yellow"))
            
            return dataset
                
        except Exception as e:
            print(colored(f"Error in API call: {str(e)}", "red"))
            return []
        
    except Exception as e:
        print(colored(f"Fatal error in dataset generation: {str(e)}", "red"))
        return []

def save_dataset(dataset: list, desc: str) -> None:
    try:
        # Sanitize first 20 chars of description for filename
        filename = re.sub(r'[^a-zA-Z0-9]', '_', desc[:20]) + '.jsonl'
        print(colored(f"Saving dataset to {filename}...", "cyan"))
        
        with open(filename, 'w', encoding='utf-8') as f:
            for example in dataset:
                f.write(json.dumps(example) + '\n')
                
        print(colored("Dataset saved successfully!", "green"))
        
    except Exception as e:
        print(colored(f"Error saving dataset: {str(e)}", "red"))

async def main():
    try:
        # Generate and save dataset
        dataset = await generate_dataset(DATASET_DESC, N_EXAMPLES)
        if dataset:
            save_dataset(dataset, DATASET_DESC)
        
    except Exception as e:
        print(colored(f"Error in main execution: {str(e)}", "red"))

if __name__ == "__main__":
    asyncio.run(main()) 