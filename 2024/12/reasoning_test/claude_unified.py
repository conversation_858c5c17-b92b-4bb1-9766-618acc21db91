import os
from anthropic import Anthropic
from termcolor import colored
import time

class claude_chat:
    def __init__(self, 
                 name="<PERSON>",
                 api_key=None, model="claude-3-opus-20240229", 
                 max_history_words=10000, 
                 max_words_per_message=None, 
                 stream=True, 
                 max_retry=10,
                 ):
        
        self.name = name
        self.api_key = api_key or os.getenv("ANTHROPIC_API_KEY") or "YOUR_API_KEY"
        self.model = model
        self.history = []
        self.max_history_words = max_history_words
        self.max_words_per_message = max_words_per_message
        self.stream = stream
        self.system_message = "You are a helpful assistant."  # Default system message
        self.max_retry = max_retry

        self.client = Anthropic(api_key=os.getenv("ANTHROPIC_API_KEY") or self.api_key)

        # print the initialization status
        print(colored(f"{self.name} initialized with stream={stream}, max_history_words={max_history_words}, max_words_per_message={max_words_per_message}", "red"))

    def set_system_message(self, message=None):
        if message:
            self.system_message = message
        else:
            self.system_message = "You are a helpful assistant."  # Reset to default if no message provided


    def add_message(self, role, content):
        if role == "user" and self.max_words_per_message:
            self.history.append({"role": role, "content": str(content) + f" please use {self.max_words_per_message} words or less"})
        elif role == "user" and self.max_words_per_message is None:
            self.history.append({"role": role, "content": str(content)})
        else:
            self.history.append({"role": role, "content": str(content)})

    def print_history_length(self):
        history_length = [len(str(message["content"]).split()) for message in self.history]
        print("\n")
        print(f"current history length is {sum(history_length)} words")

    def clear_history(self):
        self.history.clear()

    def chat(self, question, **kwargs):
        self.add_message("user", question)
        return self.get_response(**kwargs)

    def trim_history(self):
        words_count = sum(len(str(message["content"]).split()) for message in self.history if message["role"] != "system")
        while words_count > self.max_history_words and len(self.history) > 1:
            words_count -= len(self.history[0]["content"].split())
            self.history.pop(0)  # Remove second message because first is always system message

    def get_response(self, color="green", should_print=True, **kwargs):
        max_tokens = kwargs.pop('max_tokens', 4000)  # Get max_tokens from kwargs or default to 1000
        # check if the first item in messages is of role "user", if not insert a user message saying "you are a helpful assistant"
        if self.history[0]["role"] != "user":
            self.history.insert(0, {"role": "user", "content": "You are a helpful assistant."})
        retries = 0
        while retries < self.max_retry:
            try:
                response = self.client.messages.create(
                    model=self.model,
                    system=self.system_message,
                    messages=self.history,
                    stream=True if self.stream else False,
                    max_tokens=max_tokens,  # Use the max_tokens value
                    **kwargs
                )

                if self.stream:
                    assistant_response = ""
                    for event in response:
                        if getattr(event, 'type', None) == 'content_block_delta':
                            print(colored(event.delta.text, color), end="", flush=True)
                            assistant_response += event.delta.text

                    print("\n")
                else:
                    assistant_response = response.content[0].text
                    # print(assistant_response)
            
                self.add_message("assistant", str(assistant_response))
                self.trim_history()
                
                return assistant_response
                
            except Exception as e:
                # remove last message from history
                # if len(self.history) >= 1:
                #     self.history.pop()
                print("Error:", e)
                retries += 1
                time.sleep(1)
                continue
        


