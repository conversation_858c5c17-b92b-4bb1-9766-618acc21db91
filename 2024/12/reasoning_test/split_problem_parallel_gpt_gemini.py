import itertools
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from openai_unified import GPT_calls
import json
import time
import random
import os

parallel_runs_amount = 20 # set 1 for sequential runs SET IT LOW FOR GEMINI MODELS DUE TO RATE LIMITS
stream = False # select True for streaming mode
combinations_to_test = 10000 # 120 is the max combinations for 5 sentence problem. set it to a lower number if you want to test a smaller number of combinations
# List of models to test
model_names = ["gpt-4o"]

# model_names = ["gpt-4o"] 


problem_1 = "A bumper car rink has 12 red cars. They have 2 fewer green cars than they have red cars. They have 3 times the number of blue cars as they have green cars. The rink also has yellow cars. If the rink has 75 cars in total how many yellow cars do they have?"
answer_1 = "23"
problem_2 = "<PERSON> is trying to decide whether he really needs to do his homework. If the normal teacher comes in, there's a 40% chance she'll give everyone an extension. There's a 50% chance that tomorrow he'll have a substitute teacher who won't collect the homework. Even if the whole class doesn't get an extension, there's a 20% chance <PERSON> can convince the teacher his dog ate his assignment and get a personal extension. What is the percentage chance that <PERSON> will actually have to turn in his homework tomorrow?"
answer_2 = "24"
problem_3 = "<PERSON> has M sisters and N brothers. How many sisters does <PERSON>'s brother have?. . ."
answer_3 = "m + 1" or "M + 1" or "m+1" or "M+1"
problem_4 = "<PERSON> has N brothers and she also has M sisters. her mother is 44 years old and has 3 sisters. her dad is 47 and has 5 siblings. How many sisters does <PERSON>'s brother have?"
answer_4 = "m + 1" or "M + 1" or "m+1" or "M+1"

problems = [problem_3, problem_4]

for problem in problems:
    print(f"Testing problem: {problem}")
    system_message = ""

    # Split the problem into its individual sentences
    problem_sentences = problem.split(". ")

    # Generate all possible sentence combinations
    sentence_combinations = list(itertools.permutations(problem_sentences))
    print(f"Number of sentence combinations: {len(sentence_combinations)}")
    random.shuffle(sentence_combinations)
    print(f"Total combinations: {len(sentence_combinations)}")
    # Function to make a call to GPT and check if the correct answer is in the response
    def check_gpt_response(sentence_combination, model_name=None):
        if problem == problem_3 or problem == problem_4:
            reordered_problem = ". ".join(sentence_combination)
            reordered_problem = reordered_problem + " please return your answer at the very end of your response as 'Answer: X'"
        else:
            reordered_problem = ". ".join(sentence_combination)
        GPT = GPT_calls(stream=stream, model=model_name)
        if system_message != "":
            GPT.add_message("system", system_message)
        GPT.add_message("user", reordered_problem)
        
        while True:
            try:
                response = GPT.get_response()
                break
            except Exception as e:
                print(e)
                if "rate_limit_error" in str(e) or "Resource has been exhausted" in str(e):
                    print("Rate limit error. Waiting for 30 seconds")
                    time.sleep(60)
                    continue
        if problem == problem_1:
            return "23" in response[-100:], response
        elif problem == problem_2:
            return "24" in response[-100:], response
        elif problem == problem_3 or problem == problem_4:
            return "m + 1" in response.lower()[-100:] or "M + 1" in response.lower()[-100:] or "m+1" in response.lower()[-100:] or "M+1" in response.lower()[-100:], response

    # Use ThreadPoolExecutor to make parallel calls
    for model_name in model_names:
        print(f"Testing model: {model_name}")
        results = {"system_message": system_message, "correct": 0, "incorrect": 0}
        incorrect_answers = []
        
        # Create a JSON file for incorrect answers
        incorrect_answers_file = f'incorrect_answers_{model_name}_{problem[:10]}.json'
        
        with ThreadPoolExecutor(max_workers=parallel_runs_amount) as executor:
            future_to_combination = {executor.submit(check_gpt_response, combination, model_name): combination for combination in sentence_combinations[:combinations_to_test]}
            for future in as_completed(future_to_combination):
                is_correct, response = future.result()
                if is_correct:
                    print("Correct")
                    results["correct"] += 1
                else:
                    print("Incorrect")
                    results["incorrect"] += 1
                    incorrect_answers.append({
                        "problem": ". ".join(future_to_combination[future]),
                        "response": response
                    })
                    # Write incorrect answer to JSON file in real-time
                    with open(incorrect_answers_file, 'w') as f:
                        json.dump(incorrect_answers, f, indent=2)

        # check how many files in the folder start with "results"
        files = os.listdir()
        results_files = [file for file in files if file.startswith("results")]

        # Write the results to a JSON file enumerating the file number
        with open(f'results__for_{model_name}_{len(results_files)}_for_{problem[:10]}.json', 'w') as f:
            json.dump(results, f)

        print("Results written to results.json")
        print(f"Incorrect answers written to {incorrect_answers_file}")