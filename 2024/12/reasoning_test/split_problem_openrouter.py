import itertools
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from open_router_unified import router_chat
import json
import time
import random
import os

parallel_runs_amount = 20 # set 1 for sequential runs
stream = False # set True for streaming mode
combinations_to_test = 120 # 120 is the max combinations for 5 sentence problem. set it to a lower number if you want to test a smaller number of combinations

# List of models to test
# model_names = ["meta-llama/llama-3-70b-instruct", "meta-llama/llama-3-8b-instruct", "mistralai/mixtral-8x22b", "microsoft/wizardlm-2-8x22b"]
# model_names = ["mistralai/mistral-large"]
# model_names = ["qwen/qwen-2-72b-instruct"]
# model_names = ["deepseek/deepseek-coder"]
model_names = ["openai/o1-preview"]

problem_1 = "A bumper car rink has 12 red cars. They have 2 fewer green cars than they have red cars. They have 3 times the number of blue cars as they have green cars. The rink also has yellow cars. If the rink has 75 cars in total how many yellow cars do they have?"
problem_2 = "<PERSON> is trying to decide whether he really needs to do his homework. If the normal teacher comes in, there's a 40% chance she'll give everyone an extension. There's a 50% chance that tomorrow he'll have a substitute teacher who won't collect the homework. Even if the whole class doesn't get an extension, there's a 20% chance <PERSON> can convince the teacher his dog ate his assignment and get a personal extension. What is the percentage chance that <PERSON> will actually have to turn in his homework tomorrow?"
problem_3 = "<PERSON> has <PERSON> sisters and N brothers. How many sisters does <PERSON>'s brother have?. . ."
problem_4 = "<PERSON> has N brothers and she also has M sisters. her mother is 44 years old and has 3 sisters. her dad is 47 and has 5 siblings. How many sisters does <PERSON>'s brother have?"


problems = [problem_2, problem_3, problem_4]

for problem in problems:
    print(f"Testing problem: {problem}")
    system_message = "You are a perfect logician.. at the end always return the concise final answer as 'Answer: X'. do not say anything after the final answer"

    # Split the problem into its individual sentences
    problem_sentences = problem.split(". ")

    # Generate all possible sentence combinations
    sentence_combinations = list(itertools.permutations(problem_sentences))
    random.shuffle(sentence_combinations)
    print(f"Total combinations: {len(sentence_combinations)}")
    # Function to make a call to open_router and check if "24%" is in the response
    def check_model_response(sentence_combination, model_name="meta-llama/llama-3-70b-instruct"):
        if problem == problem_3 or problem == problem_4:
            reordered_problem = ". ".join(sentence_combination)
            reordered_problem = reordered_problem + " please return your answer at the very end of your response as 'Answer: X'"
        else:
            reordered_problem = ". ".join(sentence_combination)
        
        open_router = router_chat(stream=stream, model=model_name)
        if system_message != "":
            open_router.set_system_message(system_message)
        open_router.add_message("user", reordered_problem)
        
        while True:
            try:
                response = open_router.get_response(should_print=False)
                break
            except Exception as e:
                print(e)
                if "rate_limit_error" in str(e):
                    print("Rate limit error. Waiting for 30 seconds")
                    time.sleep(30)
                    continue
        if problem == problem_1:
            return "23" in response[-100:], response
        elif problem == problem_2:
            return "24" in response[-100:], response
        elif problem == problem_3 or problem == problem_4:
            return "m + 1" in response.lower()[-100:] or "M + 1" in response.lower()[-100:] or "m+1" in response.lower()[-100:] or "M+1" in response.lower()[-100:], response

    for model_name in model_names:
        print(f"Testing model: {model_name}")
        results = {"system_message": system_message, "correct": 0, "incorrect": 0}
        
        with ThreadPoolExecutor(max_workers=parallel_runs_amount) as executor:
            future_to_combination = {executor.submit(check_model_response, combination, model_name): combination for combination in sentence_combinations[:combinations_to_test]}
            for future in as_completed(future_to_combination):
                is_correct, _ = future.result()
                if is_correct:
                    print("Correct")
                    results["correct"] += 1
                else:
                    print("Incorrect")
                    results["incorrect"] += 1

        # check how many files in the folder start with "results"
        files = os.listdir()
        model_name = model_name.split("/")[-1]
        results_files = [file for file in files if file.startswith(f"results__for_{model_name}")]
        
        # Write the results to a JSON file enumerating the file number
        with open(f'results__for_{model_name}_for_{problem[:10]}_{len(results_files)}.json', 'w') as f:
            json.dump(results, f)

        print(f"Results written to results__for_{model_name}_for_{problem[:10]}_{len(results_files)}.json")
