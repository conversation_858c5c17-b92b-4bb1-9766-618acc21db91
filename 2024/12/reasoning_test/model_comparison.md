# Model Performance Comparison

Models ranked by Alice+ problem performance.
Note: Each problem is tested with all possible sentence combinations (sentences shuffled in every possible order):

- Marcus Problem: 5 sentences = 120 possible combinations (5!)
- Alice+ Problem: 4 sentences = 24 possible combinations (4!)
- Alice Problem: 2 meaningful sentences + 2 blank sentences = 24 possible combinations (4!)

| Model             | Marcus Problem | Alice Problem | Alice+ Problem | Alice+ Success Rate |
| ----------------- | -------------- | ------------- | -------------- | ------------------- |
| SONNET 3.5 (NEW)  | 115/120        | 22/24         | 18/24          | 75.0%               |
| o1-preview        | N/A            | 23/24         | 17/24          | 70.8%               |
| Qwen QWQ 32B      | 107/120        | 21/24         | 16/24          | 66.7%               |
| o1-mini           | 119/120        | 24/24         | 14/24          | 58.3%               |
| LLAMA 3.3 70B     | 103/120        | 22/24         | 10/24          | 41.7%               |
| Qwen2.5 Coder 32B | 78/120         | 14/24         | 7/24           | 29.2%               |
| GPT-4o            | 99/120         | 4/24          | 4/24           | 16.7%               |
| SONNET 3.5        | 112/120        | 21/24         | 2/24           | 8.3%                |
| 3.5 Haiku (NEW)   | 118/120        | 5/24          | 2/24           | 8.3%                |
| LLAMA3.1 405B     | 90/120         | 15/24         | 2/24           | 8.3%                |
| Gemini 2.0 Flash  | 112/120        | 3/24          | 2/24           | 8.3%                |
| Nova Micro v1     | 17/120         | 0/24          | 0/24           | 0.0%                |

# Test Methodology

Each problem is tested by presenting it to the model with its sentences in different orders. This tests the model's ability to understand the problem regardless of how the information is presented. The number of combinations depends on the number of sentences in each problem:

1. Marcus Problem (5 sentences):

   - "If the normal teacher comes in, there's a 40% chance she'll give everyone an extension"
   - "There's a 50% chance that tomorrow he'll have a substitute teacher who won't collect the homework"
   - "Even if the whole class doesn't get an extension, there's a 20% chance Marcus can convince the teacher his dog ate his assignment and get a personal extension"
   - "Marcus is trying to decide whether he really needs to do his homework"
   - Question sentence
   - Total: 5! = 120 combinations
2. Alice Problem (4 sentences, 2 meaningful + 2 blank):

   - "Alice has M sisters and N brothers"
   - "How many sisters does Alice's brother have?"
   - ". . ."
   - ". . ."
   - Total: 4! = 24 combinations
3. Alice+ Problem (4 sentences):

   - "Alice has N brothers and she also has M sisters"
   - "her mother is 44 years old and has 3 sisters"
   - "her dad is 47 and has 5 siblings"
   - Question sentence
   - Total: 4! = 24 combinations

## Test Problems

### Marcus Problem

Question: Marcus is trying to decide whether he really needs to do his homework. If the normal teacher comes in, there's a 40% chance she'll give everyone an extension. There's a 50% chance that tomorrow he'll have a substitute teacher who won't collect the homework. Even if the whole class doesn't get an extension, there's a 20% chance Marcus can convince the teacher his dog ate his assignment and get a personal extension. What is the percentage chance that Marcus will actually have to turn in his homework tomorrow?
Answer: 24%

### Alice Problem

Question: Alice has M sisters and N brothers. How many sisters does Alice's brother have?. . .
Answer: M + 1

### Alice+ Problem

Question: Alice has N brothers and she also has M sisters. her mother is 44 years old and has 3 sisters. her dad is 47 and has 5 siblings. How many sisters does Alice's brother have?
Answer: M + 1
