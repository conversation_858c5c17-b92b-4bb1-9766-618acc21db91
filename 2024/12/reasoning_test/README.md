# AI Model Problem-Solving Analysis

A project to test how different AI models handle problem-solving with varied sentence ordering.

## Core Components

### 1. Model Interfaces
- `openai_unified.py`: OpenAI and Gemini models
- `claude_unified.py`: Anthropic Claude models
- `open_router_unified.py`: OpenRouter models

### 2. Testing Scripts
- `split_problem_parallel_gpt_gemini.py`
- `split_problem_parallel_claude3.py`
- `split_problem_openrouter.py`

## How It Works

1. **Problem Definition**
   - Each script contains 4 test problems
   - Problems test different reasoning types
   - Each problem is split into sentences

2. **Parallel Processing**
   - Uses ThreadPoolExecutor
   - Tests multiple sentence orderings simultaneously
   - Configurable number of parallel runs

3. **Model Testing**
   ```python
   # Configure models to test
   model_names = ["gpt-4o"]  # For OpenAI
   model_names = ["claude-3-opus-20240229"]  # For Claude
   model_names = ["meta-llama/llama-3-70b-instruct"]  # For OpenRouter
   ```

4. **Problem Types**
   ```python
   # Mathematical
   problem_1 = "A bumper car rink..."
   
   # Probability
   problem_2 = "Marcus homework..."
   
   # Logic
   problem_3 = "Alice siblings..."
   
   # Extended Logic
   problem_4 = "Alice family..."
   ```

## Usage

1. Set environment variables:
```bash
export OPENAI_API_KEY="your_key"
export ANTHROPIC_API_KEY="your_key"
export OPENROUTER_API_KEY="your_key"
export GEMINI_API_KEY="your_key"
```

2. Run tests:
```bash
python split_problem_parallel_gpt_gemini.py
python split_problem_parallel_claude3.py
python split_problem_openrouter.py
```

## Configuration

- `parallel_runs_amount`: Concurrent API calls (default: 20)
- `combinations_to_test`: Number of permutations (default: 10000)
- `stream`: Enable/disable streaming
- `model_names`: Models to test

## Results

Results saved as JSON files:
- `results__for_{model_name}_for_{problem_preview}_{count}.json`
- `incorrect_answers_{model_name}_{problem_preview}.json`

## Requirements

- Python 3.8+
- API access:
  - OpenAI
  - Anthropic
  - OpenRouter
  - Google (Gemini)