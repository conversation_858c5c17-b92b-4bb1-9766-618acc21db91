<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8"/>
<meta name="viewport" content="width=device-width, initial-scale=1.0"/>
<title>Interactive Reactive Music Visualization with Deeper Shadows</title>
<style>
  body {
    margin:0; 
    overflow:hidden; 
    font-family:sans-serif; 
    background: linear-gradient(135deg, #2a034f 0%, #ff0080 100%);
  }
  #audioInput {
    position: absolute;
    top: 20px;
    left: 20px;
    z-index: 999;
    color: #fff;
    background: rgba(0,0,0,0.5);
    border: none;
    padding: 8px;
    cursor: pointer;
    font-size: 14px;
    border-radius: 4px;
  }
  .dg {
    z-index: 1000 !important;
  }
</style>
</head>
<body>
<input type="file" accept="audio/*" id="audioInput" />

<script src="https://cdn.jsdelivr.net/npm/three@0.146.0/build/three.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/three@0.146.0/examples/js/controls/OrbitControls.js"></script>
<script src="https://cdn.jsdelivr.net/npm/dat.gui@0.7.9/build/dat.gui.min.js"></script>
<script>
let scene, camera, renderer, controls;
let mainSphere, originalPositions;
let analyser, dataArray;
let audioContext, source, audio;
let ringGroup;
let numObjects = 64;
let innerGlow;

let params = {
  bassImpact: 0.5,
  midImpact: 0.3,
  highImpact: 0.15,
  hueSpeed: 10.0,
  ringBaseRadius: 2.2,
  ringJitterScale: 0.05,
  sphereEmissiveIntensity: 1.0,
  rotationSpeedBass: 0.001,
  rotationSpeedMid: 0.0005,
  ringRotationBass: 0.0005,
  ringRotationMid: 0.0003
};

init();
animate();
createGUI();

function init() {
  scene = new THREE.Scene();
  scene.fog = new THREE.Fog(0x120025, 5, 20);

  camera = new THREE.PerspectiveCamera(65, window.innerWidth/window.innerHeight, 0.1, 1000);
  camera.position.z = 4;

  renderer = new THREE.WebGLRenderer({antialias:true});
  renderer.setSize(window.innerWidth, window.innerHeight);
  renderer.shadowMap.enabled = true;
  renderer.shadowMap.type = THREE.PCFSoftShadowMap;
  document.body.appendChild(renderer.domElement);

  controls = new THREE.OrbitControls(camera, renderer.domElement);
  controls.enableDamping = true;
  controls.dampingFactor = 0.05;
  controls.enablePan = false;
  controls.minDistance = 2;
  controls.maxDistance = 10;

  // Key Light - stronger and more directional
  const keyLight = new THREE.DirectionalLight(0xffffff, 2.0);
  keyLight.position.set(-2, 2, 2);
  keyLight.castShadow = true;
  keyLight.shadow.mapSize.width = 2048;
  keyLight.shadow.mapSize.height = 2048;
  scene.add(keyLight);

  // Fill Light - made dimmer or optional
  const fillLight = new THREE.DirectionalLight(0xffffff, 0.1);
  fillLight.position.set(2, -2, -2);
  fillLight.castShadow = false;
  scene.add(fillLight);
  
  // Reduce ambient light significantly for deeper shadows
  const ambient = new THREE.AmbientLight(0xffffff, 0.05);
  scene.add(ambient);

  let geometry = new THREE.SphereGeometry(1, 128, 128);
  let material = new THREE.MeshPhysicalMaterial({
    color: 0xffffff,
    roughness: 0.2,
    metalness: 0.4,
    emissive: 0x000000,
    emissiveIntensity: params.sphereEmissiveIntensity,
    clearcoat: 1.0,
    clearcoatRoughness: 0.05,
    // Reduce transmission and thickness to make the sphere more opaque, enhancing shadow visibility
    transmission: 0.0,
    thickness: 0.1,
    ior: 1.4,
    sheen: 1.0,
    sheenRoughness: 0.2,
    reflectivity: 0.9
  });
  mainSphere = new THREE.Mesh(geometry, material);
  mainSphere.castShadow = true;
  mainSphere.receiveShadow = true;
  scene.add(mainSphere);
  originalPositions = mainSphere.geometry.attributes.position.array.slice();

  // Inner glow remains for atmosphere, but won't cast/receive shadows
  let glowGeo = new THREE.SphereGeometry(0.9, 64, 64);
  let glowMat = new THREE.ShaderMaterial({
    uniforms: {
      "c": { type: "f", value: 0.6 },
      "p": { type: "f", value: 2.0 },
      glowColor: { type: "c", value: new THREE.Color(0xffffff) },
      viewVector: { type: "v3", value: camera.position }
    },
    vertexShader: `
      uniform vec3 viewVector;
      varying float intensity;
      void main() {
        vec3 vNormal = normalize(normalMatrix * normal);
        vec3 vNormel = normalize(normalMatrix * viewVector);
        intensity = pow(dot(vNormal, vNormel), 6.0);
        gl_Position = projectionMatrix * modelViewMatrix * vec4(position,1.0);
      }
    `,
    fragmentShader: `
      uniform vec3 glowColor;
      varying float intensity;
      void main() {
        vec3 color = glowColor * intensity;
        gl_FragColor = vec4(color, 0.45);
      }
    `,
    side: THREE.BackSide,
    blending: THREE.AdditiveBlending,
    transparent: true
  });
  innerGlow = new THREE.Mesh(glowGeo, glowMat);
  innerGlow.scale.multiplyScalar(1.1);
  scene.add(innerGlow);

  ringGroup = new THREE.Group();
  scene.add(ringGroup);

  const smallGeom = new THREE.IcosahedronGeometry(0.08, 2);
  for(let i=0; i<numObjects; i++){
    let smallMat = new THREE.MeshStandardMaterial({
      color:0xffffff, 
      roughness:0.4, 
      metalness:0.5, 
      emissive:0x000000
    });
    let s = new THREE.Mesh(smallGeom, smallMat);
    s.castShadow = true;  
    s.receiveShadow = false; 
    ringGroup.add(s);
  }

  document.getElementById('audioInput').addEventListener('change', handleFile, false);
  window.addEventListener('resize', onWindowResize, false);
}

function handleFile(e) {
  let file = e.target.files[0];
  if(!file) return;
  
  if(audio) {
    audio.pause();
    audio = null;
  }

  audio = new Audio();
  audio.src = URL.createObjectURL(file);
  audio.load();
  audio.play();

  audioContext = new (window.AudioContext || window.webkitAudioContext)();
  source = audioContext.createMediaElementSource(audio);
  analyser = audioContext.createAnalyser();
  analyser.fftSize = 2048;
  dataArray = new Uint8Array(analyser.frequencyBinCount);

  source.connect(analyser);
  analyser.connect(audioContext.destination);
}

function onWindowResize() {
  camera.aspect = window.innerWidth / window.innerHeight;
  camera.updateProjectionMatrix();
  renderer.setSize(window.innerWidth, window.innerHeight);
}

function animate() {
  requestAnimationFrame(animate);
  controls.update();
  render();
}

function render() {
  if(analyser && dataArray) {
    analyser.getByteFrequencyData(dataArray);

    let positions = mainSphere.geometry.attributes.position.array;
    let len = positions.length;
    let time = Date.now()*0.001;

    let bassRange = 64;
    let midRange = 512;
    let bassSum = 0;
    for(let i=0; i<bassRange; i++) bassSum += dataArray[i];
    let bass = bassSum / bassRange;

    let midSum = 0;
    for(let i=bassRange; i<bassRange+midRange && i<dataArray.length; i++) midSum += dataArray[i];
    let mids = midSum / midRange;

    let highSum = 0;
    let highCount = 0;
    for(let i=bassRange+midRange; i<dataArray.length; i++) {
      highSum += dataArray[i];
      highCount++;
    }
    let highs = (highCount > 0) ? (highSum / highCount) : 0;

    for(let i = 0; i < len; i += 3) {
      let vx = originalPositions[i];
      let vy = originalPositions[i+1];
      let vz = originalPositions[i+2];

      let freqIndex = Math.floor((i/3) % dataArray.length);
      let localFreqVal = dataArray[freqIndex]/255;

      let bassFactor = (bass/255)*params.bassImpact;
      let midFactor = (mids/255)*params.midImpact;
      let highFactor = (highs/255)*params.highImpact;

      let offset = localFreqVal * (0.4 + bassFactor) 
                  + Math.sin(time*2 + vx*3 + vy*3 + vz*3)*midFactor
                  + (Math.sin(time*5 + vx*10)*highFactor)*0.5;

      positions[i]   = vx * (1.0 + offset);
      positions[i+1] = vy * (1.0 + offset);
      positions[i+2] = vz * (1.0 + offset);
    }
    mainSphere.geometry.attributes.position.needsUpdate = true;

    let hue = ((time*params.hueSpeed) % 360)/360;
    let saturation = 0.9;
    let lightness = 0.5 + (highs/255)*0.3;
    mainSphere.material.color.setHSL(hue, saturation, lightness);

    mainSphere.material.emissive.setHSL((hue+0.1)%1, 1.0, 0.5);
    mainSphere.material.emissiveIntensity = params.sphereEmissiveIntensity;

    let sheenHue = (hue + 0.5) % 1;
    mainSphere.material.sheenColor = new THREE.Color().setHSL(sheenHue, 1.0, 0.5);

    mainSphere.rotation.y += params.rotationSpeedBass * bass; 
    mainSphere.rotation.x += params.rotationSpeedMid * mids;

    let ringChildren = ringGroup.children;
    let ringLen = ringChildren.length;
    for(let i=0; i<ringLen; i++){
      let obj = ringChildren[i];
      let angle = (i/ringLen)*Math.PI*2 + time*0.2;
      let ringIndex = Math.floor((i/ringLen)*dataArray.length);
      let ringVal = dataArray[ringIndex]/255;

      let jitterVal = highVal(dataArray, i, ringLen)*params.ringJitterScale;
      let baseRadius = params.ringBaseRadius + (highs/255)*0.3;
      obj.position.set(Math.cos(angle)*(baseRadius+jitterVal), Math.sin(angle)*(baseRadius+jitterVal), 0);
      obj.scale.setScalar(1 + ringVal*2.5*(1+(highs/255)*0.5));

      let objHue = (hue + ringVal*0.5) % 1;
      let objLight = 0.4 + ringVal*0.4;
      obj.material.color.setHSL(objHue, 1.0, objLight);
      obj.material.emissive.setHSL((objHue+0.5)%1, 1.0, 0.5);
    }

    ringGroup.rotation.z += params.ringRotationBass*bass;
    ringGroup.rotation.x += params.ringRotationMid*mids;

    let glowHue = (hue + 0.2) % 1;
    innerGlow.material.uniforms.viewVector.value = new THREE.Vector3().subVectors(camera.position, innerGlow.position);
    innerGlow.material.uniforms.glowColor.value.setHSL(glowHue, 1.0, 0.5 + (highs/255)*0.3);
  }

  renderer.render(scene, camera);
}

function highVal(data, i, total) {
  let start = Math.floor(data.length*0.7);
  let end = data.length-1;
  let idx = start + Math.floor(((i/total) * (end - start)));
  return data[idx]/255;
}

function createGUI() {
  const gui = new dat.GUI();
  gui.add(params, 'bassImpact', 0.0, 1.0).name('Bass Impact');
  gui.add(params, 'midImpact', 0.0, 1.0).name('Mid Impact');
  gui.add(params, 'highImpact', 0.0, 1.0).name('High Impact');
  gui.add(params, 'hueSpeed', 1.0, 50.0).name('Hue Speed');
  gui.add(params, 'ringBaseRadius', 1.0, 5.0).name('Ring Base Radius');
  gui.add(params, 'ringJitterScale', 0.0, 0.2).name('Ring Jitter');
  gui.add(params, 'sphereEmissiveIntensity', 0.0, 2.0).name('Sphere Emissive');
  gui.add(params, 'rotationSpeedBass', 0.0, 0.005).name('Rotation Bass');
  gui.add(params, 'rotationSpeedMid', 0.0, 0.005).name('Rotation Mid');
  gui.add(params, 'ringRotationBass', 0.0, 0.005).name('Ring Rot Bass');
  gui.add(params, 'ringRotationMid', 0.0, 0.005).name('Ring Rot Mid');
}
</script>
</body>
</html>
