<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0"/>
<title>Fluid Reactive Audio Visualization</title>
<style>
  body { margin:0; background: #000; overflow:hidden; font-family:sans-serif; color:#fff; }
  #upload { position:absolute; top:10px; left:10px; z-index:999; color:#fff; }
  #controls {
    position:absolute; 
    top:50px; 
    left:10px; 
    z-index:999; 
    background:rgba(0,0,0,0.3); 
    padding:10px; 
    border-radius:5px; 
    font-size:14px;
  }
  #controls label {
    display:block; 
    margin-bottom:5px; 
    font-weight:bold;
  }
  #controls input[type=range] {
    width:100px;
    margin-bottom:10px;
  }
</style>
</head>
<body>
<input id="upload" type="file" accept="audio/*"/>
<div id="controls">
  <label>Bloom</label>
  <input id="bloomSlider" type="range" min="0" max="1" step="0.01" value="0.5">
  <label>Ring Reactivity</label>
  <input id="ringSlider" type="range" min="0" max="5" step="0.1" value="1">
  <label>Hue Shift</label>
  <input id="hueSlider" type="range" min="-0.5" max="0.5" step="0.01" value="0">
</div>

<script src="https://cdn.jsdelivr.net/npm/three@0.127.0/build/three.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/three@0.127.0/examples/js/shaders/CopyShader.js"></script>
<script src="https://cdn.jsdelivr.net/npm/three@0.127.0/examples/js/shaders/LuminosityHighPassShader.js"></script>
<script src="https://cdn.jsdelivr.net/npm/three@0.127.0/examples/js/postprocessing/EffectComposer.js"></script>
<script src="https://cdn.jsdelivr.net/npm/three@0.127.0/examples/js/postprocessing/RenderPass.js"></script>
<script src="https://cdn.jsdelivr.net/npm/three@0.127.0/examples/js/postprocessing/ShaderPass.js"></script>
<script src="https://cdn.jsdelivr.net/npm/three@0.127.0/examples/js/postprocessing/UnrealBloomPass.js"></script>
<script src="https://cdn.jsdelivr.net/npm/three@0.127.0/examples/js/controls/OrbitControls.js"></script>

<script>
let scene, camera, renderer, composer;
let analyser, dataArray, uniforms, clock;
let barsInner = [], barsOuter = [], particles, centralMesh, envMesh, innerCoreMesh;
let ringMesh, ringMesh2;
let orbitingSpheres = [];
let audioCtx, audioSource, controls;
const FFT_SIZE = 256;

// User-controlled parameters
let userBloom = 0.5;
let userRingFactor = 1.0;
let userHueShift = 0.0;

let ringUniforms = {
  time: { value:0 },
  amplitude: { value:0.0 },
  userHue: { value:0.0 }
};

let ringUniforms2 = {
  time: { value:0 },
  amplitude: { value:0.0 },
  userHue: { value:0.0 }
};

let envUniforms = {
  time: { value:0 },
  intensity: { value:0.0 },
  userHue: { value:0.0 }
};

let bloomPass;

init();
animate();

function init() {
  scene = new THREE.Scene();
  // Start the camera more zoomed in and looking down at the core
  camera = new THREE.PerspectiveCamera(70, innerWidth/innerHeight, 0.1, 3000);
  camera.position.set(0, 50, 100);
  camera.lookAt(0,0,0);

  renderer = new THREE.WebGLRenderer({antialias:true});
  renderer.setSize(innerWidth, innerHeight);
  document.body.appendChild(renderer.domElement);

  window.addEventListener('resize', ()=> {
    camera.aspect = innerWidth/innerHeight;
    camera.updateProjectionMatrix();
    renderer.setSize(innerWidth, innerHeight);
    composer.setSize(innerWidth, innerHeight);
    uniforms.resolution.value.set(innerWidth, innerHeight);
  });

  const mainLight = new THREE.PointLight(0xffffff, 0.7);
  mainLight.position.set(0,30,50);
  scene.add(mainLight);

  const ambient = new THREE.AmbientLight(0x404040,0.3);
  scene.add(ambient);

  clock = new THREE.Clock();

  uniforms = {
    time: { value:0 },
    intensity: { value:0.0 },
    resolution: { value: new THREE.Vector2(innerWidth, innerHeight) }
  };

  // Bars
  createBars(64, 30, barsInner, 0.5, 1);
  createBars(64, 45, barsOuter, 0.3, 0.8);

  // Central sphere
  centralMesh = createCentralSphere();
  scene.add(centralMesh);

  // Inner core sphere
  innerCoreMesh = createInnerCore();
  scene.add(innerCoreMesh);

  // Two reactive rings
  ringMesh = createReactiveRing(20, 1, ringUniforms);
  scene.add(ringMesh);

  ringMesh2 = createReactiveRing(10, 0.5, ringUniforms2);
  scene.add(ringMesh2);

  // Particles
  particles = createParticles(3000,500);
  scene.add(particles);

  // Orbiting spheres
  createOrbitingSpheres(8,12);

  // Environment sphere
  envMesh = createEnvSphere(envUniforms);
  scene.add(envMesh);

  // Ground plane
  let plane = createGroundPlane();
  scene.add(plane);

  // Post-processing
  const renderScene = new THREE.RenderPass(scene, camera);
  bloomPass = new THREE.UnrealBloomPass(new THREE.Vector2(innerWidth, innerHeight), 1.0, 0.4, 0.75);
  bloomPass.threshold = 0.3;
  bloomPass.strength = 0.9;
  bloomPass.radius = 0.35;

  composer = new THREE.EffectComposer(renderer);
  composer.setSize(innerWidth, innerHeight);
  composer.addPass(renderScene);
  composer.addPass(bloomPass);

  // OrbitControls for user rotation and zoom
  controls = new THREE.OrbitControls(camera, renderer.domElement);
  controls.enableDamping = true;
  controls.dampingFactor = 0.05;
  controls.minDistance = 50;
  controls.maxDistance = 500;
  controls.maxPolarAngle = Math.PI/2;
  controls.enablePan = false;

  document.getElementById('upload').addEventListener('change', handleUpload);

  // Slider event listeners
  document.getElementById('bloomSlider').addEventListener('input', (e)=>{
    userBloom = parseFloat(e.target.value);
  });
  document.getElementById('ringSlider').addEventListener('input', (e)=>{
    userRingFactor = parseFloat(e.target.value);
  });
  document.getElementById('hueSlider').addEventListener('input', (e)=>{
    userHueShift = parseFloat(e.target.value);
  });
}

function createBars(count, radius, barArray, width, heightFactor){
  for (let i = 0; i < count; i++) {
    let angle = (i / count) * Math.PI * 2;
    let geo = new THREE.BoxGeometry(width, 1, width);
    let mat = new THREE.MeshStandardMaterial({color:0xffffff, emissive:0x000000, metalness:0.3, roughness:0.5});
    let mesh = new THREE.Mesh(geo, mat);
    mesh.position.set(Math.cos(angle)*radius, 0, Math.sin(angle)*radius);
    mesh.lookAt(new THREE.Vector3(0,0,0));
    mesh.userData = { radius: radius, angle: angle, heightFactor: heightFactor };
    scene.add(mesh);
    barArray.push(mesh);
  }
}

function createCentralSphere(){
  let sphereGeo = new THREE.SphereBufferGeometry(6,64,64);
  let sphereMat = new THREE.ShaderMaterial({
    uniforms:uniforms,
    vertexShader:`
      varying vec3 vPos;
      varying vec2 vUv;
      uniform float time;
      uniform float intensity;
      void main(){
        vUv = uv;
        vec3 displaced = position + normal * sin((position.x+position.y+position.z)*0.15 + time)*intensity*1.2;
        vPos = displaced;
        gl_Position = projectionMatrix * modelViewMatrix * vec4(displaced,1.0);
      }
    `,
    fragmentShader:`
      uniform float time;
      uniform float intensity;
      varying vec2 vUv;
      varying vec3 vPos;
      void main(){
        float r = length(vUv-0.5);
        float fractal = sin((vPos.x+vPos.z)*3.0 + time*3.0 + intensity*8.0)
                      * cos((vPos.y)*3.0 - time*1.5 - intensity*4.0);
        fractal *= sin((r*10.0 - time)*(intensity*2.0+1.0));
        float colR = 0.5+0.35*sin(time+fractal);
        float colG = 0.5+0.35*cos(time*1.2+fractal+0.5);
        float colB = 0.5+0.4*sin(time*1.7+fractal+1.0);
        vec3 base = vec3(colR, colG, colB)*0.7;
        float fresnel = pow(1.0 - dot(normalize(vPos), vec3(0.0,1.0,0.0)), 3.0);
        base += vec3(0.2,0.15,0.25)*fresnel*0.3;
        gl_FragColor = vec4(base,1.0);
      }
    `,
    side:THREE.DoubleSide
  });
  return new THREE.Mesh(sphereGeo, sphereMat);
}

function createInnerCore(){
  let coreGeo = new THREE.SphereBufferGeometry(2.5,64,64);
  let coreMat = new THREE.MeshStandardMaterial({
    color:0xffffff,
    emissive:0x111111,
    metalness:0.5,
    roughness:0.3
  });
  return new THREE.Mesh(coreGeo, coreMat);
}

function createReactiveRing(rad, tube, ringUniformsLocal){
  let ringGeo = new THREE.TorusKnotBufferGeometry(rad, tube, 300, 32, 2, 3);
  let ringMat = new THREE.ShaderMaterial({
    uniforms: ringUniformsLocal,
    vertexShader:`
      uniform float time;
      uniform float amplitude;
      uniform float userHue;
      varying vec3 vPos;
      varying float vDist;
      void main(){
        vPos = position;
        float disp = sin(position.x*2.0 + position.y*2.0 + position.z*2.0 + time*3.0)*0.4*amplitude;
        vec3 displaced = position + normalize(position)*disp;

        float angle = time*0.1*amplitude;
        mat3 rotY = mat3(
          cos(angle),0.0,sin(angle),
          0.0,1.0,0.0,
          -sin(angle),0.0,cos(angle)
        );
        displaced = rotY*displaced;

        vDist = length(displaced);
        gl_Position = projectionMatrix * modelViewMatrix * vec4(displaced,1.0);
      }
    `,
    fragmentShader:`
      uniform float time;
      uniform float amplitude;
      uniform float userHue;
      varying vec3 vPos;
      varying float vDist;
      void main(){
        float pulse = sin(vDist*5.0 - time*2.0)*0.5+0.5;
        float hueShift = fract(time*0.05 + amplitude*0.5 + userHue);
        float r = 0.5+0.5*sin((hueShift)*6.2831+pulse*3.14159);
        float g = 0.5+0.5*sin((hueShift+0.33)*6.2831+pulse*3.14159);
        float b = 0.5+0.5*sin((hueShift+0.66)*6.2831+pulse*3.14159);
        float brightness = 0.3 + amplitude*0.7;
        r *= brightness;
        g *= brightness;
        b *= brightness;
        gl_FragColor = vec4(r,g,b,1.0);
      }
    `,
    transparent:true,
    blending:THREE.AdditiveBlending
  });
  return new THREE.Mesh(ringGeo, ringMat);
}

function createParticles(count, range){
  let pGeo = new THREE.BufferGeometry();
  let positions = new Float32Array(count * 3);
  let colors = new Float32Array(count * 3);
  for(let i=0;i<count;i++){
    positions[i*3] = (Math.random()-0.5)*range;
    positions[i*3+1] = (Math.random()-0.5)*range;
    positions[i*3+2] = (Math.random()-0.5)*range;
    let c = 0.3+Math.random()*0.7;
    colors[i*3]=c; colors[i*3+1]=c*0.9; colors[i*3+2]=c*0.95;
  }
  pGeo.setAttribute('position', new THREE.BufferAttribute(positions, 3));
  pGeo.setAttribute('color', new THREE.BufferAttribute(colors, 3));
  const pMat = new THREE.PointsMaterial({
    vertexColors:true, size:0.6, blending:THREE.AdditiveBlending, transparent:true
  });
  return new THREE.Points(pGeo, pMat);
}

function createEnvSphere(envU){
  let envGeo = new THREE.SphereBufferGeometry(1000,64,64);
  let envMat = new THREE.ShaderMaterial({
    uniforms: envU,
    vertexShader:`
      varying vec2 vUv;
      varying vec3 vPos;
      uniform float time;
      uniform float intensity;
      uniform float userHue;
      void main(){
        vUv = uv;
        vPos = position;
        gl_Position = projectionMatrix * modelViewMatrix * vec4(position,1.0);
      }
    `,
    fragmentShader:`
      uniform float time;
      uniform float intensity;
      uniform float userHue;
      varying vec2 vUv;
      varying vec3 vPos;
      float rand(vec2 co){
        return fract(sin(dot(co.xy, vec2(12.9898,78.233))) * 43758.5453);
      }
      void main(){
        float r = length(vUv - 0.5);
        float noise = rand(floor(vUv*50.0));
        float pattern = sin(vPos.y*0.008 + vPos.x*0.008 + time*0.2)*cos(vPos.z*0.008 - time*0.1);
        pattern *= sin(r*20.0 + time) + intensity*1.2;
        float baseHue = sin(time*0.1)*0.5+0.5+userHue;
        baseHue = fract(baseHue);
        float rr = 0.1+0.07*sin(6.2831*(baseHue));
        float gg = 0.1+0.07*sin(6.2831*(baseHue+0.33));
        float bb = 0.1+0.07*sin(6.2831*(baseHue+0.66));
        vec3 baseColor = vec3(rr,gg,bb);
        baseColor += 0.05*sin(pattern)*0.2;
        float vignette = smoothstep(0.8,1.0,r);
        baseColor *= (1.0-vignette*0.7);
        gl_FragColor = vec4(baseColor,1.0);
      }
    `,
    side:THREE.BackSide
  });
  return new THREE.Mesh(envGeo, envMat);
}

function createGroundPlane(){
  let planeGeo = new THREE.PlaneBufferGeometry(600,600,100,100);
  let planeMat = new THREE.ShaderMaterial({
    uniforms: { time: uniforms.time, intensity: uniforms.intensity },
    vertexShader:`
      varying vec2 vUv;
      uniform float time;
      void main(){
        vUv = uv;
        vec3 pos = position;
        pos.y += sin((pos.x+pos.z)*0.1 + time)*0.7;
        gl_Position = projectionMatrix * modelViewMatrix * vec4(pos,1.0);
      }
    `,
    fragmentShader:`
      uniform float time;
      uniform float intensity;
      varying vec2 vUv;
      void main(){
        float dist = distance(vUv, vec2(0.5));
        float pattern = sin(dist*35.0 - time*2.0)+cos(dist*18.0 + time*1.5);
        pattern *= intensity*1.3;
        vec3 col = vec3(0.12+0.35*sin(pattern), 
                        0.1+0.3*cos(pattern), 
                        0.18+0.35*sin(pattern+1.0));
        col *= 0.4;
        gl_FragColor = vec4(col,0.3);
      }
    `,
    transparent:true,
    side:THREE.DoubleSide,
    blending:THREE.AdditiveBlending
  });
  let plane = new THREE.Mesh(planeGeo, planeMat);
  plane.rotation.x = -Math.PI/2;
  plane.position.y = -20;
  return plane;
}

function createOrbitingSpheres(count, radius){
  for(let i=0; i<count; i++){
    let angle = i*(Math.PI*2/count);
    let geo = new THREE.SphereBufferGeometry(0.8,16,16);
    let mat = new THREE.MeshStandardMaterial({
      color:0xffffff,
      emissive:0x000000,
      metalness:0.4,
      roughness:0.4
    });
    let sphere = new THREE.Mesh(geo, mat);
    sphere.userData = { baseAngle: angle, radius: radius };
    scene.add(sphere);
    orbitingSpheres.push(sphere);
  }
}

function handleUpload(e){
  const file = e.target.files[0];
  if(!file) return;
  if(audioCtx) audioCtx.close();
  audioCtx = new (window.AudioContext || window.webkitAudioContext)();

  const reader = new FileReader();
  reader.onload = async (evt)=>{
    const arrayBuffer = evt.target.result;
    const audioBuffer = await audioCtx.decodeAudioData(arrayBuffer);
    audioSource = audioCtx.createBufferSource();
    audioSource.buffer = audioBuffer;
    const analyserNode = audioCtx.createAnalyser();
    analyserNode.fftSize = FFT_SIZE;
    dataArray = new Uint8Array(analyserNode.frequencyBinCount);

    audioSource.connect(analyserNode);
    analyser = analyserNode;
    analyser.connect(audioCtx.destination);
    audioSource.start(0);
  };
  reader.readAsArrayBuffer(file);
}

function animate() {
  requestAnimationFrame(animate);
  const time = clock.getElapsedTime();
  uniforms.time.value = time;
  if(envUniforms) envUniforms.time.value = time;

  controls.update();

  if(analyser && dataArray){
    analyser.getByteFrequencyData(dataArray);
    let avg = dataArray.reduce((a,b)=>a+b,0)/dataArray.length;
    uniforms.intensity.value = avg/255.0;
    if(envUniforms) envUniforms.intensity.value = uniforms.intensity.value;

    let ringIndex = Math.floor(dataArray.length*0.25);
    let ringAmplitude = dataArray[ringIndex]/255.0;

    let ringIndex2 = Math.floor(dataArray.length*0.75);
    let ringAmplitude2 = dataArray[ringIndex2]/255.0;

    ringUniforms.time.value = time;
    ringUniforms.amplitude.value = ringAmplitude * uniforms.intensity.value * userRingFactor;
    ringUniforms.userHue.value = userHueShift;

    ringUniforms2.time.value = time;
    ringUniforms2.amplitude.value = ringAmplitude2 * uniforms.intensity.value * userRingFactor;
    ringUniforms2.userHue.value = userHueShift;

    if(envUniforms) envUniforms.userHue.value = userHueShift;

    // Adjust bloom strength from userBloom
    bloomPass.strength = 0.5 + userBloom * 1.5;

    // Rings rotate and react
    ringMesh.rotation.x += 0.01 + 0.05*ringAmplitude;
    ringMesh.rotation.y += 0.006 + 0.03*ringAmplitude;

    ringMesh2.rotation.x -= 0.008 + 0.04*ringAmplitude2;
    ringMesh2.rotation.y += 0.01 + 0.04*ringAmplitude2;

    // Bars update
    updateBars(barsInner, dataArray, time, 0.5);
    updateBars(barsOuter, dataArray, time, -0.4);

    // Central spheres
    centralMesh.rotation.y = time*0.4;
    centralMesh.rotation.x = Math.sin(time*0.2)*0.2;
    innerCoreMesh.rotation.y = -time*0.6;
    innerCoreMesh.rotation.x = Math.cos(time*0.3)*0.2;
    innerCoreMesh.material.emissiveIntensity = uniforms.intensity.value*0.2;

    // Particles subtle motion
    particles.rotation.y += 0.00015*avg/50;
    particles.rotation.x += 0.00005*avg/50;

    // Orbiting spheres revolve
    let baseRadius = 12 + uniforms.intensity.value*5.0;
    for(let i=0; i<orbitingSpheres.length; i++){
      let s = orbitingSpheres[i];
      let angle = s.userData.baseAngle + time*0.5;
      s.position.x = Math.cos(angle)*baseRadius;
      s.position.z = Math.sin(angle)*baseRadius;
      s.position.y = Math.sin(time*0.7 + i)*2;
      s.material.emissive.setHSL((time*0.3 + i*0.1)%1.0,0.7,uniforms.intensity.value*0.3);
    }
  }

  composer.render();
}

function updateBars(barArray, freqData, time, rotationSpeed){
  let length = barArray.length;
  for(let i=0; i<length; i++){
    let scale = freqData[i]/35;
    scale = Math.max(scale, 0.1);
    barArray[i].scale.y = scale*barArray[i].userData.heightFactor;
    barArray[i].material.emissive.setHSL(((i/length)+(time*0.1))%1.0,0.7,0.3*scale);
    barArray[i].rotation.y += rotationSpeed*0.005;
  }
}
</script>
</body>
</html>
