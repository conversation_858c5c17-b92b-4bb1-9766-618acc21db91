<!DOCTYPE html>
<html lang="en" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Visualization Switcher</title>
    <link href="https://cdn.jsdelivr.net/npm/daisyui@3.9.4/dist/full.css" rel="stylesheet" type="text/css" />
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js"></script>
    <style>
        .btn-glow:hover {
            animation: glow 1.5s ease-in-out infinite alternate;
        }
        @keyframes glow {
            from {
                box-shadow: 0 0 5px #4f46e5, 0 0 10px #4f46e5, 0 0 15px #4338ca;
            }
            to {
                box-shadow: 0 0 10px #4f46e5, 0 0 20px #4f46e5, 0 0 30px #4338ca;
            }
        }
        body, html {
            margin: 0;
            padding: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
        }
        .gradient-text {
            background: linear-gradient(
                to right,
                #ff6b6b,
                #4ecdc4,
                #45b7d1,
                #a06cd5,
                #ff6b6b
            );
            background-size: 200% auto;
            color: transparent;
            background-clip: text;
            -webkit-background-clip: text;
            animation: gradient 3s linear infinite;
        }
        @keyframes gradient {
            to {
                background-position: 200% center;
            }
        }
        .title-hover {
            transition: transform 0.3s ease;
        }
        .title-hover:hover {
            transform: scale(1.05);
        }
    </style>
</head>
<body class="bg-base-300">
    <!-- Fullscreen iframe -->
    <iframe id="visualFrame" src="second.html" class="w-full h-screen border-none absolute top-0 left-0"></iframe>
    
    <!-- Title and Buttons container -->
    <div class="fixed top-4 left-1/2 transform -translate-x-1/2 z-50 flex flex-col items-center gap-4">
        <a href="https://www.echohive.live" target="_blank" rel="noopener noreferrer" class="title-hover">
            <h1 class="gradient-text text-4xl font-bold mb-2">www.echohive.live</h1>
        </a>
        <div class="flex gap-4 bg-base-300/50 p-3 rounded-full backdrop-blur-sm">
            <button id="firstBtn" class="btn btn-primary btn-glow">First Visualization</button>
            <button id="secondBtn" class="btn btn-secondary btn-glow">Second Visualization</button>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const frame = document.getElementById('visualFrame');
            const firstBtn = document.getElementById('firstBtn');
            const secondBtn = document.getElementById('secondBtn');

            // Animation for button clicks
            function animateButton(button) {
                anime({
                    targets: button,
                    scale: [1, 1.1, 1],
                    duration: 300,
                    easing: 'easeInOutQuad'
                });
            }

            firstBtn.addEventListener('click', () => {
                frame.src = 'first.html';
                animateButton(firstBtn);
            });

            secondBtn.addEventListener('click', () => {
                frame.src = 'second.html';
                animateButton(secondBtn);
            });
        });
    </script>
</body>
</html>
