import asyncio
import base64
import json
import os
import pyaudio
from websockets.asyncio.client import connect
from termcolor import colored


class SimpleGeminiVoice:
    def __init__(self):
        try:
            self.api_key = os.getenv("GEMINI_API_KEY")
            if not self.api_key:
                raise ValueError("GEMINI_API_KEY environment variable not found")
            
            # Constants
            self.MODEL = "gemini-2.0-flash-exp"
            self.URI = f"wss://generativelanguage.googleapis.com/ws/google.ai.generativelanguage.v1alpha.GenerativeService.BidiGenerateContent?key={self.api_key}"
            self.FORMAT = pyaudio.paInt16
            self.CHANNELS = 1
            self.CHUNK = 512
            self.INPUT_SAMPLE_RATE = 16000
            self.OUTPUT_SAMPLE_RATE = 24000

            # Initialize PyAudio and print available devices
            self.audio = pyaudio.PyAudio()
            self.print_audio_devices()
            
            print(colored("Initialized SimpleGeminiVoice successfully", "green"))
        except Exception as e:
            print(colored(f"Error during initialization: {str(e)}", "red"))
            raise

    def print_audio_devices(self):
        """Print all available audio devices"""
        print(colored("\nAvailable Audio Devices:", "cyan"))
        for i in range(self.audio.get_device_count()):
            try:
                device_info = self.audio.get_device_info_by_index(i)
                print(colored(f"Device {i}: {device_info['name']}", "cyan"))
                print(colored(f"  Max Input Channels: {device_info['maxInputChannels']}", "cyan"))
                print(colored(f"  Max Output Channels: {device_info['maxOutputChannels']}", "cyan"))
                print(colored(f"  Default Sample Rate: {device_info['defaultSampleRate']}", "cyan"))
                if device_info['maxInputChannels'] > 0:
                    print(colored("  This is an INPUT device", "green"))
                if device_info['maxOutputChannels'] > 0:
                    print(colored("  This is an OUTPUT device", "yellow"))
                print()
            except Exception as e:
                print(colored(f"Error getting device info for index {i}: {str(e)}", "red"))

    async def start(self):
        try:
            # Initialize websocket
            print(colored("Connecting to Gemini...", "yellow"))
            self.ws = await connect(
                self.URI, additional_headers={"Content-Type": "application/json"}
            )
            await self.ws.send(json.dumps({"setup": {"model": f"models/{self.MODEL}"}}))
            await self.ws.recv(decode=False)
            print(colored("Connected to Gemini, You can start talking now", "green"))
            
            # Start audio streaming
            async with asyncio.TaskGroup() as tg:
                tg.create_task(self.send_user_audio())
                tg.create_task(self.recv_model_audio())
        except Exception as e:
            print(colored(f"Error during start: {str(e)}", "red"))
            raise

    async def send_user_audio(self):
        try:
            # Get default input device info
            default_input = self.audio.get_default_input_device_info()
            print(colored(f"\nUsing input device: {default_input['name']}", "green"))
            print(colored(f"Input device details:", "cyan"))
            print(colored(f"  Index: {default_input['index']}", "cyan"))
            print(colored(f"  Sample Rate: {default_input['defaultSampleRate']}", "cyan"))
            print(colored(f"  Max Input Channels: {default_input['maxInputChannels']}", "cyan"))

            stream = self.audio.open(
                format=self.FORMAT,
                channels=self.CHANNELS,
                rate=self.INPUT_SAMPLE_RATE,
                input=True,
                frames_per_buffer=self.CHUNK,
                input_device_index=default_input['index']  # Explicitly specify input device
            )
            print(colored("Audio input stream opened successfully", "green"))

            while True:
                try:
                    data = await asyncio.to_thread(stream.read, self.CHUNK, exception_on_overflow=False)
                    print(colored(".", "green", attrs=["bold"]), end="", flush=True)  # Audio level indicator
                    await self.ws.send(
                        json.dumps(
                            {
                                "realtime_input": {
                                    "media_chunks": [
                                        {
                                            "data": base64.b64encode(data).decode(),
                                            "mime_type": "audio/pcm",
                                        }
                                    ]
                                }
                            }
                        )
                    )
                except OSError as e:
                    print(colored(f"\nAudio read error: {str(e)}", "red"))
                    continue
        except Exception as e:
            print(colored(f"Error in send_user_audio: {str(e)}", "red"))
            raise

    async def recv_model_audio(self):
        try:
            # Get default output device info
            default_output = self.audio.get_default_output_device_info()
            print(colored(f"\nUsing output device: {default_output['name']}", "green"))

            stream = self.audio.open(
                format=self.FORMAT,
                channels=self.CHANNELS,
                rate=self.OUTPUT_SAMPLE_RATE,
                output=True,
                output_device_index=default_output['index']  # Explicitly specify output device
            )
            print(colored("Audio output stream opened successfully", "green"))

            async for msg in self.ws:
                response = json.loads(msg)
                try:
                    audio_data = response["serverContent"]["modelTurn"]["parts"][0][
                        "inlineData"
                    ]["data"]
                    await asyncio.to_thread(stream.write, base64.b64decode(audio_data))
                    print(colored("*", "yellow", attrs=["bold"]), end="", flush=True)  # Response indicator
                except KeyError:
                    pass
        except Exception as e:
            print(colored(f"Error in recv_model_audio: {str(e)}", "red"))
            raise

    def __del__(self):
        try:
            self.audio.terminate()
            print(colored("\nCleaned up PyAudio resources", "green"))
        except:
            pass


if __name__ == "__main__":
    try:
        client = SimpleGeminiVoice()
        asyncio.run(client.start())
    except Exception as e:
        print(colored(f"Application error: {str(e)}", "red"))