# EchoHive.live - Real-time Voice Translation

A sophisticated real-time voice translation application featuring two AI agents working in tandem to provide seamless bidirectional translation. Built with OpenAI's Real-time API and WebRTC.

## Features

### Bidirectional Translation System
- **English to Target Translator**: Translates English speech into the target language
- **Target to English Translator**: Translates target language speech back to English
- Distinct voices (Verse for English to Target, Shimmer for Target to English)
- Intelligent turn-taking system for smooth conversation flow

### Real-time Voice Interaction
- WebRTC-based audio streaming
- Real-time voice activity detection
- Visual speaking indicators for all participants
- Smooth animations and transitions

### Visual Feedback
- Dynamic speaking indicators with:
  - Pulsing animations
  - Color-coded responses (Green for English to Target, Pink for Target to English, Blue for User)
  - Multiple animation layers (glow, pulse, ripple)
- Real-time status updates
- Comprehensive event logging

### Translation Management
- Automatic language detection
- Smart turn-passing system
- Context-aware translations
- Clear visual cues for current speaker
- Dynamic target language selection

## Technical Stack

- **Backend**: FastAPI
- **Frontend**: HTML, JavaScript, TailwindCSS, DaisyUI
- **Real-time Communication**: WebRTC
- **API Integration**: OpenAI Real-time API
- **Audio Processing**: Web Audio API
- **Animations**: CSS Animations
- **Styling**: Dark mode with dynamic UI

## Setup

1. Set your OpenAI API key:
```bash
# Windows
set OPENAI_API_KEY=your_api_key_here

# Linux/Mac
export OPENAI_API_KEY=your_api_key_here
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Run the application:
```bash
python main.py
```

4. Open in browser:
```
http://127.0.0.1:8000
```

## Usage Guide

### Starting a Translation Session
1. Click "Start Voice Chat"
2. Grant microphone permissions
3. Wait for both translators to connect
4. Start speaking in either English or the target language

### Translation Flow
- **English to Target Translator**
  - Activates when English is spoken
  - Translates to the selected target language
  - Automatically passes turn after translation

- **Target to English Translator**
  - Activates when target language is spoken
  - Translates back to English
  - Automatically passes turn after translation

### Visual Indicators
- **Speaking Indicators**
  - Green pulse: English to Target translator is speaking
  - Pink pulse: Target to English translator is speaking
  - Blue pulse: User is speaking
  - Multiple animation layers for clear visibility

- **Status Indicators**
  - Connection status for each translator
  - Real-time event logging
  - Message history with translator identification

### Language Selection
- Default target language: Spanish
- Dynamic language switching through UI
- Supports multiple target languages
- Real-time language updates

## Requirements

- Python 3.7+
- Modern web browser with WebRTC support
- Microphone access
- OpenAI API key

## Customization

1. **Translation Settings**:
   - Modify translator instructions in main.py
   - Customize turn-taking behavior
   - Adjust translation patterns

2. **UI Customization**:
   - Modify TailwindCSS classes
   - Update DaisyUI theme
   - Customize animations and colors

3. **Audio Settings**:
   - Adjust speaking detection sensitivity
   - Modify animation timings
   - Customize visual feedback

## Contributing

Feel free to submit issues and enhancement requests!