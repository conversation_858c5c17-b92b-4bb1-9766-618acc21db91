<!DOCTYPE html>
<html lang="en" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multi-Agent Voice Chat</title>
    <link href="https://cdn.jsdelivr.net/npm/daisyui@3.9.4/dist/full.css" rel="stylesheet" type="text/css" />
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js"></script>
    <style>
        #messages {
            max-height: 400px;
            overflow-y: auto;
            scroll-behavior: smooth;
        }

        /* Title Animation */
        .title-gradient {
            background: linear-gradient(
                300deg,
                #00ff00,
                #ff00ff,
                #0000ff,
                #00ffff,
                #ff00ff
            );
            background-size: 300% 300%;
            animation: gradient 15s ease infinite;
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }

        .subtitle {
            background: linear-gradient(
                120deg,
                #ff00ff,
                #00ffff,
                #00ff00
            );
            background-size: 200% 200%;
            animation: gradient 10s ease infinite;
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            font-size: 0.9em;
        }

        @keyframes gradient {
            0% {
                background-position: 0% 50%;
            }
            50% {
                background-position: 100% 50%;
            }
            100% {
                background-position: 0% 50%;
            }
        }

        .message {
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.5s ease-out;
            margin: 0.5rem 0;
            padding: 1rem;
            border-radius: 0.5rem;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .message.show {
            opacity: 1;
            transform: translateY(0);
        }
        .message.show.alpha {
            border-left: 3px solid #00ff00;
        }
        .message.show.beta {
            border-left: 3px solid #ff00ff;
        }
        .message:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
            transition: all 0.3s ease;
        }
        .message.error {
            background: rgba(255, 0, 0, 0.1);
            border-color: rgba(255, 0, 0, 0.2);
        }
        .message.info {
            background: rgba(0, 255, 255, 0.05);
            border-color: rgba(0, 255, 255, 0.1);
        }

        .speaking-indicator {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            position: relative;
            margin: 0 auto;
            transition: all 0.3s ease;
        }

        .speaking-indicator::before {
            content: '';
            position: absolute;
            top: -10px;
            left: -10px;
            right: -10px;
            bottom: -10px;
            border-radius: 50%;
            opacity: 0;
            transition: all 0.3s ease;
        }

        .speaking-indicator.alpha {
            background: linear-gradient(145deg, #003300, #006600);
            box-shadow: 0 0 15px #00ff00;
        }
        .speaking-indicator.beta {
            background: linear-gradient(145deg, #330033, #660066);
            box-shadow: 0 0 15px #ff00ff;
        }
        .speaking-indicator.user {
            background: linear-gradient(145deg, #000033, #000066);
            box-shadow: 0 0 15px #0000ff;
        }

        .speaking-indicator.speaking {
            transform: scale(1.1);
        }

        .speaking-indicator.alpha.speaking {
            background: linear-gradient(145deg, #006600, #00cc00);
            box-shadow: 0 0 30px #00ff00, 0 0 50px #00ff00, 0 0 70px #00ff00;
        }
        .speaking-indicator.beta.speaking {
            background: linear-gradient(145deg, #660066, #cc00cc);
            box-shadow: 0 0 30px #ff00ff, 0 0 50px #ff00ff, 0 0 70px #ff00ff;
        }
        .speaking-indicator.user.speaking {
            background: linear-gradient(145deg, #000066, #0000cc);
            box-shadow: 0 0 30px #0000ff, 0 0 50px #0000ff, 0 0 70px #0000ff;
        }

        .speaking-indicator.speaking::before {
            animation: pulse 1s ease-out infinite;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
                opacity: 0.8;
                border: 2px solid rgba(255, 255, 255, 0.5);
            }
            50% {
                transform: scale(1.5);
                opacity: 0;
                border: 2px solid rgba(255, 255, 255, 0.2);
            }
            100% {
                transform: scale(1);
                opacity: 0.8;
                border: 2px solid rgba(255, 255, 255, 0.5);
            }
        }

        /* Add ripple effect for speaking indicators */
        .speaking-indicator::after {
            content: '';
            position: absolute;
            top: -5px;
            left: -5px;
            right: -5px;
            bottom: -5px;
            border-radius: 50%;
            opacity: 0;
            transition: all 0.3s ease;
        }

        .speaking-indicator.speaking::after {
            animation: ripple 2s linear infinite;
        }

        .speaking-indicator.alpha.speaking::after {
            border: 2px solid rgba(0, 255, 0, 0.5);
        }
        .speaking-indicator.beta.speaking::after {
            border: 2px solid rgba(255, 0, 255, 0.5);
        }
        .speaking-indicator.user.speaking::after {
            border: 2px solid rgba(0, 0, 255, 0.5);
        }

        @keyframes ripple {
            0% {
                transform: scale(1);
                opacity: 1;
            }
            100% {
                transform: scale(2);
                opacity: 0;
            }
        }

        /* Scrollbar styling */
        #messages::-webkit-scrollbar {
            width: 8px;
        }
        #messages::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.1);
            border-radius: 4px;
        }
        #messages::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
        }
        #messages::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.2);
        }
    </style>
</head>
<body class="min-h-screen bg-base-300">
    <div class="container mx-auto p-4">
        <div class="card bg-base-100 shadow-xl">
            <div class="card-body">
                <div class="flex flex-col items-center mb-6">
                    <h1 class="card-title text-3xl font-bold title-gradient mb-2">
                        www.echohive.live
                    </h1>
                    <div class="subtitle font-semibold">
                        Multi Agent Real-time Voice Template with Tools
                    </div>
                </div>
                
                <!-- Agent Status indicators -->
                <div class="flex flex-col gap-2 mb-4">
                    <div class="flex items-center gap-2 p-2 bg-base-200 rounded-lg">
                        <div id="alphaStatusIndicator" class="w-3 h-3 rounded-full bg-error"></div>
                        <span class="text-sm font-medium">English to Target:</span>
                        <span id="alphaStatusText" class="text-sm">Disconnected</span>
                    </div>
                    <div class="flex items-center gap-2 p-2 bg-base-200 rounded-lg">
                        <div id="betaStatusIndicator" class="w-3 h-3 rounded-full bg-error"></div>
                        <span class="text-sm font-medium">Target to English:</span>
                        <span id="betaStatusText" class="text-sm">Disconnected</span>
                    </div>
                </div>

                <!-- Speaking Indicators -->
                <div class="grid grid-cols-3 gap-4 mb-4">
                    <!-- Agent Alpha -->
                    <div class="text-center p-4 bg-base-200 rounded-lg">
                        <div id="alphaSpeaking" class="speaking-indicator alpha"></div>
                        <div class="mt-2 font-medium text-success">English to Target</div>
                    </div>
                    
                    <!-- User -->
                    <div class="text-center p-4 bg-base-200 rounded-lg">
                        <div id="userSpeaking" class="speaking-indicator user"></div>
                        <div class="mt-2 font-medium text-info">User</div>
                    </div>
                    
                    <!-- Agent Beta -->
                    <div class="text-center p-4 bg-base-200 rounded-lg">
                        <div id="betaSpeaking" class="speaking-indicator beta"></div>
                        <div class="mt-2 font-medium text-error">Target to English</div>
                    </div>
                </div>

                <!-- Messages -->
                <div id="messages" class="mb-4 p-4 bg-base-200 rounded-lg"></div>

                <!-- Language Selection -->
                <div class="flex gap-2 mb-4">
                    <input type="text" id="targetLanguage" placeholder="Enter target language" class="input input-bordered w-full max-w-xs" value="Spanish">
                    <button id="changeLanguageBtn" class="btn btn-secondary">
                        Change Language
                    </button>
                </div>

                <!-- Controls -->
                <div class="flex gap-2">
                    <button id="startButton" class="btn btn-primary">
                        Start Voice Chat
                    </button>
                    <button id="stopButton" class="btn btn-error" disabled>
                        Stop Voice Chat
                    </button>
                </div>

                <!-- Loading animation -->
                <div id="loadingAnimation" class="hidden mt-4">
                    <span class="loading loading-dots loading-lg text-primary"></span>
                </div>

                <!-- Events Log -->
                <div class="mt-4">
                    <div class="collapse bg-base-200">
                        <input type="checkbox" /> 
                        <div class="collapse-title text-xl font-medium flex items-center gap-2">
                            <span>Events Log</span>
                            <div id="eventIndicator" class="w-2 h-2 rounded-full bg-primary animate-pulse hidden"></div>
                        </div>
                        <div class="collapse-content">
                            <div id="eventsLog" class="bg-base-300 rounded-lg p-4 max-h-60 overflow-y-auto text-sm font-mono">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="/static/js/app.js"></script>
</body>
</html> 