// DOM Elements
const startButton = document.getElementById('startButton');
const stopButton = document.getElementById('stopButton');
const targetLanguageInput = document.getElementById('targetLanguage');
const changeLanguageBtn = document.getElementById('changeLanguageBtn');
const alphaStatusIndicator = document.getElementById('alphaStatusIndicator');
const betaStatusIndicator = document.getElementById('betaStatusIndicator');
const alphaStatusText = document.getElementById('alphaStatusText');
const betaStatusText = document.getElementById('betaStatusText');
const loadingAnimation = document.getElementById('loadingAnimation');
const messagesDiv = document.getElementById('messages');
const eventsLog = document.getElementById('eventsLog');
const eventIndicator = document.getElementById('eventIndicator');
const alphaSpeaking = document.getElementById('alphaSpeaking');
const betaSpeaking = document.getElementById('betaSpeaking');
const userSpeaking = document.getElementById('userSpeaking');

// Global variables
const agents = {
    alpha: {
        peerConnection: null,
        dataChannel: null,
        mediaStream: null,
        audioElement: null,
        connected: false,
        speaking: false
    },
    beta: {
        peerConnection: null,
        dataChannel: null,
        mediaStream: null,
        audioElement: null,
        connected: false,
        speaking: false
    }
};
let lastEventTime = Date.now();
let userIsSpeaking = false;

// Audio processing setup
let audioContext;
let audioAnalyser;
let audioDataArray;
const SPEAKING_THRESHOLD = 15; // Lower threshold for more sensitivity
const SPEAKING_HISTORY_SIZE = 5; // Number of samples to keep for smoothing
const MIN_SPEAKING_SAMPLES = 3; // Minimum samples above threshold to trigger speaking

// Speaking detection state
const speakingHistory = {
    alpha: Array(SPEAKING_HISTORY_SIZE).fill(false),
    beta: Array(SPEAKING_HISTORY_SIZE).fill(false),
    user: Array(SPEAKING_HISTORY_SIZE).fill(false)
};

// Session configuration
const SESSION_CONFIG = {
    modalities: ['text', 'audio']
};

// Helper functions
function updateStatus(agent, status, isConnected = false) {
    const statusText = agent === 'alpha' ? alphaStatusText : betaStatusText;
    const statusIndicator = agent === 'alpha' ? alphaStatusIndicator : betaStatusIndicator;
    
    statusText.textContent = status;
    statusIndicator.className = `w-3 h-3 rounded-full ${isConnected ? 'bg-success' : 'bg-error'}`;
    agents[agent].connected = isConnected;
    
    logEvent('Status', `${agent === 'alpha' ? 'English to Target' : 'Target to English'}: ${status}`);
}

function addMessage(text, type = 'info', agent = null) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}`;
    if (agent) {
        messageDiv.classList.add(agent);
    }
    messageDiv.textContent = text;
    messagesDiv.appendChild(messageDiv);
    
    messageDiv.offsetHeight;
    requestAnimationFrame(() => {
        messageDiv.classList.add('show');
    });
    
    messageDiv.scrollIntoView({ behavior: 'smooth', block: 'end' });
    
    const messages = messagesDiv.getElementsByClassName('message');
    if (messages.length > 100) {
        messagesDiv.removeChild(messages[0]);
    }

    logEvent('Message', `${agent ? `[${agent === 'alpha' ? 'English to Target' : 'Target to English'}] ` : ''}${text}`);
}

function logEvent(type, details) {
    const now = new Date();
    const timestamp = now.toLocaleTimeString('en-US', { 
        hour12: false, 
        hour: '2-digit', 
        minute: '2-digit',
        second: '2-digit',
        fractionalSecondDigits: 3 
    });

    const eventDiv = document.createElement('div');
    eventDiv.className = 'text-sm mb-2 opacity-0 transition-opacity duration-300';
    eventDiv.innerHTML = `
        <span class="text-primary">[${timestamp}]</span>
        <span class="text-accent">[${type}]</span>
        <span class="text-base-content">${details}</span>
    `;

    eventsLog.appendChild(eventDiv);
    requestAnimationFrame(() => {
        eventDiv.style.opacity = '1';
    });

    // Limit the number of events
    const events = eventsLog.children;
    if (events.length > 200) {
        eventsLog.removeChild(events[0]);
    }

    // Auto-scroll if near bottom
    if (eventsLog.scrollTop + eventsLog.clientHeight >= eventsLog.scrollHeight - 100) {
        eventsLog.scrollTop = eventsLog.scrollHeight;
    }

    // Show event indicator if collapsed
    const timeSinceLastEvent = Date.now() - lastEventTime;
    if (timeSinceLastEvent > 1000) { // Only show indicator if more than 1 second since last event
        eventIndicator.classList.remove('hidden');
        setTimeout(() => {
            eventIndicator.classList.add('hidden');
        }, 1000);
    }
    lastEventTime = Date.now();
}

function showLoading(show) {
    loadingAnimation.className = show ? 'mt-4' : 'hidden mt-4';
    startButton.disabled = show;
    logEvent('System', show ? 'Loading started' : 'Loading finished');
}

function updateSpeakingHistory(type, isSpeaking) {
    speakingHistory[type].shift();
    speakingHistory[type].push(isSpeaking);
    
    const speakingCount = speakingHistory[type].filter(Boolean).length;
    return speakingCount >= MIN_SPEAKING_SAMPLES;
}

function updateSpeakingIndicator(type, isSpeaking) {
    const isReallySpeaking = updateSpeakingHistory(type, isSpeaking);
    const indicator = type === 'alpha' ? alphaSpeaking : 
                     type === 'beta' ? betaSpeaking : 
                     userSpeaking;
    
    if (isReallySpeaking) {
        indicator.classList.add('speaking');
    } else {
        indicator.classList.remove('speaking');
    }

    if (type === 'alpha') agents.alpha.speaking = isReallySpeaking;
    if (type === 'beta') agents.beta.speaking = isReallySpeaking;
    if (type === 'user') userIsSpeaking = isReallySpeaking;
}

async function initializeAudioProcessing(stream) {
    try {
        audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const source = audioContext.createMediaStreamSource(stream);
        audioAnalyser = audioContext.createAnalyser();
        audioAnalyser.fftSize = 1024; // Increased for better frequency resolution
        audioAnalyser.smoothingTimeConstant = 0.8; // Increased smoothing
        source.connect(audioAnalyser);
        
        audioDataArray = new Uint8Array(audioAnalyser.frequencyBinCount);
        
        function checkAudioLevel() {
            if (!audioAnalyser) return;
            
            audioAnalyser.getByteFrequencyData(audioDataArray);
            
            // Calculate RMS value for better speech detection
            const rms = Math.sqrt(
                audioDataArray.reduce((acc, val) => acc + (val * val), 0) 
                / audioDataArray.length
            );
            
            const isSpeaking = rms > SPEAKING_THRESHOLD;
            updateSpeakingIndicator('user', isSpeaking);
            
            requestAnimationFrame(checkAudioLevel);
        }
        
        checkAudioLevel();
    } catch (error) {
        console.error('Error initializing audio processing:', error);
        logEvent('Error', `Audio processing initialization failed: ${error.message}`);
    }
}

// WebRTC implementation
async function initializeAgent(agent) {
    try {
        logEvent('WebRTC', `Initializing Agent ${agent.toUpperCase()}`);
        updateStatus(agent, 'Initializing...');

        const tokenResponse = await fetch(`/session/${agent}`);
        const data = await tokenResponse.json();
        
        if (!data.client_secret?.value) {
            throw new Error(`Failed to get ephemeral token for Agent ${agent}`);
        }

        logEvent('Session', `Token received for Agent ${agent}`);
        const EPHEMERAL_KEY = data.client_secret.value;

        agents[agent].peerConnection = new RTCPeerConnection();
        logEvent('WebRTC', `Peer connection created for Agent ${agent}`);

        agents[agent].audioElement = document.createElement('audio');
        agents[agent].audioElement.autoplay = true;
        
        // Set up audio level monitoring for the agent
        agents[agent].peerConnection.ontrack = e => {
            agents[agent].audioElement.srcObject = e.streams[0];
            logEvent('WebRTC', `Remote track received for Agent ${agent}`);
            
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const source = audioContext.createMediaStreamSource(e.streams[0]);
            const analyser = audioContext.createAnalyser();
            analyser.fftSize = 1024;
            analyser.smoothingTimeConstant = 0.8;
            source.connect(analyser);
            
            const dataArray = new Uint8Array(analyser.frequencyBinCount);
            
            function checkAgentAudioLevel() {
                if (!analyser) return;
                
                analyser.getByteFrequencyData(dataArray);
                const rms = Math.sqrt(
                    dataArray.reduce((acc, val) => acc + (val * val), 0) 
                    / dataArray.length
                );
                
                const isSpeaking = rms > SPEAKING_THRESHOLD;
                updateSpeakingIndicator(agent, isSpeaking);
                
                requestAnimationFrame(checkAgentAudioLevel);
            }
            
            checkAgentAudioLevel();
        };

        if (!agents[agent].mediaStream) {
            agents[agent].mediaStream = await navigator.mediaDevices.getUserMedia({ audio: true });
            if (agent === 'alpha') { // Only initialize once
                await initializeAudioProcessing(agents[agent].mediaStream);
            }
        }
        agents[agent].peerConnection.addTrack(agents[agent].mediaStream.getTracks()[0]);
        logEvent('WebRTC', `Local track added for Agent ${agent}`);

        agents[agent].dataChannel = agents[agent].peerConnection.createDataChannel('oai-events');
        agents[agent].dataChannel.addEventListener('message', (event) => handleDataChannelMessage(event, agent));
        agents[agent].dataChannel.addEventListener('open', () => {
            updateStatus(agent, 'Connected', true);
            if (allAgentsConnected()) {
                stopButton.disabled = false;
                startButton.disabled = true;
            }
            addMessage(`Agent ${agent} connected`, 'info', agent);
            configureTools(agent);
            logEvent('DataChannel', `Channel opened for Agent ${agent}`);
        });

        const offer = await agents[agent].peerConnection.createOffer();
        await agents[agent].peerConnection.setLocalDescription(offer);
        logEvent('WebRTC', `Local description set for Agent ${agent}`);

        const baseUrl = 'https://api.openai.com/v1/realtime';
        const model = 'gpt-4o-realtime-preview';
        const sdpResponse = await fetch(`${baseUrl}?model=${model}`, {
            method: 'POST',
            body: offer.sdp,
            headers: {
                Authorization: `Bearer ${EPHEMERAL_KEY}`,
                'Content-Type': 'application/sdp'
            },
        });

        if (!sdpResponse.ok) {
            throw new Error(`Failed to connect to OpenAI Realtime API for Agent ${agent}`);
        }

        logEvent('API', `OpenAI connection established for Agent ${agent}`);

        const answer = {
            type: 'answer',
            sdp: await sdpResponse.text(),
        };
        await agents[agent].peerConnection.setRemoteDescription(answer);
        logEvent('WebRTC', `Remote description set for Agent ${agent}`);

    } catch (error) {
        console.error(`Error initializing Agent ${agent}:`, error);
        updateStatus(agent, `Error: ${error.message}`);
        addMessage(error.message, 'error', agent);
        logEvent('Error', `Agent ${agent}: ${error.message}`);
        throw error;
    }
}

function allAgentsConnected() {
    return agents.alpha.connected && agents.beta.connected;
}

async function initializeWebRTC() {
    try {
        showLoading(true);
        await Promise.all([
            initializeAgent('alpha'),
            initializeAgent('beta')
        ]);
        showLoading(false);
    } catch (error) {
        console.error('Error during initialization:', error);
        showLoading(false);
    }
}

function configureTools(agent) {
    const event = {
        type: 'session.update',
        session: {
            ...SESSION_CONFIG,
            tools: [
                {
                    type: 'function',
                    name: 'passTurn',
                    description: 'Pass your turn without responding when appropriate',
                    parameters: {
                        type: 'object',
                        properties: {},
                        required: []
                    }
                }
            ]
        }
    };
    agents[agent].dataChannel.send(JSON.stringify(event));
    logEvent('Tools', `Configured pass turn functionality for Agent ${agent}`);
}

async function handleDataChannelMessage(event, agent) {
    try {
        const msg = JSON.parse(event.data);
        logEvent('Received', `[${agent.toUpperCase()}] ${JSON.stringify(msg).slice(0, 100)}${JSON.stringify(msg).length > 100 ? '...' : ''}`);
        
        if (msg.type === 'response.function_call_arguments.done') {
            if (msg.name === 'passTurn') {
                const responseEvent = {
                    type: 'conversation.item.create',
                    item: {
                        type: 'function_call_output',
                        call_id: msg.call_id,
                        output: JSON.stringify({ status: 'turn_passed' })
                    }
                };
                agents[agent].dataChannel.send(JSON.stringify(responseEvent));
                logEvent('Function', `Pass turn executed by Agent ${agent}`);
                
                // Add colorful console logging
                const agentColor = agent === 'alpha' ? '\x1b[32m' : '\x1b[35m'; // Green for Alpha, Magenta for Beta
                console.log(
                    `${agentColor}[PASS TURN] Agent ${agent.toUpperCase()} passed their turn\x1b[0m`,
                    '\nTimestamp:', new Date().toISOString(),
                    '\nMessage Context:', msg
                );
            }
        } else if (msg.content) {
            addMessage(`${agent === 'alpha' ? 'Agent Alpha' : 'Agent Beta'}: ${msg.content}`, 'info', agent);
        }
    } catch (error) {
        console.error(`Error parsing message from Agent ${agent}:`, error);
        addMessage(`Error parsing message from Agent ${agent}: ${error.message}`, 'error', agent);
        logEvent('Error', `Message parsing failed for Agent ${agent}: ${error.message}`);
    }
}

function cleanup() {
    try {
        logEvent('System', 'Starting cleanup');
        
        for (const agent of ['alpha', 'beta']) {
            if (agents[agent].mediaStream) {
                for (const track of agents[agent].mediaStream.getTracks()) {
                    track.stop();
                    logEvent('Cleanup', `Media track stopped for Agent ${agent}`);
                }
                agents[agent].mediaStream = null;
            }

            if (agents[agent].audioElement) {
                agents[agent].audioElement.srcObject = null;
                agents[agent].audioElement.remove();
                agents[agent].audioElement = null;
                logEvent('Cleanup', `Audio element removed for Agent ${agent}`);
            }

            if (agents[agent].dataChannel) {
                agents[agent].dataChannel.close();
                agents[agent].dataChannel = null;
                logEvent('Cleanup', `Data channel closed for Agent ${agent}`);
            }

            if (agents[agent].peerConnection) {
                agents[agent].peerConnection.close();
                agents[agent].peerConnection = null;
                logEvent('Cleanup', `Peer connection closed for Agent ${agent}`);
            }

            updateStatus(agent, 'Disconnected', false);
        }

        startButton.disabled = false;
        stopButton.disabled = true;
        addMessage('All connections closed and resources cleaned up');
        logEvent('System', 'Cleanup completed');
    } catch (error) {
        console.error('Error during cleanup:', error);
        addMessage(`Error during cleanup: ${error.message}`, 'error');
        logEvent('Error', `Cleanup failed: ${error.message}`);
    }
}

// Language change handler
async function handleLanguageChange() {
    try {
        const newLanguage = targetLanguageInput.value.trim();
        if (!newLanguage) {
            addMessage('Please enter a valid language', 'error');
            return;
        }

        // If agents are connected, we need to disconnect and reconnect
        const wasConnected = allAgentsConnected();
        if (wasConnected) {
            await cleanup();
        }

        const response = await fetch('/update_language', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ target_language: newLanguage })
        });

        if (!response.ok) {
            throw new Error('Failed to update language');
        }

        addMessage(`Target language updated to: ${newLanguage}`, 'info');
        logEvent('Language', `Updated target language to ${newLanguage}`);

        // If agents were connected, reconnect them
        if (wasConnected) {
            await initializeWebRTC();
        }
    } catch (error) {
        console.error('Error updating language:', error);
        addMessage(`Error updating language: ${error.message}`, 'error');
        logEvent('Error', `Language update failed: ${error.message}`);
    }
}

// Event listeners
startButton.addEventListener('click', initializeWebRTC);
stopButton.addEventListener('click', cleanup);
changeLanguageBtn.addEventListener('click', handleLanguageChange);

// Initial setup
updateStatus('alpha', 'Disconnected');
updateStatus('beta', 'Disconnected');
addMessage('Ready to start voice chat');
logEvent('System', 'Application initialized'); 