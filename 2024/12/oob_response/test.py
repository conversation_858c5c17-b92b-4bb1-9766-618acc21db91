from termcolor import colored
import sys

# CONSTANTS
MAX_RETRIES = 3
DEFAULT_MESSAGE = "Hello, <PERSON>!"

def greet_user(name=None):
    """
    A simple greeting function that demonstrates error handling and colored output
    Args:
        name (str, optional): Name of the user to greet. Defaults to None.
    Returns:
        str: Formatted greeting message
    """
    try:
        print(colored("Starting greeting process...", "cyan"))
        
        if name is None:
            message = DEFAULT_MESSAGE
        else:
            # Ensure name is a string and not empty
            if not isinstance(name, str):
                raise TypeError("Name must be a string")
            if not name.strip():
                raise ValueError("Name cannot be empty")
                
            message = f"Hello, {name}!"
            
        print(colored(f"Successfully created greeting: {message}", "green"))
        return message
        
    except (TypeError, ValueError) as e:
        error_msg = f"Error in greet_user: {str(e)}"
        print(colored(error_msg, "red"))
        return error_msg
    except Exception as e:
        error_msg = f"Unexpected error in greet_user: {str(e)}"
        print(colored(error_msg, "red"))
        return error_msg

if __name__ == "__main__":
    # Example usage
    print(greet_user())
    print(greet_user("Alice"))

