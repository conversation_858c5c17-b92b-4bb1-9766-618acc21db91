from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
import httpx
import os
from termcolor import colored
import json

app = FastAPI(title="Voice Chat App")

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")

OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
if not OPENAI_API_KEY:
    print(colored("Warning: OPENAI_API_KEY not found in environment variables!", "red"))

@app.get("/", response_class=HTMLResponse)
async def root():
    try:
        with open("static/index.html", encoding="utf-8") as f:
            return f.read()
    except Exception as e:
        print(colored(f"Error reading index.html: {str(e)}", "red"))
        raise HTTPException(status_code=500, detail="Internal server error")

@app.get("/session")
async def create_session():
    try:
        print(colored("Creating new session...", "cyan"))
        async with httpx.AsyncClient() as client:
            response = await client.post(
                "https://api.openai.com/v1/realtime/sessions",
                headers={
                    "Authorization": f"Bearer {OPENAI_API_KEY}",
                    "Content-Type": "application/json"
                },
                json={
                    "model": "gpt-4o-realtime-preview-2024-12-17",
                    "voice": "verse"
                }
            )
            
            if response.status_code != 200:
                print(colored(f"Error from OpenAI API: {response.text}", "red"))
                raise HTTPException(status_code=response.status_code, detail="Failed to create session")
                
            print(colored("Session created successfully!", "green"))
            return response.json()
            
    except Exception as e:
        print(colored(f"Error creating session: {str(e)}", "red"))
        raise HTTPException(status_code=500, detail="Failed to create session")

if __name__ == "__main__":
    import uvicorn
    print(colored("Starting Voice Chat Server...", "green"))
    uvicorn.run("main:app", host="127.0.0.1", port=8000, reload=True)