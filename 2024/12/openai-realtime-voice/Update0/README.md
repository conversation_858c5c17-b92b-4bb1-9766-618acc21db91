# Real-time Voice Chat with OpenAI

A simple real-time voice chat application that lets you have conversations with an AI assistant using your microphone. The application features a clean dark-mode interface with audio visualizations for both you and the AI.

## How It Works

1. **Backend (FastAPI)**
   - Creates secure ephemeral tokens for OpenAI authentication
   - <PERSON>les WebRTC session initialization
   - Serves the web interface

2. **Frontend**
   - Uses WebRTC for real-time audio communication
   - Shows live audio visualizations (3-bar display)
   - Provides simple start/stop controls
   - Dark mode interface using DaisyUI

3. **Technical Flow**
   - When you click "Start Chat":
     1. Your browser requests a secure token from the server
     2. The server gets an ephemeral token from OpenAI
     3. WebRTC connection is established for audio streaming
     4. Your microphone input is sent to OpenAI
     5. AI responses come back as both text and audio

## Setup

1. Set your OpenAI API key as an environment variable:
   ```
   set OPENAI_API_KEY=your_api_key_here
   ```

2. Install required packages:
   ```
   pip install -r requirements.txt
   ```

3. Run the application:
   ```
   python main.py
   ```

4. Open your browser and go to:
   ```
   http://127.0.0.1:8000
   ```

## Features

- Real-time voice conversation with AI
- Live audio visualization for both user and AI
- Clean, dark-mode user interface
- Connection status indicator
- Simple start/stop controls

## Requirements

- Python 3.7+
- Modern web browser with WebRTC support
- Microphone
- OpenAI API key

## Files Structure

- `main.py` - FastAPI server and OpenAI session handling
- `static/index.html` - Web interface with dark mode UI
- `static/app.js` - WebRTC and audio visualization logic
- `requirements.txt` - Python package dependencies 