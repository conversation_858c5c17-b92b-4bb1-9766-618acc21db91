// DOM Elements
const startBtn = document.getElementById('startBtn');
const stopBtn = document.getElementById('stopBtn');
const statusDot = document.getElementById('statusDot');
const statusText = document.getElementById('statusText');
const userBars = Array.from(document.querySelector('#userVisualizer').children);
const modelBars = Array.from(document.querySelector('#modelVisualizer').children);

// Global variables
let peerConnection = null;
let dataChannel = null;
let audioContext = null;
let userAnalyser = null;
let modelAnalyser = null;
let animationFrame = null;

// Constants
const VISUALIZATION_SMOOTHING = 0.7;
const VISUALIZATION_FFT_SIZE = 32;

function updateStatus(status, isError = false) {
    statusText.textContent = status;
    statusDot.className = `w-3 h-3 rounded-full ${isError ? 'bg-error' : 'bg-success'}`;
}

async function setupAudioAnalysis(stream, isUser = true) {
    if (!audioContext) {
        audioContext = new (window.AudioContext || window.webkitAudioContext)();
    }

    const analyser = audioContext.createAnalyser();
    analyser.fftSize = VISUALIZATION_FFT_SIZE;
    analyser.smoothingTimeConstant = VISUALIZATION_SMOOTHING;

    const source = audioContext.createMediaStreamSource(stream);
    source.connect(analyser);

    if (isUser) {
        userAnalyser = analyser;
    } else {
        modelAnalyser = analyser;
    }
}

function updateVisualization() {
    if (!animationFrame) return;

    if (userAnalyser) {
        const dataArray = new Uint8Array(userAnalyser.frequencyBinCount);
        userAnalyser.getByteFrequencyData(dataArray);
        
        // Use only 3 frequency bands for the 3 bars
        const step = Math.floor(dataArray.length / 3);
        userBars.forEach((bar, i) => {
            const value = dataArray[i * step] / 255;
            bar.style.height = `${value * 80}px`;
        });
    }

    if (modelAnalyser) {
        const dataArray = new Uint8Array(modelAnalyser.frequencyBinCount);
        modelAnalyser.getByteFrequencyData(dataArray);
        
        const step = Math.floor(dataArray.length / 3);
        modelBars.forEach((bar, i) => {
            const value = dataArray[i * step] / 255;
            bar.style.height = `${value * 80}px`;
        });
    }

    animationFrame = requestAnimationFrame(updateVisualization);
}

async function startChat() {
    try {
        updateStatus('Connecting...', false);
        startBtn.disabled = true;

        // Get session token
        const tokenResponse = await fetch('/session');
        if (!tokenResponse.ok) throw new Error('Failed to get session token');
        const data = await tokenResponse.json();
        const ephemeralKey = data.client_secret.value;

        // Create peer connection
        peerConnection = new RTCPeerConnection();

        // Set up audio
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        setupAudioAnalysis(stream, true);
        peerConnection.addTrack(stream.getTracks()[0], stream);

        // Handle remote audio
        peerConnection.ontrack = (e) => {
            setupAudioAnalysis(e.streams[0], false);
            const audio = new Audio();
            audio.srcObject = e.streams[0];
            audio.play();
        };

        // Set up data channel
        dataChannel = peerConnection.createDataChannel('oai-events');
        
        // Add data channel event handlers
        dataChannel.onopen = () => {
            console.log('Data channel opened');
            // Send initial prompt once the channel is open
            const responseCreate = {
                type: "response.create",
                response: {
                    modalities: ["text", "audio"],
                    instructions: "You are a helpful AI assistant. Please introduce yourself briefly.",
                },
            };
            dataChannel.send(JSON.stringify(responseCreate));
        };
        
        dataChannel.onclose = () => {
            console.log('Data channel closed');
        };
        
        dataChannel.onerror = (error) => {
            console.error('Data channel error:', error);
            updateStatus('Data channel error: ' + error.message, true);
        };
        
        dataChannel.onmessage = (e) => {
            const data = JSON.parse(e.data);
            console.log('Received:', data);
            
            // Handle different event types
            switch (data.type) {
                case 'session.created':
                case 'session.updated':
                    console.log(`Session ${data.type.split('.')[1]}:`, data);
                    break;
                case 'error':
                    console.error('Server error:', data);
                    updateStatus('Server error: ' + data.error?.message, true);
                    break;
                default:
                    console.log('Unhandled event type:', data.type);
            }
        };

        // Create and set local description
        const offer = await peerConnection.createOffer();
        await peerConnection.setLocalDescription(offer);

        // Send offer to OpenAI
        const baseUrl = 'https://api.openai.com/v1/realtime';
        const model = 'gpt-4o-realtime-preview-2024-12-17';
        const sdpResponse = await fetch(`${baseUrl}?model=${model}`, {
            method: 'POST',
            body: offer.sdp,
            headers: {
                Authorization: `Bearer ${ephemeralKey}`,
                'Content-Type': 'application/sdp'
            },
        });

        if (!sdpResponse.ok) throw new Error('Failed to connect to OpenAI');

        // Set remote description
        const answer = {
            type: 'answer',
            sdp: await sdpResponse.text(),
        };
        await peerConnection.setRemoteDescription(answer);

        // Start visualization
        animationFrame = requestAnimationFrame(updateVisualization);

        // Update UI
        updateStatus('Connected', false);
        stopBtn.disabled = false;
        
    } catch (error) {
        console.error('Error:', error);
        updateStatus('Error: ' + error.message, true);
        startBtn.disabled = false;
    }
}

function stopChat() {
    if (peerConnection) {
        peerConnection.close();
        peerConnection = null;
    }
    if (dataChannel) {
        dataChannel.close();
        dataChannel = null;
    }
    if (animationFrame) {
        cancelAnimationFrame(animationFrame);
        animationFrame = null;
    }
    if (audioContext) {
        audioContext.close();
        audioContext = null;
    }
    
    userAnalyser = null;
    modelAnalyser = null;
    
    // Reset visualizers
    userBars.forEach(bar => bar.style.height = '0px');
    modelBars.forEach(bar => bar.style.height = '0px');
    
    updateStatus('Disconnected', true);
    startBtn.disabled = false;
    stopBtn.disabled = true;
}

// Event listeners
startBtn.addEventListener('click', startChat);
stopBtn.addEventListener('click', stopChat);
