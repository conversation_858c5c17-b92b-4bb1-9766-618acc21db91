<!DOCTYPE html>
<html lang="en" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Voice Chat</title>
    <link href="https://cdn.jsdelivr.net/npm/daisyui@3.9.4/dist/full.css" rel="stylesheet" type="text/css" />
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js"></script>
</head>
<body class="min-h-screen bg-base-300">
    <div class="container mx-auto px-4 py-8">
        <div class="card bg-base-100 shadow-xl">
            <div class="card-body">
                <h2 class="card-title text-primary mb-4">Voice Chat</h2>
                
                <!-- Status indicator -->
                <div class="flex items-center gap-2 mb-4">
                    <div id="statusDot" class="w-3 h-3 rounded-full bg-error"></div>
                    <span id="statusText" class="text-sm">Disconnected</span>
                </div>

                <!-- Audio visualizers -->
                <div class="flex justify-between gap-4 mb-6">
                    <div class="flex-1 p-4 bg-base-200 rounded-lg">
                        <p class="text-sm mb-2">Your Audio</p>
                        <div class="flex items-end justify-center h-20 gap-1" id="userVisualizer">
                            <div class="w-4 bg-primary rounded-t"></div>
                            <div class="w-4 bg-primary rounded-t"></div>
                            <div class="w-4 bg-primary rounded-t"></div>
                        </div>
                    </div>
                    <div class="flex-1 p-4 bg-base-200 rounded-lg">
                        <p class="text-sm mb-2">Model Audio</p>
                        <div class="flex items-end justify-center h-20 gap-1" id="modelVisualizer">
                            <div class="w-4 bg-secondary rounded-t"></div>
                            <div class="w-4 bg-secondary rounded-t"></div>
                            <div class="w-4 bg-secondary rounded-t"></div>
                        </div>
                    </div>
                </div>

                <!-- Controls -->
                <div class="flex justify-center gap-4">
                    <button id="startBtn" class="btn btn-primary">
                        Start Chat
                    </button>
                    <button id="stopBtn" class="btn btn-error" disabled>
                        Stop
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="/static/app.js"></script>
</body>
</html>