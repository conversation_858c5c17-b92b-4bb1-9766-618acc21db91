<!DOCTYPE html>
<html lang="en" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Real-time Voice Chat</title>
    <link href="https://cdn.jsdelivr.net/npm/daisyui@3.9.4/dist/full.css" rel="stylesheet" type="text/css" />
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js"></script>
</head>
<body class="min-h-screen bg-base-300">
    <div class="container mx-auto px-4 py-8">
        <div class="card bg-base-100 shadow-xl">
            <div class="card-body">
                <h2 class="card-title text-primary mb-4">Real-time Voice Chat</h2>
                
                <!-- Status indicator -->
                <div class="alert mb-4" id="statusAlert">
                    <span id="statusText">Initializing...</span>
                </div>

                <!-- Waveform containers -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <!-- User's waveform -->
                    <div class="p-4 bg-base-200 rounded-lg">
                        <h3 class="text-sm font-semibold mb-2">You</h3>
                        <div class="flex justify-center items-center h-24">
                            <div id="userWaveform" class="flex items-center space-x-1">
                                <!-- Waveform bars will be added dynamically -->
                            </div>
                        </div>
                        <p id="userTranscript" class="mt-2 text-sm"></p>
                    </div>

                    <!-- Model's waveform -->
                    <div class="p-4 bg-base-200 rounded-lg">
                        <h3 class="text-sm font-semibold mb-2">Assistant</h3>
                        <div class="flex justify-center items-center h-24">
                            <div id="modelWaveform" class="flex items-center space-x-1">
                                <!-- Waveform bars will be added dynamically -->
                            </div>
                        </div>
                        <p id="modelTranscript" class="mt-2 text-sm"></p>
                    </div>
                </div>

                <!-- Controls -->
                <div class="flex justify-center space-x-4">
                    <button id="startBtn" class="btn btn-primary">
                        <span class="normal-state">Start Conversation</span>
                        <span class="loading-state hidden">
                            <span class="loading loading-spinner loading-sm mr-2"></span>
                            Connecting...
                        </span>
                    </button>
                    <button id="stopBtn" class="btn btn-error" disabled>
                        Stop
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let audioContext, userAnalyser, modelAnalyser;
        let userAnimationFrame, modelAnimationFrame;

        // Initialize waveforms
        function createWaveform(containerId, barCount = 20) {
            const container = document.getElementById(containerId);
            container.innerHTML = '';
            for (let i = 0; i < barCount; i++) {
                const bar = document.createElement('div');
                bar.className = 'w-1 bg-primary';
                bar.style.height = '20px';
                container.appendChild(bar);
            }
            return container.children;
        }

        // Update waveform based on audio analyzer data
        function updateWaveform(analyser, bars) {
            const dataArray = new Uint8Array(analyser.frequencyBinCount);
            analyser.getByteFrequencyData(dataArray);
            
            // Calculate average levels for each bar
            const barCount = bars.length;
            const samplesPerBar = Math.floor(dataArray.length / barCount);
            
            for (let i = 0; i < barCount; i++) {
                const startSample = i * samplesPerBar;
                const endSample = startSample + samplesPerBar;
                let sum = 0;
                
                for (let j = startSample; j < endSample; j++) {
                    sum += dataArray[j];
                }
                
                const average = sum / samplesPerBar;
                const height = Math.max(20, (average / 255) * 80); // Min height 20px, max 80px
                bars[i].style.height = `${height}px`;
                
                // Add smooth transition
                bars[i].style.transition = 'height 0.1s ease-out';
            }
        }

        // Initialize WebRTC and audio handling
        let pc, dc;
        const startBtn = document.getElementById('startBtn');
        const stopBtn = document.getElementById('stopBtn');
        const statusText = document.getElementById('statusText');
        const statusAlert = document.getElementById('statusAlert');
        const userBars = createWaveform('userWaveform');
        const modelBars = createWaveform('modelWaveform');

        function updateStatus(message, type = 'info') {
            statusText.textContent = message;
            statusAlert.className = `alert alert-${type}`;
        }

        function setButtonLoading(isLoading) {
            const btn = document.getElementById('startBtn');
            const normalState = btn.querySelector('.normal-state');
            const loadingState = btn.querySelector('.loading-state');
            
            if (isLoading) {
                normalState.classList.add('hidden');
                loadingState.classList.remove('hidden');
                btn.disabled = true;
            } else {
                normalState.classList.remove('hidden');
                loadingState.classList.add('hidden');
                btn.disabled = false;
            }
        }

        function startUserWaveform(stream) {
            if (!audioContext) {
                audioContext = new AudioContext();
            }
            
            userAnalyser = audioContext.createAnalyser();
            userAnalyser.fftSize = 256;
            
            const source = audioContext.createMediaStreamSource(stream);
            source.connect(userAnalyser);
            
            function animate() {
                updateWaveform(userAnalyser, userBars);
                userAnimationFrame = requestAnimationFrame(animate);
            }
            animate();
        }

        function startModelWaveform(stream) {
            if (!audioContext) {
                audioContext = new AudioContext();
            }
            
            // Create and set up audio element for playback
            const audioEl = document.createElement('audio');
            audioEl.autoplay = true;
            audioEl.srcObject = stream;
            document.body.appendChild(audioEl);
            
            modelAnalyser = audioContext.createAnalyser();
            modelAnalyser.fftSize = 256;
            
            const source = audioContext.createMediaStreamSource(stream);
            source.connect(modelAnalyser);
            
            function animate() {
                updateWaveform(modelAnalyser, modelBars);
                modelAnimationFrame = requestAnimationFrame(animate);
            }
            animate();
        }

        async function init() {
            try {
                setButtonLoading(true);
                updateStatus('Requesting session token...', 'info');
                const tokenResponse = await fetch("/session");
                const data = await tokenResponse.json();
                const EPHEMERAL_KEY = data.client_secret.value;

                pc = new RTCPeerConnection();
                
                // Set up audio element for model's voice
                pc.ontrack = e => {
                    startModelWaveform(e.streams[0]);
                };

                // Set up microphone
                const ms = await navigator.mediaDevices.getUserMedia({ audio: true });
                pc.addTrack(ms.getTracks()[0]);
                startUserWaveform(ms);
                
                // Set up data channel
                dc = pc.createDataChannel("oai-events");
                dc.addEventListener("message", (e) => {
                    const event = JSON.parse(e.data);
                    console.log('Received WebRTC event:', event);

                    // Handle different event types
                    switch (event.type) {
                        case 'conversation.item.input_audio_transcription.completed':
                            document.getElementById('userTranscript').textContent = event.transcript;
                            console.log('User transcript:', event.transcript);
                            break;
                            
                        case 'response.audio_transcript.done':
                            document.getElementById('modelTranscript').textContent = event.transcript;
                            console.log('Model transcript:', event.transcript);
                            break;
                            
                        case 'text':
                            document.getElementById('modelTranscript').textContent = event.text;
                            console.log('Model text:', event.text);
                            break;
                            
                        case 'response.output_item.done':
                            if (event.item?.content?.[0]?.text) {
                                document.getElementById('modelTranscript').textContent = event.item.content[0].text;
                                console.log('Model response:', event.item.content[0].text);
                            }
                            break;
                    }
                });

                dc.addEventListener("open", () => {
                    console.log('Data channel opened');
                });

                dc.addEventListener("close", () => {
                    console.log('Data channel closed');
                });

                dc.addEventListener("error", (error) => {
                    console.error('Data channel error:', error);
                });

                // Initialize WebRTC connection
                const offer = await pc.createOffer();
                await pc.setLocalDescription(offer);

                const baseUrl = "https://api.openai.com/v1/realtime";
                const model = "gpt-4o-realtime-preview-2024-12-17";
                const sdpResponse = await fetch(`${baseUrl}?model=${model}`, {
                    method: "POST",
                    body: offer.sdp,
                    headers: {
                        Authorization: `Bearer ${EPHEMERAL_KEY}`,
                        "Content-Type": "application/sdp"
                    },
                });

                const answer = {
                    type: "answer",
                    sdp: await sdpResponse.text(),
                };
                await pc.setRemoteDescription(answer);

                updateStatus('Connected! Start speaking...', 'success');
                setButtonLoading(false);
                startBtn.disabled = true;
                stopBtn.disabled = false;

            } catch (error) {
                console.error('Error:', error);
                updateStatus('Error connecting: ' + error.message, 'error');
                setButtonLoading(false);
            }
        }

        function cleanup() {
            if (pc) {
                pc.close();
            }
            if (userAnimationFrame) {
                cancelAnimationFrame(userAnimationFrame);
            }
            if (modelAnimationFrame) {
                cancelAnimationFrame(modelAnimationFrame);
            }
            if (audioContext) {
                audioContext.close();
                audioContext = null;
            }
            
            // Reset waveforms to default state
            Array.from(userBars).forEach(bar => {
                bar.style.height = '20px';
            });
            Array.from(modelBars).forEach(bar => {
                bar.style.height = '20px';
            });
            
            updateStatus('Disconnected', 'warning');
            startBtn.disabled = false;
            stopBtn.disabled = true;
        }

        // Event listeners
        startBtn.addEventListener('click', init);
        stopBtn.addEventListener('click', cleanup);
    </script>
</body>
</html>
