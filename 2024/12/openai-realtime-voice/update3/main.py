from fastapi import FastAP<PERSON>, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
import httpx
import os
from termcolor import colored
import json

# Constants
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
MODEL_ID = "gpt-4o-realtime-preview-2024-12-17"
# VOICE = "verse"
VOICE = "shimmer"

app = FastAPI(title="Real-time Voice Chat")

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")

@app.get("/", response_class=HTMLResponse)
async def root():
    try:
        with open("static/index.html", encoding="utf-8") as f:
            return f.read()
    except Exception as e:
        print(colored(f"Error reading index.html: {str(e)}", "red"))
        raise HTTPException(status_code=500, detail="Internal server error")

@app.get("/session")
async def create_session():
    try:
        print(colored("Creating new session...", "cyan"))
        async with httpx.AsyncClient() as client:
            response = await client.post(
                "https://api.openai.com/v1/realtime/sessions",
                headers={
                    "Authorization": f"Bearer {OPENAI_API_KEY}",
                    "Content-Type": "application/json"
                },
                json={
                    "model": MODEL_ID,
                    "voice": VOICE
                }
            )
            
            if response.status_code != 200:
                print(colored(f"Error from OpenAI API: {response.text}", "red"))
                raise HTTPException(status_code=response.status_code, detail=response.text)
                
            print(colored("Session created successfully!", "green"))
            return response.json()
            
    except Exception as e:
        print(colored(f"Error creating session: {str(e)}", "red"))
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:app", host="127.0.0.1", port=8000, reload=True)
