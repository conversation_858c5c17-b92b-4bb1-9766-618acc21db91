<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Hand Gesture Tracker</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/daisyui@3.9.4/dist/full.css" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@mediapipe/hands"></script>
    <script src="https://cdn.jsdelivr.net/npm/@mediapipe/camera_utils"></script>
    <script src="https://cdn.jsdelivr.net/npm/@mediapipe/drawing_utils"></script>
    <style>
        body {
            background-color: #1a1a1a;
            color: #ffffff;
            margin: 0;
            overflow: hidden;
        }
        #video {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            transform: scaleX(-1); /* Mirror video */
            z-index: 1;
            opacity: 0.7; /* Make video slightly transparent */
        }
        #output {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 2;
            transform: scaleX(-1); /* Mirror canvas to match video */
        }
        .movable-object {
            position: absolute;
            border-radius: 8px;
            cursor: pointer;
            transition: transform 0.1s ease-out;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            user-select: none;
            z-index: 3;
            box-shadow: 0 0 15px rgba(0,0,0,0.3);
        }
        .gesture-indicator {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            padding: 15px;
            border-radius: 12px;
            z-index: 1000;
            font-size: 18px;
            color: #fff;
            box-shadow: 0 0 20px rgba(0,0,0,0.5);
        }
        .pinch-indicator {
            width: 20px;
            height: 20px;
            background: #ff3366;
            border-radius: 50%;
            position: fixed;
            pointer-events: none;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.2s;
            box-shadow: 0 0 10px rgba(255,51,102,0.5);
            transform: translate(-50%, -50%) scaleX(-1);
        }
        #loading {
            background: rgba(0, 0, 0, 0.8);
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 0 25px rgba(0,0,0,0.5);
        }
        .title-container {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
            text-align: center;
            pointer-events: none;
        }
        .animated-title {
            font-size: 2.5rem;
            font-weight: bold;
            background: linear-gradient(
                300deg,
                #ff3366,
                #ff6b6b,
                #4ecdc4,
                #45b7d1,
                #96c93d,
                #ff3366
            );
            background-size: 300% 300%;
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            animation: gradient 10s ease infinite;
            text-shadow: 0 0 20px rgba(0,0,0,0.3);
            font-family: 'Arial', sans-serif;
            letter-spacing: 2px;
        }
        @keyframes gradient {
            0% {
                background-position: 0% 50%;
            }
            50% {
                background-position: 100% 50%;
            }
            100% {
                background-position: 0% 50%;
            }
        }
        .title-glow {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(
                circle at center,
                rgba(255,255,255,0.1) 0%,
                rgba(255,255,255,0) 70%
            );
            filter: blur(8px);
            pointer-events: none;
            animation: glow 3s ease-in-out infinite alternate;
        }
        @keyframes glow {
            from {
                opacity: 0.5;
                transform: scale(0.95);
            }
            to {
                opacity: 0.8;
                transform: scale(1.05);
            }
        }
    </style>
</head>
<body class="min-h-screen">
    <div class="title-container">
        <div class="title-glow"></div>
        <h1 class="animated-title">www.echohive.live</h1>
    </div>

    <div id="loading" class="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-50">
        <div class="loading loading-spinner loading-lg text-primary"></div>
        <p class="mt-2 text-primary text-xl">Initializing Gesture Tracking...</p>
    </div>

    <div class="gesture-indicator">
        <p id="gestureText" class="text-lg font-semibold">Waiting for camera...</p>
    </div>

    <div class="pinch-indicator" id="pinchIndicator"></div>

    <video id="video" playsinline autoplay></video>
    <canvas id="output"></canvas>

    <script>
        // Constants and Variables
        const OBJECTS_COUNT = 5;
        const PINCH_THRESHOLD = 0.06;
        const GESTURE_SMOOTHING = 0.2;

        // DOM Elements
        const video = document.getElementById('video');
        const canvas = document.getElementById('output');
        const ctx = canvas.getContext('2d', { alpha: false });
        const loading = document.getElementById('loading');
        const gestureText = document.getElementById('gestureText');
        const pinchIndicator = document.getElementById('pinchIndicator');

        let isTracking = false;
        let camera = null;
        let hands = null;
        let movableObjects = [];
        let selectedObject = null;
        let lastPinchDistance = 0;
        let isPinching = false;

        // Initialize canvas and create objects
        function initializeCanvas() {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
            video.width = window.innerWidth;
            video.height = window.innerHeight;

            // Create movable objects
            const colors = ['#FF3366', '#33FF88', '#3366FF', '#FFCC33', '#FF33CC'];
            for (let i = 0; i < OBJECTS_COUNT; i++) {
                const obj = document.createElement('div');
                obj.className = 'movable-object';
                obj.style.width = '100px';
                obj.style.height = '100px';
                obj.style.backgroundColor = colors[i];
                obj.style.left = `${Math.random() * (window.innerWidth - 100)}px`;
                obj.style.top = `${Math.random() * (window.innerHeight - 100)}px`;
                obj.textContent = `Object ${i + 1}`;
                document.body.appendChild(obj);
                movableObjects.push(obj);
            }
        }

        // Gesture detection functions
        function getPinchDistance(landmarks) {
            const thumb = landmarks[4];
            const index = landmarks[8];
            return Math.hypot(
                (thumb.x - index.x) * canvas.width,
                (thumb.y - index.y) * canvas.height
            );
        }

        function detectGestures(landmarks) {
            const pinchDistance = getPinchDistance(landmarks);
            const thumbTip = landmarks[4];
            const indexTip = landmarks[8];
            
            // Calculate the midpoint in screen coordinates
            const pinchMidpoint = {
                x: ((1 - ((thumbTip.x + indexTip.x) / 2)) * canvas.width),
                y: ((thumbTip.y + indexTip.y) / 2) * canvas.height
            };

            // Update pinch indicator position
            pinchIndicator.style.left = `${pinchMidpoint.x}px`;
            pinchIndicator.style.top = `${pinchMidpoint.y}px`;

            // Detect pinch gesture
            if (pinchDistance < PINCH_THRESHOLD * canvas.width) {
                pinchIndicator.style.opacity = '1';
                if (!isPinching) {
                    // Start of pinch
                    isPinching = true;
                    const pinchX = pinchMidpoint.x;
                    const pinchY = pinchMidpoint.y;
                    
                    // Find closest object
                    selectedObject = movableObjects.find(obj => {
                        const rect = obj.getBoundingClientRect();
                        const centerX = rect.left + rect.width / 2;
                        const centerY = rect.top + rect.height / 2;
                        const distance = Math.hypot(centerX - pinchX, centerY - pinchY);
                        return distance < 100; // Selection radius
                    });

                    if (selectedObject) {
                        selectedObject.style.transform = 'scale(1.1)';
                        gestureText.textContent = '✨ Moving Object';
                    }
                } else if (selectedObject) {
                    // Continue pinch movement with corrected coordinates
                    const newX = pinchMidpoint.x - selectedObject.offsetWidth / 2;
                    const newY = pinchMidpoint.y - selectedObject.offsetHeight / 2;
                    selectedObject.style.left = `${newX}px`;
                    selectedObject.style.top = `${newY}px`;
                }
            } else {
                // End of pinch
                pinchIndicator.style.opacity = '0';
                if (isPinching) {
                    if (selectedObject) {
                        selectedObject.style.transform = 'scale(1)';
                    }
                    selectedObject = null;
                    isPinching = false;
                    gestureText.textContent = 'Ready for gestures';
                }
            }

            // Detect other gestures
            const palmBase = landmarks[0];
            const middleFinger = landmarks[12];
            const palmAngle = Math.atan2(
                middleFinger.y - palmBase.y,
                middleFinger.x - palmBase.x
            ) * 180 / Math.PI;

            if (!isPinching) {
                if (palmAngle > 45 && palmAngle < 135) {
                    gestureText.textContent = '👆 Palm Up';
                } else if (palmAngle < -45 && palmAngle > -135) {
                    gestureText.textContent = '👇 Palm Down';
                }
            }
        }

        function onResults(results) {
            if (!isTracking) return;

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            if (results.multiHandLandmarks && results.multiHandLandmarks.length > 0) {
                const landmarks = results.multiHandLandmarks[0]; // Use first hand
                detectGestures(landmarks);

                // Draw hand landmarks with corrected coordinates
                results.multiHandLandmarks.forEach(landmarks => {
                    // Draw connections
                    const connections = HAND_CONNECTIONS;
                    for (const connection of connections) {
                        const [i, j] = connection;
                        ctx.beginPath();
                        ctx.moveTo(
                            landmarks[i].x * canvas.width,
                            landmarks[i].y * canvas.height
                        );
                        ctx.lineTo(
                            landmarks[j].x * canvas.width,
                            landmarks[j].y * canvas.height
                        );
                        ctx.strokeStyle = '#00ff88';
                        ctx.lineWidth = 3;
                        ctx.stroke();
                    }

                    // Draw landmarks
                    landmarks.forEach((landmark, index) => {
                        const isSpecialPoint = [0, 4, 8, 12, 16, 20].includes(index);
                        ctx.beginPath();
                        ctx.arc(
                            landmark.x * canvas.width,
                            landmark.y * canvas.height,
                            isSpecialPoint ? 6 : 3,
                            0,
                            2 * Math.PI
                        );
                        ctx.fillStyle = isSpecialPoint ? '#ff3366' : '#ffffff';
                        ctx.fill();
                    });
                });
            } else {
                gestureText.textContent = 'Show your hand to the camera';
            }
        }

        async function initializeHandTracking() {
            try {
                loading.style.display = 'block';
                gestureText.textContent = 'Starting camera...';

                hands = new Hands({
                    locateFile: (file) => {
                        return `https://cdn.jsdelivr.net/npm/@mediapipe/hands/${file}`;
                    }
                });

                hands.setOptions({
                    maxNumHands: 1,
                    modelComplexity: 1,
                    minDetectionConfidence: 0.5,
                    minTrackingConfidence: 0.5
                });

                hands.onResults(onResults);

                camera = new Camera(video, {
                    onFrame: async () => {
                        await hands.send({image: video});
                    },
                    width: 1280,
                    height: 720
                });

                await camera.start();
                isTracking = true;
                loading.style.display = 'none';
                gestureText.textContent = 'Ready for gestures';
            } catch (error) {
                console.error('Error initializing hand tracking:', error);
                loading.style.display = 'none';
                gestureText.textContent = 'Error: Could not start camera';
            }
        }

        // Event Listeners
        window.addEventListener('load', () => {
            initializeCanvas();
            initializeHandTracking();
        });

        window.addEventListener('resize', () => {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
            video.width = window.innerWidth;
            video.height = window.innerHeight;
        });
    </script>
</body>
</html> 