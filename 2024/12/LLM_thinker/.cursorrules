api call we will be using:

from openai import AsyncOpenAI

client = AsyncOpenAI(
  base_url="https://openrouter.ai/api/v1",
  api_key="$OPENROUTER_API_KEY",
)

completion = client.chat.completions.create(

  model="openai/gpt-3.5-turbo",
  messages=[
    {
      "role": "user",
      "content": "What is the meaning of life?"
    }
  ]
)
print(completion.choices[0].message.content)

step 1:
user string as var to ask a question for a list of items

instruct the models to return the list  using <></> tags in a way to ensure perfect parsing everytim

using these models we get answers in parallel 3 times each:
openai/chatgpt-4o-latest
openai/o1-mini-2024-09-12
openai/o1-preview
openai/o1
deepseek/deepseek-chat
qwen/qwq-32b-preview
google/gemini-2.0-flash-thinking-exp:free
x-ai/grok-2-1212
google/gemini-exp-1206:free
amazon/nova-pro-v1
anthropic/claude-3.5-haiku-********:beta
anthropic/claude-3.5-sonnet:beta

when we have the results
create:
a markdown table in a md File
a beautiful table in html page

note how many times each item has been mentioned (account for different ways an item may be mentioned)
we want the table to be about which item was mentioned how many times and by which model how many times as concisely as possible
