import os
import json
from openai import OpenAI
from termcolor import colored
import sys
from typing import Dict

# Constants
API_KEY = os.getenv("OPENAI_API_KEY")
MODEL = "gpt-4o"

# Schema for customer feedback analysis
SCHEMA = """
{
    "feedback_analysis": {
        "overall_sentiment": {
            "score": "number",
            "label": "string"
        },
        "categories": {
            "product_quality": {
                "mentions": "number",
                "sentiment": "string",
                "key_issues": ["string"]
            },
            "customer_service": {
                "mentions": "number",
                "sentiment": "string",
                "key_issues": ["string"]
            },
            "user_experience": {
                "mentions": "number",
                "sentiment": "string",
                "key_issues": ["string"]
            }
        },
        "actionable_insights": [{
            "issue": "string",
            "priority": "string",
            "recommendation": "string",
            "impact_area": "string"
        }],
        "customer_satisfaction_indicators": {
            "nps_indicator": "number",
            "repeat_purchase_likelihood": "string",
            "advocacy_potential": "string"
        }
    }
}
"""

# Sample customer feedback
FEEDBACK = """
I've been using the product for 3 months now. The mobile app is quite buggy 
and crashes frequently, which is frustrating. However, the customer service 
team is amazing and always responds within minutes. The product quality is 
good but could be better - the battery life isn't as long as advertised. 
I would recommend this to others, but with some reservations about the app issues.
"""

def analyze_feedback(client: OpenAI, feedback: str) -> Dict:
    """Analyze customer feedback and generate structured insights"""
    try:
        print(colored("Analyzing customer feedback...", "cyan"))
        response = client.chat.completions.create(
            model=MODEL,
            messages=[
                {
                    "role": "system",
                    "content": f"""
                    Analyze the customer feedback according to this schema: {SCHEMA}
                    - Calculate sentiment scores on a scale of 0-100
                    - Identify specific issues in each category
                    - Prioritize actionable insights as HIGH/MEDIUM/LOW
                    - Estimate NPS indicator on a scale of 0-10
                    Respond only with valid JSON.
                    """
                },
                {"role": "user", "content": feedback}
            ],
            response_format={"type": "json_object"}
        )
        
        return json.loads(response.choices[0].message.content)

    except Exception as e:
        print(colored(f"Analysis error: {str(e)}", "red"))
        raise

def main():
    try:
        print(colored("Initializing OpenAI client...", "cyan"))
        client = OpenAI(api_key=API_KEY)

        # Analyze feedback
        results = analyze_feedback(client, FEEDBACK)

        # Output results
        print(colored("\nFeedback Analysis Results:", "green"))
        print(json.dumps(results, indent=2))

    except Exception as e:
        print(colored(f"Critical error: {str(e)}", "red"))
        sys.exit(1)

if __name__ == "__main__":
    main() 