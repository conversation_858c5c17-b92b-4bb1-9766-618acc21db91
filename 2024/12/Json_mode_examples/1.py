import os
import json
from openai import OpenAI
from termcolor import colored
import sys

try:
    # Constants
    API_KEY = os.getenv("OPENAI_API_KEY")
    MODEL = "gpt-4o"
    
    # Simple context
    TXT = "<PERSON> is 25 years old and lives in London"

    # Simple schema as a string
    SCHEMA = """
    {
        "name": "string",
        "age": "number",
        "city": "string"
    }
    """

    print(colored("Initializing OpenAI client...", "cyan"))
    client = OpenAI(api_key=API_KEY)

    print(colored("Sending request to OpenAI...", "cyan"))
    response = client.chat.completions.create(
        model=MODEL,
        messages=[
            {"role": "system", "content": f"Extract information according to this schema: {SCHEMA}. Respond only with valid JSON."},
            {"role": "user", "content": TXT}
        ],
        response_format={"type": "json_object"}
    )

    print(colored("\nParsed response:", "green"))
    parsed_response = json.loads(response.choices[0].message.content)
    print(json.dumps(parsed_response, indent=2))

except Exception as e:
    print(colored(f"An error occurred: {str(e)}", "red"))
    sys.exit(1)