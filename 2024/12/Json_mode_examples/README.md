# AI-Powered Business Analysis Tools

A collection of Python scripts using OpenAI's GPT-4o model for various business analysis tasks.

## Setup
1. Set your OpenAI API key as an environment variable:   ```
   OPENAI_API_KEY=your-api-key-here   ```

2. Install required packages:   ```
   pip install -r requirements.txt   ```

## Available Scripts

### 1. Basic JSON Extractor (1.py)
A simple script that extracts structured data from text using a basic JSON schema.
- Processes single text input
- Returns basic structured data
- Good starting point for understanding the pattern

### 2. Advanced Text Analyzer (2.py)
A more complex script that handles multiple text inputs with advanced validation.
- Processes multiple text inputs
- Includes retry logic
- Performs data aggregation
- Provides detailed error handling

### 3. Customer Feedback Analyzer (3.py)
Analyzes customer feedback and reviews to extract actionable insights.
- Calculates sentiment scores
- Categorizes feedback by area (product, service, UX)
- Provides prioritized recommendations
- Estimates customer satisfaction metrics

### 4. Resume Parser (4.py)
Extracts structured information from resumes and performs candidate assessment.
- Parses resume text into structured data
- Identifies key skills and experience
- Assesses candidate qualifications
- Generates standardized profiles
- Provides job fit analysis

### 5. Financial Report Analyzer (5.py)
Processes financial reports to extract key metrics and insights.
- Extracts financial metrics
- Analyzes trends and risks
- Calculates performance indicators
- Generates executive summaries
- Provides strategic recommendations

## Usage
Each script can be run independently: 