1. Create a simple Python script (1.py) that:
   - Uses OpenAI API with gpt-4o model
   - Implements a simple JSON schema
   - Uses JSON mode for response
   - Processes a simple text input
   - Extracts structured data according to schema

2. Create an advanced Python script (2.py) that:
   - Uses OpenAI API with gpt-4o model
   - Implements a complex nested JSON schema
   - Uses JSON mode with validation
   - Processes multiple text inputs
   - Includes detailed error handling
   - Adds data transformation capabilities

3. Create a customer feedback analyzer (3.py) that:
   - Processes customer reviews and feedback
   - Extracts sentiment, topics, and actionable insights
   - Categorizes feedback into product/service areas
   - Provides priority recommendations

4. Create a resume parser (4.py) that:
   - Extracts structured information from job applications
   - Identifies key skills, experience, and qualifications
   - Scores candidates against job requirements
   - Generates standardized candidate profiles

5. Create a financial report analyzer (5.py) that:
   - Processes quarterly financial reports
   - Extracts key metrics and performance indicators
   - Identifies trends and risk factors
   - Generates executive summaries

+ Note about JSON mode:
   - Ensures structured, parseable responses
   - Enforces strict JSON formatting
   - Reduces hallucinations in structured data extraction