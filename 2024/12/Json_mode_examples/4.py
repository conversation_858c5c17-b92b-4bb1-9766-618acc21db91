import os
import json
from openai import OpenAI
from termcolor import colored
import sys
from typing import Dict

# Constants
API_KEY = os.getenv("OPENAI_API_KEY")
MODEL = "gpt-4o"

# Schema for resume parsing
SCHEMA = """
{
    "candidate_profile": {
        "personal_info": {
            "name": "string",
            "contact": {
                "email": "string?",
                "phone": "string?"
            },
            "location": "string?"
        },
        "professional_summary": {
            "years_experience": "number",
            "key_roles": ["string"],
            "industries": ["string"]
        },
        "skills_assessment": {
            "technical_skills": [{
                "skill": "string",
                "level": "string",
                "years_experience": "number"
            }],
            "soft_skills": ["string"]
        },
        "experience_highlights": [{
            "role": "string",
            "achievements": ["string"],
            "technologies_used": ["string"]
        }],
        "education": [{
            "degree": "string",
            "field": "string",
            "institution": "string",
            "year": "number?"
        }],
        "job_fit_analysis": {
            "overall_match_score": "number",
            "strength_areas": ["string"],
            "gap_areas": ["string"],
            "recommendations": ["string"]
        }
    }
}
"""

# Sample resume text
RESUME = """
JANE SMITH
Senior Software Engineer with 8 years of experience

Professional Experience:
- Lead Developer at Tech Solutions (2020-Present)
  * Led team of 5 developers on cloud migration project
  * Implemented CI/CD pipeline reducing deployment time by 60%
  * Tech stack: Python, AWS, Docker, Kubernetes

- Software Engineer at DataCorp (2015-2020)
  * Developed scalable microservices architecture
  * Improved system performance by 40%
  * Technologies: Java, Spring Boot, MongoDB

Education:
- M.S. Computer Science, Stanford University (2015)
- B.S. Computer Engineering, MIT (2013)

Skills:
- Programming: Python, Java, JavaScript
- Cloud: AWS, Azure
- Tools: Docker, Kubernetes, Jenkins
- Soft Skills: Team Leadership, Project Management
"""

def parse_resume(client: OpenAI, resume_text: str) -> Dict:
    """Parse resume and generate structured profile with job fit analysis"""
    try:
        print(colored("Parsing resume...", "cyan"))
        response = client.chat.completions.create(
            model=MODEL,
            messages=[
                {
                    "role": "system",
                    "content": f"""
                    Parse the resume according to this schema: {SCHEMA}
                    - Extract all relevant information
                    - Assess skill levels as EXPERT/ADVANCED/INTERMEDIATE/BASIC
                    - Calculate job fit score on scale of 0-100
                    - Identify strengths and gaps
                    Respond only with valid JSON.
                    """
                },
                {"role": "user", "content": resume_text}
            ],
            response_format={"type": "json_object"}
        )
        
        return json.loads(response.choices[0].message.content)

    except Exception as e:
        print(colored(f"Parsing error: {str(e)}", "red"))
        raise

def main():
    try:
        print(colored("Initializing OpenAI client...", "cyan"))
        client = OpenAI(api_key=API_KEY)

        # Parse resume
        results = parse_resume(client, RESUME)

        # Output results
        print(colored("\nResume Analysis Results:", "green"))
        print(json.dumps(results, indent=2))

    except Exception as e:
        print(colored(f"Critical error: {str(e)}", "red"))
        sys.exit(1)

if __name__ == "__main__":
    main() 