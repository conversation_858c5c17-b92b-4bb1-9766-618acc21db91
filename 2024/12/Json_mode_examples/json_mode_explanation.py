"""
JSON Mode in OpenAI API: When and Why to Use It

1. Purpose 🎯
   JSON mode forces the API to return only valid JSON responses, making it 
   ideal for data extraction and processing tasks.

2. Key Benefits 💪
   - Guaranteed parseable JSON output
   - Strict schema adherence
   - Reduced hallucinations in structured data
   - Elimination of formatting errors
   - Prevention of explanatory text in responses

3. Best Use Cases 📋
   - Data extraction pipelines
   - API integrations
   - Automated workflows
   - Standardized reporting
   - Schema-based parsing

4. When to Avoid ⚠️
   - Conversational responses needed
   - Explanatory text required
   - Markdown/formatting desired

Example Implementation: 