import os
import json
from openai import OpenAI
from termcolor import colored
import sys
from typing import Dict, List
from datetime import datetime

# Constants
API_KEY = os.getenv("OPENAI_API_KEY")
MODEL = "gpt-4o"
MAX_RETRIES = 3

# Complex nested schema
SCHEMA = """
{
    "document_analysis": {
        "metadata": {
            "processed_date": "string",
            "confidence_score": "number"
        },
        "entities": {
            "people": [{
                "name": "string",
                "age": "number",
                "occupation": "string",
                "relationships": ["string"]
            }],
            "locations": [{
                "name": "string",
                "type": "string",
                "coordinates": {
                    "latitude": "number?",
                    "longitude": "number?"
                }
            }]
        },
        "summary": {
            "main_topics": ["string"],
            "sentiment": "string",
            "word_count": "number"
        }
    }
}
"""

# Sample texts for processing
TEXTS = [
    """
    <PERSON>, a 32-year-old software engineer, works at Tech Corp in New York City. 
    She frequently collaborates with her team lead <PERSON> (45) and often visits their 
    Silicon Valley office. Her sister <PERSON> (28) is a local artist.
    """,
    """
    The project meeting at London headquarters was highly successful, with positive feedback 
    from all stakeholders. <PERSON>, the 39-year-old project manager, presented the quarterly results.
    """
]

def validate_json_structure(data: Dict) -> bool:
    """Validate the basic structure of the JSON response"""
    try:
        required_keys = ["document_analysis", "metadata", "entities", "summary"]
        for key in required_keys:
            if key not in str(data):
                print(colored(f"Missing required key: {key}", "yellow"))
                return False
        return True
    except Exception as e:
        print(colored(f"Validation error: {str(e)}", "red"))
        return False

def process_text(client: OpenAI, text: str, retry_count: int = 0) -> Dict:
    """Process a single text with retry logic"""
    try:
        print(colored(f"\nProcessing text (attempt {retry_count + 1})...", "cyan"))
        response = client.chat.completions.create(
            model=MODEL,
            messages=[
                {
                    "role": "system",
                    "content": f"""
                    Analyze the following text according to this schema: {SCHEMA}
                    - Include current timestamp in processed_date
                    - Calculate confidence score based on text clarity
                    - Extract all people and their relationships
                    - Identify locations with their types
                    - Provide sentiment analysis and main topics
                    Respond only with valid JSON.
                    """
                },
                {"role": "user", "content": text}
            ],
            response_format={"type": "json_object"}
        )
        
        result = json.loads(response.choices[0].message.content)
        
        if not validate_json_structure(result):
            raise ValueError("Invalid JSON structure received")
            
        return result

    except Exception as e:
        if retry_count < MAX_RETRIES:
            print(colored(f"Retry attempt {retry_count + 1} due to: {str(e)}", "yellow"))
            return process_text(client, text, retry_count + 1)
        else:
            raise Exception(f"Failed after {MAX_RETRIES} attempts: {str(e)}")

def transform_results(results: List[Dict]) -> Dict:
    """Transform and aggregate results from multiple texts"""
    try:
        print(colored("\nTransforming results...", "cyan"))
        
        all_people = []
        all_locations = set()
        all_topics = set()
        total_confidence = 0

        for result in results:
            doc = result["document_analysis"]
            all_people.extend(doc["entities"]["people"])
            all_locations.update(loc["name"] for loc in doc["entities"]["locations"])
            all_topics.update(doc["summary"]["main_topics"])
            total_confidence += doc["metadata"]["confidence_score"]

        return {
            "aggregate_analysis": {
                "total_documents": len(results),
                "average_confidence": total_confidence / len(results),
                "unique_locations": list(all_locations),
                "total_people_mentioned": len(all_people),
                "all_topics": list(all_topics),
                "processed_timestamp": datetime.now().isoformat()
            }
        }

    except Exception as e:
        print(colored(f"Transformation error: {str(e)}", "red"))
        raise

def main():
    try:
        print(colored("Initializing OpenAI client...", "cyan"))
        client = OpenAI(api_key=API_KEY)

        # Process all texts
        results = []
        for i, text in enumerate(TEXTS, 1):
            print(colored(f"\nProcessing document {i}/{len(TEXTS)}", "cyan"))
            result = process_text(client, text)
            results.append(result)
            print(colored("Document processed successfully", "green"))

        # Transform and aggregate results
        aggregate_results = transform_results(results)

        # Output results
        print(colored("\nFinal Results:", "green"))
        print(json.dumps(aggregate_results, indent=2))

    except Exception as e:
        print(colored(f"Critical error: {str(e)}", "red"))
        sys.exit(1)

if __name__ == "__main__":
    main() 