import os
import json
from openai import OpenAI
from termcolor import colored
import sys
from typing import Dict

# Constants
API_KEY = os.getenv("OPENAI_API_KEY")
MODEL = "gpt-4o"

# Schema for financial report analysis
SCHEMA = """
{
    "financial_analysis": {
        "report_metadata": {
            "company_name": "string",
            "period": "string",
            "report_type": "string"
        },
        "key_metrics": {
            "revenue": {
                "value": "number",
                "change_percentage": "number",
                "trend": "string"
            },
            "profit_margins": {
                "gross_margin": "number",
                "operating_margin": "number",
                "net_margin": "number"
            },
            "cash_flow": {
                "operating_cash_flow": "number",
                "free_cash_flow": "number",
                "cash_ratio": "number"
            }
        },
        "risk_assessment": {
            "financial_risks": [{
                "category": "string",
                "severity": "string",
                "potential_impact": "string"
            }],
            "market_position": {
                "strengths": ["string"],
                "vulnerabilities": ["string"]
            }
        },
        "performance_indicators": {
            "kpis": [{
                "metric_name": "string",
                "current_value": "number",
                "target_value": "number",
                "status": "string"
            }],
            "year_over_year_growth": {
                "revenue_growth": "number",
                "profit_growth": "number",
                "market_share_growth": "number"
            }
        },
        "executive_summary": {
            "key_highlights": ["string"],
            "challenges": ["string"],
            "recommendations": ["string"]
        }
    }
}
"""

# Sample financial report text
FINANCIAL_REPORT = """
Q3 2023 Financial Report - TechCorp Inc.

Revenue reached $125M, up 15% YoY, driven by strong product sales and 
subscription growth. Gross margin improved to 68% from 65% last quarter.
Operating expenses increased by 8% due to expansion into new markets.

Cash flow from operations was $28M, with free cash flow of $22M.
Market share in core segments grew by 2.3 points to 18.5%.

Key challenges included supply chain pressures and increased competition
in the APAC region. New product launch in Q4 expected to drive growth.

Current ratio: 2.1
Debt to Equity: 0.45
Operating margin: 22%
"""

def analyze_financial_report(client: OpenAI, report_text: str) -> Dict:
    """Analyze financial report and generate structured insights"""
    try:
        print(colored("Analyzing financial report...", "cyan"))
        response = client.chat.completions.create(
            model=MODEL,
            messages=[
                {
                    "role": "system",
                    "content": f"""
                    Analyze the financial report according to this schema: {SCHEMA}
                    - Extract all key financial metrics
                    - Assess risks as HIGH/MEDIUM/LOW severity
                    - Calculate year-over-year changes
                    - Provide strategic recommendations
                    Respond only with valid JSON.
                    """
                },
                {"role": "user", "content": report_text}
            ],
            response_format={"type": "json_object"}
        )
        
        return json.loads(response.choices[0].message.content)

    except Exception as e:
        print(colored(f"Analysis error: {str(e)}", "red"))
        raise

def main():
    try:
        print(colored("Initializing OpenAI client...", "cyan"))
        client = OpenAI(api_key=API_KEY)

        # Analyze financial report
        results = analyze_financial_report(client, FINANCIAL_REPORT)

        # Output results
        print(colored("\nFinancial Analysis Results:", "green"))
        print(json.dumps(results, indent=2))

    except Exception as e:
        print(colored(f"Critical error: {str(e)}", "red"))
        sys.exit(1)

if __name__ == "__main__":
    main() 