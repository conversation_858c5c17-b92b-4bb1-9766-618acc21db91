STEP 1:

from openai import OpenAI

client = OpenAI(
  base_url="https://openrouter.ai/api/v1",
  api_key="$OPENROUTER_API_KEY",
)

completion = client.chat.completions.create(

  model="deepseek/deepseek-chat",
  messages=[
    {
      "role": "user",
      "content": "What is the meaning of life?"
    }
  ]
)
print(completion.choices[0].message.content)

usign this api call we want to build an auto coder. 
we take user input as variable(at the top of the code) and let the model generate code(user role. make it empty string by default). dont take user input from terminal
instruct the model for python code generation wiht system prompt:
model should return the code in between <code> and </code> tags.
parse it carefully and save it in a file.
use streaming responses and print to terminal as it comes in.

STEP 2:
we want an iterative code generation system
run the code with subprocess for max of 5 seconds
feed back any terminal output including errors back to model
ask it to fix errors or otherwise improve the code, add more features, etc.
add a max iteration variable at the top of the code.
save each new iteration to new file as ..._1.py, ..._2.py, ..._3.py, etc.
do not use the current context window of the model but use a fresh api call for error fixing and also a sepearete one for improvements when there are no errors.
print the terminal output from subprocess to the terminal as well
timing out of the subprocess shouldnt be considered an error.
