<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hand Grid Visualization</title>
    <script src="https://cdn.jsdelivr.net/npm/@mediapipe/hands"></script>
    <script src="https://cdn.jsdelivr.net/npm/@mediapipe/camera_utils"></script>
    <script src="https://cdn.jsdelivr.net/npm/@mediapipe/drawing_utils"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <style>
        body {
            margin: 0;
            overflow: hidden;
            background: #000;
            font-family: Arial, sans-serif;
        }
        #info {
            position: absolute;
            top: 10px;
            left: 10px;
            color: #fff;
            z-index: 100;
            font-size: 14px;
            text-shadow: 0 0 5px #00ff00;
        }
        #legend {
            position: absolute;
            top: 50px;
            left: 10px;
            color: #fff;
            z-index: 100;
            font-size: 12px;
            background: rgba(0,0,0,0.7);
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #00ff00;
        }
        #controls {
            position: absolute;
            top: 50%;
            right: 20px;
            transform: translateY(-50%);
            background: rgba(0,0,0,0.7);
            padding: 15px;
            border-radius: 10px;
            border: 1px solid #00ff00;
            color: #fff;
            z-index: 100;
        }
        .slider-container {
            margin: 15px 0;
        }
        .slider-container label {
            display: block;
            margin-bottom: 5px;
            color: #00ff00;
        }
        input[type="range"] {
            width: 200px;
            background: #000;
            height: 2px;
            -webkit-appearance: none;
            margin: 10px 0;
        }
        input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            border: 1px solid #00ff00;
            height: 16px;
            width: 16px;
            border-radius: 8px;
            background: #000;
            cursor: pointer;
        }
        .value-display {
            font-size: 12px;
            color: #888;
        }
        canvas {
            display: block;
        }
        #handCanvas {
            position: absolute;
            top: 0;
            left: 0;
            z-index: 1;
            pointer-events: none;
        }
    </style>
</head>
<body>
    <div id="info">Hand Grid Visualization</div>
    <div id="legend">
        <h3>Mapping Guide:</h3>
        <p>🤚 Hand Position:</p>
        <ul>
            <li>X-axis → Grid Width</li>
            <li>Y-axis → Grid Depth</li>
            <li>Z-axis → Bar Height</li>
        </ul>
        <p>🎨 Colors:</p>
        <ul>
            <li>Thumb: Purple</li>
            <li>Index: Blue</li>
            <li>Middle: Green</li>
            <li>Ring: Yellow</li>
            <li>Pinky: Red</li>
        </ul>
    </div>
    <div id="controls">
        <h3>Controls</h3>
        <div class="slider-container">
            <label for="heightSensitivity">Height Sensitivity</label>
            <input type="range" id="heightSensitivity" min="0.1" max="1.0" step="0.1" value="0.6">
            <div class="value-display">0.6</div>
        </div>
        <div class="slider-container">
            <label for="decayRate">Decay Rate</label>
            <input type="range" id="decayRate" min="0.8" max="0.99" step="0.01" value="0.95">
            <div class="value-display">0.95</div>
        </div>
        <div class="slider-container">
            <label for="glowIntensity">Glow Intensity</label>
            <input type="range" id="glowIntensity" min="0.5" max="3.0" step="0.1" value="1.2">
            <div class="value-display">1.2</div>
        </div>
    </div>
    <canvas id="handCanvas"></canvas>
    <script>
        // Constants
        const GRID_SIZE = 50;
        const SPACING = 2;
        const MAX_HEIGHT = 60; // Reduced from 100
        const FINGER_COLORS = {
            thumb: new THREE.Color(0x800080),    // Purple
            index: new THREE.Color(0x0000FF),    // Blue
            middle: new THREE.Color(0x00FF00),   // Green
            ring: new THREE.Color(0xFFFF00),     // Yellow
            pinky: new THREE.Color(0xFF0000)     // Red
        };

        // Control variables
        let heightSensitivity = 0.6;  // Reduced from 1.0
        let decayRate = 0.95;
        let glowIntensity = 1.2;      // Reduced from 2.0

        // Setup control listeners
        document.getElementById('heightSensitivity').addEventListener('input', function(e) {
            heightSensitivity = parseFloat(e.target.value);
            e.target.nextElementSibling.textContent = heightSensitivity.toFixed(2);
        });

        document.getElementById('decayRate').addEventListener('input', function(e) {
            decayRate = parseFloat(e.target.value);
            e.target.nextElementSibling.textContent = decayRate.toFixed(2);
        });

        document.getElementById('glowIntensity').addEventListener('input', function(e) {
            glowIntensity = parseFloat(e.target.value);
            e.target.nextElementSibling.textContent = glowIntensity.toFixed(2);
        });

        // Three.js setup
        const scene = new THREE.Scene();
        const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        const renderer = new THREE.WebGLRenderer({ antialias: true });
        renderer.setSize(window.innerWidth, window.innerHeight);
        document.body.appendChild(renderer.domElement);

        // Grid setup
        const bars = [];
        const geometry = new THREE.BoxGeometry(1, 1, 1);
        const material = new THREE.MeshPhongMaterial({
            color: 0x00ff00,
            emissive: 0x00ff00,
            emissiveIntensity: 0.2,
            transparent: true,
            opacity: 0.8
        });

        // Create grid of bars
        for (let x = 0; x < GRID_SIZE; x++) {
            bars[x] = [];
            for (let z = 0; z < GRID_SIZE; z++) {
                const bar = new THREE.Mesh(geometry, material.clone());
                bar.position.set(
                    (x - GRID_SIZE/2) * SPACING,
                    0,
                    (z - GRID_SIZE/2) * SPACING
                );
                bar.scale.y = 1;
                scene.add(bar);
                bars[x][z] = bar;
            }
        }

        // Enhanced lighting
        const ambientLight = new THREE.AmbientLight(0x404040);
        scene.add(ambientLight);
        const pointLight = new THREE.PointLight(0xffffff, 1, 100);
        pointLight.position.set(0, 50, 0);
        scene.add(pointLight);

        // Camera position
        camera.position.set(50, 50, 50);
        camera.lookAt(0, 0, 0);

        // Hand detection setup
        const handCanvas = document.getElementById('handCanvas');
        const handCtx = handCanvas.getContext('2d');
        handCanvas.width = window.innerWidth;
        handCanvas.height = window.innerHeight;

        const hands = new Hands({
            locateFile: (file) => {
                return `https://cdn.jsdelivr.net/npm/@mediapipe/hands/${file}`;
            }
        });

        hands.setOptions({
            maxNumHands: 2,
            modelComplexity: 1,
            minDetectionConfidence: 0.5,
            minTrackingConfidence: 0.5
        });

        // Get finger color based on landmark index
        function getFingerColor(landmarkIndex) {
            if (landmarkIndex <= 4) return FINGER_COLORS.thumb;
            if (landmarkIndex <= 8) return FINGER_COLORS.index;
            if (landmarkIndex <= 12) return FINGER_COLORS.middle;
            if (landmarkIndex <= 16) return FINGER_COLORS.ring;
            return FINGER_COLORS.pinky;
        }

        // Process hand landmarks
        hands.onResults((results) => {
            handCtx.clearRect(0, 0, handCanvas.width, handCanvas.height);
            
            if (results.multiHandLandmarks) {
                results.multiHandLandmarks.forEach((landmarks, handIndex) => {
                    // Draw hand skeleton with different colors for each finger
                    landmarks.forEach((_, index) => {
                        const color = getFingerColor(index);
                        drawConnectors(handCtx, landmarks, HAND_CONNECTIONS, 
                            {color: `rgb(${color.r * 255},${color.g * 255},${color.b * 255})`, lineWidth: 2});
                        drawLandmarks(handCtx, [landmarks[index]], 
                            {color: `rgb(${color.r * 255},${color.g * 255},${color.b * 255})`, lineWidth: 1});
                    });

                    // Update grid bars based on hand position
                    landmarks.forEach((landmark, index) => {
                        const x = Math.floor((landmark.x) * GRID_SIZE);
                        const z = Math.floor((landmark.y) * GRID_SIZE);
                        const height = (1 - landmark.z) * MAX_HEIGHT * heightSensitivity;

                        if (x >= 0 && x < GRID_SIZE && z >= 0 && z < GRID_SIZE) {
                            const bar = bars[x][z];
                            bar.scale.y = height;
                            bar.position.y = height / 2;
                            
                            // Set color based on finger
                            const fingerColor = getFingerColor(index);
                            bar.material.color.copy(fingerColor);
                            bar.material.emissive.copy(fingerColor);
                            
                            // Add intensity based on height
                            const intensity = height / MAX_HEIGHT;
                            bar.material.emissiveIntensity = intensity * glowIntensity;
                            
                            // Add slight color variation based on depth
                            const hueShift = (landmark.z * 0.2);
                            bar.material.color.offsetHSL(hueShift, 0, 0);
                        }
                    });
                });
            }
        });

        // Camera setup
        const camera_utils = new Camera(document.createElement('video'), {
            onFrame: async () => {
                await hands.send({image: camera_utils.video});
            },
            width: 1280,
            height: 720
        });
        camera_utils.start();

        // Animation loop
        function animate() {
            requestAnimationFrame(animate);
            
            // Rotate scene slightly
            scene.rotation.y += 0.001;
            
            // Smooth decay of bar heights and colors
            for (let x = 0; x < GRID_SIZE; x++) {
                for (let z = 0; z < GRID_SIZE; z++) {
                    const bar = bars[x][z];
                    bar.scale.y *= decayRate;
                    bar.position.y = bar.scale.y / 2;
                    bar.material.emissiveIntensity *= decayRate;
                }
            }
            
            renderer.render(scene, camera);
        }

        // Handle window resize
        window.addEventListener('resize', () => {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
            handCanvas.width = window.innerWidth;
            handCanvas.height = window.innerHeight;
        });

        animate();
    </script>
</body>
</html> 