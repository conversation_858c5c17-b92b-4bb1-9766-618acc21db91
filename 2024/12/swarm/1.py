from swarm import Swarm, Agent
from termcolor import colored
import os

# pip install following instructions here: https://github.com/openai/swarm

# Constants
OUTPUT_DIR = "agent_outputs"

def write_to_file(content: str, filename: str = "output.txt") -> str:
    """
    Tool to write content to a file
    """
    if not os.path.exists(OUTPUT_DIR):
        print(colored(f"Creating directory: {OUTPUT_DIR}", "yellow"))
        os.makedirs(OUTPUT_DIR)
    
    filepath = os.path.join(OUTPUT_DIR, filename)
    try:
        with open(filepath, "w", encoding="utf-8") as f:
            f.write(content)
        print(colored(f"Successfully wrote content to {filepath}", "green"))
        return f"Content has been written to {filepath}"
    except Exception as e:
        error_msg = f"Error writing to file: {str(e)}"
        print(colored(error_msg, "red"))
        return error_msg

def read_from_file(filename: str) -> str:
    """
    Tool to read content from a file
    """
    filepath = os.path.join(OUTPUT_DIR, filename)
    try:
        if not os.path.exists(filepath):
            error_msg = f"File not found: {filepath}"
            print(colored(error_msg, "red"))
            return error_msg
            
        with open(filepath, "r", encoding="utf-8") as f:
            content = f.read()
        print(colored(f"Successfully read content from {filepath}", "green"))
        return content
    except Exception as e:
        error_msg = f"Error reading file: {str(e)}"
        print(colored(error_msg, "red"))
        return error_msg

# Initialize the Swarm client and create agent
client = Swarm()
agent = Agent(
    name="File Manager",
    instructions="""You are a helpful agent that can read from and write to files.
    When asked to write something, use the write_to_file function.
    When asked to read something, use the read_from_file function.
    You can create various types of content and save them to files.
    You can also read and display the contents of existing files.
    Maintain a conversational tone with the user.""",
    functions=[write_to_file, read_from_file]
)

print(colored("File Manager Agent is ready! Type 'quit' to exit.", "green"))

def chat_with_agent():
    messages = []
    while True:
        user_input = input(colored("\nYou: ", "cyan"))
        
        if user_input.lower() == 'quit':
            print(colored("Goodbye!", "yellow"))
            break
            
        messages.append({"role": "user", "content": user_input})
        print(colored("\nAgent is thinking...", "yellow"))
        
        response = client.run(
            agent=agent,
            messages=messages
        )
        
        assistant_message = response.messages[-1]["content"]
        messages.append({"role": "assistant", "content": assistant_message})
        print(colored("Agent: ", "green") + assistant_message)

if __name__ == "__main__":
    chat_with_agent()