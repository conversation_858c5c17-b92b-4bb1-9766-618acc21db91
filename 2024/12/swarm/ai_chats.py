from swarm import Swarm, Agent
from termcolor import colored
import os
import json
from datetime import datetime

# Constants
OUTPUT_DIR = "agent_outputs"
CONVERSATION_FILE = "intellectual_discussion.json"
DEFAULT_TURNS = 5

def save_conversation(messages, filepath):
    """
    Save conversation to a JSON file in real-time
    """
    try:
        with open(filepath, "w", encoding="utf-8") as f:
            json.dump(messages, f, indent=4)
    except Exception as e:
        print(colored(f"Error saving conversation: {str(e)}", "red"))

# Initialize the Swarm client
client = Swarm()

# Create two intellectual agents
philosopher = Agent(
    name="Philosopher",
    instructions="""You are a deep-thinking philosopher who engages in intellectual discussions.
    You should:
    - Draw from various philosophical traditions and thinkers
    - Ask thought-provoking questions
    - Challenge assumptions
    - Provide well-reasoned arguments
    - Stay focused on the topic at hand
    Keep responses concise but meaningful."""
)

scientist = Agent(
    name="Scientist",
    instructions="""You are a scientifically-minded intellectual who engages in deep discussions.
    You should:
    - Use empirical evidence and scientific methods
    - Challenge claims that lack evidence
    - Bring up relevant research and discoveries
    - Connect scientific concepts to philosophical ideas
    - Maintain scientific rigor while being accessible
    Keep responses concise but meaningful."""
)

def start_intellectual_discussion():
    # Get number of turns from user (each turn is now a full exchange)
    while True:
        turns_input = input(colored("\nEnter number of discussion exchanges (default 5): ", "cyan")) or DEFAULT_TURNS
        turns = int(turns_input) if isinstance(turns_input, str) else turns_input
        if turns > 0:
            break
        print(colored("Please enter a positive number", "yellow"))

    print(colored(f"\nStarting an intellectual discussion for {turns} exchanges...", "green"))
    
    # Initialize conversation
    messages = []
    filepath = os.path.join(OUTPUT_DIR, CONVERSATION_FILE)
    
    # Create output directory if it doesn't exist
    if not os.path.exists(OUTPUT_DIR):
        os.makedirs(OUTPUT_DIR)
        print(colored(f"Created directory: {OUTPUT_DIR}", "yellow"))

    # Initial prompt to start the discussion
    initial_prompt = "Let's begin an intellectual discussion about the nature of consciousness and its relationship to reality."
    messages.append({"role": "system", "content": initial_prompt, "timestamp": str(datetime.now())})
    
    # Main discussion loop
    current_agent = philosopher  # Start with philosopher
    other_agent = scientist
    
    for exchange in range(turns):
        print(colored(f"\nExchange {exchange + 1}/{turns}", "yellow"))
        
        # First agent's turn
        print(colored(f"{current_agent.name}'s turn...", "cyan"))
        response = client.run(
            agent=current_agent,
            messages=[
                *[{"role": "assistant" if msg["role"] not in ["system", "user"] else msg["role"], 
                   "content": msg["content"]} for msg in messages]
            ]
        )
        
        assistant_message = response.messages[-1]["content"]
        message_entry = {
            "role": current_agent.name,
            "content": assistant_message,
            "timestamp": str(datetime.now())
        }
        messages.append(message_entry)
        save_conversation(messages, filepath)
        print(colored(f"{current_agent.name}: ", "green") + assistant_message)
        
        # Second agent's turn
        print(colored(f"\n{other_agent.name}'s turn...", "cyan"))
        response = client.run(
            agent=other_agent,
            messages=[
                *[{"role": "assistant" if msg["role"] not in ["system", "user"] else msg["role"], 
                   "content": msg["content"]} for msg in messages]
            ]
        )
        
        assistant_message = response.messages[-1]["content"]
        message_entry = {
            "role": other_agent.name,
            "content": assistant_message,
            "timestamp": str(datetime.now())
        }
        messages.append(message_entry)
        save_conversation(messages, filepath)
        print(colored(f"{other_agent.name}: ", "green") + assistant_message)
        
        # Swap agents for next exchange
        current_agent, other_agent = other_agent, current_agent
        
    print(colored("\nDiscussion completed! Check the JSON file for the full conversation.", "green"))

if __name__ == "__main__":
    start_intellectual_discussion() 