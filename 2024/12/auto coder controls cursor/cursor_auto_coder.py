import cv2
import numpy as np
import pyautogui
from termcolor import cprint
import time

"""
REQUIREMENTS:
- Visual Studio Code Theme: Dark (Visual Studio) / Visual Studio Dark
- Zoom Level: Default (100%)
- All required images must be in the same directory as this script
"""

# User Input Settings
INITIAL_USER_TEXT = "build a beatuiful daisy ui landing page in page.html"  # Default initial message
CONTINUED_USER_TEXT = "Please improve this"  # Text for subsequent iterations
NUMBER_OF_ITERATIONS = 5  # Default number of iterations

# Image and Detection Settings
CONFIDENCE_THRESHOLD = 0.9
SCALE_RANGE = np.linspace(0.2, 3.0, 50)
ITERATION_DELAY = 2  # Delay between iterations in seconds
MAX_RETRIES = 3  # Maximum retries for failed actions
CLICK_OFFSET_RIGHT = 30  # Percentage to offset click to the right for submit button

# Required Images and Descriptions
IMAGE_INFO = {
    'is_composer_open.png': 'Image to detect if the composer window is already open',
    'agent_toggle.png': 'Button to toggle agent mode in composer',
    'composer_input.png': 'Text input area in the composer',
    'submit.png': 'Submit button (clicked 30% to the right of center)'
}

def print_instructions():
    cprint("\n⚠️  WARNING: DO NOT CLICK INSIDE THE TERMINAL WHILE THE SCRIPT IS RUNNING! ⚠️", "red", attrs=['bold'])
    cprint("This will interrupt the automation process.\n", "red", attrs=['bold'])
    
    cprint("\nRequired Images:", "yellow")
    for img, desc in IMAGE_INFO.items():
        cprint(f"- {img}: {desc}", "cyan")
    
    cprint("\nCurrent Settings:", "yellow")
    cprint(f"- Text: {INITIAL_USER_TEXT}", "cyan")
    cprint(f"- Iterations: {NUMBER_OF_ITERATIONS}", "cyan")
    cprint(f"- Continued Text: {CONTINUED_USER_TEXT}", "cyan")
    
    cprint("\nAutomation Process:", "yellow")
    cprint("1. Keep all required images in the same directory", "cyan")
    cprint("2. The script will automatically run for the specified iterations", "cyan")
    cprint("3. Press Ctrl+C to stop at any time\n", "cyan")
    
    cprint("Starting in 1 seconds...", "yellow")
    time.sleep(1)

def check_image_exists(image_path):
    try:
        cprint(f"Checking if {image_path} exists on screen...", "cyan")
        ref_image = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
        if ref_image is None:
            cprint(f"Failed to load {image_path}. Check file path and integrity.", "red")
            return False

        screen = pyautogui.screenshot()
        screen = cv2.cvtColor(np.array(screen), cv2.COLOR_RGB2GRAY)

        for scale in SCALE_RANGE:
            resized = cv2.resize(ref_image, None, fx=scale, fy=scale, interpolation=cv2.INTER_AREA)
            res = cv2.matchTemplate(screen, resized, cv2.TM_CCOEFF_NORMED)
            _, max_val, _, _ = cv2.minMaxLoc(res)
            if max_val > CONFIDENCE_THRESHOLD:
                cprint(f"Found {image_path} on screen", "green")
                return True
        
        cprint(f"Could not find {image_path} on screen", "yellow")
        return False
    except Exception as e:
        cprint(f"Error checking for {image_path}: {str(e)}", "red")
        return False

def send_ctrl_i():
    try:
        cprint("Attempting to send Ctrl+I command...", "cyan")
        pyautogui.hotkey('ctrl', 'i')
        cprint("Successfully sent Ctrl+I command", "green")
        time.sleep(1)  # Wait for composer to open
    except Exception as e:
        cprint(f"Error sending Ctrl+I command: {str(e)}", "red")

def click_image_center(image_path):
    try:
        cprint(f"Attempting to click {image_path} at center...", "cyan")
        ref_image = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
        if ref_image is None:
            cprint(f"Failed to load {image_path}. Check file path and integrity.", "red")
            return False

        screen = pyautogui.screenshot()
        screen = cv2.cvtColor(np.array(screen), cv2.COLOR_RGB2GRAY)

        for scale in SCALE_RANGE:
            resized = cv2.resize(ref_image, None, fx=scale, fy=scale, interpolation=cv2.INTER_AREA)
            h, w = resized.shape
            res = cv2.matchTemplate(screen, resized, cv2.TM_CCOEFF_NORMED)
            _, max_val, _, max_loc = cv2.minMaxLoc(res)
            
            if max_val > CONFIDENCE_THRESHOLD:
                center_x = max_loc[0] + w // 2
                center_y = max_loc[1] + h // 2
                cprint(f"Clicking at center position ({center_x}, {center_y})...", "cyan")
                pyautogui.click(center_x, center_y)
                cprint(f"Successfully clicked on {image_path}", "green")
                return True
        
        cprint(f"Could not find {image_path} to click", "red")
        return False
    except Exception as e:
        cprint(f"Error clicking {image_path}: {str(e)}", "red")
        return False

def click_image_with_offset(image_path, x_offset_percent=0, y_offset_percent=0):
    try:
        cprint(f"Attempting to click {image_path} with offset...", "cyan")
        ref_image = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
        if ref_image is None:
            cprint(f"Failed to load {image_path}. Check file path and integrity.", "red")
            return False

        screen = pyautogui.screenshot()
        screen = cv2.cvtColor(np.array(screen), cv2.COLOR_RGB2GRAY)

        for scale in SCALE_RANGE:
            resized = cv2.resize(ref_image, None, fx=scale, fy=scale, interpolation=cv2.INTER_AREA)
            h, w = resized.shape
            res = cv2.matchTemplate(screen, resized, cv2.TM_CCOEFF_NORMED)
            _, max_val, _, max_loc = cv2.minMaxLoc(res)
            
            if max_val > CONFIDENCE_THRESHOLD:
                # Calculate base center position
                center_x = max_loc[0] + w // 2
                center_y = max_loc[1] + h // 2
                
                # Apply offsets
                offset_x = int(w * (x_offset_percent / 100))
                offset_y = int(h * (y_offset_percent / 100))
                
                final_x = center_x + offset_x
                final_y = center_y + offset_y
                
                cprint(f"Clicking at position ({final_x}, {final_y}) with offset...", "cyan")
                pyautogui.click(final_x, final_y)
                cprint(f"Successfully clicked on {image_path} with offset", "green")
                return True
        
        cprint(f"Could not find {image_path} to click", "red")
        return False
    except Exception as e:
        cprint(f"Error clicking {image_path} with offset: {str(e)}", "red")
        return False

def type_text(text):
    try:
        cprint("Typing user input...", "cyan")
        pyautogui.write(text)
        cprint("Successfully typed user input", "green")
        return True
    except Exception as e:
        cprint(f"Error typing text: {str(e)}", "red")
        return False

def wait_for_generation_complete():
    try:
        cprint("Waiting for generation to complete...", "cyan")
        while check_image_exists('is_generating.png'):
            cprint("Still generating...", "yellow")
            time.sleep(2)
        cprint("Generation completed!", "green")
        return True
    except Exception as e:
        cprint(f"Error while waiting for generation: {str(e)}", "red")
        return False

def insert_and_submit_text(text):
    try:
        cprint("Starting text insertion and submission process...", "cyan")
        
        # Click composer input and type text
        if not click_image_center('composer_input.png'):
            cprint("Failed to find composer input", "red")
            return False
        
        time.sleep(0.5)
        if not type_text(text):
            cprint("Failed to type text", "red")
            return False
            
        time.sleep(0.5)
        
        # Click submit button with 30% right offset
        if not click_image_with_offset('submit.png', x_offset_percent=CLICK_OFFSET_RIGHT):
            cprint("Failed to find submit button", "red")
            return False
        
        # Wait for generation to complete
        if not wait_for_generation_complete():
            cprint("Failed to wait for generation completion", "red")
            return False
            
        cprint("Successfully inserted and submitted text", "green")
        return True
    except Exception as e:
        cprint(f"Error in insert_and_submit_text: {str(e)}", "red")
        return False

if __name__ == "__main__":
    try:
        print_instructions()
        cprint(f"\nStarting automation for {NUMBER_OF_ITERATIONS} iterations...", "green")
        time.sleep(1)
        
        for iteration in range(1, NUMBER_OF_ITERATIONS + 1):
            try:
                cprint(f"\n[Iteration {iteration}/{NUMBER_OF_ITERATIONS}]", "yellow", attrs=['bold'])
                
                # 1. Check if composer is already open
                if not check_image_exists('is_composer_open.png'):
                    send_ctrl_i()
                    time.sleep(1)

                # 2. Click agent toggle at the center
                click_image_center('agent_toggle.png')
                time.sleep(0.5)

                # 3. Insert and submit text
                current_text = INITIAL_USER_TEXT if iteration == 1 else CONTINUED_USER_TEXT
                if not insert_and_submit_text(current_text):
                    cprint(f"Failed iteration {iteration}, retrying...", "red")
                    continue
                
                cprint(f"Iteration {iteration} completed successfully", "green")
                
                # Wait before next iteration
                if iteration < NUMBER_OF_ITERATIONS:
                    cprint(f"Waiting {ITERATION_DELAY} seconds before next iteration...", "cyan")
                    time.sleep(ITERATION_DELAY)

            except Exception as e:
                cprint(f"Error in iteration {iteration}: {str(e)}", "red")
                continue

        cprint("\nAll iterations completed!", "green", attrs=['bold'])

    except KeyboardInterrupt:
        cprint("\nProgram terminated by user", "yellow")
    except Exception as e:
        cprint(f"\nUnexpected error occurred: {str(e)}", "red")
