import os
from openai import OpenAI
from termcolor import colored
import json
from typing import Dict, Any

# Constants
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
MODEL_NAME = "gpt-4o"
TEMPERATURE = 0.7

# Agent definitions
AGENTS = {
    "customer_service": {
        "role": "You are a helpful customer service agent focused on resolving product-related issues.",
        "tasks": ["refunds", "product_issues", "shipping"]
    },
    "technical_support": {
        "role": "You are a technical support specialist who helps with software and hardware problems.",
        "tasks": ["bugs", "installation", "configuration"]
    },
    "sales": {
        "role": "You are a sales representative helping customers find the right products.",
        "tasks": ["pricing", "products", "deals"]
    }
}

class LLMRouter:
    def __init__(self):
        try:
            self.client = OpenAI(api_key=OPENAI_API_KEY)
            print(colored("✓ LLM Router initialized successfully", "green"))
        except Exception as e:
            print(colored(f"✗ Error initializing OpenAI client: {str(e)}", "red"))
            raise

    def _get_routing_prompt(self) -> str:
        return """You are a routing agent that determines which specialized agent should handle a user query.
        Analyze the user's message and return a JSON response with the following structure:
        {
            "selected_agent": "agent_name",
            "confidence": float,
            "reasoning": "string"
        }
        Available agents: customer_service, technical_support, sales"""

    def _call_llm(self, messages: list) -> Dict[str, Any]:
        try:
            response = self.client.chat.completions.create(
                model=MODEL_NAME,
                messages=messages,
                temperature=TEMPERATURE,
                response_format={"type": "json_object"}
            )
            return json.loads(response.choices[0].message.content)
        except Exception as e:
            print(colored(f"✗ Error calling LLM: {str(e)}", "red"))
            raise

    def route_query(self, user_query: str) -> Dict[str, Any]:
        try:
            print(colored("→ Routing user query...", "cyan"))
            
            messages = [
                {"role": "system", "content": self._get_routing_prompt()},
                {"role": "user", "content": user_query}
            ]
            
            routing_response = self._call_llm(messages)
            
            print(colored(f"✓ Query routed to: {routing_response['selected_agent']}", "green"))
            print(colored(f"  Confidence: {routing_response['confidence']}", "yellow"))
            print(colored(f"  Reasoning: {routing_response['reasoning']}", "cyan"))
            
            return routing_response
        except Exception as e:
            print(colored(f"✗ Error in route_query: {str(e)}", "red"))
            raise

    def get_agent_response(self, user_query: str, agent_name: str) -> str:
        try:
            print(colored(f"→ Getting response from {agent_name}...", "cyan"))
            
            if agent_name not in AGENTS:
                raise ValueError(f"Invalid agent name: {agent_name}")
            
            messages = [
                {"role": "system", "content": AGENTS[agent_name]["role"]},
                {"role": "user", "content": user_query}
            ]
            
            response = self.client.chat.completions.create(
                model=MODEL_NAME,
                messages=messages,
                temperature=TEMPERATURE
            )
            
            agent_response = response.choices[0].message.content
            print(colored("✓ Agent response received", "green"))
            return agent_response
        except Exception as e:
            print(colored(f"✗ Error getting agent response: {str(e)}", "red"))
            raise

def print_welcome_message():
    print(colored("\n=== Welcome to Smart Query Router ===", "cyan"))
    print(colored("\nAvailable Departments:", "yellow"))
    print(colored("1. Customer Service", "green"))
    print(colored("   • Product refunds", "white"))
    print(colored("   • Shipping issues", "white"))
    print(colored("   • General product concerns", "white"))
    
    print(colored("\n2. Technical Support", "green"))
    print(colored("   • Software/Hardware problems", "white"))
    print(colored("   • Installation assistance", "white"))
    print(colored("   • Configuration help", "white"))
    
    print(colored("\n3. Sales Department", "green"))
    print(colored("   • Product pricing", "white"))
    print(colored("   • Special deals", "white"))
    print(colored("   • Product information", "white"))
    
    print(colored("\nHow to use:", "yellow"))
    print(colored("Simply type your question, and we'll route you to the best department!", "white"))
    print(colored("Type 'quit' to exit the program", "white"))
    print(colored("\n" + "="*40 + "\n", "cyan"))

def main():
    try:
        router = LLMRouter()
        print_welcome_message()
        
        while True:
            user_input = input(colored("What can we help you with today? (or 'quit' to exit): ", "yellow"))
            
            if user_input.lower() == 'quit':
                print(colored("Goodbye!", "green"))
                break
            
            # Get routing decision
            routing_result = router.route_query(user_input)
            
            # Get response from selected agent
            if routing_result['confidence'] > 0.7:
                agent_response = router.get_agent_response(
                    user_input, 
                    routing_result['selected_agent']
                )
                print(colored("\nAgent Response:", "green"))
                print(colored(agent_response, "white"))
            else:
                print(colored("\nConfidence too low for automatic routing. Please be more specific.", "yellow"))
    except Exception as e:
        print(colored(f"Error in main loop: {str(e)}", "red"))
        raise

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print(colored("\nProgram terminated by user", "yellow"))
    except Exception as e:
        print(colored(f"Fatal error: {str(e)}", "red"))
