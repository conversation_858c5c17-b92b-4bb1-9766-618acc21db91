Your task is to create html web tools for user to use. do not create any additonal folders inside this folder
use daisy ui, tailwind and anime js where needed to save on code. But you can still use css and js if needed.

You will server these tools via a single fastapi server.
Fastapi backend's purpose is to serve the different html tools to the user. 
therefore, almost always the full functionality of the tool will be in the html file. 
unless user specifies otherwise.

main.py will always be in root folder and run the app with main:app 127 with reload
You must always design the fastapi server to launch the app in the browser while launching the server.

Your action cycle steps must always follow this pattern:
1- carefully observe the user's request
2- read the html tools already existing under tools folder
3- create tools folder if it does not exist
4- if an appropriate tool is not found, create a new one that meets the user's request
5- then launch the tool with fastapi server always ensuring that while launching the server it also launches the tool in the browser(ESSENTIAL)

