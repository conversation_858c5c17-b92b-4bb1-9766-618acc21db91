# Multi-Language Voice & Text Translator

A real-time voice and text translation application that supports multiple languages with a modern, dark-themed user interface.

## Features

- Real-time voice and text translation
- Support for 20 languages including Spanish, French, German, and more
- Modern dark-themed UI with smooth animations
- Multiple language selection for simultaneous translations
- Real-time conversation logging
- Voice chat capabilities

## Requirements

- Python 3.7+
- OpenAI API Key

## Installation

1. Install the required packages:
```bash
pip install -r requirements.txt
```

2. Set up your OpenAI API key as an environment variable:
```bash
# Windows
set OPENAI_API_KEY=your_api_key_here

# Linux/Mac
export OPENAI_API_KEY=your_api_key_here
```

## Running the Application

1. Start the server:
```bash
python multi_language_tranlator_with_oob.py
```

2. Open your browser and navigate to:
```
http://127.0.0.1:8000
```

## Usage

1. Click "Connect & Start Chat" to begin a session
2. Select your desired translation languages from the dropdown menu
3. Type your message or speak using your microphone
4. View translations in real-time in the sidebar
5. Monitor the conversation log for a complete chat history

## Supported Languages

- Spanish
- French
- German
- Italian
- Portuguese
- Russian
- Japanese
- Korean
- Chinese
- Arabic
- Hindi
- Turkish
- Dutch
- Polish
- Vietnamese
- Thai
- Swedish
- Danish
- Finnish
- Greek 