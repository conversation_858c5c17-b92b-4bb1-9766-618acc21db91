#!/usr/bin/env python3
import os
import requests
from fastapi import FastAPI, Request
from fastapi.responses import HTMLResponse
import uvicorn
from termcolor import colored

app = FastAPI()

class TranslationSystem:
    """Class to handle translations and language management"""
    
    @staticmethod
    def get_languages():
        """Returns a list of available languages for translation"""
        return [
            {"code": "es", "name": "Spanish"},
            {"code": "fr", "name": "French"},
            {"code": "de", "name": "German"},
            {"code": "it", "name": "Italian"},
            {"code": "pt", "name": "Portuguese"},
            {"code": "ru", "name": "Russian"},
            {"code": "ja", "name": "Japanese"},
            {"code": "ko", "name": "Korean"},
            {"code": "zh", "name": "Chinese"},
            {"code": "ar", "name": "Arabic"},
            {"code": "hi", "name": "Hindi"},
            {"code": "tr", "name": "Turkish"},
            {"code": "nl", "name": "Dutch"},
            {"code": "pl", "name": "Polish"},
            {"code": "vi", "name": "Vietnamese"},
            {"code": "th", "name": "Thai"},
            {"code": "sv", "name": "Swedish"},
            {"code": "da", "name": "Danish"},
            {"code": "fi", "name": "Finnish"},
            {"code": "el", "name": "Greek"}
        ]

HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="en" data-theme="dark">
<head>
    <meta charset="UTF-8" />
    <title>Multi-Language Voice & Text Translator</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/daisyui@4.7.2/dist/full.min.css" rel="stylesheet" type="text/css" />
    <script src="https://cdn.jsdelivr.net/npm/daisyui@4.7.2/dist/full.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js"></script>
    <style>
        .gradient-text {
            background: linear-gradient(45deg, #6366f1, #8b5cf6, #d946ef);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            animation: gradient 6s ease infinite;
            background-size: 200% 200%;
        }
        
        @keyframes gradient {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .loading-dots::after {
            content: '...';
            animation: dots 1.5s steps(4, end) infinite;
        }

        @keyframes dots {
            0%, 20% { content: '.'; }
            40% { content: '..'; }
            60% { content: '...'; }
            80%, 100% { content: ''; }
        }

        .glass-morphism {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 6px 24px 0 rgba(31, 38, 135, 0.37);
        }

        #log {
            color: #e2e8f0;
            font-size: 0.75rem;
        }

        .translation-badge {
            animation: fadeIn 0.5s ease-in;
            padding: 0.75rem;
            margin-bottom: 0.75rem;
            font-size: 0.875rem;
        }

        .translation-badge:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
            transition: all 0.3s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .translation-container {
            max-height: calc(100vh - 2rem);
            overflow-y: auto;
            padding-right: 0.5rem;
        }

        .translation-container::-webkit-scrollbar {
            width: 6px;
        }

        .translation-container::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
        }

        .translation-container::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
        }

        .translation-container::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .language-select {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 0.5rem;
            border-radius: 0.375rem;
            margin-bottom: 0.375rem;
            width: 100%;
            font-size: 0.875rem;
        }

        .language-select:hover {
            background: rgba(255, 255, 255, 0.15);
            border-color: rgba(255, 255, 255, 0.3);
        }

        .language-select:focus {
            outline: none;
            border-color: rgba(139, 92, 246, 0.5);
            box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.2);
        }

        .language-select option {
            background: #2a303c;
            color: white;
            padding: 0.5rem;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 min-h-screen p-4">
    <div class="container mx-auto max-w-[70%]">
        <h1 class="text-3xl font-bold mb-4 text-center gradient-text">Multi-Language Voice & Text Translator</h1>
        
        <div class="flex gap-3">
            <!-- Main Content -->
            <div class="flex-grow max-w-[60%]">
                <!-- Main Card Container -->
                <div class="glass-morphism rounded-lg p-4 space-y-4">
                    <!-- Status Section -->
                    <div class="space-y-3">
                        <button id="btn-start" class="btn btn-sm btn-primary w-full bg-gradient-to-r from-violet-600 to-indigo-600 hover:from-violet-700 hover:to-indigo-700 border-0 transform transition-transform duration-200 hover:scale-[1.02]">
                            Connect & Start Chat
                        </button>
                        <div id="status" class="text-xs text-gray-300 text-center">
                            Click "Connect & Start Chat" above to begin
                        </div>
                    </div>

                    <!-- Text Input Section -->
                    <div class="flex items-center space-x-2">
                        <input type="text" id="text-input" 
                            placeholder="Type your message..." 
                            class="input input-sm input-bordered w-full bg-opacity-20 focus:ring-2 focus:ring-violet-500 transition-all duration-300"
                        />
                        <button id="btn-send-text" 
                            class="btn btn-sm btn-accent bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 border-0">
                            Send
                        </button>
                    </div>

                    <!-- Conversation Log -->
                    <div class="mt-4">
                        <h2 class="font-bold mb-2 text-sm text-violet-300">Conversation Log</h2>
                        <div class="glass-morphism rounded-lg p-3 h-48 overflow-y-auto">
                            <pre id="log" class="text-xs whitespace-pre-wrap"></pre>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Translation Sidebar -->
            <div class="w-[40%] min-w-[300px]">
                <div class="glass-morphism rounded-lg p-3 sticky top-4">
                    <h2 class="text-lg font-bold mb-3 text-violet-300">Translations</h2>
                    
                    <!-- Language Selection Dropdowns -->
                    <div id="language-selections" class="space-y-2 mb-4">
                        <div class="flex items-center gap-2">
                            <select id="lang1" class="language-select text-sm"></select>
                            <button onclick="addLanguageSelect()" class="btn btn-circle btn-xs bg-violet-500 hover:bg-violet-600 border-0">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
                                </svg>
                            </button>
                        </div>
                    </div>

                    <!-- Translation Results -->
                    <div id="translations" class="space-y-3 translation-container" style="max-height: 50vh; overflow-y-auto;">
                        <!-- Translation results will be added here dynamically -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let pc, dc;
        const startButton = document.getElementById("btn-start");
        const textInput = document.getElementById("text-input");
        const translationsContainer = document.getElementById("translations");
        const languageSelectionsContainer = document.getElementById("language-selections");
        let languageSelectCount = 1;
        
        // Available languages
        const languages = [
            {code: "es", name: "Spanish"},
            {code: "fr", name: "French"},
            {code: "de", name: "German"},
            {code: "it", name: "Italian"},
            {code: "pt", name: "Portuguese"},
            {code: "ru", name: "Russian"},
            {code: "ja", name: "Japanese"},
            {code: "ko", name: "Korean"},
            {code: "zh", name: "Chinese"},
            {code: "ar", name: "Arabic"},
            {code: "hi", name: "Hindi"},
            {code: "tr", name: "Turkish"},
            {code: "nl", name: "Dutch"},
            {code: "pl", name: "Polish"},
            {code: "vi", name: "Vietnamese"},
            {code: "th", name: "Thai"},
            {code: "sv", name: "Swedish"},
            {code: "da", name: "Danish"},
            {code: "fi", name: "Finnish"},
            {code: "el", name: "Greek"}
        ];

        function createLanguageSelect(id) {
            const select = document.createElement('select');
            select.id = `lang${id}`;
            select.className = 'language-select';
            select.innerHTML = '<option value="">Select Language</option>';
            languages.forEach(lang => {
                const option = document.createElement('option');
                option.value = lang.code;
                option.textContent = lang.name;
                select.appendChild(option);
            });
            return select;
        }

        function createRemoveButton() {
            const button = document.createElement('button');
            button.className = 'btn btn-circle btn-xs bg-red-500 hover:bg-red-600 border-0';
            button.innerHTML = `
                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M5 10a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1z" clip-rule="evenodd" />
                </svg>
            `;
            return button;
        }

        function addLanguageSelect() {
            if (languageSelectCount >= 5) return;
            
            languageSelectCount++;
            const container = document.createElement('div');
            container.className = 'flex items-center gap-2';
            container.id = `lang-container-${languageSelectCount}`;
            
            const select = createLanguageSelect(languageSelectCount);
            const removeButton = createRemoveButton();
            
            removeButton.onclick = () => {
                container.remove();
                languageSelectCount--;
            };
            
            container.appendChild(select);
            container.appendChild(removeButton);
            
            // Add animation
            container.style.opacity = '0';
            container.style.transform = 'translateY(-10px)';
            languageSelectionsContainer.appendChild(container);
            
            // Trigger animation
            requestAnimationFrame(() => {
                container.style.transition = 'all 0.3s ease';
                container.style.opacity = '1';
                container.style.transform = 'translateY(0)';
            });
        }

        // Initialize first language dropdown
        function initializeLanguageDropdowns() {
            const firstSelect = document.getElementById('lang1');
            firstSelect.innerHTML = '<option value="">Select Language</option>';
            languages.forEach(lang => {
                const option = document.createElement('option');
                option.value = lang.code;
                option.textContent = lang.name;
                firstSelect.appendChild(option);
            });
            firstSelect.value = 'es'; // Set default selection
        }

        initializeLanguageDropdowns();
        
        startButton.addEventListener("click", handleConnection);
        textInput.addEventListener("keypress", (e) => {
            if (e.key === "Enter") {
                sendTextMessage();
            }
        });

        function handleConnection() {
            if (pc && (pc.connectionState === "connected" || pc.connectionState === "connecting")) {
                disconnectChat();
            } else {
                startChat();
            }
        }

        function disconnectChat() {
            if (pc) {
                pc.close();
                if (dc) dc.close();
            }
            pc = null;
            dc = null;
            startButton.textContent = "Connect & Start Chat";
            startButton.classList.remove("bg-gradient-to-r", "from-red-600", "to-red-700", "hover:from-red-700", "hover:to-red-800");
            startButton.classList.add("bg-gradient-to-r", "from-violet-600", "to-indigo-600", "hover:from-violet-700", "hover:to-indigo-700");
            document.getElementById("status").textContent = "Disconnected. Click Connect to start a new chat.";
            logMessage("[INFO] Disconnected from chat.");
        }

        async function startChat() {
            startButton.textContent = "Connecting...";
            startButton.classList.remove("bg-gradient-to-r", "from-violet-600", "to-indigo-600", "hover:from-violet-700", "hover:to-indigo-700");
            startButton.classList.add("bg-gradient-to-r", "from-red-600", "to-red-700", "hover:from-red-700", "hover:to-red-800");
            
            logMessage("[INFO] Requesting ephemeral token...");
            document.getElementById("status").textContent = "Requesting ephemeral token from server...";

            const tokenResp = await fetch("/session");
            const tokenData = await tokenResp.json();
            if (tokenData.error) {
                logMessage("[ERROR] " + JSON.stringify(tokenData, null, 2));
                document.getElementById("status").textContent = "Failed to get ephemeral key.";
                startButton.textContent = "Connect & Start Chat";
                return;
            }

            const EPHEMERAL_KEY = tokenData.client_secret.value;
            document.getElementById("status").textContent = "Ephemeral key acquired. Creating RTCPeerConnection...";

            pc = new RTCPeerConnection();

            pc.ontrack = (event) => {
                logMessage("[INFO] Received remote audio track from model.");
                const audioEl = document.createElement("audio");
                audioEl.autoplay = true;
                audioEl.srcObject = event.streams[0];
            };

            try {
                const mediaStream = await navigator.mediaDevices.getUserMedia({ audio: true });
                mediaStream.getTracks().forEach((track) => pc.addTrack(track, mediaStream));
                logMessage("[INFO] Local microphone track acquired and added.");
            } catch (err) {
                logMessage("[ERROR] Unable to acquire microphone: " + err);
                document.getElementById("status").textContent = "Could not access microphone.";
                startButton.textContent = "Connect & Start Chat";
                return;
            }

            dc = pc.createDataChannel("oai-events");
            dc.addEventListener("open", () => {
                logMessage("[INFO] Data channel opened.");
                document.getElementById("status").textContent = "Connected! You can chat now.";
                startButton.textContent = "Disconnect";
            });

            dc.addEventListener("message", (e) => {
                let serverEvent;
                try {
                    serverEvent = JSON.parse(e.data);
                } catch {
                    logMessage("[WARN] Received non-JSON data from server: " + e.data);
                    return;
                }

                // Handle translation responses
                if (serverEvent.type === "response.done" && 
                    serverEvent.response.metadata?.type === "translation") {
                    addTranslation(
                        serverEvent.response.metadata.language,
                        serverEvent.response.output[0].content[0].text
                    );
                    return;
                }

                // Monitor for new conversation items (both text and audio)
                if (serverEvent.type === "conversation.item.created" && 
                    serverEvent.item.role === "user") {
                    // Request translations for each selected language
                    requestTranslations(serverEvent.item.content[0].text);
                }

                // Log other events
                logMessage("[SERVER EVENT] " + JSON.stringify(serverEvent, null, 2));
            });

            pc.onicecandidate = async (evt) => {
                if (!evt.candidate && pc.iceGatheringState === "complete") {
                    document.getElementById("status").textContent = "Sending SDP offer to Realtime API...";
                    const offer = pc.localDescription;

                    const baseUrl = "https://api.openai.com/v1/realtime";
                    const model = "gpt-4o-realtime-preview-2024-12-17";

                    try {
                        const sdpResponse = await fetch(`${baseUrl}?model=${model}`, {
                            method: "POST",
                            headers: {
                                "Authorization": `Bearer ${EPHEMERAL_KEY}`,
                                "Content-Type": "application/sdp"
                            },
                            body: offer.sdp
                        });

                        if (!sdpResponse.ok) {
                            const errText = await sdpResponse.text();
                            logMessage("[ERROR] Realtime API error: " + errText);
                            document.getElementById("status").textContent = "Error from Realtime API (check console).";
                            startButton.textContent = "Connect & Start Chat";
                            return;
                        }

                        const answerSdp = await sdpResponse.text();
                        const answer = { type: "answer", sdp: answerSdp };
                        await pc.setRemoteDescription(answer);

                        document.getElementById("status").textContent = "Connected! You can chat now.";
                        startButton.textContent = "Disconnect";
                        logMessage("[INFO] WebRTC connection established.");
                    } catch (error) {
                        logMessage("[ERROR] " + error);
                        document.getElementById("status").textContent = "Error sending SDP offer.";
                        startButton.textContent = "Connect & Start Chat";
                    }
                }
            };

            const offer = await pc.createOffer();
            await pc.setLocalDescription(offer);
        }

        function sendTextMessage() {
            if (!dc || dc.readyState !== "open") {
                logMessage("[WARN] Data channel not open yet.");
                return;
            }

            const textVal = document.getElementById("text-input").value.trim();
            if (!textVal) return;

            // Add user message to conversation
            const userEvent = {
                type: "conversation.item.create",
                item: {
                    type: "message",
                    role: "user",
                    content: [
                        {
                            type: "input_text",
                            text: textVal
                        }
                    ]
                }
            };
            dc.send(JSON.stringify(userEvent));
            logMessage("[YOU] " + textVal);

            // Request normal response
            const responseEvent = {
                type: "response.create",
                response: {
                    modalities: ["text", "audio"]
                }
            };
            dc.send(JSON.stringify(responseEvent));

            // Clear input
            document.getElementById("text-input").value = "";
        }

        function requestTranslations(text) {
            const selects = document.querySelectorAll('.language-select');
            const selectedLanguages = Array.from(selects)
                .map(select => select.value)
                .filter(lang => lang); // Remove empty selections

            selectedLanguages.forEach(lang => {
                const translationEvent = {
                    type: "response.create",
                    response: {
                        conversation: "none",
                        metadata: { 
                            type: "translation",
                            language: lang
                        },
                        modalities: ["text"],
                        instructions: `Translate what the user just said into ${lang}. Only output the original user message and the translation, nothing else. you must not engage in conversation or respond normally. just provide the translation along with what the user said.`
                    }
                };
                dc.send(JSON.stringify(translationEvent));
            });
        }

        function addTranslation(language, translatedText) {
            const langName = languages.find(l => l.code === language)?.name || language;
            const div = document.createElement("div");
            div.className = "translation-badge flex flex-col p-2 glass-morphism rounded-lg text-sm";
            
            div.innerHTML = `
                <div class="flex justify-between items-center mb-1">
                    <span class="font-semibold text-violet-300 text-xs">${langName}</span>
                    <span class="text-xs text-gray-400">${new Date().toLocaleTimeString()}</span>
                </div>
                <p class="text-gray-200 text-sm">${translatedText}</p>
            `;
            
            translationsContainer.insertBefore(div, translationsContainer.firstChild);

            // Keep only the last 10 translations
            while (translationsContainer.children.length > 10) {
                translationsContainer.removeChild(translationsContainer.lastChild);
            }
        }

        function logMessage(message) {
            const logEl = document.getElementById("log");
            logEl.textContent += "\\n" + message;
            logEl.scrollTop = logEl.scrollHeight;
            console.log(message);
        }
    </script>
</body>
</html>
"""

@app.get("/", response_class=HTMLResponse)
async def index():
    """
    Serve the single-page HTML UI.
    """
    try:
        print(colored("[INFO] Serving HTML template", "green"))
        return HTML_TEMPLATE
    except Exception as e:
        print(colored(f"[ERROR] Failed to serve HTML template: {str(e)}", "red"))
        raise

@app.get("/session")
async def session():
    """
    Create an ephemeral Realtime key.
    Requires a standard API key (OPENAI_API_KEY) on the server side.
    """
    try:
        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key:
            print(colored("[ERROR] No OPENAI_API_KEY found in environment variables", "red"))
            return {"error": "No OPENAI_API_KEY found in environment variables."}

        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json",
        }
        data = {
            "model": "gpt-4o-realtime-preview-2024-12-17",
            "voice": "verse"
        }

        print(colored("[INFO] Requesting ephemeral session token", "cyan"))
        resp = requests.post(
            "https://api.openai.com/v1/realtime/sessions", 
            headers=headers, 
            json=data
        )
        
        if resp.status_code != 200:
            print(colored(f"[ERROR] Failed to create ephemeral session: {resp.text}", "red"))
            return {"error": f"Could not create ephemeral session: {resp.text}"}

        print(colored("[SUCCESS] Ephemeral session token created", "green"))
        return resp.json()
    except Exception as e:
        print(colored(f"[ERROR] Session creation failed: {str(e)}", "red"))
        return {"error": f"Session creation failed: {str(e)}"}

if __name__ == "__main__":
    print(colored("[INFO] Starting server...", "cyan"))
    uvicorn.run("multi_language_translator_with_oob:app", host="0.0.0.0", port=8000, reload=True)