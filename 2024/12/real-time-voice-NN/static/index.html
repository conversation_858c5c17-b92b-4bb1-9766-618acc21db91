<!DOCTYPE html>
<html lang="en" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Neural Voice Architect</title>
    <link href="https://cdn.jsdelivr.net/npm/daisyui@3.9.4/dist/full.css" rel="stylesheet" type="text/css" />
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/controls/OrbitControls.js"></script>
    <style>
        .network-container {
            position: relative;
            width: 100%;
            height: 65vh;
            background: radial-gradient(circle at center, rgba(15,23,42,0.8) 0%, rgba(0,0,0,0.95) 100%);
            border-radius: 1rem;
            overflow: hidden;
        }
        
        .glass-panel {
            background: rgba(15,23,42,0.6);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.1);
            border-radius: 1rem;
            padding: 0.75rem;
        }
        
        .neuron {
            position: absolute;
            width: 20px;
            height: 20px;
            background: rgba(147,51,234,0.8);
            border-radius: 50%;
            box-shadow: 0 0 20px rgba(147,51,234,0.4);
        }
        
        .connection {
            position: absolute;
            height: 2px;
            background: linear-gradient(90deg, rgba(147,51,234,0.5), rgba(59,130,246,0.5));
            transform-origin: left center;
        }
        
        .loading-neuron {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: rgba(147,51,234,0.2);
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.5); opacity: 0.5; }
            100% { transform: scale(1); opacity: 1; }
        }
        
        #commandLog {
            height: 28px;
            overflow-y: auto;
        }
        
        .card-body {
            padding: 1rem;
        }
        
        .grid {
            gap: 0.75rem;
        }
        
        .stat-box {
            padding: 0.5rem;
        }
        
        .card-title {
            margin-bottom: 0.5rem;
        }
    </style>
</head>
<body class="min-h-screen bg-base-300">
    <div class="container mx-auto p-4">
        <div class="card bg-base-100 shadow-xl">
            <div class="card-body">
                <h2 class="card-title text-primary">Neural Voice Architect</h2>
                
                <!-- Network Visualization -->
                <div class="network-container" id="network-container">
                    <!-- Three.js will render here -->
                </div>
                
                <!-- Control Panel -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                    <!-- Voice Controls -->
                    <div class="glass-panel">
                        <h3 class="text-lg font-bold mb-3 text-primary">Voice Controls</h3>
                        <div class="space-y-2">
                            <button id="startButton" class="btn btn-primary btn-sm w-full">
                                Start Voice Navigation
                            </button>
                            <button id="stopButton" class="btn btn-error btn-sm w-full" disabled>
                                Stop Voice Navigation
                            </button>
                        </div>
                        <div id="status" class="mt-2 text-sm">
                            <div class="flex items-center gap-2">
                                <div id="statusIndicator" class="w-3 h-3 rounded-full bg-error"></div>
                                <span id="statusText">Disconnected</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Network Stats -->
                    <div class="glass-panel">
                        <h3 class="text-lg font-bold mb-3 text-primary">Network Statistics</h3>
                        <div class="grid grid-cols-2 gap-2">
                            <div class="stat-box p-2 bg-base-200 rounded">
                                <div class="text-2xl font-bold" id="layerCount">0</div>
                                <div class="text-xs opacity-70">Layers</div>
                            </div>
                            <div class="stat-box p-2 bg-base-200 rounded">
                                <div class="text-2xl font-bold" id="neuronCount">0</div>
                                <div class="text-xs opacity-70">Neurons</div>
                            </div>
                            <div class="stat-box p-2 bg-base-200 rounded">
                                <div class="text-2xl font-bold" id="connectionCount">0</div>
                                <div class="text-xs opacity-70">Connections</div>
                            </div>
                            <div class="stat-box p-2 bg-base-200 rounded">
                                <div class="text-2xl font-bold" id="activationType">-</div>
                                <div class="text-xs opacity-70">Activation</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Command Log -->
                    <div class="glass-panel">
                        <h3 class="text-lg font-bold mb-3 text-primary">Command Log</h3>
                        <div id="commandLog" class="h-32 overflow-y-auto text-sm space-y-1">
                            <!-- Commands will be logged here -->
                        </div>
                    </div>
                </div>
                
                <!-- Loading Animation -->
                <div id="loadingAnimation" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div class="text-center">
                        <div class="loading-neuron mx-auto mb-4"></div>
                        <div class="text-lg">Processing Command...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="/static/js/network.js"></script>
    <script src="/static/js/voice.js"></script>
</body>
</html> 