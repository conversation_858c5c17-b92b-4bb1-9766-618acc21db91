// Network Visualization with Three.js
let scene, camera, renderer, controls;
let network = {
    layers: [],
    connections: []
};

// Constants for visualization
const NEURON_COLORS = {
    input: 0x9333EA,    // Purple
    hidden: 0x3B82F6,   // Blue
    output: 0x10B981    // Emerald
};

const LAYER_SPACING = 4;
const NEURON_SPACING = 2;
const NEURON_RADIUS = 0.3;
const GLOW_INTENSITY = 5;
const GLOW_SCALE = 2.0;

// Add simulation-related constants
const SIMULATION_SPEED = 1000;
const PARTICLE_SPEED = 800;
const ACTIVATION_FUNCTIONS = {
    relu: x => Math.max(0, x),
    sigmoid: x => 1 / (1 + Math.exp(-x)),
    tanh: x => Math.tanh(x)
};

// Add to network state
let isSimulating = false;
let simulationData = null;

// Initialize Three.js scene
function initNetwork() {
    const container = document.getElementById('network-container');
    
    // Scene setup
    scene = new THREE.Scene();
    scene.fog = new THREE.FogExp2(0x000000, 0.05);
    
    // Camera setup
    const width = container.clientWidth;
    const height = container.clientHeight;
    camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000);
    camera.position.z = 15;
    camera.position.y = 5;
    
    // Renderer setup
    renderer = new THREE.WebGLRenderer({ 
        antialias: true,
        alpha: true
    });
    renderer.setSize(width, height);
    renderer.setPixelRatio(window.devicePixelRatio);
    renderer.setClearColor(0x000000, 0);
    container.appendChild(renderer.domElement);
    
    // Controls
    controls = new THREE.OrbitControls(camera, renderer.domElement);
    controls.enableDamping = true;
    controls.dampingFactor = 0.05;
    controls.rotateSpeed = 0.5;
    controls.autoRotate = true;
    controls.autoRotateSpeed = 0.5;
    
    // Lighting
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
    scene.add(ambientLight);
    
    const pointLight1 = new THREE.PointLight(0x9333EA, 1);
    pointLight1.position.set(10, 10, 10);
    scene.add(pointLight1);
    
    const pointLight2 = new THREE.PointLight(0x3B82F6, 1);
    pointLight2.position.set(-10, -10, -10);
    scene.add(pointLight2);
    
    // Animation loop
    animate();
    
    // Handle window resize
    window.addEventListener('resize', onWindowResize, false);
}

// Create a neuron mesh with enhanced glow effect
function createNeuron(type = 'hidden') {
    const color = NEURON_COLORS[type];
    
    // Core sphere with emission
    const geometry = new THREE.SphereGeometry(NEURON_RADIUS, 32, 32);
    const material = new THREE.MeshPhysicalMaterial({
        color: color,
        metalness: 0.2,
        roughness: 0.3,
        clearcoat: 1.0,
        clearcoatRoughness: 0.1,
        emissive: new THREE.Color(color),
        emissiveIntensity: 0
    });
    const neuron = new THREE.Mesh(geometry, material);
    
    // Enhanced glow effect
    const glowGeometry = new THREE.SphereGeometry(NEURON_RADIUS * GLOW_SCALE, 32, 32);
    const glowMaterial = new THREE.ShaderMaterial({
        uniforms: {
            glowColor: { type: "c", value: new THREE.Color(color) },
            viewVector: { type: "v3", value: camera.position },
            activation: { type: "f", value: 0.0 }
        },
        vertexShader: `
            uniform vec3 viewVector;
            varying float intensity;
            void main() {
                vec3 vNormal = normalize(normalMatrix * normal);
                vec3 vNormel = normalize(normalMatrix * viewVector);
                intensity = pow(0.8 - dot(vNormal, vNormel), 2.0) * 1.5;
                gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
            }
        `,
        fragmentShader: `
            uniform vec3 glowColor;
            uniform float activation;
            varying float intensity;
            void main() {
                vec3 glow = glowColor * (intensity + activation * 2.0);
                gl_FragColor = vec4(glow, min(intensity + activation, 1.0));
            }
        `,
        side: THREE.BackSide,
        blending: THREE.AdditiveBlending,
        transparent: true
    });
    
    const glowMesh = new THREE.Mesh(glowGeometry, glowMaterial);
    neuron.add(glowMesh);
    
    // Add activation property
    neuron.userData.activation = 0;
    neuron.userData.type = type;
    
    return neuron;
}

// Create a connection with enhanced data flow visualization
function createConnection(startPos, endPos) {
    const direction = new THREE.Vector3().subVectors(endPos, startPos);
    const length = direction.length();
    
    // Enhanced connection appearance with better lighting
    const geometry = new THREE.CylinderGeometry(0.05, 0.05, length, 12, 1);
    const material = new THREE.MeshPhysicalMaterial({
        color: 0x3B82F6,
        metalness: 0.5,
        roughness: 0.2,
        opacity: 0.8,
        transparent: true,
        emissive: new THREE.Color(0x3B82F6),
        emissiveIntensity: 0.2,
        clearcoat: 1.0,
        clearcoatRoughness: 0.1
    });
    
    const connection = new THREE.Mesh(geometry, material);
    connection.position.copy(startPos);
    connection.position.lerp(endPos, 0.5);
    connection.lookAt(endPos);
    connection.rotateX(Math.PI / 2);
    
    // Add glow effect to connection
    const glowGeometry = new THREE.CylinderGeometry(0.08, 0.08, length, 12, 1);
    const glowMaterial = new THREE.ShaderMaterial({
        uniforms: {
            glowColor: { type: "c", value: new THREE.Color(0x3B82F6) },
            viewVector: { type: "v3", value: camera.position },
            glowIntensity: { type: "f", value: 0.0 }
        },
        vertexShader: `
            uniform vec3 viewVector;
            varying float intensity;
            void main() {
                vec3 vNormal = normalize(normalMatrix * normal);
                vec3 vNormel = normalize(normalMatrix * viewVector);
                intensity = pow(0.6 - dot(vNormal, vNormel), 2.0);
                gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
            }
        `,
        fragmentShader: `
            uniform vec3 glowColor;
            uniform float glowIntensity;
            varying float intensity;
            void main() {
                vec3 glow = glowColor * (intensity + glowIntensity);
                gl_FragColor = vec4(glow, min(intensity + glowIntensity * 0.5, 0.7));
            }
        `,
        side: THREE.FrontSide,
        blending: THREE.AdditiveBlending,
        transparent: true
    });

    const glowMesh = new THREE.Mesh(glowGeometry, glowMaterial);
    connection.add(glowMesh);
    
    // Enhanced data flow particles
    const particleGeometry = new THREE.SphereGeometry(0.1, 12, 12);
    const particleMaterial = new THREE.MeshPhysicalMaterial({
        color: 0x4CAF50,
        emissive: 0x4CAF50,
        emissiveIntensity: 1.5,
        transparent: true,
        opacity: 0,
        metalness: 0.5,
        roughness: 0.2,
        clearcoat: 1.0
    });
    
    const particle = new THREE.Mesh(particleGeometry, particleMaterial);
    connection.add(particle);
    connection.userData.particle = particle;
    connection.userData.glowMesh = glowMesh;
    connection.userData.startPos = startPos.clone();
    connection.userData.endPos = endPos.clone();
    connection.userData.weight = Math.random() * 2 - 1;
    
    return connection;
}

// Update network visualization
function updateNetwork(structure) {
    // Clear existing network
    network.layers.forEach(layer => {
        layer.forEach(neuron => scene.remove(neuron));
    });
    network.connections.forEach(connection => scene.remove(connection));
    network.layers = [];
    network.connections = [];
    
    // Create new network
    structure.forEach((layerSize, layerIndex) => {
        const layer = [];
        const layerX = (layerIndex - (structure.length - 1) / 2) * LAYER_SPACING;
        
        for (let i = 0; i < layerSize; i++) {
            const neuronY = (i - (layerSize - 1) / 2) * NEURON_SPACING;
            const type = layerIndex === 0 ? 'input' : 
                        layerIndex === structure.length - 1 ? 'output' : 'hidden';
            
            const neuron = createNeuron(type);
            neuron.position.set(layerX, neuronY, 0);
            scene.add(neuron);
            layer.push(neuron);
            
            // Animate neuron appearance
            neuron.scale.set(0, 0, 0);
            anime({
                targets: neuron.scale,
                x: 1,
                y: 1,
                z: 1,
                duration: 1000,
                delay: layerIndex * 200 + i * 100,
                easing: 'easeOutElastic(1, 0.8)'
            });
        }
        
        network.layers.push(layer);
        
        // Create connections to previous layer
        if (layerIndex > 0) {
            const prevLayer = network.layers[layerIndex - 1];
            layer.forEach(neuron => {
                prevLayer.forEach(prevNeuron => {
                    const connection = createConnection(
                        prevNeuron.position,
                        neuron.position
                    );
                    scene.add(connection);
                    network.connections.push(connection);
                    
                    // Animate connection appearance
                    connection.scale.y = 0;
                    anime({
                        targets: connection.scale,
                        y: 1,
                        duration: 800,
                        delay: layerIndex * 200,
                        easing: 'easeOutQuad'
                    });
                });
            });
        }
    });
    
    // Update stats
    document.getElementById('layerCount').textContent = structure.length;
    document.getElementById('neuronCount').textContent = structure.reduce((a, b) => a + b, 0);
    document.getElementById('connectionCount').textContent = network.connections.length;
}

// Animation loop
function animate() {
    requestAnimationFrame(animate);
    
    if (controls) controls.update();
    
    // Update glow effects and handle simulation animations
    network.layers.forEach(layer => {
        layer.forEach(neuron => {
            // Update view vector for glow effect
            if (neuron.children[0] && neuron.children[0].material.uniforms) {
                neuron.children[0].material.uniforms.viewVector.value = 
                    new THREE.Vector3().subVectors(camera.position, neuron.position);
            }
            
            // Update neuron material if it has activation
            if (neuron.userData.activation > 0) {
                neuron.material.emissive = new THREE.Color(NEURON_COLORS[neuron.userData.type] || 0x4CAF50);
                neuron.material.emissiveIntensity = neuron.userData.activation;
            }
        });
    });
    
    // Update connection effects
    network.connections.forEach(connection => {
        // Update view vector for connection glow
        if (connection.userData.glowMesh && connection.userData.glowMesh.material.uniforms) {
            connection.userData.glowMesh.material.uniforms.viewVector.value = 
                new THREE.Vector3().subVectors(camera.position, connection.position);
        }
        
        // Update particle fade
        if (connection.userData.particle) {
            connection.userData.particle.material.opacity *= 0.95;
        }
    });
    
    renderer.render(scene, camera);
}

// Handle window resize
function onWindowResize() {
    const container = document.getElementById('network-container');
    const width = container.clientWidth;
    const height = container.clientHeight;
    
    camera.aspect = width / height;
    camera.updateProjectionMatrix();
    renderer.setSize(width, height);
}

// Initialize on load
window.addEventListener('load', () => {
    initNetwork();
    
    // Create demo network
    const demoStructure = [4, 6, 6, 3];
    updateNetwork(demoStructure);
});

// Simulate network forward pass with completion callback
function simulateNetwork(inputData) {
    if (isSimulating) return;
    isSimulating = true;
    
    console.log('Starting simulation with input:', inputData);
    
    // Reset all activations with fade effect
    network.layers.forEach(layer => {
        layer.forEach(neuron => {
            anime({
                targets: [neuron.material, neuron.children[0].material.uniforms.activation],
                emissiveIntensity: 0,
                value: 0,
                duration: 500,
                easing: 'easeOutQuad'
            });
            neuron.userData.activation = 0;
        });
    });
    
    network.connections.forEach(connection => {
        if (connection.userData.particle) {
            connection.userData.particle.material.opacity = 0;
        }
        connection.material.emissiveIntensity = 0;
    });
    
    // Prepare simulation data
    simulationData = {
        currentLayer: 0,
        layerActivations: [inputData],
        activationFunction: ACTIVATION_FUNCTIONS[currentNetwork.activation || 'relu']
    };
    
    // Start simulation animation
    simulateLayer(() => {
        // Simulation complete callback
        isSimulating = false;
        
        // Highlight output layer with special effect
        const outputLayer = network.layers[network.layers.length - 1];
        outputLayer.forEach(neuron => {
            anime({
                targets: neuron.scale,
                x: [1, 1.2, 1],
                y: [1, 1.2, 1],
                z: [1, 1.2, 1],
                duration: 1000,
                easing: 'easeInOutElastic(1, .5)',
                complete: () => {
                    // Notify that simulation is complete
                    const event = new CustomEvent('simulation_complete', {
                        detail: {
                            outputValues: simulationData.layerActivations[simulationData.layerActivations.length - 1]
                        }
                    });
                    window.dispatchEvent(event);
                }
            });
        });
    });
}

// Simulate single layer with completion callback
function simulateLayer(onComplete) {
    if (!isSimulating || !simulationData || simulationData.currentLayer >= network.layers.length) {
        if (onComplete) onComplete();
        return;
    }
    
    const currentLayerIndex = simulationData.currentLayer;
    const currentLayer = network.layers[currentLayerIndex];
    const currentActivations = simulationData.layerActivations[currentLayerIndex];
    
    // Process layer
    if (currentLayerIndex === 0) {
        // Input layer
        currentLayer.forEach((neuron, i) => {
            const activation = currentActivations[i] || 0;
            animateNeuronActivation(neuron, activation);
        });
        
        simulationData.currentLayer++;
        setTimeout(() => simulateLayer(onComplete), SIMULATION_SPEED);
    } else {
        // Hidden and output layers
        const prevLayer = network.layers[currentLayerIndex - 1];
        const prevActivations = simulationData.layerActivations[currentLayerIndex - 1];
        const newActivations = [];
        let animationsComplete = 0;
        
        currentLayer.forEach((neuron, i) => {
            let sum = 0;
            prevLayer.forEach((prevNeuron, j) => {
                const connection = network.connections.find(c => 
                    c.userData.startPos.equals(prevNeuron.position) && 
                    c.userData.endPos.equals(neuron.position)
                );
                
                if (connection) {
                    const weight = connection.userData.weight;
                    const input = prevActivations[j];
                    sum += weight * input;
                    animateDataFlow(connection, input * weight);
                }
            });
            
            const activation = simulationData.activationFunction(sum);
            newActivations.push(activation);
            
            setTimeout(() => {
                animateNeuronActivation(neuron, activation);
                animationsComplete++;
                
                if (animationsComplete === currentLayer.length) {
                    simulationData.layerActivations.push(newActivations);
                    simulationData.currentLayer++;
                    setTimeout(() => simulateLayer(onComplete), SIMULATION_SPEED * 0.5);
                }
            }, SIMULATION_SPEED * 0.3);
        });
    }
}

// Animate neuron activation with enhanced effects
function animateNeuronActivation(neuron, activation) {
    const normalizedActivation = Math.min(Math.max(activation, 0), 1);
    const enhancedActivation = normalizedActivation * 1.5;
    
    // Animate core with enhanced glow
    anime({
        targets: neuron.material,
        emissiveIntensity: enhancedActivation,
        duration: SIMULATION_SPEED * 0.3,
        easing: 'easeOutQuad'
    });
    
    // Animate glow with pulsing effect
    anime({
        targets: neuron.children[0].material.uniforms.activation,
        value: enhancedActivation,
        duration: SIMULATION_SPEED * 0.3,
        easing: 'easeOutQuad',
        complete: () => {
            // Add subtle pulse effect
            anime({
                targets: neuron.children[0].scale,
                x: [1, 1.1, 1],
                y: [1, 1.1, 1],
                z: [1, 1.1, 1],
                duration: 1000,
                easing: 'easeInOutQuad',
                loop: true
            });
        }
    });
    
    neuron.userData.activation = activation;
}

// Animate data flow with enhanced effects
function animateDataFlow(connection, value) {
    const particle = connection.userData.particle;
    const glowMesh = connection.userData.glowMesh;
    const normalizedValue = Math.min(Math.max(Math.abs(value), 0), 1);
    const enhancedValue = normalizedValue * 2;
    
    // Reset and position particle
    particle.position.set(0, -connection.geometry.parameters.height/2, 0);
    
    // Animate particle with enhanced trail effect
    anime({
        targets: particle.position,
        y: connection.geometry.parameters.height/2,
        duration: PARTICLE_SPEED,
        easing: 'easeInQuad',
        begin: () => {
            particle.material.opacity = enhancedValue;
            particle.material.emissiveIntensity = enhancedValue * 2;
            
            // Enhanced connection glow
            connection.material.emissiveIntensity = enhancedValue * 0.5;
            connection.material.opacity = 0.9;
            
            // Animate glow mesh
            if (glowMesh && glowMesh.material.uniforms) {
                anime({
                    targets: glowMesh.material.uniforms.glowIntensity,
                    value: enhancedValue,
                    duration: PARTICLE_SPEED * 0.8,
                    easing: 'easeOutExpo'
                });
            }
        },
        update: (anim) => {
            // Create dynamic trailing glow effect
            const progress = anim.progress / 100;
            const trailIntensity = enhancedValue * (1 - progress);
            connection.material.emissiveIntensity = trailIntensity * 0.5;
            
            if (glowMesh && glowMesh.material.uniforms) {
                glowMesh.material.uniforms.glowIntensity.value = trailIntensity;
            }
            
            // Add subtle pulse to particle
            const pulseScale = 1 + Math.sin(progress * Math.PI) * 0.2;
            particle.scale.set(pulseScale, pulseScale, pulseScale);
        },
        complete: () => {
            particle.material.opacity = 0;
            particle.scale.set(1, 1, 1);
            
            // Smooth fade out of connection glow
            anime({
                targets: [
                    connection.material,
                    { emissiveIntensity: 0.2, opacity: 0.8 }
                ],
                duration: 500,
                easing: 'easeOutQuad'
            });
            
            if (glowMesh && glowMesh.material.uniforms) {
                anime({
                    targets: glowMesh.material.uniforms.glowIntensity,
                    value: 0,
                    duration: 500,
                    easing: 'easeOutQuad'
                });
            }
        }
    });
}

// Export simulation functions
window.networkSimulation = {
    simulate: simulateNetwork,
    isSimulating: () => isSimulating,
    getCurrentState: () => ({
        isSimulating,
        simulationData,
        networkState: network
    })
}; 