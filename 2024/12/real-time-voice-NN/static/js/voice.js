// Voice Control Implementation
let peerConnection = null;
let dataChannel = null;
let mediaStream = null;
let audioElement = null;

// Network state tracking
let currentNetwork = {
    layers: [4, 6, 6, 3],  // Default structure
    activation: 'relu',     // Default activation
    lastAction: null       // Track last modification
};

// Separate history management
const MAX_HISTORY = 10;
let networkHistory = [];
let historyIndex = -1;

// DOM Elements
const startButton = document.getElementById('startButton');
const stopButton = document.getElementById('stopButton');
const statusIndicator = document.getElementById('statusIndicator');
const statusText = document.getElementById('statusText');
const loadingAnimation = document.getElementById('loadingAnimation');
const commandLog = document.getElementById('commandLog');

// Initialize WebRTC connection
async function initializeWebRTC() {
    try {
        showLoading(true);
        updateStatus('Initializing...');

        // Get ephemeral token
        const tokenResponse = await fetch('/session');
        const data = await tokenResponse.json();
        
        if (!data.client_secret?.value) {
            throw new Error('Failed to get ephemeral token');
        }

        const EPHEMERAL_KEY = data.client_secret.value;

        // Create peer connection
        peerConnection = new RTCPeerConnection();

        // Set up audio element
        audioElement = document.createElement('audio');
        audioElement.autoplay = true;
        peerConnection.ontrack = e => audioElement.srcObject = e.streams[0];

        // Add local audio track
        mediaStream = await navigator.mediaDevices.getUserMedia({ audio: true });
        peerConnection.addTrack(mediaStream.getTracks()[0]);

        // Set up data channel
        dataChannel = peerConnection.createDataChannel('oai-events');
        dataChannel.addEventListener('message', handleDataChannelMessage);
        dataChannel.addEventListener('open', () => {
            updateStatus('Connected', true);
            stopButton.disabled = false;
            startButton.disabled = true;
            addCommand('Connection established');
            configureTools();
        });

        // Create and set local description
        const offer = await peerConnection.createOffer();
        await peerConnection.setLocalDescription(offer);

        // Connect to OpenAI Realtime API
        const baseUrl = 'https://api.openai.com/v1/realtime';
        const model = 'gpt-4o-realtime-preview-2024-12-17';
        const sdpResponse = await fetch(`${baseUrl}?model=${model}`, {
            method: 'POST',
            body: offer.sdp,
            headers: {
                Authorization: `Bearer ${EPHEMERAL_KEY}`,
                'Content-Type': 'application/sdp'
            },
        });

        if (!sdpResponse.ok) {
            throw new Error('Failed to connect to OpenAI Realtime API');
        }

        const answer = {
            type: 'answer',
            sdp: await sdpResponse.text(),
        };
        await peerConnection.setRemoteDescription(answer);

        showLoading(false);
    } catch (error) {
        console.error('Error:', error);
        showLoading(false);
        updateStatus('Error: ' + error.message);
        addCommand(error.message, 'error');
    }
}

// Configure available tools
function configureTools() {
    const event = {
        type: 'session.update',
        session: {
            modalities: ['text', 'audio'],
            tools: [
                {
                    type: 'function',
                    name: 'updateNetworkStructure',
                    description: 'Update or modify the neural network structure and visualization.',
                    parameters: {
                        type: 'object',
                        properties: {
                            action: {
                                type: 'string',
                                enum: ['create', 'modify', 'add_layer', 'remove_layer', 'change_activation', 'undo', 'redo'],
                                description: 'Type of network modification'
                            },
                            layers: {
                                type: 'array',
                                items: { type: 'integer' },
                                description: 'Array of layer sizes for new network or modified layers'
                            },
                            layer_index: {
                                type: 'integer',
                                description: 'Index of layer to modify (for add/remove operations)'
                            },
                            neurons: {
                                type: 'integer',
                                description: 'Number of neurons for layer modification'
                            },
                            activation: {
                                type: 'string',
                                enum: ['relu', 'sigmoid', 'tanh'],
                                description: 'Activation function'
                            }
                        },
                        required: ['action']
                    }
                },
                {
                    type: 'function',
                    name: 'simulateNetwork',
                    description: 'Simulate data flow through the neural network with given input values.',
                    parameters: {
                        type: 'object',
                        properties: {
                            input_values: {
                                type: 'array',
                                items: { type: 'number' },
                                description: 'Input values for the network simulation'
                            },
                            description: {
                                type: 'string',
                                description: 'Description of what this simulation represents'
                            }
                        },
                        required: ['input_values']
                    }
                },
                {
                    type: 'function',
                    name: 'getNetworkInfo',
                    description: 'Get current network architecture information',
                    parameters: {
                        type: 'object',
                        properties: {}
                    }
                }
            ]
        }
    };
    dataChannel.send(JSON.stringify(event));
}

// Handle incoming messages
function handleDataChannelMessage(event) {
    try {
        const msg = JSON.parse(event.data);
        
        // Handle function calls
        if (msg.type === 'response.function_call_arguments.done') {
            const args = JSON.parse(msg.arguments);
            args.call_id = msg.call_id; // Pass call_id to handlers
            
            if (msg.name === 'updateNetworkStructure') {
                handleNetworkUpdate(args);
                
                const responseEvent = {
                    type: 'conversation.item.create',
                    item: {
                        type: 'function_call_output',
                        call_id: msg.call_id,
                        output: JSON.stringify({ 
                            success: true,
                            current_structure: {
                                layers: currentNetwork.layers,
                                activation: currentNetwork.activation,
                                lastAction: currentNetwork.lastAction
                            }
                        })
                    }
                };
                dataChannel.send(JSON.stringify(responseEvent));
            }
            else if (msg.name === 'simulateNetwork') {
                handleSimulation(args);
            }
            else if (msg.name === 'getNetworkInfo') {
                const state = window.networkSimulation ? window.networkSimulation.getCurrentState() : null;
                const responseEvent = {
                    type: 'conversation.item.create',
                    item: {
                        type: 'function_call_output',
                        call_id: msg.call_id,
                        output: JSON.stringify({
                            current_structure: currentNetwork.layers,
                            activation: currentNetwork.activation,
                            total_neurons: currentNetwork.layers.reduce((a, b) => a + b, 0),
                            total_layers: currentNetwork.layers.length,
                            last_action: currentNetwork.lastAction,
                            can_undo: historyIndex > 0,
                            can_redo: historyIndex < networkHistory.length - 1,
                            is_simulating: state ? state.isSimulating : false,
                            simulation_progress: state && state.simulationData ? 
                                state.simulationData.currentLayer / currentNetwork.layers.length : 0
                        })
                    }
                };
                dataChannel.send(JSON.stringify(responseEvent));
            }
        }
        
        // Log all messages for debugging
        console.log('Received:', msg);
    } catch (error) {
        console.error('Error parsing message:', error);
        addCommand(`Error parsing message: ${error.message}`, 'error');
    }
}

// Handle network updates
function handleNetworkUpdate(args) {
    try {
        // Save current state to history (deep copy)
        saveToHistory();
        
        switch (args.action) {
            case 'create':
                if (args.layers) {
                    currentNetwork.layers = [...args.layers];
                    currentNetwork.activation = args.activation || 'relu';
                    addCommand(`Created new network: ${args.layers.join('-')}`);
                }
                break;
                
            case 'modify':
                if (args.layers) {
                    currentNetwork.layers = [...args.layers];
                    addCommand(`Modified network structure: ${args.layers.join('-')}`);
                }
                break;
                
            case 'add_layer':
                if (typeof args.layer_index === 'number' && args.neurons) {
                    const newLayers = [...currentNetwork.layers];
                    newLayers.splice(args.layer_index, 0, args.neurons);
                    currentNetwork.layers = newLayers;
                    addCommand(`Added layer with ${args.neurons} neurons at position ${args.layer_index}`);
                }
                break;
                
            case 'remove_layer':
                if (typeof args.layer_index === 'number') {
                    if (currentNetwork.layers.length > 2) {  // Maintain at least input and output
                        const newLayers = [...currentNetwork.layers];
                        newLayers.splice(args.layer_index, 1);
                        currentNetwork.layers = newLayers;
                        addCommand(`Removed layer at position ${args.layer_index}`);
                    } else {
                        addCommand('Cannot remove layer: minimum network size reached', 'error');
                        return;
                    }
                }
                break;
                
            case 'change_activation':
                if (args.activation) {
                    currentNetwork.activation = args.activation;
                    addCommand(`Changed activation function to ${args.activation}`);
                }
                break;
                
            case 'undo':
                if (undoNetworkChange()) {
                    addCommand('Undid last change');
                }
                break;
                
            case 'redo':
                if (redoNetworkChange()) {
                    addCommand('Redid last change');
                }
                break;
        }
        
        // Update visualization
        updateNetwork(currentNetwork.layers);
        document.getElementById('activationType').textContent = currentNetwork.activation;
        
        // Store last action
        currentNetwork.lastAction = args.action;
        
    } catch (error) {
        console.error('Error updating network:', error);
        addCommand(`Error updating network: ${error.message}`, 'error');
    }
}

// Save current state to history
function saveToHistory() {
    // Create deep copy of current state
    const stateCopy = {
        layers: [...currentNetwork.layers],
        activation: currentNetwork.activation,
        lastAction: currentNetwork.lastAction
    };
    
    // Remove any redo states if we're not at the end of history
    if (historyIndex < networkHistory.length - 1) {
        networkHistory = networkHistory.slice(0, historyIndex + 1);
    }
    
    // Add new state to history
    networkHistory.push(stateCopy);
    
    // Maintain history size limit
    if (networkHistory.length > MAX_HISTORY) {
        networkHistory.shift();
    }
    
    historyIndex = networkHistory.length - 1;
}

// Undo last change
function undoNetworkChange() {
    if (historyIndex > 0) {
        historyIndex--;
        const previousState = networkHistory[historyIndex];
        currentNetwork.layers = [...previousState.layers];
        currentNetwork.activation = previousState.activation;
        currentNetwork.lastAction = previousState.lastAction;
        return true;
    }
    addCommand('No more changes to undo', 'error');
    return false;
}

// Redo last undone change
function redoNetworkChange() {
    if (historyIndex < networkHistory.length - 1) {
        historyIndex++;
        const nextState = networkHistory[historyIndex];
        currentNetwork.layers = [...nextState.layers];
        currentNetwork.activation = nextState.activation;
        currentNetwork.lastAction = nextState.lastAction;
        return true;
    }
    addCommand('No more changes to redo', 'error');
    return false;
}

// Cleanup resources
function cleanup() {
    try {
        // Stop all media tracks
        if (mediaStream) {
            mediaStream.getTracks().forEach(track => track.stop());
            mediaStream = null;
        }

        // Clean up audio element
        if (audioElement) {
            audioElement.srcObject = null;
            audioElement.remove();
            audioElement = null;
        }

        // Close data channel
        if (dataChannel) {
            dataChannel.close();
            dataChannel = null;
        }

        // Close peer connection
        if (peerConnection) {
            peerConnection.close();
            peerConnection = null;
        }

        updateStatus('Disconnected');
        startButton.disabled = false;
        stopButton.disabled = true;
        addCommand('Connection closed');
    } catch (error) {
        console.error('Error during cleanup:', error);
        addCommand(`Error during cleanup: ${error.message}`, 'error');
    }
}

// Helper functions
function updateStatus(status, isConnected = false) {
    statusText.textContent = status;
    statusIndicator.className = `w-3 h-3 rounded-full ${isConnected ? 'bg-success' : 'bg-error'}`;
}

function addCommand(text, type = 'info') {
    const commandDiv = document.createElement('div');
    
    // Enhanced styling based on message type
    let className = 'p-2 rounded text-sm ';
    switch (type) {
        case 'error':
            className += 'bg-error/20 text-error border-l-4 border-error';
            break;
        case 'success':
            className += 'bg-success/20 text-success border-l-4 border-success';
            break;
        case 'info':
        default:
            className += 'bg-primary/20 text-primary border-l-4 border-primary';
    }
    
    commandDiv.className = className;
    commandDiv.textContent = text;
    
    // Enhanced animation
    commandDiv.style.opacity = '0';
    commandDiv.style.transform = 'translateX(-20px)';
    commandLog.appendChild(commandDiv);
    
    anime({
        targets: commandDiv,
        opacity: [0, 1],
        translateX: [-20, 0],
        duration: 400,
        easing: 'easeOutQuad'
    });
    
    // Ensure latest messages are visible
    commandLog.scrollTop = commandLog.scrollHeight;
    
    // Add subtle highlight effect for important messages
    if (type === 'success' || type === 'error') {
        anime({
            targets: commandDiv,
            backgroundColor: [
                { value: type === 'success' ? 'rgba(0,255,0,0.2)' : 'rgba(255,0,0,0.2)', duration: 0 },
                { value: type === 'success' ? 'rgba(0,255,0,0.1)' : 'rgba(255,0,0,0.1)', duration: 1000 }
            ],
            easing: 'easeOutQuad'
        });
    }
}

function showLoading(show) {
    loadingAnimation.className = show ? 
        'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50' : 
        'hidden';
    startButton.disabled = show;
}

// Event listeners
startButton.addEventListener('click', initializeWebRTC);
stopButton.addEventListener('click', cleanup);

// Initial setup
updateStatus('Disconnected');
addCommand('Ready to start voice navigation');

// Example voice commands to show in the log
setTimeout(() => {
    addCommand('Try saying: "Create a network with 3 input neurons"');
    addCommand('Or: "Add a hidden layer with 8 neurons after the first layer"');
    addCommand('Or: "Remove the second hidden layer"');
    addCommand('Or: "Change to sigmoid activation"');
    addCommand('Or: "Simulate with inputs 0.5, 0.8, 0.2"');
    addCommand('Or: "Show me how the network processes [1, 0, 1]"');
}, 1000); 

// Handle simulation messages
function handleSimulation(args) {
    try {
        if (!window.networkSimulation) {
            throw new Error('Network simulation not initialized');
        }

        if (window.networkSimulation.isSimulating()) {
            addCommand('Network is already simulating, please wait...', 'error');
            return false;
        }

        // Validate input values
        if (!args.input_values || !Array.isArray(args.input_values)) {
            throw new Error('Invalid input values');
        }

        if (args.input_values.length !== currentNetwork.layers[0]) {
            addCommand(`Error: Expected ${currentNetwork.layers[0]} input values, got ${args.input_values.length}`, 'error');
            return false;
        }

        // Add simulation completion listener
        window.addEventListener('simulation_complete', function onSimComplete(event) {
            window.removeEventListener('simulation_complete', onSimComplete);
            
            const outputValues = event.detail.outputValues;
            addCommand('Simulation complete!', 'success');
            addCommand(`Final outputs: [${outputValues.map(v => v.toFixed(3)).join(', ')}]`, 'success');
            
            // Send completion event in the format expected by the model
            const completionEvent = {
                type: 'conversation.item.create',
                item: {
                    type: 'function_call_output',
                    call_id: args.call_id, // Important: Use the same call_id from the original request
                    output: JSON.stringify({
                        success: true,
                        input_values: args.input_values,
                        output_values: outputValues.map(v => parseFloat(v.toFixed(3))),
                        activation_function: currentNetwork.activation,
                        network_structure: currentNetwork.layers,
                        message: 'Simulation completed successfully'
                    })
                }
            };
            
            if (dataChannel) {
                console.log('Sending simulation results:', completionEvent);
                dataChannel.send(JSON.stringify(completionEvent));
            }
        });

        // Start simulation
        window.networkSimulation.simulate(args.input_values);
        
        // Log simulation start with enhanced styling
        const inputStr = args.input_values.map(v => v.toFixed(3)).join(', ');
        addCommand(`Starting simulation with inputs: [${inputStr}]`, 'info');
        if (args.description) {
            addCommand(`Purpose: ${args.description}`, 'info');
        }
        addCommand(`Using ${currentNetwork.activation} activation function`, 'info');

        // Send initial acknowledgment
        const responseEvent = {
            type: 'conversation.item.create',
            item: {
                type: 'function_call_output',
                call_id: args.call_id,
                output: JSON.stringify({ 
                    success: true,
                    message: 'Simulation started',
                    status: 'running'
                })
            }
        };
        if (dataChannel) {
            dataChannel.send(JSON.stringify(responseEvent));
        }

        return true;
    } catch (error) {
        console.error('Simulation error:', error);
        addCommand(`Simulation error: ${error.message}`, 'error');
        
        // Send error to model
        const errorEvent = {
            type: 'conversation.item.create',
            item: {
                type: 'function_call_output',
                call_id: args.call_id,
                output: JSON.stringify({ 
                    success: false,
                    error: error.message
                })
            }
        };
        if (dataChannel) {
            dataChannel.send(JSON.stringify(errorEvent));
        }
        
        return false;
    }
} 