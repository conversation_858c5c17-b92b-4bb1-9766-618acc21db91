from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jin<PERSON>2<PERSON>emplates
import os
import httpx
from termcolor import colored
import traceback

# Constants
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
if not OPENAI_API_KEY:
    print(colored("Error: OPENAI_API_KEY environment variable is not set", "red"))
    exit(1)

MODEL = "gpt-4o-realtime-preview-2024-12-17"
API_BASE = "https://api.openai.com/v1"

# Initialize FastAPI
app = FastAPI(title="Neural Voice Architect")

try:
    # Mount static files
    app.mount("/static", StaticFiles(directory="static"), name="static")
    templates = Jinja2Templates(directory="static")
    print(colored("✓ Static files mounted successfully", "green"))
except Exception as e:
    print(colored(f"Error mounting static files: {str(e)}", "red"))
    exit(1)

@app.get("/")
async def read_root(request: Request):
    """Serve the main application page."""
    try:
        print(colored("→ Serving index page", "cyan"))
        return templates.TemplateResponse("index.html", {"request": request})
    except Exception as e:
        print(colored(f"Error serving index page: {str(e)}", "red"))
        print(colored(traceback.format_exc(), "red"))
        raise HTTPException(status_code=500, detail="Internal server error")

@app.get("/session")
async def create_session():
    """Create a new WebRTC session with OpenAI."""
    try:
        print(colored("→ Creating new WebRTC session", "cyan"))
        
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{API_BASE}/realtime/sessions",
                headers={
                    "Authorization": f"Bearer {OPENAI_API_KEY}",
                    "Content-Type": "application/json"
                },
                json={
                    "model": MODEL,
                    "voice": "verse"
                },
                timeout=10.0
            )
            
            if response.status_code != 200:
                error_msg = f"OpenAI API error: {response.text}"
                print(colored(error_msg, "red"))
                raise HTTPException(status_code=response.status_code, detail=error_msg)
            
            print(colored("✓ Session created successfully", "green"))
            return response.json()
            
    except httpx.TimeoutException:
        error_msg = "Timeout while connecting to OpenAI API"
        print(colored(error_msg, "red"))
        raise HTTPException(status_code=504, detail=error_msg)
        
    except httpx.RequestError as e:
        error_msg = f"Error connecting to OpenAI API: {str(e)}"
        print(colored(error_msg, "red"))
        raise HTTPException(status_code=502, detail=error_msg)
        
    except Exception as e:
        print(colored(f"Unexpected error: {str(e)}", "red"))
        print(colored(traceback.format_exc(), "red"))
        raise HTTPException(status_code=500, detail="Internal server error")

@app.on_event("startup")
async def startup_event():
    """Run startup tasks."""
    print(colored("\n=== Neural Voice Architect ===", "magenta"))
    print(colored("Starting server...", "yellow"))
    
    # Verify API key format
    if not OPENAI_API_KEY.startswith("sk-"):
        print(colored("Warning: OPENAI_API_KEY format looks incorrect", "yellow"))

@app.on_event("shutdown")
async def shutdown_event():
    """Run cleanup tasks."""
    print(colored("\nShutting down server...", "yellow"))
    print(colored("Goodbye! 👋\n", "magenta"))

if __name__ == "__main__":
    import uvicorn
    
    print(colored("\nStarting development server...", "cyan"))
    print(colored("Press Ctrl+C to exit\n", "yellow"))
    
    uvicorn.run(
        "main:app",
        host="127.0.0.1",
        port=8000,
        reload=True,
        reload_excludes=["*.pyc", "*.log"],
        log_level="info"
    )