# Neural Voice Architect

An interactive 3D neural network visualization application that allows you to explore and modify neural network architectures using voice commands. The application combines real-time voice processing with beautiful 3D visualizations and network simulations.

## Features

- **Voice Navigation**: Control and modify neural networks through voice commands
- **3D Network Visualization**: Beautiful interactive neural network display with:
  - Dynamic network structure visualization
  - Glowing neuron effects
  - Connection animations
  - Smooth transitions
  - Auto-rotation
  - Interactive controls
  - Real-time simulation visualization

- **Network Operations**: Comprehensive network manipulation:
  - Create new networks
  - Add/remove layers
  - Modify layer sizes
  - Change activation functions
  - Undo/redo changes
  - Simulate network behavior

- **Real-time Updates**: All changes are displayed instantly as you explore
- **Responsive Design**: Works on all screen sizes
- **Dark Mode Interface**: Easy on the eyes with glass-morphism effects

## Technical Overview

### Components

1. **FastAPI Backend**
   - Handles API requests
   - Manages WebRTC connections
   - Routes static files

2. **WebRTC Integration**
   - Real-time voice communication
   - Data channel for commands and responses
   - Ephemeral token management
   - Automatic reconnection handling

3. **3D Visualization**
   - Built with Three.js
   - Custom shaders for glow effects
   - Dynamic network rendering
   - Interactive camera controls
   - Smooth animations

4. **Network Simulation**
   - Real-time forward propagation
   - Multiple activation functions (<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>h)
   - Visual feedback of data flow
   - Animated neuron activations

### Voice Navigation Flow

1. User clicks "Start Voice Navigation"
2. WebRTC connection is established
3. Voice input is processed in real-time
4. Commands are interpreted and executed
5. Network is updated with animations
6. Simulation results are displayed

## Setup and Requirements

### Prerequisites
- Python 3.7 or higher
- Modern web browser with WebRTC support
- Microphone access

### Installation

1. Install Python dependencies:
```bash
pip install -r requirements.txt
```

2. Set up environment variables:
```bash
OPENAI_API_KEY=your_api_key_here
```

3. Run the application:
```bash
python main.py
```

4. Open your browser and navigate to:
```
http://127.0.0.1:8000
```

## Usage Guide

1. **Starting Navigation**
   - Click "Start Voice Navigation"
   - Grant microphone permissions when prompted
   - Wait for connection confirmation

2. **Voice Commands**
   - "Create a network with [X] input neurons"
   - "Add a hidden layer with [X] neurons"
   - "Remove the second hidden layer"
   - "Change to sigmoid activation"
   - "Simulate with inputs [X, Y, Z]"
   - "Show me how the network processes [1, 0, 1]"

3. **Visualization Controls**
   - Rotate: Click and drag
   - Zoom: Mouse wheel
   - Pan: Right click and drag
   - Auto-rotation: Enabled by default

4. **Interface Elements**
   - Left side: Network visualization
   - Right side: Control panel
   - Status indicators
   - Command log

## Technical Details

### API Endpoints

- `/`: Serves the main application
- `/session`: Creates WebRTC sessions

### Data Flow

1. Voice Input → WebRTC → OpenAI Processing
2. Command Recognition → Function Calls
3. Network Updates → 3D Rendering
4. Simulation → Visual Feedback

### Technologies Used

- FastAPI for backend
- WebRTC for real-time communication
- Three.js for 3D visualization
- DaisyUI for UI components
- Tailwind CSS for styling
- Anime.js for animations
- Custom shaders for effects

## Performance Considerations

- Automatic cleanup of WebRTC resources
- Efficient 3D rendering with Three.js
- Smooth animations with requestAnimationFrame
- Responsive network updates
- Memory management for audio streams
- Optimized geometry handling

## Error Handling

- Connection recovery
- Input validation
- Clear error messages
- Resource cleanup
- Graceful fallbacks

The application combines cutting-edge web technologies to create an immersive, educational experience for exploring neural network architectures through voice commands while providing real-time visualization of network behavior. 