a beautiful webapp that displays the process elegantly

step 1:
we will use this api call to generate n many 100 token max responses(parallel) to a given user query(variable)
from openai import OpenAI
client = OpenAI()

completion = client.chat.completions.create(
    model="gpt-4o",
    messages=[
        {"role": "developer", "content": "You are a helpful assistant."},
        {
            "role": "user",
            "content": "Write a haiku about recursion in programming."
        }
    ]
)

print(completion.choices[0].message)

step 2:
we will use this api call to decide which one is the most promising response for the query
This api call doesnt accept system messages. use "user" role for all messages.

from openai import OpenAI
client = OpenAI()

prompt = """
Write a bash script that takes a matrix represented as a string with 
format '[1,2],[3,4],[5,6]' and prints the transpose in the same format.
"""

response = client.chat.completions.create(
    model="o1-mini",
    messages=[
        {
            "role": "user", 
            "content": prompt
        }
    ]
)

print(response.choices[0].message.content)

step 3:
then we will use the gpt-4o to rewrite the top choice and continue  and generate n many new responses. this time 200 token because we are adding 100 tokens to the max tokens for each round
then we get the top choice and continue untill the full response has been generated. instruct gpt-4o to return <RESPONSE DONE> when the full response has been generated(this instsruction should be present in all system messages for gpt-4o since we dont know when the full response will be generated)
these steps will continue untill the response is complete. we add 100 extra tokens untill the response is complete.
save the final response to file

UPDATE:
- Make sure we have beautiful animations while we wait, hover and click states
- add user selection for n many responses. max 20
- update the ui to display all the responses and then the best oen then to continue from the best in a aesthetically pleasing minimal way.