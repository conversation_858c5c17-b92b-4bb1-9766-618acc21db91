# Meditating LLMs

A unique approach to text generation that uses multiple GPT-4o instances working in parallel, with o1-mini acting as a "meditation guide" to select the most promising path forward. This creates a collaborative system where multiple instances explore different possibilities while another reflects on the best direction.

## 🧠 How It Works

### Core Concept
The system generates text through a unique "meditation" process:

1. **Parallel Generation (GPT-4o)**
   - Multiple GPT-4o instances generate different possibilities simultaneously
   - Each instance works with a limited token window (starting at 100 tokens)
   - The parallel approach ensures diverse thought paths are explored

2. **Meditation Selection (o1-mini)**
   - o1-mini acts as a meditation guide
   - Reviews all parallel responses
   - Selects the most promising path to continue from
   - Can operate in two modes:
     - Full Response Mode: Judges the entire text for coherence
     - Continuation Mode: Focuses only on the new additions

3. **Iterative Growth**
   - Starts with 100 tokens per response
   - Each round adds 100 more tokens to the limit
   - Continues until a complete response is generated
   - Each iteration builds upon the previously selected best path

### Technical Architecture

#### Backend (FastAPI)
- Asynchronous processing with `asyncio`
- Server-Sent Events (SSE) for real-time updates
- Parallel API calls to OpenAI
- Queue-based update system
- Configurable number of parallel responses (1-20)

#### Frontend
- Real-time response visualization
- Beautiful dark mode UI with DaisyUI
- Animated transitions and updates
- Progress tracking
- Token limit indicators
- Selected response highlighting

## 🚀 Features

- **Parallel Processing**: Generate multiple responses simultaneously
- **Meditation Modes**: Choose between full response or continuation-only judgment
- **Real-time Updates**: See responses as they're generated
- **Interactive UI**: Beautiful dark mode interface with animations
- **Configurable**: Adjust number of parallel responses
- **Visual Feedback**: Track progress and see token limits
- **Responsive Design**: Works on all screen sizes

## 💻 Technical Requirements

```
openai
fastapi
uvicorn
python-multipart
termcolor
aiofiles
sse-starlette
```

## 🛠️ Setup

1. Set your OpenAI API key:
   ```bash
   set OPENAI_API_KEY=your-api-key-here
   ```

2. Install requirements:
   ```bash
   pip install -r requirements.txt
   ```

3. Run the server:
   ```bash
   python main.py
   ```

4. Open in browser:
   ```
   http://127.0.0.1:8000
   ```

## 🎯 Usage

1. **Set Parallel Responses**: Choose how many parallel paths to explore (1-20)
2. **Choose Meditation Mode**:
   - Full Response: o1-mini judges entire text coherence
   - Continuations Only: o1-mini focuses on new additions
3. **Enter Query**: Type your prompt or use the default
4. **Watch the Process**:
   - See parallel responses generate
   - Watch o1-mini select the best path
   - Observe the text grow and evolve
   - View the final complete response

## 🔄 Process Flow

1. **Initial Generation**
   - Multiple GPT-4o instances generate initial 100-token responses
   - o1-mini selects the most promising start

2. **Continuation Phase**
   - New GPT-4o instances generate continuations
   - Token limit increases by 100 each round
   - o1-mini selects best continuation
   - Process repeats until completion

3. **Completion**
   - Final response is displayed
   - Full generation history is preserved
   - Response is saved to file

## 🎨 UI Elements

- **Meditation Circle**: Visual indicator of processing
- **Progress Bar**: Shows completion percentage
- **Response Cards**: Display parallel generations
- **Token Badges**: Show current token limits
- **Selection Indicators**: Highlight chosen paths
- **Status Messages**: Provide process updates

## 🤝 Model Collaboration

The system demonstrates a unique form of model collaboration:
- GPT-4o instances act as "thought generators"
- o1-mini serves as a "meditation guide"
- Together they create a balanced system of exploration and selection

## 📝 Notes

- The system is designed for thoughtful, iterative text generation
- Each round builds upon previous selections
- The parallel approach helps avoid local optima
- The meditation process ensures coherent progression
- Real-time visualization helps understand the generation process 