from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel, Field
import os
from termcolor import colored
import asyncio
from openai import AsyncOpenA<PERSON>
from typing import List, Dict, AsyncGenerator
import json
from sse_starlette.sse import EventSourceResponse

# Constants
MODELS = {
    "generator": "gpt-4o",
    "selector": "o1-mini",
}
MAX_INITIAL_TOKENS = 100
TOKEN_INCREMENT = 100
DEFAULT_NUM_RESPONSES = 3
MAX_NUM_RESPONSES = 20
API_KEY = os.getenv("OPENAI_API_KEY")

class Query(BaseModel):
    query: str
    num_responses: int = Field(default=DEFAULT_NUM_RESPONSES, ge=1, le=MAX_NUM_RESPONSES)

class ProgressResponse(BaseModel):
    response: str
    status: str
    progress: int
    all_responses: List[str] = []
    selected_index: int = -1
    current_token_limit: int = MAX_INITIAL_TOKENS

# Initialize FastAPI app
app = FastAPI(title="Meditating LLMs")

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize AsyncOpenAI client
try:
    client = AsyncOpenAI(api_key=API_KEY)
    print(colored("OpenAI client initialized successfully", "green"))
except Exception as e:
    print(colored(f"Error initializing OpenAI client: {str(e)}", "red"))
    raise

async def generate_initial_responses(query: str, num_responses: int) -> List[str]:
    """Generate multiple initial responses in parallel"""
    try:
        print(colored(f"Generating {num_responses} initial responses...", "cyan"))
        tasks = []
        for _ in range(num_responses):
            tasks.append(
                client.chat.completions.create(
                    model=MODELS["generator"],
                    messages=[
                        {"role": "system", "content": "You are a helpful assistant. Return <RESPONSE DONE> when the full response has been generated."},
                        {"role": "user", "content": query}
                    ],
                    max_tokens=MAX_INITIAL_TOKENS
                )
            )
        responses = await asyncio.gather(*tasks)
        return [resp.choices[0].message.content for resp in responses]
    except Exception as e:
        print(colored(f"Error generating initial responses: {str(e)}", "red"))
        raise HTTPException(status_code=500, detail=str(e))

async def select_best_response(responses: List[str], query: str, meditate_full: bool = True) -> tuple[str, int]:
    """Select the most promising response using o1-mini"""
    try:
        print(colored("Selecting best response...", "cyan"))
        
        if meditate_full:
            selection_prompt = f"""Given the original query: '{query}'

I have multiple complete responses/continuations. Each one represents the FULL text so far.
Please analyze them for coherence, story progression, and adherence to the query.
Choose the most promising one that best continues the story.

Responses:
"""
        else:
            selection_prompt = f"""Given the original query: '{query}'

I have multiple continuations. Each one represents ONLY THE NEW CONTINUATION part.
Please analyze them for how well they continue from the previous text.
Choose the most promising continuation that best advances the story.

Continuations:
"""
        
        for i, resp in enumerate(responses, 1):
            # If not meditating on full response, only show the new part
            if not meditate_full:
                resp = resp.split(" ")[-50:]  # Take last ~50 words as the continuation
                resp = " ".join(resp)
            selection_prompt += f"\n{i}. {resp}\n"
        
        selection_prompt += "\nWhich response number is the best? Just return the number."

        response = await client.chat.completions.create(
            model=MODELS["selector"],
            messages=[{"role": "user", "content": selection_prompt}]
        )
        selected_index = int(response.choices[0].message.content.strip()) - 1
        return responses[selected_index], selected_index
    except Exception as e:
        print(colored(f"Error selecting best response: {str(e)}", "red"))
        raise HTTPException(status_code=500, detail=str(e))

async def generate_continuations(current_response: str, query: str, max_tokens: int, num_responses: int) -> List[str]:
    """Generate multiple continuation responses in parallel"""
    try:
        tasks = []
        for _ in range(num_responses):
            tasks.append(
                client.chat.completions.create(
                    model=MODELS["generator"],
                    messages=[
                        {"role": "system", "content": "You are a helpful assistant. Continue the previous response. Return <RESPONSE DONE> when the full response has been generated."},
                        {"role": "user", "content": query},
                        {"role": "assistant", "content": current_response},
                        {"role": "user", "content": "Please continue..."}
                    ],
                    max_tokens=max_tokens
                )
            )
        responses = await asyncio.gather(*tasks)
        return [current_response + " " + resp.choices[0].message.content for resp in responses]
    except Exception as e:
        print(colored(f"Error generating continuations: {str(e)}", "red"))
        raise HTTPException(status_code=500, detail=str(e))

async def continue_response(initial_response: str, query: str, num_responses: int, queue: asyncio.Queue) -> str:
    """Continue and expand the selected response with increasing token limits"""
    try:
        print(colored("Starting response continuation process...", "cyan"))
        current_response = initial_response
        current_max_tokens = MAX_INITIAL_TOKENS + TOKEN_INCREMENT
        iteration = 1
        
        while "<RESPONSE DONE>" not in current_response:
            print(colored(f"Iteration {iteration}: Generating {num_responses} continuations with {current_max_tokens} tokens...", "cyan"))
            
            # Generate multiple continuations
            continuations = await generate_continuations(current_response, query, current_max_tokens, num_responses)
            
            # Send update with all continuations
            await queue.put({
                "status": "generating_continuations",
                "progress": min(90, 30 + iteration * 15),
                "all_responses": continuations,
                "current_token_limit": current_max_tokens,
                "response": current_response
            })
            
            # Select the best continuation
            current_response, selected_index = await select_best_response(continuations, query)
            
            # Send update with selection
            await queue.put({
                "status": "selected_continuation",
                "progress": min(90, 30 + iteration * 15),
                "all_responses": continuations,
                "selected_index": selected_index,
                "current_token_limit": current_max_tokens,
                "response": current_response
            })
            
            # Increase token limit for next iteration
            current_max_tokens += TOKEN_INCREMENT
            iteration += 1
        
        return current_response.replace("<RESPONSE DONE>", "").strip()
    except Exception as e:
        print(colored(f"Error in continuation process: {str(e)}", "red"))
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/generate")
async def generate_response_stream(
    query: str, 
    num_responses: int = DEFAULT_NUM_RESPONSES,
    meditate_full: bool = True
):
    """Streaming endpoint to handle the generation process"""
    # Validate num_responses
    if num_responses < 1 or num_responses > MAX_NUM_RESPONSES:
        raise HTTPException(status_code=400, detail=f"Number of responses must be between 1 and {MAX_NUM_RESPONSES}")

    async def event_generator():
        # Create a queue for updates
        queue = asyncio.Queue()
        
        try:
            # Step 1: Generate initial responses
            initial_responses = await generate_initial_responses(query, num_responses)
            print(colored("Initial responses generated", "green"))
            
            # Send update with initial responses
            data = {
                "status": "initial_responses",
                "progress": 20,
                "all_responses": initial_responses,
                "current_token_limit": MAX_INITIAL_TOKENS
            }
            yield {"event": "message", "data": json.dumps(data)}
            await asyncio.sleep(0.1)
            
            # Step 2: Select the best response
            best_response, selected_index = await select_best_response(initial_responses, query, meditate_full)
            print(colored("Best initial response selected", "green"))
            
            # Send update with selection
            data = {
                "status": "initial_selected",
                "progress": 30,
                "all_responses": initial_responses,
                "selected_index": selected_index,
                "response": best_response,
                "current_token_limit": MAX_INITIAL_TOKENS
            }
            yield {"event": "message", "data": json.dumps(data)}
            await asyncio.sleep(0.1)
            
            # Step 3: Continue and complete the response
            current_response = best_response
            current_max_tokens = MAX_INITIAL_TOKENS + TOKEN_INCREMENT
            iteration = 1
            
            while "<RESPONSE DONE>" not in current_response:
                print(colored(f"Iteration {iteration}: Generating continuations...", "cyan"))
                
                # Generate multiple continuations
                continuations = await generate_continuations(current_response, query, current_max_tokens, num_responses)
                
                # Send update with continuations
                data = {
                    "status": "generating_continuations",
                    "progress": min(90, 30 + iteration * 15),
                    "all_responses": continuations,
                    "current_token_limit": current_max_tokens,
                    "response": current_response
                }
                yield {"event": "message", "data": json.dumps(data)}
                await asyncio.sleep(0.1)
                
                # Select the best continuation
                current_response, selected_index = await select_best_response(continuations, query, meditate_full)
                
                # Send update with selection
                data = {
                    "status": "selected_continuation",
                    "progress": min(90, 30 + iteration * 15),
                    "all_responses": continuations,
                    "selected_index": selected_index,
                    "current_token_limit": current_max_tokens,
                    "response": current_response
                }
                yield {"event": "message", "data": json.dumps(data)}
                await asyncio.sleep(0.1)
                
                # Increase token limit for next iteration
                current_max_tokens += TOKEN_INCREMENT
                iteration += 1
            
            final_response = current_response.replace("<RESPONSE DONE>", "").strip()
            
            # Save response to file
            try:
                with open("response.txt", "w", encoding="utf-8") as f:
                    f.write(final_response)
                print(colored("Response saved to file successfully", "green"))
            except Exception as e:
                print(colored(f"Error saving response to file: {str(e)}", "red"))
            
            # Send final update
            data = {
                "status": "completed",
                "progress": 100,
                "response": final_response
            }
            yield {"event": "message", "data": json.dumps(data)}
            
        except Exception as e:
            print(colored(f"Error in generate_response endpoint: {str(e)}", "red"))
            yield {
                "event": "error",
                "data": json.dumps({"error": str(e)})
            }

    return EventSourceResponse(event_generator())

# Mount static files
app.mount("/", StaticFiles(directory="static", html=True), name="static")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:app", host="127.0.0.1", port=8000, reload=True) 