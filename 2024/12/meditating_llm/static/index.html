<!DOCTYPE html>
<html lang="en" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Meditating LLMs</title>
    <link href="https://cdn.jsdelivr.net/npm/daisyui@3.9.4/dist/full.css" rel="stylesheet" type="text/css" />
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js"></script>
    <style>
        .meditation-circle {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background: linear-gradient(45deg, #4f46e5, #7c3aed);
            position: relative;
            box-shadow: 0 0 20px rgba(124, 58, 237, 0.5);
        }
        
        @keyframes pulse {
            0% { transform: scale(1); opacity: 1; box-shadow: 0 0 20px rgba(124, 58, 237, 0.5); }
            50% { transform: scale(1.2); opacity: 0.7; box-shadow: 0 0 40px rgba(124, 58, 237, 0.7); }
            100% { transform: scale(1); opacity: 1; box-shadow: 0 0 20px rgba(124, 58, 237, 0.5); }
        }
        
        .pulsing {
            animation: pulse 2s infinite;
        }

        .btn-primary {
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(79, 70, 229, 0.4);
        }

        .btn-primary:active {
            transform: translateY(0);
        }

        .textarea {
            transition: all 0.3s ease;
        }

        .textarea:focus {
            transform: scale(1.01);
            box-shadow: 0 0 20px rgba(124, 58, 237, 0.2);
        }

        .card {
            transition: all 0.5s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .response-card {
            transition: all 0.3s ease;
        }

        .response-card.selected {
            border: 2px solid #4f46e5;
            box-shadow: 0 0 20px rgba(79, 70, 229, 0.3);
        }

        .response-card:hover {
            transform: translateY(-3px);
        }

        @keyframes ripple {
            0% { transform: scale(1); opacity: 1; }
            100% { transform: scale(2); opacity: 0; }
        }

        .ripple-container {
            position: relative;
        }

        .ripple {
            position: absolute;
            border-radius: 50%;
            background: rgba(124, 58, 237, 0.3);
            width: 100px;
            height: 100px;
            pointer-events: none;
        }

        .token-badge {
            position: absolute;
            top: -8px;
            right: -8px;
            background: #4f46e5;
            color: white;
            border-radius: 9999px;
            padding: 2px 8px;
            font-size: 0.75rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        @keyframes glow {
            0% {
                box-shadow: 0 0 5px rgba(79, 70, 229, 0.2),
                            0 0 10px rgba(79, 70, 229, 0.2),
                            0 0 15px rgba(79, 70, 229, 0.2);
                border-color: rgba(79, 70, 229, 0.6);
            }
            50% {
                box-shadow: 0 0 20px rgba(79, 70, 229, 0.6),
                            0 0 40px rgba(79, 70, 229, 0.4),
                            0 0 60px rgba(79, 70, 229, 0.2);
                border-color: rgba(79, 70, 229, 1);
            }
            100% {
                box-shadow: 0 0 5px rgba(79, 70, 229, 0.2),
                            0 0 10px rgba(79, 70, 229, 0.2),
                            0 0 15px rgba(79, 70, 229, 0.2);
                border-color: rgba(79, 70, 229, 0.6);
            }
        }

        @keyframes selected-pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.02); }
            100% { transform: scale(1); }
        }

        .selected-badge {
            position: absolute;
            top: -12px;
            left: -12px;
            background: linear-gradient(45deg, #4f46e5, #7c3aed);
            color: white;
            border-radius: 9999px;
            padding: 4px 12px;
            font-size: 0.75rem;
            font-weight: bold;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            animation: selected-pulse 2s ease-in-out infinite;
            z-index: 10;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .selected-badge::before {
            content: '✨';
            font-size: 1rem;
        }

        .response-card.selected {
            border: 2px solid #4f46e5;
            animation: glow 2s ease-in-out infinite;
            transform-origin: center;
        }

        .response-card.selected .card-body {
            background: linear-gradient(to bottom right, rgba(79, 70, 229, 0.1), transparent);
        }
    </style>
</head>
<body class="min-h-screen bg-base-300">
    <div class="container mx-auto px-4 py-8">
        <div class="text-center mb-8">
            <div class="flex items-center justify-center gap-2 mb-2">
                <h1 class="text-4xl font-bold text-primary">Meditating LLMs</h1>
                <div class="tooltip tooltip-bottom" data-tip="Multiple GPT-4o instances generate parallel possibilities, while o1-mini reflects on these thoughts to select the most promising path forward, gradually building the perfect response">
                    <button class="btn btn-circle btn-ghost btn-xs">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="w-4 h-4">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </button>
                </div>
            </div>
            <p class="text-lg text-base-content opacity-80">Multiple GPT-4o instances generate parallel thoughts, while o1-mini meditates to choose the most promising path</p>
            <p class="text-sm text-base-content opacity-60 mt-2">Each round, several GPT-4o instances generate 100-token continuations in parallel, letting o1-mini contemplate and select the best one to build upon</p>
        </div>

        <div class="max-w-4xl mx-auto">
            <div class="form-control mb-4">
                <label class="label">
                    <span class="label-text">Number of Parallel Responses (max 20)</span>
                </label>
                <input type="number" id="numResponses" class="input input-bordered w-full max-w-xs" value="3" min="1" max="20">
            </div>

            <div class="form-control mb-4">
                <label class="label cursor-pointer justify-start gap-4">
                    <span class="label-text">Meditate on...</span>
                    <div class="tooltip" data-tip="Choose whether to judge the entire response or just the new continuations">
                        <input type="checkbox" id="meditateMode" class="toggle toggle-primary" checked />
                        <span id="meditateModeText" class="ml-2">Full Response</span>
                    </div>
                </label>
            </div>

            <div class="form-control">
                <textarea id="queryInput" class="textarea textarea-primary h-32 mb-4" placeholder="Enter your query here...">write the best 300 word short funny sci-fi story</textarea>
                <button id="generateBtn" class="btn btn-primary ripple-container">Generate Response</button>
            </div>

            <div id="processingContainer" class="hidden mt-8">
                <div class="flex flex-col items-center space-y-4 mb-8">
                    <div class="meditation-circle pulsing"></div>
                    <div id="statusText" class="text-lg text-primary font-semibold"></div>
                    <div class="w-full max-w-md">
                        <div class="flex justify-between mb-2">
                            <span class="text-sm text-primary">Progress</span>
                            <span id="progressText" class="text-sm text-primary">0%</span>
                        </div>
                        <progress id="progressBar" class="progress progress-primary w-full" value="0" max="100"></progress>
                    </div>
                </div>

                <div id="responsesGrid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <!-- Response cards will be dynamically added here -->
                </div>
            </div>

            <div id="responseContainer" class="hidden mt-8">
                <div class="card bg-base-200 shadow-xl">
                    <div class="card-body">
                        <h2 class="card-title text-primary">Final Response</h2>
                        <pre id="responseText" class="whitespace-pre-wrap text-base-content"></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const generateBtn = document.getElementById('generateBtn');
        const queryInput = document.getElementById('queryInput');
        const numResponses = document.getElementById('numResponses');
        const processingContainer = document.getElementById('processingContainer');
        const responseContainer = document.getElementById('responseContainer');
        const responseText = document.getElementById('responseText');
        const statusText = document.getElementById('statusText');
        const progressBar = document.getElementById('progressBar');
        const progressText = document.getElementById('progressText');
        const responsesGrid = document.getElementById('responsesGrid');
        const DEFAULT_QUERY = "write the best 300 word short funny sci-fi story";
        const meditateMode = document.getElementById('meditateMode');
        const meditateModeText = document.getElementById('meditateModeText');

        function createResponseCard(response, index, isSelected = false, tokenLimit = null) {
            const card = document.createElement('div');
            card.className = `card bg-base-200 shadow-xl response-card relative ${isSelected ? 'selected' : ''}`;
            
            if (tokenLimit) {
                const badge = document.createElement('div');
                badge.className = 'token-badge';
                badge.textContent = `${tokenLimit} tokens`;
                card.appendChild(badge);
            }

            if (isSelected) {
                const selectedBadge = document.createElement('div');
                selectedBadge.className = 'selected-badge';
                selectedBadge.textContent = 'Selected';
                card.appendChild(selectedBadge);
            }

            const cardBody = document.createElement('div');
            cardBody.className = 'card-body p-4';
            
            const header = document.createElement('div');
            header.className = 'flex items-center justify-between mb-2';
            
            const title = document.createElement('h3');
            title.className = 'card-title text-primary text-sm';
            title.textContent = `Response ${index + 1}`;
            
            const content = document.createElement('pre');
            content.className = 'whitespace-pre-wrap text-base-content text-sm mt-2 overflow-auto max-h-48';
            content.textContent = response;

            header.appendChild(title);
            cardBody.appendChild(header);
            cardBody.appendChild(content);
            card.appendChild(cardBody);

            return card;
        }

        function updateResponses(responses, selectedIndex = -1, tokenLimit = null) {
            console.log('Updating responses:', { responses, selectedIndex, tokenLimit });
            
            // Clear existing responses with a fade out
            const existingCards = responsesGrid.children;
            if (existingCards.length > 0) {
                anime({
                    targets: existingCards,
                    opacity: 0,
                    duration: 300,
                    easing: 'easeOutQuad',
                    complete: () => {
                        responsesGrid.innerHTML = '';
                        // Add new responses
                        responses.forEach((response, index) => {
                            const card = createResponseCard(response, index, index === selectedIndex, tokenLimit);
                            card.style.opacity = 0;  // Start invisible
                            responsesGrid.appendChild(card);
                        });
                        
                        // Animate new cards in
                        anime({
                            targets: responsesGrid.children,
                            opacity: [0, 1],
                            translateY: [20, 0],
                            duration: 500,
                            delay: anime.stagger(100),
                            easing: 'easeOutCubic'
                        });
                    }
                });
            } else {
                // First time adding responses
                responses.forEach((response, index) => {
                    const card = createResponseCard(response, index, index === selectedIndex, tokenLimit);
                    card.style.opacity = 0;  // Start invisible
                    responsesGrid.appendChild(card);
                });
                
                // Animate cards in
                anime({
                    targets: responsesGrid.children,
                    opacity: [0, 1],
                    translateY: [20, 0],
                    duration: 500,
                    delay: anime.stagger(100),
                    easing: 'easeOutCubic'
                });
            }
        }

        // Add ripple effect
        function createRipple(event) {
            const button = event.currentTarget;
            const ripple = document.createElement('div');
            ripple.classList.add('ripple');
            
            const rect = button.getBoundingClientRect();
            const x = event.clientX - rect.left;
            const y = event.clientY - rect.top;
            
            ripple.style.left = `${x - 50}px`;
            ripple.style.top = `${y - 50}px`;
            
            button.appendChild(ripple);
            
            anime({
                targets: ripple,
                scale: [1, 2],
                opacity: [1, 0],
                duration: 1000,
                easing: 'easeOutExpo',
                complete: () => ripple.remove()
            });
        }

        generateBtn.addEventListener('mousedown', createRipple);

        generateBtn.addEventListener('click', async () => {
            const query = queryInput.value.trim();
            if (!query) return;

            const numResponsesValue = parseInt(numResponses.value);
            if (isNaN(numResponsesValue) || numResponsesValue < 1 || numResponsesValue > 20) {
                statusText.textContent = 'Please enter a valid number of responses (1-20)';
                statusText.classList.add('text-error');
                return;
            }

            // Reset and show processing state
            generateBtn.disabled = true;
            processingContainer.classList.remove('hidden');
            responseContainer.classList.add('hidden');
            responsesGrid.innerHTML = '';
            progressBar.value = 0;
            progressText.textContent = '0%';
            statusText.textContent = 'Initializing...';
            statusText.classList.remove('text-error', 'text-success');

            let eventSource;
            try {
                // Close any existing EventSource
                if (window.currentEventSource) {
                    window.currentEventSource.close();
                }

                // Create new EventSource
                const url = `/generate?query=${encodeURIComponent(query)}&num_responses=${numResponsesValue}&meditate_full=${meditateMode.checked}`;
                console.log('Connecting to:', url);
                eventSource = new EventSource(url);
                window.currentEventSource = eventSource;

                eventSource.addEventListener('message', (event) => {
                    try {
                        console.log('Raw event data:', event.data);
                        const data = JSON.parse(event.data);
                        console.log('Parsed update:', data);

                        // Update progress
                        if (data.progress !== undefined) {
                            anime({
                                targets: progressBar,
                                value: [progressBar.value, data.progress],
                                duration: 500,
                                easing: 'easeInOutQuad',
                                update: function() {
                                    progressText.textContent = `${Math.round(progressBar.value)}%`;
                                }
                            });
                        }

                        // Update status and responses based on the current state
                        switch (data.status) {
                            case 'initial_responses':
                                console.log('Showing initial responses:', data.all_responses);
                                statusText.textContent = 'Generated initial responses, selecting the best one...';
                                if (Array.isArray(data.all_responses) && data.all_responses.length > 0) {
                                    updateResponses(data.all_responses, -1, data.current_token_limit);
                                }
                                break;
                            
                            case 'initial_selected':
                                console.log('Showing selected initial response:', data.selected_index);
                                statusText.textContent = 'Best response selected, continuing generation...';
                                if (Array.isArray(data.all_responses) && data.all_responses.length > 0) {
                                    updateResponses(data.all_responses, data.selected_index, data.current_token_limit);
                                }
                                break;
                            
                            case 'generating_continuations':
                                console.log('Showing continuations:', data.all_responses);
                                statusText.textContent = `Generating continuations (${data.current_token_limit} tokens)...`;
                                if (Array.isArray(data.all_responses) && data.all_responses.length > 0) {
                                    updateResponses(data.all_responses, -1, data.current_token_limit);
                                }
                                break;
                            
                            case 'selected_continuation':
                                console.log('Showing selected continuation:', data.selected_index);
                                statusText.textContent = 'Selected best continuation...';
                                if (Array.isArray(data.all_responses) && data.all_responses.length > 0) {
                                    updateResponses(data.all_responses, data.selected_index, data.current_token_limit);
                                }
                                break;
                            
                            case 'completed':
                                console.log('Completed with response:', data.response);
                                if (eventSource) {
                                    eventSource.close();
                                }
                                responseContainer.classList.remove('hidden');
                                responseText.textContent = data.response;
                                
                                // Animate final response appearance
                                anime({
                                    targets: '#responseContainer',
                                    opacity: [0, 1],
                                    translateY: [20, 0],
                                    duration: 800,
                                    easing: 'easeOutCubic'
                                });
                                
                                generateBtn.disabled = false;
                                statusText.textContent = 'Generation completed!';
                                statusText.classList.add('text-success');
                                break;
                        }
                    } catch (error) {
                        console.error('Error parsing SSE data:', error);
                        console.error('Raw data:', event.data);
                    }
                });

                eventSource.addEventListener('error', (error) => {
                    console.error('EventSource error:', error);
                    if (eventSource.readyState === EventSource.CLOSED) {
                        console.log('EventSource closed normally');
                        return;
                    }
                    eventSource.close();
                    statusText.textContent = 'Error generating response. Please try again.';
                    statusText.classList.add('text-error');
                    generateBtn.disabled = false;
                });

                eventSource.addEventListener('open', () => {
                    console.log('EventSource connection opened');
                    statusText.textContent = 'Connected, starting generation...';
                });
            } catch (error) {
                console.error('Error:', error);
                if (eventSource) {
                    eventSource.close();
                }
                statusText.textContent = 'Error generating response. Please try again.';
                statusText.classList.add('text-error');
                generateBtn.disabled = false;
            }
        });

        // Validate number of responses input
        numResponses.addEventListener('input', (e) => {
            const value = parseInt(e.target.value);
            if (value < 1) e.target.value = 1;
            if (value > 20) e.target.value = 20;
        });

        queryInput.addEventListener('focus', function(e) {
            if (e.target.value === DEFAULT_QUERY) {
                e.target.value = '';
            }
        });

        queryInput.addEventListener('blur', function(e) {
            if (e.target.value.trim() === '') {
                e.target.value = DEFAULT_QUERY;
            }
        });

        meditateMode.addEventListener('change', function() {
            meditateModeText.textContent = this.checked ? 'Full Response' : 'Continuations Only';
        });
    </script>
</body>
</html> 