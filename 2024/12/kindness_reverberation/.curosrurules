beautiful and colorfulhtml canvas animation

step 1:
birds eye view of a city roughly
100 small circles on the screen(these will be people)
3 types of people:
- neutral (gray)
- angry (red)
- happy (green)

each time angry or happy person comes within a certain distance of another person they will output a gesture(neutral people wont put out a gesture)
make the gesture animated and with a small font simple sayings like ("thank you", "I appreciate it", "I am sorry", "how can I help you?" etc)
angry gestures also is a list of common simple texts these texts should appear(pop in and out and shouldnt be too distracting and small)

step 2:
these gestures have a chance to influence others to change their emotion
each person starts with a scale to 99 first 33 is happy 33-66 is neutral 66-99 is angry
kindness gesture will have a chance to increase happiness
anger gesture will have a chance to increase anger
chance of influence will be impacted by how many of the same group are within a certain distance
happy people will have a slight chance to hang together when encountering a happy person for brief periods of time then they go on their way
angry interaction has a higher chance for people to more speedily move away from one another
lets show these stats on the screen with small fonts at the corner with realtime updates in a nice way with bars

step 3:
background subtly shifts colors based on population emotions:
- more happy people makes background shift to slight green tint
- more angry people makes background shift to slight red tint
show celebration card with hearts and particles when reaching 100% happiness
show sad card with rain effects and sad emojis when angry people become majority
make the simulation mobile friendly with proper controls and stats display
add echohive.live link at bottom with animated gradient
add sliders to control:
- base movement speed
- angry repulsion speed
- happy grouping duration
- influence radius
- influence chance
- influence amount

