// Dynamic constants that can be modified by sliders
window.BASE_SPEED = 1;
window.ANGRY_REPULSION_SPEED_MULTIPLIER = 1.5;
window.HAPPY_GROUPING_DURATION = 2000;
window.INFLUENCE_RADIUS = 100;
window.BASE_INFLUENCE_CHANCE = 0.3;
window.INFLUENCE_AMOUNT = 8;

// Fixed constants
const PERSON_RADIUS = 5;
const INTERACTION_RADIUS = 50;
const TOTAL_PEOPLE = 100;
const ANGRY_REPULSION_DURATION = 1500;
const HAPPY_GROUPING_SPEED = 1;

// Emotional scale thresholds (0-99)
const EMOTION_THRESHOLDS = {
    HAPPY: 33,
    NEUTRAL: 66,
    ANGRY: 99
};

const PERSON_TYPES = {
    NEUTRAL: 'neutral',
    ANGRY: 'angry',
    HAPPY: 'happy'
};

const PERSON_COLORS = {
    [PERSON_TYPES.NEUTRAL]: '#808080',
    [PERSON_TYPES.ANGRY]: '#ff4444',
    [PERSON_TYPES.HAPPY]: '#44ff44'
};

// Influence chances
const GROUP_INFLUENCE_MULTIPLIER = 0.15;
const HAPPY_GROUPING_CHANCE = 0.4;

const POSITIVE_GESTURES = [
    "Thank you!",
    "I appreciate it",
    "You're amazing",
    "How can I help?",
    "Have a great day!",
    "You matter",
    "Keep smiling"
];

const NEGATIVE_GESTURES = [
    "Whatever...",
    "Not now",
    "Leave me alone",
    "*sigh*",
    "No way",
    "Ugh..."
];

const GESTURE_DURATION = 2000;
const GESTURE_FONT = '12px Arial';
const STATS_FONT = '14px Arial';

// Edge avoidance
const EDGE_BUFFER = 50;
const EDGE_TURN_FACTOR = 0.8;