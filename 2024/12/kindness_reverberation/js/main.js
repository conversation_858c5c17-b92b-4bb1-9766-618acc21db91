// Initialize canvas
const canvas = document.getElementById('cityCanvas');
const ctx = canvas.getContext('2d');

// Title animation properties
let titleOpacity = 0;
let subtitleOpacity = 0;
let titleY = 0;
let subtitleY = 0;
const TITLE_FONT = '48px Arial';
const SUBTITLE_FONT = '24px Arial';
const FOOTER_FONT = '16px Arial';
let fadeInComplete = false;
let moveUpComplete = false;
let shouldFadeOut = false;
let fadeOutStartTime = 0;

// Celebration properties
let showingCelebration = false;
let celebrationStartTime = 0;
const CELEBRATION_DURATION = 5000;
let celebrationParticles = [];
const PARTICLE_COUNT = 100;

// Footer link properties
let footerGradientOffset = 0;
let footerClickable = true;

// Add new properties for background and sad celebration
let backgroundGradientStart = '#1a1a2e';
let backgroundGradientEnd = '#16213e';
let showingSadState = false;
let sadStateStartTime = 0;
const SAD_STATE_DURATION = 5000;
let sadEmojis = ['😢', '😔', '😞', '💔', '😥'];
let sadParticles = [];

// Create celebration particles
function createCelebrationParticles() {
    celebrationParticles = [];
    for (let i = 0; i < PARTICLE_COUNT; i++) {
        celebrationParticles.push({
            x: Math.random() * canvas.width,
            y: canvas.height + Math.random() * 20,
            speed: 2 + Math.random() * 3,
            radius: 2 + Math.random() * 3,
            color: `hsl(${Math.random() * 60 + 30}, 100%, 60%)`, // Golden colors
            angle: Math.PI * (1 + Math.random() * 0.2)
        });
    }
}

// Create sad particles
function createSadParticles() {
    sadParticles = [];
    for (let i = 0; i < PARTICLE_COUNT; i++) {
        sadParticles.push({
            x: Math.random() * canvas.width,
            y: -Math.random() * 20,
            speed: 1 + Math.random() * 2,
            radius: 2 + Math.random() * 3,
            color: `rgba(100, 149, 237, ${0.3 + Math.random() * 0.3})`, // Soft blue
            angle: Math.PI * (1 + Math.random() * 0.2)
        });
    }
}

// Draw celebration
function drawCelebration() {
    const elapsed = Date.now() - celebrationStartTime;
    const progress = Math.min(elapsed / CELEBRATION_DURATION, 1);
    
    // Update and draw particles
    celebrationParticles.forEach(particle => {
        particle.y -= particle.speed;
        particle.x += Math.cos(particle.angle) * 0.5;
        
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.radius, 0, Math.PI * 2);
        ctx.fillStyle = particle.color;
        ctx.fill();
    });

    // Draw celebration message
    ctx.save();
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    
    // Draw glowing background
    const celebrationY = canvas.height / 2;
    ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    ctx.roundRect(canvas.width/2 - 200, celebrationY - 100, 400, 200, 20);
    ctx.fill();

    // Draw celebration text with glow
    ctx.font = '36px Arial';
    ctx.shadowColor = 'rgba(255, 215, 0, 0.8)';
    ctx.shadowBlur = 20;
    ctx.fillStyle = '#FFD700';
    ctx.fillText('100% KINDNESS ACHIEVED!', canvas.width/2, celebrationY - 20);

    // Draw hearts
    const heartSize = 20 + Math.sin(Date.now() / 300) * 5; // Pulsing effect
    const hearts = ['❤️', '💖', '💝', '💕', '💗'];
    hearts.forEach((heart, i) => {
        ctx.font = `${heartSize}px Arial`;
        const x = canvas.width/2 + (i - 2) * 40;
        const y = celebrationY + 40 + Math.sin(Date.now() / 500 + i) * 10;
        ctx.fillText(heart, x, y);
    });

    ctx.restore();

    // Reset celebration if duration is over
    if (progress >= 1) {
        showingCelebration = false;
    }
}

// Draw sad state
function drawSadState() {
    const elapsed = Date.now() - sadStateStartTime;
    const progress = Math.min(elapsed / SAD_STATE_DURATION, 1);
    
    // Update and draw particles (falling rain effect)
    sadParticles.forEach(particle => {
        particle.y += particle.speed;
        particle.x += Math.sin(particle.angle) * 0.5;
        
        if (particle.y > canvas.height) {
            particle.y = -particle.radius;
            particle.x = Math.random() * canvas.width;
        }
        
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.radius, 0, Math.PI * 2);
        ctx.fillStyle = particle.color;
        ctx.fill();
    });

    // Draw sad message
    ctx.save();
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    
    // Draw glowing background
    const celebrationY = canvas.height / 2;
    ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
    ctx.roundRect(canvas.width/2 - 200, celebrationY - 100, 400, 200, 20);
    ctx.fill();

    // Draw sad text with glow
    ctx.font = '36px Arial';
    ctx.shadowColor = 'rgba(100, 149, 237, 0.8)';
    ctx.shadowBlur = 20;
    ctx.fillStyle = '#6495ED';
    ctx.fillText('Kindness Fading...', canvas.width/2, celebrationY - 20);

    // Draw sad emojis with wave effect
    const emojiSize = 20 + Math.sin(Date.now() / 500) * 3;
    sadEmojis.forEach((emoji, i) => {
        ctx.font = `${emojiSize}px Arial`;
        const x = canvas.width/2 + (i - 2) * 40;
        const y = celebrationY + 40 + Math.sin(Date.now() / 700 + i) * 5;
        ctx.fillText(emoji, x, y);
    });

    ctx.restore();

    // Reset sad state if duration is over
    if (progress >= 1) {
        showingSadState = false;
    }
}

// Calculate initial positions
function initializeTitlePositions() {
    titleY = canvas.height / 2;
    subtitleY = titleY + 50;
}

// Animate title sequence
function startTitleSequence() {
    // First animation: Fade in
    anime({
        targets: { opacity: 0 },
        opacity: 1,
        duration: 2000,
        easing: 'easeInOutQuad',
        update: function(anim) {
            titleOpacity = anim.progress / 100;
            if (anim.progress > 50 && !fadeInComplete) {
                fadeInComplete = true;
                // Start subtitle fade in
                anime({
                    targets: { opacity: 0 },
                    opacity: 1,
                    duration: 2000,
                    easing: 'easeInOutQuad',
                    update: function(anim) {
                        subtitleOpacity = anim.progress / 100;
                    },
                    complete: function() {
                        // Start move up animation after fade in
                        setTimeout(() => {
                            const targetTitleY = canvas.height / 6;
                            const targetSubtitleY = targetTitleY + 50;
                            anime({
                                targets: { y: titleY },
                                y: targetTitleY,
                                duration: 1500,
                                easing: 'easeOutQuad',
                                update: function(anim) {
                                    titleY = anim.animations[0].currentValue;
                                    subtitleY = titleY + 50;
                                },
                                complete: function() {
                                    moveUpComplete = true;
                                    // Start fade out after 5 seconds
                                    setTimeout(() => {
                                        shouldFadeOut = true;
                                        fadeOutStartTime = Date.now();
                                    }, 5000);
                                }
                            });
                        }, 1000);
                    }
                });
            }
        }
    });
}

// Set up controls
const controls = document.getElementById('controls');
const toggleButton = document.getElementById('toggleControls');

toggleButton.addEventListener('click', () => {
    controls.classList.toggle('visible');
});

// Close controls when clicking outside
document.addEventListener('click', (event) => {
    if (!controls.contains(event.target) && event.target !== toggleButton) {
        controls.classList.remove('visible');
    }
});

// Get mobile scale factor
function getMobileScale() {
    return window.innerWidth <= 480 ? 0.7 :
           window.innerWidth <= 768 ? 0.8 : 1;
}

// Handle footer click
canvas.addEventListener('click', (event) => {
    const rect = canvas.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    
    // Check if click is in footer area
    if (y > canvas.height - 40 && y < canvas.height - 10 &&
        x > canvas.width/2 - 100 && x < canvas.width/2 + 100) {
        window.open('https://www.echohive.live', '_blank');
    }
});

// Set up slider controls
function setupSlider(id, variable) {
    const slider = document.getElementById(id);
    const display = slider.parentElement.querySelector('.value-display');
    
    if (!slider) {
        console.error(`Slider with id ${id} not found`);
        return;
    }

    // Set initial value
    slider.value = window[variable];
    display.textContent = window[variable];
    
    slider.addEventListener('input', () => {
        const value = parseFloat(slider.value);
        display.textContent = value;
        window[variable] = value;
        console.log(`${variable} updated to:`, value); // Debug logging
    });
}

// Setup all sliders
const sliderSetups = [
    { id: 'baseSpeed', variable: 'BASE_SPEED' },
    { id: 'angrySpeedMultiplier', variable: 'ANGRY_REPULSION_SPEED_MULTIPLIER' },
    { id: 'happyGroupingDuration', variable: 'HAPPY_GROUPING_DURATION' },
    { id: 'influenceRadius', variable: 'INFLUENCE_RADIUS' },
    { id: 'baseInfluenceChance', variable: 'BASE_INFLUENCE_CHANCE' },
    { id: 'influenceAmount', variable: 'INFLUENCE_AMOUNT' }
];

sliderSetups.forEach(setup => {
    setupSlider(setup.id, setup.variable);
});

// Handle window resize
function resizeCanvas() {
    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;
    initializeTitlePositions();
}

window.addEventListener('resize', resizeCanvas);
resizeCanvas();

// Create people array
const people = [];

// Initialize people
function initializePeople() {
    for (let i = 0; i < TOTAL_PEOPLE; i++) {
        const x = Math.random() * (canvas.width - 2 * PERSON_RADIUS) + PERSON_RADIUS;
        const y = Math.random() * (canvas.height - 2 * PERSON_RADIUS) + PERSON_RADIUS;
        people.push(new Person(x, y));
    }
}

// Draw title and subtitle
function drawTitles() {
    ctx.save();
    
    let currentTitleOpacity = titleOpacity;
    let currentSubtitleOpacity = subtitleOpacity;

    if (shouldFadeOut) {
        const fadeOutDuration = 2000;
        const elapsed = Date.now() - fadeOutStartTime;
        const fadeOutProgress = Math.min(elapsed / fadeOutDuration, 1);
        currentTitleOpacity *= (1 - fadeOutProgress);
        currentSubtitleOpacity *= (1 - fadeOutProgress);
    }

    // Get responsive font sizes
    const scale = getMobileScale();
    const titleFontSize = Math.min(48 * scale, window.innerWidth / 20);
    const subtitleFontSize = Math.min(24 * scale, window.innerWidth / 35);
    
    // Draw title
    ctx.font = `${titleFontSize}px Arial`;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillStyle = `rgba(255, 255, 255, ${currentTitleOpacity})`;
    
    // Add subtle glow effect to title
    ctx.shadowColor = 'rgba(255, 255, 255, 0.5)';
    ctx.shadowBlur = 10 * scale;

    // Handle title text wrapping for mobile
    const title = 'Kindness Reverberation Simulator';
    if (window.innerWidth <= 480) {
        const words = title.split(' ');
        const line1 = words.slice(0, 2).join(' ');
        const line2 = words.slice(2).join(' ');
        ctx.fillText(line1, canvas.width / 2, titleY - titleFontSize / 2);
        ctx.fillText(line2, canvas.width / 2, titleY + titleFontSize / 2);
    } else {
        ctx.fillText(title, canvas.width / 2, titleY);
    }
    
    // Draw subtitle
    ctx.font = `${subtitleFontSize}px Arial`;
    ctx.fillStyle = `rgba(255, 255, 255, ${currentSubtitleOpacity})`;
    ctx.shadowBlur = 5 * scale;

    // Handle subtitle text wrapping for mobile
    const subtitle = 'Be kind to one another and let it reverberate';
    if (window.innerWidth <= 480) {
        const words = subtitle.split(' ');
        const midPoint = Math.ceil(words.length / 2);
        const line1 = words.slice(0, midPoint).join(' ');
        const line2 = words.slice(midPoint).join(' ');
        ctx.fillText(line1, canvas.width / 2, subtitleY - subtitleFontSize);
        ctx.fillText(line2, canvas.width / 2, subtitleY + subtitleFontSize / 2);
    } else {
        ctx.fillText(subtitle, canvas.width / 2, subtitleY);
    }
    
    // Draw animated footer with gradient
    footerGradientOffset = (footerGradientOffset + 0.005) % 1;
    const gradient = ctx.createLinearGradient(
        -canvas.width * footerGradientOffset, 
        canvas.height - 40, 
        canvas.width * (1 - footerGradientOffset), 
        canvas.height - 40
    );
    gradient.addColorStop(0, '#4CAF50');
    gradient.addColorStop(0.5, '#2196F3');
    gradient.addColorStop(1, '#4CAF50');
    
    ctx.font = `${16 * scale}px Arial`;
    ctx.fillStyle = gradient;
    ctx.shadowBlur = 3 * scale;
    
    // Make footer look clickable
    canvas.style.cursor = 'pointer';
    ctx.fillText('www.echohive.live', canvas.width / 2, canvas.height - 20 * scale);
    
    ctx.restore();
}

// Draw stats with bars
function drawStats() {
    const stats = {
        happy: people.filter(p => p.type === PERSON_TYPES.HAPPY).length,
        neutral: people.filter(p => p.type === PERSON_TYPES.NEUTRAL).length,
        angry: people.filter(p => p.type === PERSON_TYPES.ANGRY).length
    };

    // Check for 100% happiness
    if (stats.happy === TOTAL_PEOPLE && !showingCelebration) {
        showingCelebration = true;
        celebrationStartTime = Date.now();
        createCelebrationParticles();
    }

    const scale = getMobileScale();
    
    ctx.save();
    ctx.font = `${parseInt(STATS_FONT) * scale}px Arial`;
    ctx.textAlign = 'left';
    ctx.textBaseline = 'top';

    const padding = 10 * scale;
    const lineHeight = 25 * scale;
    const barWidth = 100 * scale;
    const barHeight = 15 * scale;
    const maxCount = TOTAL_PEOPLE;

    // Draw stats background
    ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
    ctx.roundRect(padding, padding, 250 * scale, 100 * scale, 5 * scale);
    ctx.fill();

    // Function to draw a bar
    function drawBar(value, y, color) {
        const barLength = (value / maxCount) * barWidth;
        
        // Draw bar background
        ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';
        ctx.fillRect(padding + (80 * scale), y + 5 * scale, barWidth, barHeight);
        
        // Draw actual bar
        const barGradient = ctx.createLinearGradient(
            padding + (80 * scale), y,
            padding + (80 * scale) + barLength, y
        );
        barGradient.addColorStop(0, color);
        barGradient.addColorStop(1, 'rgba(255, 255, 255, 0.5)');
        ctx.fillStyle = barGradient;
        ctx.fillRect(padding + (80 * scale), y + 5 * scale, barLength, barHeight);
    }

    // Draw stats and bars
    ctx.fillStyle = PERSON_COLORS[PERSON_TYPES.HAPPY];
    ctx.fillText(`Happy: ${stats.happy}`, padding + 10 * scale, padding + lineHeight * 0);
    drawBar(stats.happy, padding + lineHeight * 0, PERSON_COLORS[PERSON_TYPES.HAPPY]);

    ctx.fillStyle = PERSON_COLORS[PERSON_TYPES.NEUTRAL];
    ctx.fillText(`Neutral: ${stats.neutral}`, padding + 10 * scale, padding + lineHeight * 1);
    drawBar(stats.neutral, padding + lineHeight * 1, PERSON_COLORS[PERSON_TYPES.NEUTRAL]);

    ctx.fillStyle = PERSON_COLORS[PERSON_TYPES.ANGRY];
    ctx.fillText(`Angry: ${stats.angry}`, padding + 10 * scale, padding + lineHeight * 2);
    drawBar(stats.angry, padding + lineHeight * 2, PERSON_COLORS[PERSON_TYPES.ANGRY]);

    ctx.restore();
}

// Update background based on emotional state
function updateBackground() {
    const stats = {
        happy: people.filter(p => p.type === PERSON_TYPES.HAPPY).length,
        angry: people.filter(p => p.type === PERSON_TYPES.ANGRY).length
    };

    const totalEmotional = stats.happy + stats.angry;
    if (totalEmotional === 0) return;

    const happyRatio = stats.happy / totalEmotional;
    
    // Interpolate between angry (reddish) and happy (greenish) colors
    const r = Math.floor(lerp(26, 40, happyRatio));
    const g = Math.floor(lerp(26, 40, happyRatio));
    const b = Math.floor(lerp(46, 62, happyRatio));
    
    // Add a slight hue based on dominant emotion
    const hue = happyRatio > 0.5 ? 
        `rgba(0, ${Math.floor(happyRatio * 30)}, 0, 0.1)` : 
        `rgba(${Math.floor((1 - happyRatio) * 30)}, 0, 0, 0.1)`;

    backgroundGradientStart = `rgb(${r}, ${g}, ${b})`;
    backgroundGradientEnd = `rgb(${r-10}, ${g-10}, ${b-10})`;

    // Draw background
    const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
    gradient.addColorStop(0, backgroundGradientStart);
    gradient.addColorStop(1, backgroundGradientEnd);
    
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Add subtle emotion-based overlay
    ctx.fillStyle = hue;
    ctx.fillRect(0, 0, canvas.width, canvas.height);
}

// Linear interpolation helper
function lerp(start, end, amt) {
    return (1 - amt) * start + amt * end;
}

// Animation loop
function animate() {
    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Update and draw background
    updateBackground();

    // Draw titles
    drawTitles();

    // Update and draw each person
    for (let i = 0; i < people.length; i++) {
        people[i].update(people);
        
        // Check interactions with other people
        for (let j = i + 1; j < people.length; j++) {
            people[i].interact(people[j], people);
            people[j].interact(people[i], people);
        }
        
        people[i].draw(ctx);
    }

    // Draw stats
    drawStats();

    // Check for sad state (when angry people become majority)
    const angryCount = people.filter(p => p.type === PERSON_TYPES.ANGRY).length;
    if (angryCount > TOTAL_PEOPLE * 0.6 && !showingSadState && !showingCelebration) {
        showingSadState = true;
        sadStateStartTime = Date.now();
        createSadParticles();
    }

    // Draw celebration or sad state if active
    if (showingCelebration) {
        drawCelebration();
    } else if (showingSadState) {
        drawSadState();
    }

    // Continue animation
    requestAnimationFrame(animate);
}

// Start the simulation
initializePeople();
initializeTitlePositions();
startTitleSequence();
animate(); 