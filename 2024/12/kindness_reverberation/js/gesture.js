class Gesture {
    constructor(text, personType) {
        this.text = text;
        this.personType = personType;
        this.startTime = Date.now();
        this.opacity = 0;
        this.offsetY = 0;
        
        // Initialize anime.js animation
        this.animation = anime({
            targets: this,
            opacity: [0, 1, 0],
            offsetY: [-10, -30],
            duration: GESTURE_DURATION,
            easing: 'easeInOutQuad',
            update: () => {
                // This will be called on each frame
            }
        });
    }

    draw(ctx, x, y) {
        ctx.save();
        ctx.font = GESTURE_FONT;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        
        const color = this.personType === PERSON_TYPES.HAPPY ? 
            'rgba(68, 255, 68, ' + this.opacity + ')' : 
            'rgba(255, 68, 68, ' + this.opacity + ')';
            
        ctx.fillStyle = color;
        ctx.fillText(this.text, x, y + this.offsetY);
        ctx.restore();
    }
} 