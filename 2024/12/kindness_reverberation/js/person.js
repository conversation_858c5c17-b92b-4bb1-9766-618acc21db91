class Person {
    constructor(x, y) {
        this.x = x;
        this.y = y;
        this.emotionalScale = Math.floor(Math.random() * 99) + 1; // 1-99
        this.type = this.getTypeFromScale();
        this.dx = (Math.random() - 0.5) * BASE_SPEED;
        this.dy = (Math.random() - 0.5) * BASE_SPEED;
        this.lastInteractionTime = 0;
        this.currentGesture = null;
        this.groupingTimer = 0;
        this.groupingTarget = null;
        this.repulsionTimer = 0;
        this.repulsionDirection = { x: 0, y: 0 };
        this.targetX = x;
        this.targetY = y;
    }

    getTypeFromScale() {
        if (this.emotionalScale <= EMOTION_THRESHOLDS.HAPPY) {
            return PERSON_TYPES.HAPPY;
        } else if (this.emotionalScale <= EMOTION_THRESHOLDS.NEUTRAL) {
            return PERSON_TYPES.NEUTRAL;
        } else {
            return PERSON_TYPES.ANGRY;
        }
    }

    avoidEdges() {
        let dx = 0;
        let dy = 0;

        // Check horizontal edges
        if (this.x < EDGE_BUFFER) {
            dx = EDGE_TURN_FACTOR;
        } else if (this.x > canvas.width - EDGE_BUFFER) {
            dx = -EDGE_TURN_FACTOR;
        }

        // Check vertical edges
        if (this.y < EDGE_BUFFER) {
            dy = EDGE_TURN_FACTOR;
        } else if (this.y > canvas.height - EDGE_BUFFER) {
            dy = -EDGE_TURN_FACTOR;
        }

        return { dx, dy };
    }

    update(people) {
        let currentSpeed = BASE_SPEED;
        let targetX = this.x + this.dx * 10;
        let targetY = this.y + this.dy * 10;

        // Handle angry repulsion
        if (this.repulsionTimer > Date.now()) {
            currentSpeed = BASE_SPEED * ANGRY_REPULSION_SPEED_MULTIPLIER;
            targetX = this.x + this.repulsionDirection.x * 50;
            targetY = this.y + this.repulsionDirection.y * 50;
        }
        // Handle happy grouping
        else if (this.groupingTarget && Date.now() < this.groupingTimer) {
            currentSpeed = HAPPY_GROUPING_SPEED;
            targetX = this.groupingTarget.x;
            targetY = this.groupingTarget.y;
        }
        // Normal random movement with smooth transitions
        else if (Math.random() < 0.02) {
            this.dx = (Math.random() - 0.5) * BASE_SPEED;
            this.dy = (Math.random() - 0.5) * BASE_SPEED;
        }

        // Apply edge avoidance
        const edgeForce = this.avoidEdges();
        this.dx += edgeForce.dx;
        this.dy += edgeForce.dy;

        // Normalize direction and apply speed
        const dist = Math.sqrt(this.dx * this.dx + this.dy * this.dy);
        if (dist > 0) {
            this.dx = (this.dx / dist) * currentSpeed;
            this.dy = (this.dy / dist) * currentSpeed;
        }

        // Update position with bounds checking
        this.x = Math.max(PERSON_RADIUS, Math.min(canvas.width - PERSON_RADIUS, this.x + this.dx));
        this.y = Math.max(PERSON_RADIUS, Math.min(canvas.height - PERSON_RADIUS, this.y + this.dy));

        // Update gesture
        if (this.currentGesture) {
            if (Date.now() - this.currentGesture.startTime >= GESTURE_DURATION) {
                this.currentGesture = null;
            }
        }

        // Reset grouping if timer expired
        if (this.groupingTimer && Date.now() >= this.groupingTimer) {
            this.groupingTimer = 0;
            this.groupingTarget = null;
        }
    }

    draw(ctx) {
        // Draw person
        ctx.beginPath();
        ctx.arc(this.x, this.y, PERSON_RADIUS, 0, Math.PI * 2);
        ctx.fillStyle = PERSON_COLORS[this.type];
        ctx.fill();
        ctx.closePath();

        // Draw gesture if exists
        if (this.currentGesture) {
            this.currentGesture.draw(ctx, this.x, this.y);
        }
    }

    interact(otherPerson, people) {
        if (this.type === PERSON_TYPES.NEUTRAL || 
            Date.now() - this.lastInteractionTime < GESTURE_DURATION) {
            return;
        }

        const dx = otherPerson.x - this.x;
        const dy = otherPerson.y - this.y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        if (distance < INTERACTION_RADIUS) {
            // Create gesture
            const gestures = this.type === PERSON_TYPES.HAPPY ? POSITIVE_GESTURES : NEGATIVE_GESTURES;
            const text = gestures[Math.floor(Math.random() * gestures.length)];
            this.currentGesture = new Gesture(text, this.type);
            this.lastInteractionTime = Date.now();

            // Handle angry repulsion
            if (this.type === PERSON_TYPES.ANGRY && otherPerson.type === PERSON_TYPES.ANGRY) {
                this.repulsionTimer = Date.now() + ANGRY_REPULSION_DURATION;
                this.repulsionDirection = {
                    x: -dx / distance,
                    y: -dy / distance
                };
                otherPerson.repulsionTimer = Date.now() + ANGRY_REPULSION_DURATION;
                otherPerson.repulsionDirection = {
                    x: dx / distance,
                    y: dy / distance
                };
            }

            // Handle happy grouping
            if (this.type === PERSON_TYPES.HAPPY && otherPerson.type === PERSON_TYPES.HAPPY) {
                if (Math.random() < HAPPY_GROUPING_CHANCE) {
                    this.groupingTimer = Date.now() + HAPPY_GROUPING_DURATION;
                    this.groupingTarget = otherPerson;
                }
            }

            // Calculate influence
            this.influenceOther(otherPerson, people);
        }
    }

    influenceOther(target, people) {
        // Count similar people nearby
        const similarNearby = people.filter(p => 
            p !== this && p !== target && 
            p.type === this.type &&
            this.getDistanceTo(p) < INFLUENCE_RADIUS
        ).length;

        // Calculate influence chance
        const influenceChance = BASE_INFLUENCE_CHANCE + 
            (similarNearby * GROUP_INFLUENCE_MULTIPLIER);

        if (Math.random() < influenceChance) {
            // Influence the target's emotional scale
            const change = this.type === PERSON_TYPES.HAPPY ? 
                -INFLUENCE_AMOUNT : INFLUENCE_AMOUNT;
            
            target.emotionalScale = Math.max(1, Math.min(99, target.emotionalScale + change));
            
            // Update type based on new emotional scale
            const newType = target.getTypeFromScale();
            if (newType !== target.type) {
                target.type = newType;
                // Reset movement when type changes
                target.dx = (Math.random() - 0.5) * BASE_SPEED;
                target.dy = (Math.random() - 0.5) * BASE_SPEED;
            }
        }
    }

    getDistanceTo(other) {
        const dx = other.x - this.x;
        const dy = other.y - this.y;
        return Math.sqrt(dx * dx + dy * dy);
    }
} 