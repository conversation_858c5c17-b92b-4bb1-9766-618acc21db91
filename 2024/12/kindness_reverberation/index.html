<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kindness Reverberation Simulator</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js"></script>
    <style>
        body {
            margin: 0;
            overflow: hidden;
            background-color: #1a1a1a;
            font-family: Arial, sans-serif;
        }
        canvas {
            position: fixed;
            top: 0;
            left: 0;
            z-index: 1;
        }
        #controls {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.7);
            padding: 15px;
            border-radius: 10px;
            z-index: 2;
            color: white;
            width: 250px;
            max-height: 80vh;
            overflow-y: auto;
            transition: transform 0.3s ease;
        }
        .control-group {
            margin-bottom: 15px;
        }
        .control-group label {
            display: block;
            margin-bottom: 5px;
            font-size: 12px;
        }
        .slider-container {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        input[type="range"] {
            flex-grow: 1;
            width: 100%;
        }
        .value-display {
            min-width: 40px;
            font-size: 12px;
        }
        #toggleControls {
            position: fixed;
            top: 10px;
            right: 10px;
            z-index: 3;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }

        /* Mobile Styles */
        @media (max-width: 768px) {
            #controls {
                width: 200px;
                font-size: 14px;
                transform: translateX(100%);
            }
            #controls.visible {
                transform: translateX(0);
            }
            .control-group {
                margin-bottom: 10px;
            }
            .control-group label {
                font-size: 11px;
            }
            .value-display {
                font-size: 11px;
                min-width: 30px;
            }
            #toggleControls {
                padding: 6px 10px;
                font-size: 12px;
            }
            /* Adjust stats size for mobile */
            :root {
                --mobile-scale: 0.8;
            }
        }

        /* Small Mobile Styles */
        @media (max-width: 480px) {
            #controls {
                width: 180px;
            }
            :root {
                --mobile-scale: 0.7;
            }
        }

        /* Handle scrollbar appearance */
        #controls::-webkit-scrollbar {
            width: 6px;
        }
        #controls::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
        }
        #controls::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }
        #controls::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }
    </style>
</head>
<body>
    <canvas id="cityCanvas"></canvas>
    <button id="toggleControls">⚙️ Controls</button>
    <div id="controls">
        <div class="control-group">
            <label>Base Speed</label>
            <div class="slider-container">
                <input type="range" id="baseSpeed" min="0.5" max="3" step="0.1" value="1">
                <span class="value-display">1</span>
            </div>
        </div>
        <div class="control-group">
            <label>Angry Repulsion Speed</label>
            <div class="slider-container">
                <input type="range" id="angrySpeedMultiplier" min="1.2" max="2" step="0.1" value="1.5">
                <span class="value-display">1.5</span>
            </div>
        </div>
        <div class="control-group">
            <label>Happy Group Duration</label>
            <div class="slider-container">
                <input type="range" id="happyGroupingDuration" min="1000" max="5000" step="100" value="2000">
                <span class="value-display">2000</span>
            </div>
        </div>
        <div class="control-group">
            <label>Influence Radius</label>
            <div class="slider-container">
                <input type="range" id="influenceRadius" min="50" max="200" step="10" value="100">
                <span class="value-display">100</span>
            </div>
        </div>
        <div class="control-group">
            <label>Influence Chance</label>
            <div class="slider-container">
                <input type="range" id="baseInfluenceChance" min="0.1" max="0.5" step="0.05" value="0.3">
                <span class="value-display">0.3</span>
            </div>
        </div>
        <div class="control-group">
            <label>Influence Amount</label>
            <div class="slider-container">
                <input type="range" id="influenceAmount" min="2" max="15" step="1" value="8">
                <span class="value-display">8</span>
            </div>
        </div>
    </div>
    <script src="js/constants.js"></script>
    <script src="js/person.js"></script>
    <script src="js/gesture.js"></script>
    <script src="js/main.js"></script>
</body>
</html> 