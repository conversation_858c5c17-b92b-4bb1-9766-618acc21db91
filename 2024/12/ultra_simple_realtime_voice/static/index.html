<!DOCTYPE html>
<html lang="en" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Voice Chat</title>
    <link href="https://cdn.jsdelivr.net/npm/daisyui@3.9.4/dist/full.css" rel="stylesheet" type="text/css" />
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js"></script>
    <style>
        #messages {
            max-height: 400px;
            overflow-y: auto;
            scroll-behavior: smooth;
        }
        .message {
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.5s ease-out;
            margin: 0.5rem 0;
            padding: 1rem;
            border-radius: 0.5rem;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .message.show {
            opacity: 1;
            transform: translateY(0);
        }
        .message:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
            transition: all 0.3s ease;
        }
        .message.error {
            background: rgba(255, 0, 0, 0.1);
            border-color: rgba(255, 0, 0, 0.2);
        }
        .message.info {
            background: rgba(0, 255, 255, 0.05);
            border-color: rgba(0, 255, 255, 0.1);
        }
        /* Scrollbar styling */
        #messages::-webkit-scrollbar {
            width: 8px;
        }
        #messages::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.1);
            border-radius: 4px;
        }
        #messages::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
        }
        #messages::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.2);
        }
    </style>
</head>
<body class="min-h-screen bg-base-300">
    <div class="container mx-auto p-4">
        <div class="card bg-base-100 shadow-xl">
            <div class="card-body">
                <h2 class="card-title text-primary">Voice Chat</h2>
                
                <!-- Status indicator -->
                <div class="flex items-center gap-2 mb-4">
                    <div id="statusIndicator" class="w-3 h-3 rounded-full bg-error"></div>
                    <span id="statusText" class="text-sm">Disconnected</span>
                </div>

                <!-- Messages -->
                <div id="messages" class="mb-4 p-4 bg-base-200 rounded-lg"></div>

                <!-- Controls -->
                <div class="flex gap-2">
                    <button id="startButton" class="btn btn-primary">
                        Start Voice Chat
                    </button>
                    <button id="stopButton" class="btn btn-error" disabled>
                        Stop Voice Chat
                    </button>
                </div>

                <!-- Loading animation -->
                <div id="loadingAnimation" class="hidden mt-4">
                    <span class="loading loading-dots loading-lg text-primary"></span>
                </div>
            </div>
        </div>
    </div>

    <script src="/static/js/app.js"></script>
</body>
</html> 