import os
import json
import time
import asyncio
import aiohttp
from typing import Dict, List
from openai import AsyncOpenAI
from thought_generator import ThoughtGenerator
from thought_router import ThoughtRouter
from thinkers import get_thinker
from utils import print_step, save_to_json, load_json, API_KEY

class ThoughtProcessor:
    def __init__(self):
        self.generator = ThoughtGenerator()
        self.router = ThoughtRouter()
        self.client = AsyncOpenAI(api_key=API_KEY)
        self.thoughts_data = {
            "initial_thoughts": [],
            "processed_thoughts": [],
            "summary": ""
        }
        
    async def process_thought(self, thought: str, cycle: int) -> Dict:
        """Process a single thought asynchronously"""
        try:
            # Route the thought
            thinker_type = await self.router.route_thought(thought)
            
            # Get appropriate thinker and process
            thinker = get_thinker(thinker_type)
            expanded_thought = await thinker.process_thought_async(thought)
            
            processed_thought = {
                "original": thought,
                "thinker_type": thinker_type,
                "expanded": expanded_thought,
                "cycle": cycle
            }
            
            # Validate the processed thought
            if not all(key in processed_thought for key in ["original", "thinker_type", "expanded", "cycle"]):
                raise ValueError("Invalid processed thought structure")
                
            return processed_thought
            
        except Exception as e:
            print_step(f"Error processing thought: {str(e)}", "red")
            return {
                "original": thought,
                "thinker_type": "error",
                "expanded": thought,
                "cycle": cycle
            }
    
    async def process_cycle(self, thoughts: List[str], cycle: int) -> List[Dict]:
        """Process all thoughts in a cycle concurrently"""
        tasks = [self.process_thought(thought, cycle) for thought in thoughts]
        processed_thoughts = await asyncio.gather(*tasks)
        
        # Validate processed thoughts
        valid_thoughts = []
        for thought in processed_thoughts:
            if isinstance(thought, dict) and all(key in thought for key in ["original", "thinker_type", "expanded", "cycle"]):
                valid_thoughts.append(thought)
            else:
                print_step(f"Invalid thought structure detected, skipping: {thought}", "yellow")
                
        return valid_thoughts
        
    async def process_topic_async(self, topic: str, cycles: int = 1) -> None:
        """Process a topic through multiple cycles of thought generation and analysis"""
        try:
            print_step(f"Starting thought processing for topic: {topic}")
            
            for cycle in range(cycles):
                print_step(f"Cycle {cycle + 1}/{cycles}", "yellow")
                
                # Generate initial thoughts
                thoughts = self.generator.generate_thoughts(topic)
                if thoughts:  # Only proceed if we have thoughts
                    self.thoughts_data["initial_thoughts"].extend(thoughts)
                    
                    # Process thoughts in parallel
                    processed_thoughts = await self.process_cycle(thoughts, cycle + 1)
                    if processed_thoughts:  # Only extend if we have valid processed thoughts
                        self.thoughts_data["processed_thoughts"].extend(processed_thoughts)
                        
                        # Save progress after each cycle
                        save_to_json(self.thoughts_data, "thoughts_progress.json")
                        
                        # Feed processed thoughts back as input for next cycle
                        topic = " ".join([t["expanded"] for t in processed_thoughts if "expanded" in t])
                    else:
                        print_step("No valid processed thoughts in this cycle", "yellow")
                else:
                    print_step("No thoughts generated in this cycle", "yellow")
            
            # Generate final summary
            await self._generate_summary_async()
            
        except Exception as e:
            print_step(f"Error in thought processing: {str(e)}", "red")
            # Save what we have so far
            save_to_json(self.thoughts_data, "thoughts_progress.json")
            
    async def _generate_summary_async(self) -> None:
        """Generate a final summary of all processed thoughts asynchronously"""
        try:
            print_step("Generating final summary")
            
            if not self.thoughts_data["processed_thoughts"]:
                print_step("No processed thoughts to summarize", "yellow")
                return
                
            response = await self.client.chat.completions.create(
                model="gpt-4o",
                response_format={"type": "json_object"},
                messages=[
                    {"role": "system", "content": """Create a comprehensive summary of the following thoughts. 
                    Return as JSON with key 'summary'. The summary should be a single string with proper formatting."""},
                    {"role": "user", "content": str(self.thoughts_data["processed_thoughts"])}
                ]
            )
            
            content = json.loads(response.choices[0].message.content)
            summary = content.get('summary', '')
            
            # Ensure summary is a string
            if isinstance(summary, list):
                summary = "\n".join(str(item) for item in summary)
            elif not isinstance(summary, str):
                summary = str(summary)
            
            self.thoughts_data["summary"] = summary
            
            # Save both JSON and markdown
            save_to_json(self.thoughts_data, "final_summary.json")
            
            # Also save summary to markdown
            with open("SUMMARY.md", "w", encoding="utf-8") as f:
                f.write("# Thought Processing Summary\n\n")
                f.write(summary)
                
        except Exception as e:
            print_step(f"Error generating summary: {str(e)}", "red")
            # Save what we have so far
            save_to_json(self.thoughts_data, "final_summary.json")

def main():
    # Ensure OPENAI_API_KEY is set
    if not os.getenv("OPENAI_API_KEY"):
        print_step("ERROR: OPENAI_API_KEY environment variable is not set!", "red")
        return
        
    processor = ThoughtProcessor()
    
    topic = input("\nEnter a topic to process: ")
    cycles = int(input("Enter number of cycles (1-5): "))
    
    # Run async process
    asyncio.run(processor.process_topic_async(topic, min(max(1, cycles), 5)))

if __name__ == "__main__":
    main() 