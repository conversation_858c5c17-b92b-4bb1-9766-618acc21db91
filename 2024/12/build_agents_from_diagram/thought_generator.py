import openai
import json
from typing import List, Dict
from utils import MODEL, MAX_THOUGHT_WORDS, MAX_THOUGHTS, print_step, API_KEY

class ThoughtGenerator:
    def __init__(self):
        openai.api_key = API_KEY
        
    def generate_thoughts(self, topic: str) -> List[str]:
        """Generate initial thought fragments from a topic"""
        try:
            print_step(f"Generating thoughts for topic: {topic}")
            
            response = openai.chat.completions.create(
                model=MODEL,
                response_format={"type": "json_object"},
                messages=[
                    {"role": "system", "content": f"""You are a thought generator that creates {MAX_THOUGHTS} brief thought fragments 
                    (max {MAX_THOUGHT_WORDS} words each) about a given topic. Return as JSON array with key 'thoughts'."""},
                    {"role": "user", "content": f"Generate thoughts about: {topic}"}
                ]
            )
            
            content = json.loads(response.choices[0].message.content)
            return content['thoughts']
            
        except Exception as e:
            print_step(f"Error generating thoughts: {str(e)}", "red")
            return [] 