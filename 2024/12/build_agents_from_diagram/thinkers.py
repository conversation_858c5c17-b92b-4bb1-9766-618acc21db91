from openai import AsyncOpenAI
import json
from typing import Dict
from utils import MODEL, print_step, API_KEY

class BaseThinker:
    def __init__(self, thinker_type: str):
        self.type = thinker_type
        self.client = AsyncOpenAI(api_key=API_KEY)
        
    async def process_thought_async(self, thought: str) -> str:
        """Process a thought according to the thinker's type asynchronously"""
        try:
            print_step(f"{self.type.capitalize()} thinker processing: {thought[:30]}...")
            
            response = await self.client.chat.completions.create(
                model=MODEL,
                response_format={"type": "json_object"},
                messages=[
                    {"role": "system", "content": f"""You are a {self.type} thinker. Analyze the given thought 
                    from a {self.type} perspective and expand it into a full thought. 
                    Return as JSON with key 'expanded_thought'."""},
                    {"role": "user", "content": f"Process this thought: {thought}"}
                ]
            )
            
            content = json.loads(response.choices[0].message.content)
            return content['expanded_thought']
            
        except Exception as e:
            print_step(f"Error in {self.type} thinking: {str(e)}", "red")
            return thought

class LogicalThinker(BaseThinker):
    def __init__(self):
        super().__init__("logical")

class CreativeThinker(BaseThinker):
    def __init__(self):
        super().__init__("creative")

class PhilosophicalThinker(BaseThinker):
    def __init__(self):
        super().__init__("philosophical")

class EmotiveThinker(BaseThinker):
    def __init__(self):
        super().__init__("emotive")

class StrategicThinker(BaseThinker):
    def __init__(self):
        super().__init__("strategic")

def get_thinker(thinker_type: str) -> BaseThinker:
    """Factory function to get the appropriate thinker"""
    thinkers = {
        "logical": LogicalThinker,
        "creative": CreativeThinker,
        "philosophical": PhilosophicalThinker,
        "emotive": EmotiveThinker,
        "strategic": StrategicThinker
    }
    return thinkers.get(thinker_type, LogicalThinker)() 