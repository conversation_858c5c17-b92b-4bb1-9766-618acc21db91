from openai import AsyncOpenAI
import json
from typing import Dict, List
from utils import MODEL, THINKER_TYPES, print_step, API_KEY

class ThoughtRouter:
    def __init__(self):
        self.client = AsyncOpenAI(api_key=API_KEY)
        
    async def route_thought(self, thought: str) -> str:
        """Route a thought to the appropriate thinker type"""
        try:
            print_step(f"Routing thought: {thought[:30]}...")
            
            response = await self.client.chat.completions.create(
                model=MODEL,
                response_format={"type": "json_object"},
                messages=[
                    {"role": "system", "content": f"""You are a thought router that determines which type of thinker 
                    should process a thought. Available types: {', '.join(THINKER_TYPES)}. 
                    Return as JSON with key 'thinker_type'."""},
                    {"role": "user", "content": f"Route this thought: {thought}"}
                ]
            )
            
            content = json.loads(response.choices[0].message.content)
            return content['thinker_type']
            
        except Exception as e:
            print_step(f"Error routing thought: {str(e)}", "red")
            return "logical"  # Default to logical if there's an error 