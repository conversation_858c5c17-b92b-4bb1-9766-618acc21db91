import os
from termcolor import colored
import json
from typing import Any, Dict, List
import time
import tempfile
import shutil

# Constants
MODEL = "gpt-4o"
MAX_THOUGHT_WORDS = 20
MAX_THOUGHTS = 10
THINKER_TYPES = ["logical", "creative", "philosophical", "emotive", "strategic"]

# Ensure you set OPENAI_API_KEY in your system environment variables
API_KEY = os.getenv("OPENAI_API_KEY")

def print_step(step: str, color: str = "cyan") -> None:
    """Print a colored step message"""
    try:
        print(colored(f"\n[{step}]", color))
    except Exception as e:
        print(f"Error printing step: {str(e)}")

def save_to_json(data: Any, filename: str) -> None:
    """Save data to a JSON file with proper formatting using atomic write operations"""
    try:
        # Create a temporary file in the same directory
        temp_dir = os.path.dirname(os.path.abspath(filename))
        
        # Ensure the directory exists
        os.makedirs(temp_dir, exist_ok=True)
        
        # Create a temporary file
        with tempfile.NamedTemporaryFile(mode='w', 
                                       dir=temp_dir, 
                                       delete=False, 
                                       encoding='utf-8',
                                       suffix='.tmp') as tf:
            # Write to temporary file
            json.dump(data, tf, indent=2, ensure_ascii=False)
            temp_name = tf.name
            
        # Atomic rename of temporary file to target file
        shutil.move(temp_name, filename)
        print_step(f"Saved data to {filename}", "green")
        
    except Exception as e:
        print_step(f"Error saving JSON: {str(e)}", "red")
        # Try to clean up temporary file if it exists
        try:
            if 'temp_name' in locals():
                os.unlink(temp_name)
        except:
            pass

def load_json(filename: str) -> Dict:
    """Load data from a JSON file with validation"""
    try:
        if not os.path.exists(filename):
            print_step(f"File {filename} does not exist", "yellow")
            return {}
            
        with open(filename, 'r', encoding='utf-8') as f:
            data = json.load(f)
            
        # Validate the structure
        if not isinstance(data, dict):
            raise ValueError("Loaded JSON is not a dictionary")
            
        return data
        
    except json.JSONDecodeError as e:
        print_step(f"Error decoding JSON: {str(e)}", "red")
        # Try to recover the last valid JSON
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                content = f.read()
                # Find the last complete JSON object
                last_brace = content.rstrip().rfind('}')
                if last_brace != -1:
                    valid_content = content[:last_brace + 1]
                    return json.loads(valid_content)
        except:
            pass
        return {}
        
    except Exception as e:
        print_step(f"Error loading JSON: {str(e)}", "red")
        return {}
        